"""
Vendor management views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from sqlalchemy import or_, and_, func, desc
from sqlalchemy.orm import joinedload
from app import db
from app.models.vendor import Vendor
from app.models.account import Account
from app.forms.vendor_forms import VendorForm, VendorSearchForm, VendorImportForm, VendorBulkActionForm
from app.utils.decorators import permission_required
from app.utils.helpers import get_pagination_params, generate_reference_number
from decimal import Decimal
from datetime import datetime, date, timedelta

# Create blueprint
vendors_bp = Blueprint('vendors', __name__, url_prefix='/vendors')


@vendors_bp.route('/')
@login_required
@permission_required('vendors')
def index():
    """Display vendors list with search and filtering"""
    form = VendorSearchForm(request.args)
    page, per_page = get_pagination_params()
    
    # Build query
    query = Vendor.query.options(
        joinedload(Vendor.account)
    )
    
    # Apply search filters
    if form.search.data:
        search_term = f"%{form.search.data}%"
        query = query.filter(
            or_(
                Vendor.name.ilike(search_term),
                Vendor.name_en.ilike(search_term),
                Vendor.tax_id.ilike(search_term),
                Vendor.email.ilike(search_term),
                Vendor.phone.ilike(search_term)
            )
        )
    
    if form.vendor_type.data:
        query = query.filter(Vendor.vendor_type == form.vendor_type.data)
    
    if form.status.data:
        if form.status.data == 'active':
            query = query.filter(Vendor.is_active == True)
        elif form.status.data == 'inactive':
            query = query.filter(Vendor.is_active == False)
    
    # Order by name
    query = query.order_by(Vendor.name)
    
    # Paginate
    vendors = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Get statistics
    stats = {
        'total_vendors': Vendor.query.count(),
        'active_vendors': Vendor.query.filter_by(is_active=True).count(),
        'total_payables': Decimal('0'),  # Will be calculated from bills
        'average_balance': Decimal('0')
    }
    
    return render_template(
        'vendors/index.html',
        vendors=vendors,
        form=form,
        stats=stats,
        title='الموردين'
    )


@vendors_bp.route('/new')
@login_required
@permission_required('vendors')
def new():
    """Display form for creating new vendor"""
    form = VendorForm()
    
    return render_template(
        'vendors/form.html',
        form=form,
        title='مورد جديد'
    )


@vendors_bp.route('/create', methods=['POST'])
@login_required
@permission_required('vendors')
def create():
    """Create new vendor"""
    form = VendorForm()
    
    if form.validate_on_submit():
        try:
            # Create vendor
            vendor = Vendor(
                name=form.name.data,
                name_en=form.name_en.data or None,
                vendor_type=form.vendor_type.data,
                tax_id=form.tax_id.data or None,
                email=form.email.data or None,
                phone=form.phone.data or None,
                website=form.website.data or None,
                address=form.address.data or None,
                city=form.city.data or None,
                state=form.state.data or None,
                postal_code=form.postal_code.data or None,
                country=form.country.data or None,
                credit_limit=form.credit_limit.data or None,
                payment_terms=form.payment_terms.data or None,
                currency=form.currency.data or 'EGP',
                account_id=form.account_id.data or None,
                notes=form.notes.data or None,
                is_active=form.is_active.data,
                created_by_id=current_user.id
            )
            
            db.session.add(vendor)
            db.session.commit()
            
            flash(f'تم إنشاء المورد "{vendor.name}" بنجاح', 'success')
            return redirect(url_for('vendors.detail', vendor_id=vendor.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء المورد: {str(e)}', 'error')
    
    return render_template(
        'vendors/form.html',
        form=form,
        title='مورد جديد'
    )


@vendors_bp.route('/<uuid:vendor_id>')
@login_required
@permission_required('vendors')
def detail(vendor_id):
    """Display vendor details"""
    vendor = Vendor.query.options(
        joinedload(Vendor.account),
        joinedload(Vendor.created_by)
    ).get_or_404(vendor_id)
    
    # Get recent bills (when bill model is available)
    recent_bills = []
    
    # Get recent activity
    recent_activity = []
    
    return render_template(
        'vendors/detail.html',
        vendor=vendor,
        recent_bills=recent_bills,
        recent_activity=recent_activity,
        title=f'تفاصيل المورد: {vendor.name}'
    )


@vendors_bp.route('/<uuid:vendor_id>/edit')
@login_required
@permission_required('vendors')
def edit(vendor_id):
    """Display form for editing vendor"""
    vendor = Vendor.query.get_or_404(vendor_id)
    
    form = VendorForm(obj=vendor, vendor=vendor)
    
    return render_template(
        'vendors/form.html',
        form=form,
        vendor=vendor,
        title=f'تعديل المورد: {vendor.name}'
    )


@vendors_bp.route('/<uuid:vendor_id>/update', methods=['POST'])
@login_required
@permission_required('vendors')
def update(vendor_id):
    """Update vendor"""
    vendor = Vendor.query.get_or_404(vendor_id)
    
    form = VendorForm(vendor=vendor)
    
    if form.validate_on_submit():
        try:
            # Update vendor details
            vendor.name = form.name.data
            vendor.name_en = form.name_en.data or None
            vendor.vendor_type = form.vendor_type.data
            vendor.tax_id = form.tax_id.data or None
            vendor.email = form.email.data or None
            vendor.phone = form.phone.data or None
            vendor.website = form.website.data or None
            vendor.address = form.address.data or None
            vendor.city = form.city.data or None
            vendor.state = form.state.data or None
            vendor.postal_code = form.postal_code.data or None
            vendor.country = form.country.data or None
            vendor.credit_limit = form.credit_limit.data or None
            vendor.payment_terms = form.payment_terms.data or None
            vendor.currency = form.currency.data or 'EGP'
            vendor.account_id = form.account_id.data or None
            vendor.notes = form.notes.data or None
            vendor.is_active = form.is_active.data
            vendor.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            flash(f'تم تحديث المورد "{vendor.name}" بنجاح', 'success')
            return redirect(url_for('vendors.detail', vendor_id=vendor.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث المورد: {str(e)}', 'error')
    
    return render_template(
        'vendors/form.html',
        form=form,
        vendor=vendor,
        title=f'تعديل المورد: {vendor.name}'
    )


@vendors_bp.route('/<uuid:vendor_id>/delete', methods=['POST'])
@login_required
@permission_required('vendors')
def delete(vendor_id):
    """Delete vendor"""
    vendor = Vendor.query.get_or_404(vendor_id)
    
    if not vendor.can_be_deleted():
        flash('لا يمكن حذف هذا المورد لوجود معاملات مرتبطة به', 'error')
        return redirect(url_for('vendors.detail', vendor_id=vendor.id))
    
    try:
        vendor_name = vendor.name
        db.session.delete(vendor)
        db.session.commit()
        
        flash(f'تم حذف المورد "{vendor_name}" بنجاح', 'success')
        return redirect(url_for('vendors.index'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف المورد: {str(e)}', 'error')
        return redirect(url_for('vendors.detail', vendor_id=vendor.id))


@vendors_bp.route('/<uuid:vendor_id>/statement')
@login_required
@permission_required('vendors')
def statement(vendor_id):
    """Display vendor statement"""
    vendor = Vendor.query.get_or_404(vendor_id)
    
    # Get date range from request
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    if date_from:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
    else:
        # Default to current month
        today = date.today()
        date_from = date(today.year, today.month, 1)
    
    if date_to:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
    else:
        date_to = date.today()
    
    # Get transactions (will be implemented when bill/payment models are ready)
    transactions = []
    opening_balance = Decimal('0')
    total_purchases = Decimal('0')
    total_payments = Decimal('0')
    closing_balance = Decimal('0')
    
    # Aging analysis
    aging_analysis = {
        'current': Decimal('0'),
        'days_30': Decimal('0'),
        'days_60': Decimal('0'),
        'days_90': Decimal('0'),
        'over_120': Decimal('0'),
        'total': Decimal('0')
    }
    
    return render_template(
        'vendors/statement.html',
        vendor=vendor,
        transactions=transactions,
        date_from=date_from,
        date_to=date_to,
        opening_balance=opening_balance,
        total_purchases=total_purchases,
        total_payments=total_payments,
        closing_balance=closing_balance,
        aging_analysis=aging_analysis,
        title=f'كشف حساب المورد: {vendor.name}'
    )


@vendors_bp.route('/import_data')
@login_required
@permission_required('vendors')
def import_data():
    """Display import form"""
    form = VendorImportForm()
    
    return render_template(
        'vendors/import.html',
        form=form,
        title='استيراد الموردين'
    )


@vendors_bp.route('/bulk_action', methods=['POST'])
@login_required
@permission_required('vendors')
def bulk_action():
    """Execute bulk action on vendors"""
    action = request.form.get('action')
    vendor_ids = request.form.get('vendor_ids', '').split(',')
    
    if not action or not vendor_ids:
        flash('يرجى تحديد إجراء وموردين', 'error')
        return redirect(url_for('vendors.index'))
    
    try:
        vendors = Vendor.query.filter(Vendor.id.in_(vendor_ids)).all()
        
        if action == 'activate':
            for vendor in vendors:
                vendor.is_active = True
            db.session.commit()
            flash(f'تم تفعيل {len(vendors)} مورد بنجاح', 'success')
            
        elif action == 'deactivate':
            for vendor in vendors:
                vendor.is_active = False
            db.session.commit()
            flash(f'تم إلغاء تفعيل {len(vendors)} مورد بنجاح', 'success')
            
        elif action == 'delete':
            deletable_vendors = [v for v in vendors if v.can_be_deleted()]
            for vendor in deletable_vendors:
                db.session.delete(vendor)
            db.session.commit()
            flash(f'تم حذف {len(deletable_vendors)} مورد بنجاح', 'success')
            
            if len(deletable_vendors) < len(vendors):
                flash(f'{len(vendors) - len(deletable_vendors)} مورد لم يتم حذفهم لوجود معاملات مرتبطة', 'warning')
        
        elif action == 'export':
            # Implement export functionality
            flash('سيتم تنفيذ التصدير قريباً', 'info')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تنفيذ الإجراء: {str(e)}', 'error')
    
    return redirect(url_for('vendors.index'))


# API Routes
@vendors_bp.route('/<uuid:vendor_id>/balance')
@login_required
@permission_required('vendors')
def balance(vendor_id):
    """API endpoint to get vendor balance"""
    vendor = Vendor.query.get_or_404(vendor_id)
    
    return jsonify({
        'balance': float(vendor.get_balance()),
        'credit_limit': float(vendor.credit_limit or 0),
        'credit_available': float((vendor.credit_limit or 0) - vendor.get_balance()),
        'last_updated': datetime.utcnow().isoformat()
    })


@vendors_bp.route('/search')
@login_required
@permission_required('vendors')
def search():
    """API endpoint for vendor search"""
    query = request.args.get('q', '')
    limit = min(int(request.args.get('limit', 10)), 50)
    
    if len(query) < 2:
        return jsonify([])
    
    vendors = Vendor.query.filter(
        and_(
            Vendor.is_active == True,
            or_(
                Vendor.name.ilike(f'%{query}%'),
                Vendor.name_en.ilike(f'%{query}%'),
                Vendor.tax_id.ilike(f'%{query}%')
            )
        )
    ).limit(limit).all()
    
    return jsonify([
        {
            'id': str(vendor.id),
            'name': vendor.name,
            'name_en': vendor.name_en,
            'tax_id': vendor.tax_id,
            'email': vendor.email,
            'phone': vendor.phone,
            'balance': float(vendor.get_balance())
        }
        for vendor in vendors
    ])
