{% extends "base.html" %}

{% block title %}دفتر الأستاذ العام - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-book-open me-3"></i>
                دفتر الأستاذ العام
            </h1>
            <p class="mb-0 mt-2">عرض حركات الحسابات التفصيلية</p>
        </div>
        <div>
            <a href="{{ url_for('journal.index') }}" class="btn btn-outline-light">
                <i class="fas fa-book me-2"></i>
                دفتر اليومية
            </a>
            <button type="button" class="btn btn-outline-light" onclick="window.print()">
                <i class="fas fa-print me-2"></i>
                طباعة
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                <label for="account_id" class="form-label">الحساب</label>
                <select class="form-select" id="account_id" name="account_id">
                    <option value="">جميع الحسابات</option>
                    {% for account in accounts %}
                    <option value="{{ account.id }}" {{ 'selected' if account.id|string == selected_account_id else '' }}>
                        {{ account.code }} - {{ account.name }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <label for="date_from" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="date_from" name="date_from" 
                       value="{{ date_from.isoformat() if date_from else '' }}">
            </div>
            <div class="col-md-2">
                <label for="date_to" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="date_to" name="date_to" 
                       value="{{ date_to.isoformat() if date_to else '' }}">
            </div>
            <div class="col-md-2">
                <label for="posted_only" class="form-label">القيود المرحلة فقط</label>
                <select class="form-select" id="posted_only" name="posted_only">
                    <option value="">الكل</option>
                    <option value="1" {{ 'selected' if posted_only else '' }}>مرحلة فقط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>
                        عرض
                    </button>
                </div>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('journal.ledger') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

{% if selected_account %}
<!-- Single Account Ledger -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-user me-2"></i>
            دفتر أستاذ الحساب: {{ selected_account.code }} - {{ selected_account.name }}
        </h5>
    </div>
    <div class="card-body">
        <!-- Account Summary -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center border-primary">
                    <div class="card-body">
                        <h5 class="card-title text-primary">الرصيد الافتتاحي</h5>
                        <h3 class="text-primary">{{ opening_balance|currency }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-success">
                    <div class="card-body">
                        <h5 class="card-title text-success">إجمالي المدين</h5>
                        <h3 class="text-success">{{ total_debits|currency }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-danger">
                    <div class="card-body">
                        <h5 class="card-title text-danger">إجمالي الدائن</h5>
                        <h3 class="text-danger">{{ total_credits|currency }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-info">
                    <div class="card-body">
                        <h5 class="card-title text-info">الرصيد الختامي</h5>
                        <h3 class="text-info">{{ closing_balance|currency }}</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Transactions Table -->
        {% if transactions %}
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-light">
                    <tr>
                        <th>التاريخ</th>
                        <th>رقم القيد</th>
                        <th>البيان</th>
                        <th class="text-center">مدين</th>
                        <th class="text-center">دائن</th>
                        <th class="text-center">الرصيد</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Opening Balance Row -->
                    {% if opening_balance != 0 %}
                    <tr class="table-info">
                        <td>{{ date_from|date if date_from else 'بداية الفترة' }}</td>
                        <td class="text-center">-</td>
                        <td><strong>الرصيد الافتتاحي</strong></td>
                        <td class="text-center">
                            {% if opening_balance > 0 %}
                                <strong class="text-success">{{ opening_balance|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if opening_balance < 0 %}
                                <strong class="text-danger">{{ opening_balance|abs|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <strong class="text-primary">{{ opening_balance|currency }}</strong>
                        </td>
                        <td class="text-center">-</td>
                    </tr>
                    {% endif %}
                    
                    <!-- Transaction Rows -->
                    {% for transaction in transactions %}
                    <tr class="{{ 'table-warning' if not transaction.journal_entry.is_posted else '' }}">
                        <td>{{ transaction.journal_entry.entry_date|date }}</td>
                        <td>
                            <a href="{{ url_for('journal.detail', entry_id=transaction.journal_entry.id) }}" 
                               class="text-decoration-none">
                                {{ transaction.journal_entry.reference_number }}
                            </a>
                        </td>
                        <td>
                            {% if transaction.description %}
                                {{ transaction.description }}
                            {% else %}
                                {{ transaction.journal_entry.description }}
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if transaction.dc == 'D' %}
                                <strong class="text-success">{{ transaction.amount|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if transaction.dc == 'C' %}
                                <strong class="text-danger">{{ transaction.amount|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <strong class="text-{{ 'success' if transaction.running_balance >= 0 else 'danger' }}">
                                {{ transaction.running_balance|currency }}
                            </strong>
                        </td>
                        <td class="text-center">
                            {% if transaction.journal_entry.is_posted %}
                                <span class="badge bg-success">مرحل</span>
                            {% else %}
                                <span class="badge bg-warning">مسودة</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                    
                    <!-- Closing Balance Row -->
                    <tr class="table-dark">
                        <th colspan="3" class="text-center">الرصيد الختامي</th>
                        <th class="text-center">
                            {% if closing_balance > 0 %}
                                <strong class="text-success">{{ closing_balance|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </th>
                        <th class="text-center">
                            {% if closing_balance < 0 %}
                                <strong class="text-danger">{{ closing_balance|abs|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </th>
                        <th class="text-center">
                            <strong class="text-primary">{{ closing_balance|currency }}</strong>
                        </th>
                        <th></th>
                    </tr>
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
            <h5>لا توجد حركات</h5>
            <p class="text-muted">لا توجد حركات لهذا الحساب في الفترة المحددة.</p>
        </div>
        {% endif %}
    </div>
</div>

{% else %}
<!-- All Accounts Summary -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            ملخص جميع الحسابات
        </h5>
    </div>
    <div class="card-body">
        {% if account_summaries %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رمز الحساب</th>
                        <th>اسم الحساب</th>
                        <th>نوع الحساب</th>
                        <th class="text-center">عدد الحركات</th>
                        <th class="text-center">إجمالي المدين</th>
                        <th class="text-center">إجمالي الدائن</th>
                        <th class="text-center">الرصيد</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for summary in account_summaries %}
                    <tr>
                        <td><strong class="text-primary">{{ summary.account.code }}</strong></td>
                        <td>
                            <a href="{{ url_for('accounts.detail', account_id=summary.account.id) }}" 
                               class="text-decoration-none">
                                {{ summary.account.name }}
                            </a>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if summary.account.type == 'Asset' else 'danger' if summary.account.type == 'Liability' else 'primary' if summary.account.type == 'Equity' else 'info' if summary.account.type == 'Income' else 'warning' }}">
                                {{ summary.account.get_type_display() }}
                            </span>
                        </td>
                        <td class="text-center">{{ summary.transaction_count }}</td>
                        <td class="text-center">
                            <span class="text-success fw-bold">{{ summary.total_debits|currency }}</span>
                        </td>
                        <td class="text-center">
                            <span class="text-danger fw-bold">{{ summary.total_credits|currency }}</span>
                        </td>
                        <td class="text-center">
                            <span class="fw-bold text-{{ 'success' if summary.balance >= 0 else 'danger' }}">
                                {{ summary.balance|currency }}
                            </span>
                        </td>
                        <td>
                            <a href="{{ url_for('journal.ledger', account_id=summary.account.id, date_from=date_from, date_to=date_to) }}" 
                               class="btn btn-sm btn-outline-primary" title="عرض دفتر الأستاذ">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-book-open fa-3x text-muted mb-3"></i>
            <h5>لا توجد حركات</h5>
            <p class="text-muted">لا توجد حركات محاسبية في الفترة المحددة.</p>
            <a href="{{ url_for('journal.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة قيد جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .page-header .btn,
    .card:first-child {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 11px;
    }
    
    .badge {
        color: black !important;
        background-color: transparent !important;
        border: 1px solid black !important;
    }
}

.table th {
    background-color: #f8f9fa !important;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6 !important;
}

.running-balance {
    font-family: 'Courier New', monospace;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Set default date range to current month
    if (!$('#date_from').val()) {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        $('#date_from').val(firstDay.toISOString().split('T')[0]);
        $('#date_to').val(today.toISOString().split('T')[0]);
    }
    
    // Auto-submit form on account change
    $('#account_id').change(function() {
        $(this).closest('form').submit();
    });
});
</script>
{% endblock %}
