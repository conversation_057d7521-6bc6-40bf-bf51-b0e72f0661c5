"""
PDF generation utilities for invoices and receipts
"""

import os
from io import BytesIO
from datetime import datetime
from reportlab.lib.pagesizes import A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from flask import current_app
from app.models.system_setting import SystemSetting

class PDFGenerator:
    """Base PDF generator class"""
    
    def __init__(self):
        self.setup_fonts()
        self.company_info = SystemSetting.get_company_info()
        self.invoice_settings = SystemSetting.get_invoice_settings()
    
    def setup_fonts(self):
        """Setup Arabic fonts for PDF generation"""
        try:
            # Try to register Arabic font (you'll need to add Arabic font files)
            # For now, we'll use default fonts
            pass
        except:
            pass
    
    def create_header(self, doc_type="فاتورة", doc_number="", doc_date=""):
        """Create document header"""
        elements = []
        
        # Company info
        company_data = [
            [self.company_info['name'], doc_type],
            [self.company_info['address'], f"رقم: {doc_number}"],
            [f"هاتف: {self.company_info['phone']}", f"تاريخ: {doc_date}"],
            [f"الرقم الضريبي: {self.company_info['tax_id']}", ""]
        ]
        
        company_table = Table(company_data, colWidths=[3*inch, 3*inch])
        company_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('FONTSIZE', (1, 0), (1, 0), 16),  # Document type
            ('FONTSIZE', (1, 1), (1, 1), 14),  # Document number
            ('TEXTCOLOR', (1, 0), (1, 1), colors.blue),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        elements.append(company_table)
        elements.append(Spacer(1, 0.5*inch))
        
        return elements
    
    def create_customer_info(self, customer):
        """Create customer information section"""
        elements = []
        
        customer_data = [
            ["معلومات العميل", ""],
            [f"الاسم: {customer.name}", ""],
            [f"العنوان: {customer.address or 'غير محدد'}", ""],
            [f"الهاتف: {customer.phone or 'غير محدد'}", ""],
            [f"الرقم الضريبي: {customer.tax_id or 'غير محدد'}", ""]
        ]
        
        customer_table = Table(customer_data, colWidths=[4*inch, 2*inch])
        customer_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('FONTSIZE', (0, 0), (0, 0), 12),  # Header
            ('TEXTCOLOR', (0, 0), (0, 0), colors.blue),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 3),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ]))
        
        elements.append(customer_table)
        elements.append(Spacer(1, 0.3*inch))
        
        return elements

class InvoicePDFGenerator(PDFGenerator):
    """PDF generator for invoices"""
    
    def generate_invoice_pdf(self, invoice):
        """Generate PDF for invoice"""
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=1*inch, leftMargin=1*inch,
                               topMargin=1*inch, bottomMargin=1*inch)
        
        elements = []
        
        # Header
        elements.extend(self.create_header(
            doc_type="فاتورة ضريبية",
            doc_number=invoice.invoice_number,
            doc_date=invoice.issue_date.strftime('%Y-%m-%d')
        ))
        
        # Customer info
        elements.extend(self.create_customer_info(invoice.customer))
        
        # Invoice lines
        elements.extend(self.create_invoice_lines(invoice))
        
        # Totals
        elements.extend(self.create_invoice_totals(invoice))
        
        # Footer
        elements.extend(self.create_footer())
        
        doc.build(elements)
        buffer.seek(0)
        return buffer
    
    def create_invoice_lines(self, invoice):
        """Create invoice lines table"""
        elements = []
        
        # Table headers
        headers = ["المجموع", "ضريبة", "خصم", "السعر", "الكمية", "الوصف", "م"]
        
        # Table data
        data = [headers]
        
        for i, line in enumerate(invoice.lines, 1):
            row = [
                f"{line.line_total:.2f}",
                f"{line.get_tax_amount():.2f}",
                f"{line.get_discount_amount():.2f}",
                f"{line.unit_price:.2f}",
                f"{line.quantity:.3f}",
                line.description[:50] + "..." if len(line.description) > 50 else line.description,
                str(i)
            ]
            data.append(row)
        
        # Create table
        table = Table(data, colWidths=[1*inch, 0.8*inch, 0.8*inch, 1*inch, 0.8*inch, 2.5*inch, 0.5*inch])
        table.setStyle(TableStyle([
            # Header style
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            
            # Data style
            ('ALIGN', (5, 1), (5, -1), 'RIGHT'),  # Description column
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 0.3*inch))
        
        return elements
    
    def create_invoice_totals(self, invoice):
        """Create invoice totals section"""
        elements = []
        
        totals_data = [
            ["المجموع الفرعي:", f"{invoice.subtotal:.2f} {self.invoice_settings['currency_symbol']}"],
            ["إجمالي الخصم:", f"{invoice.discount_amount:.2f} {self.invoice_settings['currency_symbol']}"],
            ["إجمالي الضريبة:", f"{invoice.tax_amount:.2f} {self.invoice_settings['currency_symbol']}"],
            ["المجموع الكلي:", f"{invoice.total_amount:.2f} {self.invoice_settings['currency_symbol']}"]
        ]
        
        totals_table = Table(totals_data, colWidths=[2*inch, 2*inch])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('FONTSIZE', (0, -1), (-1, -1), 14),  # Total row
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.blue),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('TOPPADDING', (0, 0), (-1, -1), 6),
            ('LINEBELOW', (0, -1), (-1, -1), 2, colors.blue),
        ]))
        
        elements.append(totals_table)
        elements.append(Spacer(1, 0.5*inch))
        
        return elements
    
    def create_footer(self):
        """Create document footer"""
        elements = []
        
        footer_text = "شكراً لتعاملكم معنا"
        footer_para = Paragraph(footer_text, ParagraphStyle(
            'Footer',
            fontSize=10,
            alignment=1,  # Center
            textColor=colors.grey
        ))
        
        elements.append(footer_para)
        
        return elements

class ReceiptPDFGenerator(PDFGenerator):
    """PDF generator for receipts"""
    
    def generate_receipt_pdf(self, receipt):
        """Generate PDF for receipt"""
        buffer = BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=1*inch, leftMargin=1*inch,
                               topMargin=1*inch, bottomMargin=1*inch)
        
        elements = []
        
        # Header
        elements.extend(self.create_header(
            doc_type="إيصال استلام",
            doc_number=receipt.receipt_number,
            doc_date=receipt.receipt_date.strftime('%Y-%m-%d')
        ))
        
        # Customer info
        elements.extend(self.create_customer_info(receipt.customer))
        
        # Receipt details
        elements.extend(self.create_receipt_details(receipt))
        
        # Footer
        elements.extend(self.create_footer())
        
        doc.build(elements)
        buffer.seek(0)
        return buffer
    
    def create_receipt_details(self, receipt):
        """Create receipt details section"""
        elements = []
        
        details_data = [
            ["تفاصيل الإيصال", ""],
            [f"المبلغ المستلم: {receipt.amount_received:.2f} {self.invoice_settings['currency_symbol']}", ""],
            [f"طريقة الدفع: {receipt.get_payment_method_display()}", ""],
            [f"رقم المرجع: {receipt.reference_number or 'غير محدد'}", ""],
            [f"ملاحظات: {receipt.notes or 'لا توجد'}", ""]
        ]
        
        details_table = Table(details_data, colWidths=[4*inch, 2*inch])
        details_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('FONTSIZE', (0, 0), (0, 0), 14),  # Header
            ('TEXTCOLOR', (0, 0), (0, 0), colors.blue),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ]))
        
        elements.append(details_table)
        elements.append(Spacer(1, 0.5*inch))
        
        return elements

def generate_invoice_pdf(invoice):
    """Generate PDF for invoice"""
    generator = InvoicePDFGenerator()
    return generator.generate_invoice_pdf(invoice)

def generate_receipt_pdf(receipt):
    """Generate PDF for receipt"""
    generator = ReceiptPDFGenerator()
    return generator.generate_receipt_pdf(receipt)
