# Database Configuration
DATABASE_URL=postgresql://systemtax_user:systemtax_password@localhost:5432/systemtax

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Flask Configuration
SECRET_KEY=your-very-secret-key-change-this-in-production
FLASK_ENV=development
FLASK_DEBUG=True

# Application Settings
APP_NAME=SystemTax
APP_VERSION=1.0.0
COMPANY_NAME=شركتك
COMPANY_TAX_ID=*********

# Tax Authority API Configuration (Egypt)
TAX_API_BASE_URL=https://api.eta.gov.eg
TAX_API_CLIENT_ID=your-client-id
TAX_API_CLIENT_SECRET=your-client-secret
TAX_API_ENVIRONMENT=sandbox  # sandbox or production

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# File Upload Settings
MAX_CONTENT_LENGTH=16777216  # 16MB
UPLOAD_FOLDER=uploads

# Pagination
ITEMS_PER_PAGE=20

# Cache Settings
CACHE_TYPE=redis
CACHE_DEFAULT_TIMEOUT=300

# Security Settings
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
PERMANENT_SESSION_LIFETIME=3600  # 1 hour
