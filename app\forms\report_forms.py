"""
Forms for report generation and filtering
"""

from flask_wtf import FlaskForm
from wtforms import (
    StringField, SelectField, DateField, BooleanField, 
    TextAreaField, IntegerField, DecimalField
)
from wtforms.validators import DataRequired, Optional, NumberRange, Length
from datetime import date, datetime, timedelta
from dateutil.relativedelta import relativedelta


class BaseReportForm(FlaskForm):
    """Base form for all reports with common date fields"""
    
    date_from = DateField(
        'من تاريخ',
        validators=[DataRequired(message='تاريخ البداية مطلوب')],
        default=lambda: date.today().replace(day=1),  # First day of current month
        render_kw={'class': 'form-control'}
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[DataRequired(message='تاريخ النهاية مطلوب')],
        default=date.today,
        render_kw={'class': 'form-control'}
    )
    
    def validate(self, extra_validators=None):
        """Validate date range"""
        if not super().validate(extra_validators):
            return False
        
        if self.date_from.data and self.date_to.data:
            if self.date_from.data > self.date_to.data:
                self.date_to.errors.append('تاريخ النهاية يجب أن يكون بعد تاريخ البداية')
                return False
        
        return True


class TrialBalanceForm(BaseReportForm):
    """Form for Trial Balance report"""
    
    account_type = SelectField(
        'نوع الحساب',
        choices=[
            ('', 'جميع الحسابات'),
            ('Asset', 'الأصول'),
            ('Liability', 'الخصوم'),
            ('Equity', 'حقوق الملكية'),
            ('Income', 'الإيرادات'),
            ('Expense', 'المصروفات')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    show_zero_balances = BooleanField(
        'عرض الحسابات ذات الرصيد صفر',
        default=False,
        render_kw={'class': 'form-check-input'}
    )
    
    group_by_type = BooleanField(
        'تجميع حسب نوع الحساب',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class IncomeStatementForm(BaseReportForm):
    """Form for Income Statement report"""
    
    comparison_period = SelectField(
        'فترة المقارنة',
        choices=[
            ('', 'بدون مقارنة'),
            ('previous_month', 'الشهر السابق'),
            ('previous_quarter', 'الربع السابق'),
            ('previous_year', 'السنة السابقة'),
            ('same_period_last_year', 'نفس الفترة من السنة السابقة')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    show_details = BooleanField(
        'عرض التفاصيل',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    include_ratios = BooleanField(
        'تضمين النسب المالية',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class BalanceSheetForm(BaseReportForm):
    """Form for Balance Sheet report"""
    
    as_of_date = DateField(
        'كما في تاريخ',
        validators=[DataRequired(message='التاريخ مطلوب')],
        default=date.today,
        render_kw={'class': 'form-control'}
    )
    
    comparison_date = DateField(
        'تاريخ المقارنة',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    show_details = BooleanField(
        'عرض التفاصيل',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    include_ratios = BooleanField(
        'تضمين النسب المالية',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class CashFlowForm(BaseReportForm):
    """Form for Cash Flow Statement"""
    
    method = SelectField(
        'طريقة إعداد التقرير',
        choices=[
            ('direct', 'الطريقة المباشرة'),
            ('indirect', 'الطريقة غير المباشرة')
        ],
        default='indirect',
        validators=[DataRequired(message='طريقة إعداد التقرير مطلوبة')],
        render_kw={'class': 'form-select'}
    )
    
    show_details = BooleanField(
        'عرض التفاصيل',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class TaxReportForm(BaseReportForm):
    """Form for Tax reports"""
    
    report_type = SelectField(
        'نوع التقرير الضريبي',
        choices=[
            ('monthly', 'التقرير الشهري'),
            ('quarterly', 'التقرير الربع سنوي'),
            ('annual', 'التقرير السنوي'),
            ('vat', 'تقرير ضريبة القيمة المضافة'),
            ('withholding', 'تقرير الضريبة المقتطعة')
        ],
        validators=[DataRequired(message='نوع التقرير مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    tax_period = SelectField(
        'الفترة الضريبية',
        choices=[],  # Will be populated dynamically
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    include_details = BooleanField(
        'تضمين التفاصيل',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.populate_tax_periods()
    
    def populate_tax_periods(self):
        """Populate tax period choices based on current date"""
        current_date = date.today()
        choices = [('', 'اختر الفترة')]
        
        # Add current and previous months
        for i in range(12):
            period_date = current_date - relativedelta(months=i)
            period_value = period_date.strftime('%Y-%m')
            period_label = period_date.strftime('%B %Y')
            choices.append((period_value, period_label))
        
        self.tax_period.choices = choices


class SalesReportForm(BaseReportForm):
    """Form for Sales reports"""
    
    group_by = SelectField(
        'تجميع حسب',
        choices=[
            ('customer', 'العميل'),
            ('product', 'المنتج'),
            ('date', 'التاريخ'),
            ('salesperson', 'مندوب المبيعات')
        ],
        default='customer',
        validators=[DataRequired(message='طريقة التجميع مطلوبة')],
        render_kw={'class': 'form-select'}
    )
    
    customer_id = SelectField(
        'العميل',
        choices=[('', 'جميع العملاء')],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    include_tax = BooleanField(
        'تضمين الضرائب',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    show_details = BooleanField(
        'عرض التفاصيل',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class PurchasesReportForm(BaseReportForm):
    """Form for Purchases reports"""
    
    group_by = SelectField(
        'تجميع حسب',
        choices=[
            ('vendor', 'المورد'),
            ('product', 'المنتج'),
            ('date', 'التاريخ'),
            ('category', 'الفئة')
        ],
        default='vendor',
        validators=[DataRequired(message='طريقة التجميع مطلوبة')],
        render_kw={'class': 'form-select'}
    )
    
    vendor_id = SelectField(
        'المورد',
        choices=[('', 'جميع الموردين')],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    include_tax = BooleanField(
        'تضمين الضرائب',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    show_details = BooleanField(
        'عرض التفاصيل',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class AgingReportForm(FlaskForm):
    """Form for Aging reports"""
    
    as_of_date = DateField(
        'كما في تاريخ',
        validators=[DataRequired(message='التاريخ مطلوب')],
        default=date.today,
        render_kw={'class': 'form-control'}
    )
    
    report_type = SelectField(
        'نوع التقرير',
        choices=[
            ('receivables', 'أعمار ديون العملاء'),
            ('payables', 'أعمار ديون الموردين')
        ],
        default='receivables',
        validators=[DataRequired(message='نوع التقرير مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    aging_periods = StringField(
        'فترات التقادم (بالأيام)',
        default='30,60,90,120',
        validators=[
            DataRequired(message='فترات التقادم مطلوبة'),
            Length(max=100, message='فترات التقادم طويلة جداً')
        ],
        render_kw={
            'class': 'form-control',
            'placeholder': '30,60,90,120'
        }
    )
    
    customer_id = SelectField(
        'العميل',
        choices=[('', 'جميع العملاء')],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    vendor_id = SelectField(
        'المورد',
        choices=[('', 'جميع الموردين')],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    show_zero_balances = BooleanField(
        'عرض الأرصدة صفر',
        default=False,
        render_kw={'class': 'form-check-input'}
    )


class JournalReportForm(BaseReportForm):
    """Form for Journal Entry reports"""
    
    entry_type = SelectField(
        'نوع القيد',
        choices=[
            ('', 'جميع القيود'),
            ('manual', 'قيود يدوية'),
            ('automatic', 'قيود تلقائية'),
            ('adjustment', 'قيود تسوية'),
            ('closing', 'قيود إقفال')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    account_id = SelectField(
        'الحساب',
        choices=[('', 'جميع الحسابات')],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    posted_only = BooleanField(
        'القيود المرحلة فقط',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    show_details = BooleanField(
        'عرض التفاصيل',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class CustomReportForm(FlaskForm):
    """Form for custom report builder"""
    
    report_name = StringField(
        'اسم التقرير',
        validators=[
            DataRequired(message='اسم التقرير مطلوب'),
            Length(min=1, max=100, message='اسم التقرير يجب أن يكون بين 1 و 100 حرف')
        ],
        render_kw={
            'class': 'form-control',
            'placeholder': 'أدخل اسم التقرير'
        }
    )
    
    description = TextAreaField(
        'وصف التقرير',
        validators=[
            Optional(),
            Length(max=500, message='الوصف يجب ألا يزيد عن 500 حرف')
        ],
        render_kw={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'وصف مختصر للتقرير'
        }
    )
    
    data_source = SelectField(
        'مصدر البيانات',
        choices=[
            ('accounts', 'الحسابات'),
            ('journal_entries', 'القيود اليومية'),
            ('invoices', 'الفواتير'),
            ('receipts', 'الإيصالات'),
            ('customers', 'العملاء'),
            ('vendors', 'الموردين')
        ],
        validators=[DataRequired(message='مصدر البيانات مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    date_from = DateField(
        'من تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    filters = TextAreaField(
        'المرشحات (JSON)',
        validators=[Optional()],
        render_kw={
            'class': 'form-control',
            'rows': 5,
            'placeholder': '{"account_type": "Asset", "is_active": true}'
        }
    )
    
    columns = TextAreaField(
        'الأعمدة المطلوبة',
        validators=[
            DataRequired(message='الأعمدة مطلوبة'),
            Length(min=1, max=1000, message='قائمة الأعمدة طويلة جداً')
        ],
        render_kw={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'code,name,balance,type'
        }
    )
    
    sort_by = StringField(
        'ترتيب حسب',
        validators=[Optional()],
        render_kw={
            'class': 'form-control',
            'placeholder': 'code,name'
        }
    )
    
    is_public = BooleanField(
        'تقرير عام (متاح لجميع المستخدمين)',
        default=False,
        render_kw={'class': 'form-check-input'}
    )


class ReportScheduleForm(FlaskForm):
    """Form for scheduling reports"""
    
    report_type = SelectField(
        'نوع التقرير',
        choices=[
            ('trial_balance', 'ميزان المراجعة'),
            ('income_statement', 'قائمة الدخل'),
            ('balance_sheet', 'الميزانية العمومية'),
            ('tax_report', 'التقرير الضريبي'),
            ('sales_report', 'تقرير المبيعات')
        ],
        validators=[DataRequired(message='نوع التقرير مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    schedule_name = StringField(
        'اسم الجدولة',
        validators=[
            DataRequired(message='اسم الجدولة مطلوب'),
            Length(min=1, max=100, message='اسم الجدولة يجب أن يكون بين 1 و 100 حرف')
        ],
        render_kw={
            'class': 'form-control',
            'placeholder': 'تقرير شهري'
        }
    )
    
    frequency = SelectField(
        'التكرار',
        choices=[
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري'),
            ('quarterly', 'ربع سنوي'),
            ('annually', 'سنوي')
        ],
        validators=[DataRequired(message='التكرار مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    start_date = DateField(
        'تاريخ البداية',
        validators=[DataRequired(message='تاريخ البداية مطلوب')],
        default=date.today,
        render_kw={'class': 'form-control'}
    )
    
    email_recipients = TextAreaField(
        'المستلمون (البريد الإلكتروني)',
        validators=[Optional()],
        render_kw={
            'class': 'form-control',
            'rows': 3,
            'placeholder': '<EMAIL>, <EMAIL>'
        }
    )
    
    is_active = BooleanField(
        'نشط',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
