"""
Forms for journal entry management
"""

from flask_wtf import FlaskForm
from wtforms import (
    StringField, TextAreaField, DateField, SelectField, 
    DecimalField, FieldList, FormField, HiddenField, BooleanField
)
from wtforms.validators import DataRequired, Length, Optional, NumberRange, ValidationError
from wtforms.widgets import TextArea
from datetime import date, datetime
from decimal import Decimal
from app.models.journal import JournalEntry
from app.models.account import Account


class JournalLineForm(FlaskForm):
    """Form for individual journal line"""
    
    account_id = SelectField(
        'الحساب',
        validators=[DataRequired(message='الحساب مطلوب')],
        coerce=str,
        render_kw={'class': 'form-select account-select'}
    )
    
    description = StringField(
        'البيان',
        validators=[
            Optional(),
            Length(max=500, message='البيان يجب ألا يزيد عن 500 حرف')
        ],
        render_kw={
            'placeholder': 'بيان البند (اختياري)',
            'class': 'form-control'
        }
    )
    
    amount = DecimalField(
        'المبلغ',
        validators=[
            DataRequired(message='المبلغ مطلوب'),
            NumberRange(min=0.01, message='المبلغ يجب أن يكون أكبر من صفر')
        ],
        places=2,
        render_kw={
            'class': 'form-control amount-input',
            'step': '0.01',
            'min': '0'
        }
    )
    
    dc = SelectField(
        'مدين/دائن',
        choices=[
            ('', 'اختر'),
            ('D', 'مدين'),
            ('C', 'دائن')
        ],
        validators=[DataRequired(message='يجب تحديد مدين أو دائن')],
        render_kw={'class': 'form-select dc-select'}
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.populate_account_choices()
    
    def populate_account_choices(self):
        """Populate account choices"""
        choices = [('', 'اختر الحساب')]
        
        # Get active leaf accounts only
        accounts = Account.query.filter(
            Account.is_active == True,
            ~Account.children.any()  # No children (leaf accounts)
        ).order_by(Account.code).all()
        
        for account in accounts:
            choices.append((
                str(account.id),
                f"{account.code} - {account.name}"
            ))
        
        self.account_id.choices = choices


class JournalEntryForm(FlaskForm):
    """Form for journal entry"""
    
    reference_number = StringField(
        'رقم المرجع',
        validators=[
            Optional(),
            Length(max=50, message='رقم المرجع يجب ألا يزيد عن 50 حرف')
        ],
        render_kw={
            'placeholder': 'سيتم إنشاؤه تلقائياً إذا ترك فارغاً',
            'class': 'form-control'
        }
    )
    
    entry_date = DateField(
        'تاريخ القيد',
        validators=[DataRequired(message='تاريخ القيد مطلوب')],
        default=date.today,
        render_kw={'class': 'form-control'}
    )
    
    description = StringField(
        'البيان',
        validators=[
            DataRequired(message='البيان مطلوب'),
            Length(min=2, max=500, message='البيان يجب أن يكون بين 2 و 500 حرف')
        ],
        render_kw={
            'placeholder': 'بيان القيد المحاسبي',
            'class': 'form-control'
        }
    )
    
    description_en = StringField(
        'البيان بالإنجليزية',
        validators=[
            Optional(),
            Length(max=500, message='البيان بالإنجليزية يجب ألا يزيد عن 500 حرف')
        ],
        render_kw={
            'placeholder': 'Journal entry description in English',
            'class': 'form-control',
            'dir': 'ltr'
        }
    )
    
    # Journal lines will be handled dynamically in the view
    
    def __init__(self, entry=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.entry = entry
    
    def validate_reference_number(self, field):
        """Validate reference number uniqueness"""
        if field.data:
            query = JournalEntry.query.filter_by(reference_number=field.data)
            if self.entry:
                query = query.filter(JournalEntry.id != self.entry.id)
            
            if query.first():
                raise ValidationError('رقم المرجع موجود بالفعل')


class JournalSearchForm(FlaskForm):
    """Form for searching journal entries"""
    
    search = StringField(
        'البحث',
        validators=[Optional()],
        render_kw={
            'placeholder': 'البحث في البيان أو رقم المرجع...',
            'class': 'form-control'
        }
    )
    
    date_from = DateField(
        'من تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    account_id = SelectField(
        'الحساب',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    status = SelectField(
        'الحالة',
        choices=[
            ('', 'جميع الحالات'),
            ('posted', 'مرحل'),
            ('draft', 'مسودة')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.populate_account_choices()
    
    def populate_account_choices(self):
        """Populate account choices"""
        choices = [('', 'جميع الحسابات')]
        
        accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
        
        for account in accounts:
            choices.append((
                str(account.id),
                f"{account.code} - {account.name}"
            ))
        
        self.account_id.choices = choices


class LedgerForm(FlaskForm):
    """Form for ledger filters"""
    
    account_id = SelectField(
        'الحساب',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    date_from = DateField(
        'من تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    posted_only = BooleanField(
        'القيود المرحلة فقط',
        render_kw={'class': 'form-check-input'}
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.populate_account_choices()
    
    def populate_account_choices(self):
        """Populate account choices"""
        choices = [('', 'جميع الحسابات')]
        
        # Get only leaf accounts (accounts with transactions)
        accounts = Account.query.filter(
            Account.is_active == True,
            ~Account.children.any()
        ).order_by(Account.code).all()
        
        for account in accounts:
            choices.append((
                str(account.id),
                f"{account.code} - {account.name}"
            ))
        
        self.account_id.choices = choices


class JournalImportForm(FlaskForm):
    """Form for importing journal entries"""
    
    import_type = SelectField(
        'نوع الاستيراد',
        choices=[
            ('csv', 'ملف CSV'),
            ('excel', 'ملف Excel'),
            ('json', 'ملف JSON')
        ],
        validators=[DataRequired(message='نوع الاستيراد مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    file_content = TextAreaField(
        'محتوى الملف',
        validators=[DataRequired(message='محتوى الملف مطلوب')],
        render_kw={
            'placeholder': 'الصق محتوى الملف هنا...',
            'class': 'form-control',
            'rows': 15
        }
    )
    
    auto_post = BooleanField(
        'ترحيل تلقائي للقيود المتوازنة',
        render_kw={'class': 'form-check-input'}
    )
    
    skip_duplicates = BooleanField(
        'تجاهل القيود المكررة',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class JournalBulkActionForm(FlaskForm):
    """Form for bulk actions on journal entries"""
    
    action = SelectField(
        'الإجراء',
        choices=[
            ('', 'اختر الإجراء'),
            ('post', 'ترحيل'),
            ('unpost', 'إلغاء ترحيل'),
            ('delete', 'حذف'),
            ('export', 'تصدير')
        ],
        validators=[DataRequired(message='الإجراء مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    selected_entries = HiddenField(
        'القيود المحددة',
        validators=[DataRequired(message='يجب تحديد قيد واحد على الأقل')]
    )
    
    confirmation = BooleanField(
        'أؤكد تنفيذ هذا الإجراء',
        validators=[DataRequired(message='يجب تأكيد الإجراء')],
        render_kw={'class': 'form-check-input'}
    )


class QuickJournalForm(FlaskForm):
    """Form for quick journal entry creation"""
    
    entry_type = SelectField(
        'نوع القيد',
        choices=[
            ('', 'اختر نوع القيد'),
            ('cash_receipt', 'قبض نقدي'),
            ('cash_payment', 'دفع نقدي'),
            ('bank_deposit', 'إيداع بنكي'),
            ('bank_withdrawal', 'سحب بنكي'),
            ('sales', 'مبيعات'),
            ('purchases', 'مشتريات'),
            ('expense', 'مصروف'),
            ('adjustment', 'قيد تسوية')
        ],
        validators=[DataRequired(message='نوع القيد مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    amount = DecimalField(
        'المبلغ',
        validators=[
            DataRequired(message='المبلغ مطلوب'),
            NumberRange(min=0.01, message='المبلغ يجب أن يكون أكبر من صفر')
        ],
        places=2,
        render_kw={
            'class': 'form-control',
            'step': '0.01',
            'min': '0'
        }
    )
    
    description = StringField(
        'البيان',
        validators=[
            DataRequired(message='البيان مطلوب'),
            Length(min=2, max=200, message='البيان يجب أن يكون بين 2 و 200 حرف')
        ],
        render_kw={
            'placeholder': 'بيان القيد',
            'class': 'form-control'
        }
    )
    
    account1_id = SelectField(
        'الحساب الأول',
        choices=[],
        validators=[DataRequired(message='الحساب الأول مطلوب')],
        coerce=str,
        render_kw={'class': 'form-select'}
    )
    
    account2_id = SelectField(
        'الحساب الثاني',
        choices=[],
        validators=[DataRequired(message='الحساب الثاني مطلوب')],
        coerce=str,
        render_kw={'class': 'form-select'}
    )
    
    entry_date = DateField(
        'تاريخ القيد',
        validators=[DataRequired(message='تاريخ القيد مطلوب')],
        default=date.today,
        render_kw={'class': 'form-control'}
    )
    
    auto_post = BooleanField(
        'ترحيل تلقائي',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.populate_account_choices()
    
    def populate_account_choices(self):
        """Populate account choices"""
        choices = [('', 'اختر الحساب')]
        
        accounts = Account.query.filter(
            Account.is_active == True,
            ~Account.children.any()
        ).order_by(Account.code).all()
        
        for account in accounts:
            choices.append((
                str(account.id),
                f"{account.code} - {account.name}"
            ))
        
        self.account1_id.choices = choices
        self.account2_id.choices = choices
    
    def validate_account2_id(self, field):
        """Validate that second account is different from first"""
        if field.data == self.account1_id.data:
            raise ValidationError('الحساب الثاني يجب أن يكون مختلف عن الحساب الأول')
