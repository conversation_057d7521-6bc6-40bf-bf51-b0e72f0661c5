{% extends "base.html" %}

{% block title %}{{ customer.name }} - تفاصيل العميل - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-user me-3"></i>
                {{ customer.name }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('customers.index') }}">العملاء</a></li>
                    <li class="breadcrumb-item active">{{ customer.name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('customers.edit', customer_id=customer.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
            <a href="{{ url_for('customers.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Customer Information -->
    <div class="col-lg-8">
        <!-- Basic Info Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات العميل
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>اسم العميل:</strong></td>
                                <td>{{ customer.name }}</td>
                            </tr>
                            {% if customer.name_en %}
                            <tr>
                                <td><strong>الاسم بالإنجليزية:</strong></td>
                                <td>{{ customer.name_en }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>نوع العميل:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if customer.customer_type == 'individual' else 'primary' }}">
                                        {{ customer.get_type_display() }}
                                    </span>
                                </td>
                            </tr>
                            {% if customer.tax_id %}
                            <tr>
                                <td><strong>الرقم الضريبي:</strong></td>
                                <td><span class="badge bg-info">{{ customer.tax_id }}</span></td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td>
                                    {% if customer.email %}
                                        <a href="mailto:{{ customer.email }}">{{ customer.email }}</a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الهاتف:</strong></td>
                                <td>
                                    {% if customer.phone %}
                                        <a href="tel:{{ customer.phone }}">{{ customer.phone }}</a>
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% if customer.website %}
                            <tr>
                                <td><strong>الموقع الإلكتروني:</strong></td>
                                <td><a href="{{ customer.website }}" target="_blank">{{ customer.website }}</a></td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if customer.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ customer.created_at|datetime }}</td>
                            </tr>
                            {% if customer.updated_at %}
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ customer.updated_at|datetime }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Address Information -->
        {% if customer.address or customer.city or customer.state %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    معلومات العنوان
                </h5>
            </div>
            <div class="card-body">
                {% if customer.address %}
                <p><strong>العنوان:</strong><br>{{ customer.address }}</p>
                {% endif %}
                
                <div class="row">
                    {% if customer.city %}
                    <div class="col-md-4">
                        <strong>المدينة:</strong><br>{{ customer.city }}
                    </div>
                    {% endif %}
                    {% if customer.state %}
                    <div class="col-md-4">
                        <strong>المحافظة:</strong><br>{{ customer.state }}
                    </div>
                    {% endif %}
                    {% if customer.postal_code %}
                    <div class="col-md-4">
                        <strong>الرمز البريدي:</strong><br>{{ customer.postal_code }}
                    </div>
                    {% endif %}
                </div>
                
                {% if customer.country %}
                <p class="mt-2"><strong>البلد:</strong> {{ customer.country }}</p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Financial Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-dollar-sign me-2"></i>
                    المعلومات المالية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-{{ 'success' if customer.get_balance() >= 0 else 'danger' }}">
                                {{ customer.get_balance()|currency }}
                            </h4>
                            <small>الرصيد الحالي</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">{{ customer.credit_limit|currency if customer.credit_limit else '0.00 ج.م' }}</h4>
                            <small>حد الائتمان</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning">{{ customer.payment_terms or 0 }}</h4>
                            <small>شروط الدفع (يوم)</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary">{{ customer.currency or 'EGP' }}</h4>
                            <small>العملة</small>
                        </div>
                    </div>
                </div>
                
                {% if customer.account %}
                <hr>
                <p><strong>الحساب المحاسبي:</strong> 
                    <a href="{{ url_for('accounts.detail', account_id=customer.account.id) }}">
                        {{ customer.account.code }} - {{ customer.account.name }}
                    </a>
                </p>
                {% endif %}
            </div>
        </div>

        <!-- Recent Invoices -->
        {% if recent_invoices %}
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    آخر الفواتير
                </h5>
                <a href="{{ url_for('customers.invoices', customer_id=customer.id) }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in recent_invoices %}
                            <tr>
                                <td>{{ invoice.invoice_number }}</td>
                                <td>{{ invoice.invoice_date|date }}</td>
                                <td>{{ invoice.total_amount|currency }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'warning' if invoice.status == 'pending' else 'danger' }}">
                                        {{ invoice.get_status_display() }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('invoices.detail', invoice_id=invoice.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Notes -->
        {% if customer.notes %}
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات
                </h5>
            </div>
            <div class="card-body">
                <p>{{ customer.notes|nl2br }}</p>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('customers.edit', customer_id=customer.id) }}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل العميل
                    </a>
                    
                    <a href="{{ url_for('invoices.new') }}?customer_id={{ customer.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-file-invoice me-2"></i>
                        فاتورة جديدة
                    </a>
                    
                    <a href="{{ url_for('customers.statement', customer_id=customer.id) }}" class="btn btn-outline-info">
                        <i class="fas fa-file-alt me-2"></i>
                        كشف حساب
                    </a>
                    
                    <a href="{{ url_for('receipts.new') }}?customer_id={{ customer.id }}" class="btn btn-outline-success">
                        <i class="fas fa-receipt me-2"></i>
                        إيصال جديد
                    </a>
                    
                    {% if customer.email %}
                    <a href="mailto:{{ customer.email }}" class="btn btn-outline-secondary">
                        <i class="fas fa-envelope me-2"></i>
                        إرسال بريد
                    </a>
                    {% endif %}
                    
                    {% if customer.phone %}
                    <a href="tel:{{ customer.phone }}" class="btn btn-outline-secondary">
                        <i class="fas fa-phone me-2"></i>
                        اتصال
                    </a>
                    {% endif %}
                    
                    {% if customer.can_be_deleted() %}
                    <hr>
                    <form method="POST" action="{{ url_for('customers.delete', customer_id=customer.id) }}" 
                          onsubmit="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash me-2"></i>
                            حذف العميل
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ customer.get_invoices_count() }}</h4>
                        <small>فاتورة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ customer.get_payments_count() }}</h4>
                        <small>دفعة</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h5 class="text-info">{{ customer.get_total_sales()|currency }}</h5>
                        <small>إجمالي المبيعات</small>
                    </div>
                    <div class="col-6">
                        <h5 class="text-warning">{{ customer.get_total_payments()|currency }}</h5>
                        <small>إجمالي المدفوعات</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Credit Status -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    حالة الائتمان
                </h6>
            </div>
            <div class="card-body">
                {% set balance = customer.get_balance() %}
                {% set credit_limit = customer.credit_limit or 0 %}
                {% set credit_used = balance if balance > 0 else 0 %}
                {% set credit_available = credit_limit - credit_used %}
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <small>الائتمان المستخدم</small>
                        <small>{{ credit_used|currency }} / {{ credit_limit|currency }}</small>
                    </div>
                    <div class="progress">
                        <div class="progress-bar bg-{{ 'danger' if credit_used > credit_limit else 'warning' if credit_used > credit_limit * 0.8 else 'success' }}" 
                             style="width: {{ (credit_used / credit_limit * 100) if credit_limit > 0 else 0 }}%"></div>
                    </div>
                </div>
                
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-{{ 'success' if credit_available >= 0 else 'danger' }}">
                            {{ credit_available|currency }}
                        </h6>
                        <small>متاح</small>
                    </div>
                    <div class="col-6">
                        <h6 class="text-primary">{{ credit_limit|currency }}</h6>
                        <small>الحد الأقصى</small>
                    </div>
                </div>
                
                {% if credit_available < 0 %}
                <div class="alert alert-danger mt-2">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    تجاوز حد الائتمان!
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Recent Activity -->
        {% if recent_activity %}
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    النشاط الأخير
                </h6>
            </div>
            <div class="card-body">
                {% for activity in recent_activity %}
                <div class="d-flex align-items-start mb-3">
                    <div class="avatar-sm me-3">
                        <div class="avatar-title bg-{{ 'primary' if activity.type == 'invoice' else 'success' if activity.type == 'payment' else 'info' }} rounded-circle">
                            <i class="fas fa-{{ 'file-invoice' if activity.type == 'invoice' else 'money-bill' if activity.type == 'payment' else 'edit' }}"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ activity.description }}</h6>
                        <small class="text-muted">{{ activity.created_at|datetime }}</small>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh customer data every 60 seconds
setInterval(function() {
    fetch('{{ url_for("customers.balance", customer_id=customer.id) }}')
        .then(response => response.json())
        .then(data => {
            // Update balance display if needed
            console.log('Balance updated:', data.balance);
        })
        .catch(error => console.error('Error updating balance:', error));
}, 60000);
</script>
{% endblock %}
