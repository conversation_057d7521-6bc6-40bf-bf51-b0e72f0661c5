# 🚀 SystemTax - دليل التشغيل السريع

## المشكلة والحل

### 🔴 المشكلة الحالية
- تعارض بين Python 3.13.5 وحزمة pandas 2.1.1
- فشل في تثبيت المتطلبات

### ✅ الحلول المتاحة

## الحل الأول: التشغيل السريع (مُوصى به)

```bash
# تشغيل الملف المحدث
run-simple.bat
```

أو:

```bash
# تشغيل سكريبت الإعداد
python setup-dev.py
python app.py
```

## الحل الثاني: تحديث المتطلبات

استخدم الملف المحدث للمتطلبات:

```bash
# تفعيل البيئة الافتراضية
venv\Scripts\activate

# تثبيت المتطلبات المحدثة
pip install -r requirements-py313.txt

# تشغيل النظام
python app.py
```

## الحل الثالث: استخدام Docker (الأفضل للإنتاج)

```bash
# تشغيل النظام بالكامل
docker-compose up -d

# الوصول للنظام
# http://localhost:8000
```

## الحل الرابع: تثبيت يدوي للحزم الأساسية

```bash
# تثبيت الحزم الأساسية فقط
pip install Flask==3.0.3 Flask-SQLAlchemy==3.1.1 Flask-Login==0.6.3
pip install Flask-WTF==1.2.1 WTForms==3.1.2 requests==2.32.3
pip install python-dotenv==1.0.1 click==8.1.7

# تشغيل النظام
python app.py
```

## 📋 متطلبات النظام

- **Python**: 3.10+ (يفضل 3.11 أو 3.12)
- **نظام التشغيل**: Windows/Linux/macOS
- **الذاكرة**: 2GB RAM كحد أدنى
- **المساحة**: 1GB مساحة فارغة

## 🔧 إعداد البيئة

### 1. تحقق من إصدار Python
```bash
python --version
```

### 2. إنشاء البيئة الافتراضية
```bash
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac
```

### 3. تحديث pip
```bash
python -m pip install --upgrade pip
```

### 4. تثبيت المتطلبات
```bash
pip install -r requirements-py313.txt
```

## 🌐 الوصول للنظام

بعد التشغيل الناجح:

- **الرابط**: http://localhost:5000
- **المستخدم**: admin
- **كلمة المرور**: admin123

## 🐛 حل المشاكل الشائعة

### مشكلة pandas
```bash
# تخطي pandas مؤقتاً
pip install --no-deps pandas==2.2.3
```

### مشكلة weasyprint
```bash
# تخطي weasyprint مؤقتاً
# سيعمل النظام بدون إنتاج PDF متقدم
```

### مشكلة psycopg2
```bash
# استخدام SQLite بدلاً من PostgreSQL للتطوير
# تم تكوين النظام تلقائياً لاستخدام SQLite
```

### مشكلة Redis
```bash
# النظام يعمل بدون Redis
# الكاش سيكون معطل فقط
```

## 📁 ملفات الإعداد الجديدة

- `requirements-py313.txt` - متطلبات متوافقة مع Python 3.13
- `setup-dev.py` - سكريبت إعداد تلقائي
- `run-simple.bat` - تشغيل سريع للنظام
- `.env` - ملف الإعدادات (يتم إنشاؤه تلقائياً)

## 🎯 الميزات المتاحة

حتى مع الحزم المبسطة، ستحصل على:

✅ **النظام المحاسبي الكامل**
- دليل الحسابات
- القيود اليومية
- دفتر الأستاذ
- التقارير المالية

✅ **إدارة العملاء والموردين**
- قاعدة بيانات شاملة
- تتبع المعاملات

✅ **الفواتير والإيصالات**
- إنشاء وإدارة الفواتير
- إنتاج PDF أساسي

✅ **الأمان والصلاحيات**
- نظام المستخدمين
- حماية البيانات

✅ **لوحة التحكم**
- إحصائيات مالية
- رسوم بيانية

## 📞 الدعم

إذا واجهت أي مشاكل:

1. تأكد من إصدار Python (3.10+)
2. جرب `run-simple.bat`
3. استخدم Docker كبديل
4. راجع ملف `QUICK-START.md`

---

**SystemTax** - نظام محاسبي ويب متكامل 🚀
