/* Enhanced UI Styles for SystemTax */

/* Root Variables for Consistent Theming */
:root {
    --primary-color: #4e73df;
    --primary-dark: #3c5aa6;
    --primary-light: #6c8cff;
    --success-color: #1cc88a;
    --success-dark: #17a673;
    --warning-color: #f6c23e;
    --warning-dark: #dda20a;
    --danger-color: #e74a3b;
    --danger-dark: #c0392b;
    --info-color: #36b9cc;
    --info-dark: #2c9faf;
    --dark-color: #5a5c69;
    --light-color: #f8f9fc;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    
    /* Shadows */
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* Border Radius */
    --border-radius: 0.35rem;
    --border-radius-lg: 0.5rem;
    --border-radius-xl: 1rem;
    
    /* Transitions */
    --transition: all 0.3s ease;
    --transition-fast: all 0.15s ease;
}

/* Enhanced Body and Layout */
body {
    font-family: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background-color: var(--light-color);
    color: var(--gray-800);
    line-height: 1.6;
}

/* Enhanced Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border-bottom: none;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.card-header.bg-success {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
}

.card-header.bg-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
}

.card-header.bg-danger {
    background: linear-gradient(135deg, var(--danger-color), var(--danger-dark));
}

.card-header.bg-info {
    background: linear-gradient(135deg, var(--info-color), var(--info-dark));
}

/* Enhanced Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.5rem 1.5rem;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    box-shadow: 0 4px 15px rgba(78, 115, 223, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(78, 115, 223, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
    box-shadow: 0 4px 15px rgba(28, 200, 138, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(28, 200, 138, 0.4);
}

/* Enhanced Form Controls */
.form-control, .form-select {
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius);
    padding: 0.75rem 1rem;
    transition: var(--transition);
    background-color: var(--white);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

/* Enhanced Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: linear-gradient(135deg, var(--gray-800), var(--gray-700));
    color: var(--white);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table tbody tr:hover {
    background-color: var(--gray-100);
    transform: scale(1.01);
}

/* Enhanced Navigation */
.navbar {
    box-shadow: var(--shadow);
    background: linear-gradient(135deg, var(--white), var(--gray-100));
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: var(--primary-color);
}

.nav-link {
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Enhanced Sidebar */
.sidebar {
    background: linear-gradient(180deg, var(--gray-900), var(--gray-800));
    box-shadow: var(--shadow-lg);
}

.sidebar .nav-link {
    color: var(--gray-300);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    margin: 0.25rem 1rem;
    transition: var(--transition);
}

.sidebar .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background: var(--primary-color);
    color: var(--white);
}

/* Enhanced Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
}

.alert-success {
    background: linear-gradient(135deg, rgba(28, 200, 138, 0.1), rgba(28, 200, 138, 0.05));
    color: var(--success-dark);
}

.alert-warning {
    background: linear-gradient(135deg, rgba(246, 194, 62, 0.1), rgba(246, 194, 62, 0.05));
    color: var(--warning-dark);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(231, 74, 59, 0.1), rgba(231, 74, 59, 0.05));
    color: var(--danger-dark);
}

.alert-info {
    background: linear-gradient(135deg, rgba(54, 185, 204, 0.1), rgba(54, 185, 204, 0.05));
    color: var(--info-dark);
}

/* Enhanced Badges */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius);
    font-size: 0.75rem;
}

/* Enhanced Progress Bars */
.progress {
    height: 1rem;
    border-radius: var(--border-radius);
    background-color: var(--gray-200);
    overflow: hidden;
}

.progress-bar {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transition: width 0.6s ease;
}

/* Enhanced Dropdowns */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem 0;
}

.dropdown-item {
    padding: 0.75rem 1.5rem;
    transition: var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateX(5px);
}

/* Enhanced Modals */
.modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: var(--white);
    border-bottom: none;
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.modal-footer {
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-100);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* Loading Animations */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: var(--white);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Slide In Animation */
.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Enhanced Tooltips */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: var(--gray-900);
    border-radius: var(--border-radius);
    padding: 0.5rem 0.75rem;
}

/* Enhanced Page Header */
.page-header {
    background: linear-gradient(135deg, var(--white), var(--gray-100));
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
}

.page-header h1 {
    color: var(--gray-800);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-header p {
    color: var(--gray-600);
    font-size: 1.1rem;
}

/* Enhanced Statistics Cards */
.stat-card {
    background: linear-gradient(135deg, var(--white), var(--gray-100));
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.stat-card .stat-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.stat-card .stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.stat-card .stat-label {
    color: var(--gray-600);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
    
    .page-header {
        padding: 1rem 0;
        text-align: center;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1a1a1a;
        --white: #2d2d2d;
        --gray-100: #3a3a3a;
        --gray-200: #4a4a4a;
        --gray-800: #e0e0e0;
        --gray-900: #f0f0f0;
    }
    
    body {
        background-color: var(--light-color);
        color: var(--gray-800);
    }
    
    .card {
        background-color: var(--white);
        color: var(--gray-800);
    }
    
    .form-control, .form-select {
        background-color: var(--white);
        color: var(--gray-800);
        border-color: var(--gray-400);
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .page-header .btn-group {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }
    
    .page-header {
        background: none;
        box-shadow: none;
        border-bottom: 2px solid var(--gray-300);
    }
}
