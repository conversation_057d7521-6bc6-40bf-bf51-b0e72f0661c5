"""
Background tasks for notifications
"""

from app import db
from app.models.notification import NotificationTemplate, NotificationType
from app.models.invoice import Invoice
from app.models.receipt import Receipt
from app.models.account import Account
from app.models.user import User
from app.services.notification_service import NotificationService, notify_invoice_due, notify_payment_received, notify_low_balance
from datetime import datetime, date, timedelta
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)


def create_default_notification_templates():
    """Create default notification templates"""
    
    templates = [
        {
            'name': 'invoice_due',
            'description': 'إشعار فاتورة مستحقة',
            'type': NotificationType.INVOICE_DUE,
            'title_template': 'فاتورة مستحقة: {invoice_number}',
            'message_template': 'الفاتورة رقم {invoice_number} للعميل {customer_name} مستحقة بقيمة {amount} ج.م في تاريخ {due_date}',
            'default_channels': 'in_app,email',
            'target_role': 'admin',
            'trigger_event': 'invoice_due_check',
            'delay_minutes': 0,
            'expires_after_hours': 72
        },
        {
            'name': 'payment_received',
            'description': 'إشعار استلام دفعة',
            'type': NotificationType.PAYMENT_RECEIVED,
            'title_template': 'تم استلام دفعة: {receipt_number}',
            'message_template': 'تم استلام دفعة بقيمة {amount} ج.م من {customer_name} برقم إيصال {receipt_number}',
            'default_channels': 'in_app',
            'target_role': 'admin',
            'trigger_event': 'payment_created',
            'delay_minutes': 0,
            'expires_after_hours': 24
        },
        {
            'name': 'low_balance',
            'description': 'إشعار رصيد منخفض',
            'type': NotificationType.LOW_BALANCE,
            'title_template': 'تحذير: رصيد منخفض',
            'message_template': 'رصيد الحساب {account_name} منخفض: {balance} ج.م',
            'default_channels': 'in_app,email',
            'target_role': 'admin',
            'trigger_event': 'balance_check',
            'delay_minutes': 0,
            'expires_after_hours': 48
        },
        {
            'name': 'system_update',
            'description': 'إشعار تحديث النظام',
            'type': NotificationType.SYSTEM_UPDATE,
            'title_template': 'تحديث النظام: {version}',
            'message_template': 'تم تحديث النظام إلى الإصدار {version}. الميزات الجديدة: {features}',
            'default_channels': 'in_app',
            'is_global': True,
            'trigger_event': 'system_updated',
            'delay_minutes': 0,
            'expires_after_hours': 168  # 1 week
        },
        {
            'name': 'backup_complete',
            'description': 'إشعار اكتمال النسخ الاحتياطي',
            'type': NotificationType.BACKUP_COMPLETE,
            'title_template': 'تم إنشاء نسخة احتياطية',
            'message_template': 'تم إنشاء نسخة احتياطية بنجاح: {backup_name}. الحجم: {size}',
            'default_channels': 'in_app,email',
            'target_role': 'admin',
            'trigger_event': 'backup_created',
            'delay_minutes': 0,
            'expires_after_hours': 24
        },
        {
            'name': 'tax_deadline',
            'description': 'إشعار موعد ضريبي',
            'type': NotificationType.TAX_DEADLINE,
            'title_template': 'تذكير: موعد ضريبي قريب',
            'message_template': 'يحل موعد {tax_type} في تاريخ {deadline}. المبلغ المتوقع: {amount} ج.م',
            'default_channels': 'in_app,email',
            'target_role': 'admin',
            'trigger_event': 'tax_deadline_check',
            'delay_minutes': 0,
            'expires_after_hours': 72
        },
        {
            'name': 'user_login',
            'description': 'إشعار تسجيل دخول',
            'type': NotificationType.INFO,
            'title_template': 'تسجيل دخول جديد',
            'message_template': 'تم تسجيل دخول المستخدم {username} من {ip_address} في {login_time}',
            'default_channels': 'in_app',
            'target_role': 'admin',
            'trigger_event': 'user_login',
            'delay_minutes': 0,
            'expires_after_hours': 24
        },
        {
            'name': 'data_export',
            'description': 'إشعار تصدير البيانات',
            'type': NotificationType.SUCCESS,
            'title_template': 'تم تصدير البيانات',
            'message_template': 'تم تصدير {export_type} بنجاح. عدد السجلات: {record_count}',
            'default_channels': 'in_app',
            'trigger_event': 'data_exported',
            'delay_minutes': 0,
            'expires_after_hours': 12
        }
    ]
    
    created_count = 0
    
    for template_data in templates:
        # Check if template already exists
        existing = NotificationTemplate.query.filter_by(name=template_data['name']).first()
        
        if not existing:
            template = NotificationTemplate(**template_data)
            db.session.add(template)
            created_count += 1
            logger.info(f"Created notification template: {template_data['name']}")
    
    try:
        db.session.commit()
        logger.info(f"Created {created_count} notification templates")
        return created_count
    except Exception as e:
        db.session.rollback()
        logger.error(f"Failed to create notification templates: {e}")
        return 0


def check_due_invoices():
    """Check for due invoices and send notifications"""
    
    try:
        # Get invoices that are due today or overdue
        today = date.today()
        due_invoices = Invoice.query.filter(
            Invoice.due_date <= today,
            Invoice.status.in_(['pending', 'sent']),
            Invoice.is_active == True
        ).all()
        
        notifications_sent = 0
        
        for invoice in due_invoices:
            # Check if notification was already sent for this invoice
            existing_notification = db.session.query(
                db.session.query(NotificationTemplate).filter_by(name='invoice_due').exists()
            ).scalar()
            
            if existing_notification:
                # Send notification
                notify_invoice_due(invoice)
                notifications_sent += 1
        
        logger.info(f"Sent {notifications_sent} due invoice notifications")
        return notifications_sent
        
    except Exception as e:
        logger.error(f"Failed to check due invoices: {e}")
        return 0


def check_low_balances():
    """Check for low account balances and send notifications"""
    
    try:
        # Define low balance thresholds
        thresholds = {
            'cash': Decimal('10000'),  # 10,000 EGP for cash accounts
            'bank': Decimal('5000'),   # 5,000 EGP for bank accounts
            'default': Decimal('1000') # 1,000 EGP for other accounts
        }
        
        notifications_sent = 0
        
        # Check cash accounts
        cash_accounts = Account.query.filter(
            Account.type == 'Asset',
            Account.name.ilike('%نقد%'),
            Account.is_active == True
        ).all()
        
        for account in cash_accounts:
            balance = account.get_balance_as_of(date.today())
            threshold = thresholds.get('cash', thresholds['default'])
            
            if balance < threshold:
                notify_low_balance(account, balance)
                notifications_sent += 1
        
        # Check bank accounts
        bank_accounts = Account.query.filter(
            Account.type == 'Asset',
            Account.name.ilike('%بنك%'),
            Account.is_active == True
        ).all()
        
        for account in bank_accounts:
            balance = account.get_balance_as_of(date.today())
            threshold = thresholds.get('bank', thresholds['default'])
            
            if balance < threshold:
                notify_low_balance(account, balance)
                notifications_sent += 1
        
        logger.info(f"Sent {notifications_sent} low balance notifications")
        return notifications_sent
        
    except Exception as e:
        logger.error(f"Failed to check low balances: {e}")
        return 0


def check_tax_deadlines():
    """Check for upcoming tax deadlines and send notifications"""
    
    try:
        # Define tax deadlines (this would typically come from a configuration table)
        tax_deadlines = [
            {
                'type': 'ضريبة القيمة المضافة',
                'deadline': date(2024, 1, 31),
                'amount': Decimal('50000')
            },
            {
                'type': 'ضريبة الدخل',
                'deadline': date(2024, 3, 31),
                'amount': Decimal('100000')
            },
            {
                'type': 'ضريبة الأرباح التجارية',
                'deadline': date(2024, 6, 30),
                'amount': Decimal('75000')
            }
        ]
        
        notifications_sent = 0
        today = date.today()
        
        for tax_info in tax_deadlines:
            days_until_deadline = (tax_info['deadline'] - today).days
            
            # Send notification if deadline is within 30 days
            if 0 <= days_until_deadline <= 30:
                NotificationService.create_from_template(
                    'tax_deadline',
                    context={
                        'tax_type': tax_info['type'],
                        'deadline': tax_info['deadline'].strftime('%Y-%m-%d'),
                        'amount': tax_info['amount']
                    }
                )
                notifications_sent += 1
        
        logger.info(f"Sent {notifications_sent} tax deadline notifications")
        return notifications_sent
        
    except Exception as e:
        logger.error(f"Failed to check tax deadlines: {e}")
        return 0


def send_scheduled_notifications():
    """Send notifications that are scheduled for now"""
    
    try:
        count = NotificationService.send_scheduled_notifications()
        logger.info(f"Sent {count} scheduled notifications")
        return count
        
    except Exception as e:
        logger.error(f"Failed to send scheduled notifications: {e}")
        return 0


def cleanup_old_notifications():
    """Clean up old notifications"""
    
    try:
        count = NotificationService.cleanup_old_notifications(days=30)
        logger.info(f"Cleaned up {count} old notifications")
        return count
        
    except Exception as e:
        logger.error(f"Failed to cleanup old notifications: {e}")
        return 0


def generate_daily_summary():
    """Generate daily summary notification for admins"""
    
    try:
        today = date.today()
        yesterday = today - timedelta(days=1)
        
        # Collect daily statistics
        stats = {
            'invoices_created': Invoice.query.filter(
                Invoice.created_at >= yesterday,
                Invoice.created_at < today
            ).count(),
            'payments_received': Receipt.query.filter(
                Receipt.created_at >= yesterday,
                Receipt.created_at < today,
                Receipt.receipt_type == 'receipt'
            ).count(),
            'total_revenue': db.session.query(
                db.func.sum(Receipt.amount)
            ).filter(
                Receipt.created_at >= yesterday,
                Receipt.created_at < today,
                Receipt.receipt_type == 'receipt'
            ).scalar() or Decimal('0')
        }
        
        # Create summary notification
        NotificationService.create_notification(
            title=f'ملخص يومي - {yesterday.strftime("%Y-%m-%d")}',
            message=f'تم إنشاء {stats["invoices_created"]} فاتورة و استلام {stats["payments_received"]} دفعة بإجمالي {stats["total_revenue"]} ج.م',
            notification_type=NotificationType.INFO,
            role='admin',
            channels='in_app,email'
        )
        
        logger.info("Generated daily summary notification")
        return True
        
    except Exception as e:
        logger.error(f"Failed to generate daily summary: {e}")
        return False


def run_daily_tasks():
    """Run all daily notification tasks"""
    
    logger.info("Starting daily notification tasks")
    
    results = {
        'due_invoices': check_due_invoices(),
        'low_balances': check_low_balances(),
        'tax_deadlines': check_tax_deadlines(),
        'scheduled_notifications': send_scheduled_notifications(),
        'daily_summary': generate_daily_summary(),
        'cleanup': cleanup_old_notifications()
    }
    
    logger.info(f"Daily notification tasks completed: {results}")
    return results


def run_hourly_tasks():
    """Run hourly notification tasks"""
    
    logger.info("Starting hourly notification tasks")
    
    results = {
        'scheduled_notifications': send_scheduled_notifications()
    }
    
    logger.info(f"Hourly notification tasks completed: {results}")
    return results


# Task scheduler integration (for use with Celery or similar)
def setup_notification_scheduler():
    """Setup notification task scheduler"""
    
    # This would integrate with your task scheduler
    # For example, with Celery:
    
    # from celery import Celery
    # from celery.schedules import crontab
    
    # app = Celery('notification_tasks')
    
    # app.conf.beat_schedule = {
    #     'daily-notifications': {
    #         'task': 'app.tasks.notification_tasks.run_daily_tasks',
    #         'schedule': crontab(hour=8, minute=0),  # Run at 8:00 AM daily
    #     },
    #     'hourly-notifications': {
    #         'task': 'app.tasks.notification_tasks.run_hourly_tasks',
    #         'schedule': crontab(minute=0),  # Run every hour
    #     },
    # }
    
    pass


if __name__ == '__main__':
    # For testing purposes
    from app import create_app
    
    app = create_app()
    with app.app_context():
        print("Creating default notification templates...")
        count = create_default_notification_templates()
        print(f"Created {count} templates")
        
        print("Running daily tasks...")
        results = run_daily_tasks()
        print(f"Results: {results}")
