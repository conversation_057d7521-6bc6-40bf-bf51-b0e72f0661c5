# Core Flask dependencies
Flask==3.0.3
Flask-SQLAlchemy==3.1.1
Flask-Migrate==4.0.7
Flask-Login==0.6.3
Flask-WTF==1.2.1
WTForms==3.1.2

# Database
psycopg2-binary==2.9.9
alembic==1.13.2

# Authentication & Security
Werkzeug==3.0.4
bcrypt==4.2.0
PyJWT==2.9.0

# PDF Generation
reportlab==4.2.5
weasyprint==62.3

# Excel/CSV Export
openpyxl==3.1.5
pandas==2.2.3

# HTTP Requests (for tax API integration)
requests==2.32.3

# Redis for caching
redis==5.1.1
Flask-Caching==2.3.0
hiredis==2.3.2

# Environment variables
python-dotenv==1.0.1

# Date/Time handling
python-dateutil==2.9.0

# Validation
marshmallow==3.22.0
email-validator==2.2.0

# CLI tools
click==8.1.7

# CSRF Protection
Flask-CSRF==0.9.2

# Development dependencies
pytest==8.3.3
pytest-flask==1.3.0
pytest-cov==5.0.0
pytest-mock==3.14.0
black==24.8.0
flake8==7.1.1
isort==5.13.2
mypy==1.11.2

# Production server
gunicorn==23.0.0

# Monitoring (optional)
prometheus-flask-exporter==0.23.0

# Code quality
pre-commit==4.0.1
bandit==1.7.10

# Testing utilities
factory-boy==3.3.1
faker==30.8.2

# Backup utilities
python-crontab==3.2.0

# Email (optional)
Flask-Mail==0.10.0

# Rate limiting
Flask-Limiter==3.8.0

# Health checks
Flask-HealthCheck==1.0.0
