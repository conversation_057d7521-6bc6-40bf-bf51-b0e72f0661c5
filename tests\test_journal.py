"""
Tests for journal entry functionality
"""

import pytest
from decimal import Decimal
from datetime import date, datetime
from app import db
from app.models.journal import JournalEntry, JournalLine
from app.models.account import Account
from app.models.user import User


class TestJournalEntryModel:
    """Test JournalEntry model functionality"""
    
    def test_journal_entry_creation(self, app, admin_user):
        """Test creating a new journal entry"""
        with app.app_context():
            entry = JournalEntry(
                reference_number='JE001',
                entry_date=date.today(),
                description='Test Entry',
                created_by_id=admin_user.id
            )
            db.session.add(entry)
            db.session.commit()
            
            assert entry.id is not None
            assert entry.reference_number == 'JE001'
            assert entry.description == 'Test Entry'
            assert entry.is_posted is False
            assert entry.created_by == admin_user
    
    def test_journal_entry_with_lines(self, app, admin_user, test_accounts):
        """Test journal entry with lines"""
        with app.app_context():
            cash_account = test_accounts['cash']
            sales_account = test_accounts['sales']
            
            # Create journal entry
            entry = JournalEntry(
                reference_number='JE002',
                entry_date=date.today(),
                description='Sales Entry',
                created_by_id=admin_user.id
            )
            db.session.add(entry)
            db.session.flush()
            
            # Create journal lines
            debit_line = JournalLine(
                journal_id=entry.id,
                account_id=cash_account.id,
                description='Cash received',
                amount=Decimal('1000.00'),
                dc='D'
            )
            
            credit_line = JournalLine(
                journal_id=entry.id,
                account_id=sales_account.id,
                description='Sales revenue',
                amount=Decimal('1000.00'),
                dc='C'
            )
            
            db.session.add_all([debit_line, credit_line])
            db.session.commit()
            
            # Test relationships
            assert len(entry.lines) == 2
            assert entry.get_total_debits() == Decimal('1000.00')
            assert entry.get_total_credits() == Decimal('1000.00')
            assert entry.is_balanced()
    
    def test_journal_entry_posting(self, app, admin_user, test_accounts):
        """Test journal entry posting"""
        with app.app_context():
            cash_account = test_accounts['cash']
            sales_account = test_accounts['sales']
            
            entry = JournalEntry(
                reference_number='JE003',
                entry_date=date.today(),
                description='Test Posting',
                created_by_id=admin_user.id
            )
            db.session.add(entry)
            db.session.flush()
            
            # Add balanced lines
            lines = [
                JournalLine(journal_id=entry.id, account_id=cash_account.id,
                           amount=Decimal('500.00'), dc='D'),
                JournalLine(journal_id=entry.id, account_id=sales_account.id,
                           amount=Decimal('500.00'), dc='C')
            ]
            db.session.add_all(lines)
            
            # Post the entry
            entry.is_posted = True
            entry.posted_at = datetime.utcnow()
            entry.posted_by_id = admin_user.id
            
            db.session.commit()
            
            assert entry.is_posted is True
            assert entry.posted_at is not None
            assert entry.posted_by == admin_user
    
    def test_unbalanced_entry_validation(self, app, admin_user, test_accounts):
        """Test validation of unbalanced entries"""
        with app.app_context():
            cash_account = test_accounts['cash']
            sales_account = test_accounts['sales']
            
            entry = JournalEntry(
                reference_number='JE004',
                entry_date=date.today(),
                description='Unbalanced Entry',
                created_by_id=admin_user.id
            )
            db.session.add(entry)
            db.session.flush()
            
            # Add unbalanced lines
            lines = [
                JournalLine(journal_id=entry.id, account_id=cash_account.id,
                           amount=Decimal('1000.00'), dc='D'),
                JournalLine(journal_id=entry.id, account_id=sales_account.id,
                           amount=Decimal('500.00'), dc='C')
            ]
            db.session.add_all(lines)
            db.session.commit()
            
            assert not entry.is_balanced()
            assert entry.get_total_debits() != entry.get_total_credits()


class TestJournalLineModel:
    """Test JournalLine model functionality"""
    
    def test_journal_line_creation(self, app, admin_user, test_accounts):
        """Test creating journal lines"""
        with app.app_context():
            cash_account = test_accounts['cash']
            
            entry = JournalEntry(
                reference_number='JE005',
                entry_date=date.today(),
                description='Line Test',
                created_by_id=admin_user.id
            )
            db.session.add(entry)
            db.session.flush()
            
            line = JournalLine(
                journal_id=entry.id,
                account_id=cash_account.id,
                description='Test Line',
                amount=Decimal('250.00'),
                dc='D'
            )
            db.session.add(line)
            db.session.commit()
            
            assert line.id is not None
            assert line.journal == entry
            assert line.account == cash_account
            assert line.amount == Decimal('250.00')
            assert line.dc == 'D'
    
    def test_journal_line_validation(self, app, admin_user, test_accounts):
        """Test journal line validation"""
        with app.app_context():
            cash_account = test_accounts['cash']
            
            entry = JournalEntry(
                reference_number='JE006',
                entry_date=date.today(),
                description='Validation Test',
                created_by_id=admin_user.id
            )
            db.session.add(entry)
            db.session.flush()
            
            # Test valid line
            valid_line = JournalLine(
                journal_id=entry.id,
                account_id=cash_account.id,
                amount=Decimal('100.00'),
                dc='D'
            )
            
            assert valid_line.amount > 0
            assert valid_line.dc in ['D', 'C']


class TestJournalViews:
    """Test journal views and forms"""
    
    def test_journal_index_page(self, client, admin_user, auth):
        """Test journal index page"""
        auth.login()
        response = client.get('/journal/')
        assert response.status_code == 200
        assert 'دفتر اليومية' in response.get_data(as_text=True)
    
    def test_journal_entry_creation_form(self, client, admin_user, auth):
        """Test journal entry creation form"""
        auth.login()
        
        # Get form page
        response = client.get('/journal/new')
        assert response.status_code == 200
        assert 'قيد جديد' in response.get_data(as_text=True)
    
    def test_journal_entry_creation(self, client, admin_user, auth, test_accounts):
        """Test creating journal entry through form"""
        auth.login()
        
        cash_account = test_accounts['cash']
        sales_account = test_accounts['sales']
        
        # Submit journal entry form
        response = client.post('/journal/create', data={
            'reference_number': 'TEST001',
            'entry_date': date.today().isoformat(),
            'description': 'Test Entry',
            'lines-0-account_id': str(cash_account.id),
            'lines-0-amount': '1000.00',
            'lines-0-dc': 'D',
            'lines-1-account_id': str(sales_account.id),
            'lines-1-amount': '1000.00',
            'lines-1-dc': 'C',
            'action': 'save'
        }, follow_redirects=True)
        
        assert response.status_code == 200
    
    def test_journal_search(self, client, admin_user, auth):
        """Test journal search functionality"""
        auth.login()
        
        response = client.get('/journal/?search=test')
        assert response.status_code == 200
        
        response = client.get('/journal/?status=posted')
        assert response.status_code == 200
    
    def test_ledger_view(self, client, admin_user, auth):
        """Test ledger view"""
        auth.login()
        
        response = client.get('/journal/ledger')
        assert response.status_code == 200
        assert 'دفتر الأستاذ' in response.get_data(as_text=True)
    
    def test_ledger_with_account_filter(self, client, admin_user, auth, test_accounts):
        """Test ledger with account filter"""
        auth.login()
        
        cash_account = test_accounts['cash']
        response = client.get(f'/journal/ledger?account_id={cash_account.id}')
        assert response.status_code == 200


class TestJournalPermissions:
    """Test journal access permissions"""
    
    def test_admin_access(self, client, admin_user, auth):
        """Test admin user access to journal"""
        auth.login('admin', 'admin123')
        
        response = client.get('/journal/')
        assert response.status_code == 200
    
    def test_accountant_access(self, client, accountant_user, auth):
        """Test accountant user access to journal"""
        auth.login('accountant', 'acc123')
        
        response = client.get('/journal/')
        assert response.status_code == 200
    
    def test_employee_access(self, client, employee_user, auth):
        """Test employee user access to journal"""
        auth.login('employee', 'emp123')
        
        # Employees should not have access to journal
        response = client.get('/journal/')
        assert response.status_code == 302  # Redirect to login or access denied


class TestJournalBusinessLogic:
    """Test journal business logic"""
    
    def test_account_balance_calculation(self, app, admin_user, test_accounts):
        """Test account balance calculation with journal entries"""
        with app.app_context():
            cash_account = test_accounts['cash']
            sales_account = test_accounts['sales']
            
            # Initial balance should be zero
            assert cash_account.get_balance() == Decimal('0')
            
            # Create journal entry
            entry = JournalEntry(
                reference_number='BAL001',
                entry_date=date.today(),
                description='Balance Test',
                created_by_id=admin_user.id,
                is_posted=True
            )
            db.session.add(entry)
            db.session.flush()
            
            # Add lines
            lines = [
                JournalLine(journal_id=entry.id, account_id=cash_account.id,
                           amount=Decimal('1500.00'), dc='D'),
                JournalLine(journal_id=entry.id, account_id=sales_account.id,
                           amount=Decimal('1500.00'), dc='C')
            ]
            db.session.add_all(lines)
            db.session.commit()
            
            # Check balances
            assert cash_account.get_balance() == Decimal('1500.00')
            assert sales_account.get_balance() == Decimal('1500.00')
    
    def test_multiple_entries_balance(self, app, admin_user, test_accounts):
        """Test balance calculation with multiple entries"""
        with app.app_context():
            cash_account = test_accounts['cash']
            sales_account = test_accounts['sales']
            expenses_account = test_accounts['expenses']
            
            # Entry 1: Sales
            entry1 = JournalEntry(
                reference_number='MULTI001',
                entry_date=date.today(),
                description='Sales',
                created_by_id=admin_user.id,
                is_posted=True
            )
            db.session.add(entry1)
            db.session.flush()
            
            lines1 = [
                JournalLine(journal_id=entry1.id, account_id=cash_account.id,
                           amount=Decimal('2000.00'), dc='D'),
                JournalLine(journal_id=entry1.id, account_id=sales_account.id,
                           amount=Decimal('2000.00'), dc='C')
            ]
            db.session.add_all(lines1)
            
            # Entry 2: Expense
            entry2 = JournalEntry(
                reference_number='MULTI002',
                entry_date=date.today(),
                description='Expense',
                created_by_id=admin_user.id,
                is_posted=True
            )
            db.session.add(entry2)
            db.session.flush()
            
            lines2 = [
                JournalLine(journal_id=entry2.id, account_id=expenses_account.id,
                           amount=Decimal('500.00'), dc='D'),
                JournalLine(journal_id=entry2.id, account_id=cash_account.id,
                           amount=Decimal('500.00'), dc='C')
            ]
            db.session.add_all(lines2)
            db.session.commit()
            
            # Check final balances
            assert cash_account.get_balance() == Decimal('1500.00')  # 2000 - 500
            assert sales_account.get_balance() == Decimal('2000.00')
            assert expenses_account.get_balance() == Decimal('500.00')


if __name__ == '__main__':
    pytest.main([__file__])
