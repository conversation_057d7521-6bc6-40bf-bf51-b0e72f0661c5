"""
Account model for Chart of Accounts
"""

import uuid
from datetime import datetime
from app import db

class Account(db.Model):
    __tablename__ = 'accounts'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    code = db.Column(db.String(20), unique=True, nullable=False, index=True)
    name = db.Column(db.String(100), nullable=False)
    name_en = db.Column(db.String(100))
    type = db.Column(db.String(20), nullable=False, index=True)
    parent_id = db.Column(db.String(36), db.<PERSON><PERSON>ey('accounts.id'), index=True)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Self-referential relationship for parent-child accounts
    children = db.relationship('Account', backref=db.backref('parent', remote_side=[id]), lazy='dynamic')
    
    # Relationships
    journal_lines = db.relationship('JournalLine', backref='account', lazy='dynamic')
    
    # Account types
    ACCOUNT_TYPES = {
        'Asset': 'أصول',
        'Liability': 'خصوم', 
        'Equity': 'رأس المال',
        'Income': 'إيرادات',
        'Expense': 'مصروفات'
    }
    
    def __init__(self, code, name, type, name_en=None, parent_id=None):
        self.code = code
        self.name = name
        self.name_en = name_en
        self.type = type
        self.parent_id = parent_id
    
    def get_type_display(self):
        """Get Arabic display name for account type"""
        return self.ACCOUNT_TYPES.get(self.type, self.type)
    
    def get_full_code(self):
        """Get full hierarchical code"""
        if self.parent:
            return f"{self.parent.get_full_code()}.{self.code}"
        return self.code
    
    def get_full_name(self):
        """Get full hierarchical name"""
        if self.parent:
            return f"{self.parent.get_full_name()} > {self.name}"
        return self.name
    
    def get_level(self):
        """Get account hierarchy level"""
        level = 0
        parent = self.parent
        while parent:
            level += 1
            parent = parent.parent
        return level
    
    def get_balance(self, as_of_date=None):
        """Calculate account balance"""
        from app.models.journal import JournalLine, JournalEntry

        query = JournalLine.query.filter_by(account_id=self.id)

        if as_of_date:
            query = query.join(JournalEntry).filter(
                JournalEntry.date <= as_of_date
            )

        debit_total = query.with_entities(
            db.func.coalesce(db.func.sum(JournalLine.debit), 0)
        ).scalar() or 0

        credit_total = query.with_entities(
            db.func.coalesce(db.func.sum(JournalLine.credit), 0)
        ).scalar() or 0

        # Calculate balance based on account type
        if self.type in ['Asset', 'Expense']:
            return debit_total - credit_total
        else:  # Liability, Equity, Income
            return credit_total - debit_total

    def get_balance_for_period(self, start_date=None, end_date=None):
        """Calculate account balance for a specific period"""
        from app.models.journal import JournalLine, JournalEntry

        query = JournalLine.query.filter_by(account_id=self.id)

        if start_date and end_date:
            query = query.join(JournalEntry).filter(
                JournalEntry.date >= start_date,
                JournalEntry.date <= end_date
            )
        elif start_date:
            query = query.join(JournalEntry).filter(
                JournalEntry.date >= start_date
            )
        elif end_date:
            query = query.join(JournalEntry).filter(
                JournalEntry.date <= end_date
            )

        debit_total = query.with_entities(
            db.func.coalesce(db.func.sum(JournalLine.debit), 0)
        ).scalar() or 0

        credit_total = query.with_entities(
            db.func.coalesce(db.func.sum(JournalLine.credit), 0)
        ).scalar() or 0

        # Calculate balance based on account type
        if self.type in ['Asset', 'Expense']:
            return debit_total - credit_total
        else:  # Liability, Equity, Income
            return credit_total - debit_total

    def get_period_activity(self, start_date=None, end_date=None):
        """Get account activity for a specific period"""
        from app.models.journal import JournalLine, JournalEntry

        query = JournalLine.query.filter_by(account_id=self.id)

        if start_date and end_date:
            query = query.join(JournalEntry).filter(
                JournalEntry.date >= start_date,
                JournalEntry.date <= end_date
            )
        elif start_date:
            query = query.join(JournalEntry).filter(
                JournalEntry.date >= start_date
            )
        elif end_date:
            query = query.join(JournalEntry).filter(
                JournalEntry.date <= end_date
            )

        debit_total = query.with_entities(
            db.func.coalesce(db.func.sum(JournalLine.debit), 0)
        ).scalar() or 0

        credit_total = query.with_entities(
            db.func.coalesce(db.func.sum(JournalLine.credit), 0)
        ).scalar() or 0

        lines_count = query.count()

        return {
            'total_debit': float(debit_total),
            'total_credit': float(credit_total),
            'net_change': float(debit_total - credit_total) if self.type in ['Asset', 'Expense'] else float(credit_total - debit_total),
            'transaction_count': lines_count
        }

    def get_children_recursive(self):
        """Get all child accounts recursively"""
        children = []
        for child in self.children:
            children.append(child)
            children.extend(child.get_children_recursive())
        return children
    
    def is_leaf_account(self):
        """Check if account is a leaf (has no children)"""
        return self.children.count() == 0
    
    def can_post_transactions(self):
        """Check if transactions can be posted to this account"""
        return self.is_leaf_account() and self.is_active
    
    @classmethod
    def get_by_type(cls, account_type):
        """Get accounts by type"""
        return cls.query.filter_by(type=account_type, is_active=True).all()
    
    @classmethod
    def get_root_accounts(cls):
        """Get root level accounts"""
        return cls.query.filter_by(parent_id=None, is_active=True).order_by(cls.code).all()
    
    @classmethod
    def search(cls, term):
        """Search accounts by code or name"""
        return cls.query.filter(
            db.or_(
                cls.code.ilike(f'%{term}%'),
                cls.name.ilike(f'%{term}%'),
                cls.name_en.ilike(f'%{term}%') if term else False
            ),
            cls.is_active == True
        ).order_by(cls.code).all()
    
    def to_dict(self):
        """Convert account to dictionary"""
        return {
            'id': self.id,
            'code': self.code,
            'name': self.name,
            'name_en': self.name_en,
            'type': self.type,
            'type_display': self.get_type_display(),
            'parent_id': self.parent_id,
            'is_active': self.is_active,
            'level': self.get_level(),
            'balance': float(self.get_balance()),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Account {self.code}: {self.name}>'
