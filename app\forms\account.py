"""
Account forms for Chart of Accounts
"""

from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, SelectField, TextAreaField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, ValidationError
from app.models.account import Account

class AccountForm(FlaskForm):
    code = StringField('رمز الحساب', validators=[
        DataRequired(), 
        Length(min=1, max=20)
    ])
    name = StringField('اسم الحساب', validators=[
        DataRequired(), 
        Length(min=1, max=100)
    ])
    name_en = StringField('الاسم بالإنجليزية', validators=[
        Length(max=100)
    ])
    type = SelectField('نوع الحساب', choices=[
        ('Asset', 'أصول'),
        ('Liability', 'خصوم'),
        ('Equity', 'رأس المال'),
        ('Income', 'إيرادات'),
        ('Expense', 'مصروفات')
    ], validators=[DataRequired()])
    parent_id = SelectField('الحساب الأب', coerce=str, choices=[])
    is_active = BooleanField('نشط', default=True)
    submit = SubmitField('حفظ')
    
    def __init__(self, account=None, *args, **kwargs):
        super(AccountForm, self).__init__(*args, **kwargs)
        self.account = account
        
        # Populate parent account choices
        self.parent_id.choices = [('', 'لا يوجد')]
        accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
        
        for acc in accounts:
            if not account or acc.id != account.id:  # Don't allow self as parent
                self.parent_id.choices.append((acc.id, f"{acc.code} - {acc.name}"))
    
    def validate_code(self, code):
        account = Account.query.filter_by(code=code.data).first()
        if account and (not self.account or account.id != self.account.id):
            raise ValidationError('رمز الحساب مستخدم بالفعل. يرجى اختيار رمز آخر.')
    
    def validate_parent_id(self, parent_id):
        if parent_id.data:
            parent = Account.query.get(parent_id.data)
            if not parent:
                raise ValidationError('الحساب الأب غير موجود.')
            
            # Check for circular reference
            if self.account and self._would_create_circular_reference(parent):
                raise ValidationError('لا يمكن اختيار هذا الحساب كحساب أب لأنه سيؤدي إلى مرجع دائري.')
    
    def _would_create_circular_reference(self, parent):
        """Check if setting this parent would create a circular reference"""
        current = parent
        while current:
            if current.id == self.account.id:
                return True
            current = current.parent
        return False

class AccountSearchForm(FlaskForm):
    search = StringField('البحث', validators=[Length(max=100)])
    type = SelectField('نوع الحساب', choices=[
        ('', 'جميع الأنواع'),
        ('Asset', 'أصول'),
        ('Liability', 'خصوم'),
        ('Equity', 'رأس المال'),
        ('Income', 'إيرادات'),
        ('Expense', 'مصروفات')
    ])
    submit = SubmitField('بحث')
