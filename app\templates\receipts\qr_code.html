{% extends "base.html" %}

{% block title %}QR Code - {{ receipt.receipt_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">QR Code للإيصال الإلكتروني</h1>
                    <p class="text-muted">إيصال رقم: {{ receipt.receipt_number }}</p>
                </div>
                <div>
                    <a href="{{ url_for('receipts.detail', receipt_id=receipt.id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للإيصال
                    </a>
                </div>
            </div>

            <!-- QR Code Display -->
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-qrcode"></i> QR Code للطباعة
                            </h5>
                        </div>
                        <div class="card-body text-center">
                            {% if receipt.eta_qr_image_path %}
                                <!-- QR Code Image -->
                                <div class="mb-4">
                                    <img src="{{ url_for('receipts.qr_code', receipt_id=receipt.id) }}" 
                                         alt="QR Code" 
                                         class="img-fluid border rounded"
                                         style="max-width: 400px;">
                                </div>

                                <!-- Receipt Information -->
                                <div class="mb-4">
                                    <h6 class="text-muted">معلومات الإيصال</h6>
                                    <div class="row text-start">
                                        <div class="col-sm-6">
                                            <strong>رقم الإيصال:</strong><br>
                                            <span class="text-muted">{{ receipt.receipt_number }}</span>
                                        </div>
                                        <div class="col-sm-6">
                                            <strong>تاريخ الإصدار:</strong><br>
                                            <span class="text-muted">{{ receipt.receipt_date.strftime('%Y-%m-%d') }}</span>
                                        </div>
                                        {% if receipt.eta_uuid %}
                                        <div class="col-sm-6 mt-2">
                                            <strong>UUID:</strong><br>
                                            <span class="text-muted small">{{ receipt.eta_uuid[:16] }}...</span>
                                        </div>
                                        {% endif %}
                                        <div class="col-sm-6 mt-2">
                                            <strong>المبلغ:</strong><br>
                                            <span class="text-muted">{{ receipt.amount }} {{ receipt.currency or 'EGP' }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- QR Code Data -->
                                {% if receipt.eta_qr_code %}
                                <div class="mb-4">
                                    <h6 class="text-muted">بيانات QR Code</h6>
                                    <div class="bg-light p-3 rounded">
                                        <code class="small">{{ receipt.eta_qr_code }}</code>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Action Buttons -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <a href="{{ url_for('receipts.download_qr_code', receipt_id=receipt.id) }}" 
                                       class="btn btn-success">
                                        <i class="fas fa-download"></i> تحميل QR Code
                                    </a>
                                    <button type="button" class="btn btn-info" onclick="printQRCode()">
                                        <i class="fas fa-print"></i> طباعة
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="regenerateQR()">
                                        <i class="fas fa-sync"></i> إعادة إنشاء
                                    </button>
                                </div>

                            {% else %}
                                <!-- No QR Code Available -->
                                <div class="text-center py-5">
                                    <i class="fas fa-qrcode fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">QR Code غير متوفر</h5>
                                    <p class="text-muted">لم يتم إنشاء QR Code لهذا الإيصال بعد</p>
                                    
                                    {% if receipt.eta_uuid %}
                                        <button type="button" class="btn btn-primary" onclick="generateQR()">
                                            <i class="fas fa-plus"></i> إنشاء QR Code
                                        </button>
                                    {% else %}
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            يجب إرسال الإيصال إلى مصلحة الضرائب أولاً لإنشاء QR Code
                                        </div>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Instructions -->
            <div class="row justify-content-center mt-4">
                <div class="col-md-8 col-lg-6">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-info-circle"></i> تعليمات الاستخدام
                            </h6>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled mb-0">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    يمكن مسح QR Code باستخدام أي تطبيق قارئ QR Code
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    يحتوي QR Code على معلومات الإيصال المعتمدة من مصلحة الضرائب
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success"></i>
                                    يمكن طباعة QR Code وإرفاقه مع الإيصال الورقي
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-check text-success"></i>
                                    QR Code صالح للتحقق من صحة الإيصال الإلكتروني
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Print Styles -->
<style>
@media print {
    .btn, .card-header, nav, .breadcrumb {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .container-fluid {
        padding: 0 !important;
    }
    
    body {
        background: white !important;
    }
}
</style>

<script>
function printQRCode() {
    window.print();
}

function regenerateQR() {
    if (confirm('هل تريد إعادة إنشاء QR Code؟')) {
        fetch('{{ url_for("receipts.generate_qr_code", receipt_id=receipt.id) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                showAlert('error', data.message);
            }
        })
        .catch(error => {
            showAlert('error', 'حدث خطأ في إعادة إنشاء QR Code');
        });
    }
}

function generateQR() {
    fetch('{{ url_for("receipts.generate_qr_code", receipt_id=receipt.id) }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            showAlert('error', data.message);
        }
    })
    .catch(error => {
        showAlert('error', 'حدث خطأ في إنشاء QR Code');
    });
}

function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertAdjacentHTML('afterbegin', alertHtml);
}
</script>
{% endblock %}
