"""
نموذج إعدادات النظام مع حفظ دائم
System Settings Model with Persistent Storage
"""

from app import db
from datetime import datetime
from sqlalchemy import text
import json
from typing import Any, Dict, Optional


class SystemSettings(db.Model):
    """نموذج إعدادات النظام"""
    __tablename__ = 'system_settings'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    value = db.Column(db.Text)
    data_type = db.Column(db.String(20), default='string')  # string, integer, float, boolean, json
    category = db.Column(db.String(50), default='general', index=True)
    description = db.Column(db.Text)
    is_encrypted = db.Column(db.Bo<PERSON>, default=False)
    is_system = db.Column(db.Bo<PERSON>, default=False)  # System settings cannot be deleted
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    updated_by = db.Column(db.String(100))
    
    def __init__(self, key: str, value: Any, data_type: str = 'string', 
                 category: str = 'general', description: str = '', 
                 is_encrypted: bool = False, is_system: bool = False):
        self.key = key
        self.set_value(value, data_type)
        self.data_type = data_type
        self.category = category
        self.description = description
        self.is_encrypted = is_encrypted
        self.is_system = is_system
    
    def set_value(self, value: Any, data_type: str = None):
        """تعيين القيمة مع التحويل المناسب"""
        if data_type:
            self.data_type = data_type
        
        if self.data_type == 'json':
            self.value = json.dumps(value, ensure_ascii=False)
        elif self.data_type == 'boolean':
            self.value = str(bool(value)).lower()
        else:
            self.value = str(value) if value is not None else None
        
        self.updated_at = datetime.utcnow()
    
    def get_value(self) -> Any:
        """الحصول على القيمة مع التحويل المناسب"""
        if self.value is None:
            return None
        
        try:
            if self.data_type == 'integer':
                return int(self.value)
            elif self.data_type == 'float':
                return float(self.value)
            elif self.data_type == 'boolean':
                return self.value.lower() in ('true', '1', 'yes', 'on')
            elif self.data_type == 'json':
                return json.loads(self.value)
            else:
                return self.value
        except (ValueError, json.JSONDecodeError):
            return self.value
    
    @classmethod
    def get_setting(cls, key: str, default: Any = None, category: str = None) -> Any:
        """الحصول على إعداد معين"""
        query = cls.query.filter_by(key=key)
        if category:
            query = query.filter_by(category=category)
        
        setting = query.first()
        if setting:
            return setting.get_value()
        return default
    
    @classmethod
    def set_setting(cls, key: str, value: Any, data_type: str = 'string', 
                   category: str = 'general', description: str = '', 
                   is_encrypted: bool = False, is_system: bool = False,
                   updated_by: str = None) -> 'SystemSettings':
        """تعيين إعداد معين"""
        setting = cls.query.filter_by(key=key).first()
        
        if setting:
            setting.set_value(value, data_type)
            setting.category = category
            setting.description = description
            setting.is_encrypted = is_encrypted
            setting.updated_by = updated_by
        else:
            setting = cls(
                key=key,
                value=value,
                data_type=data_type,
                category=category,
                description=description,
                is_encrypted=is_encrypted,
                is_system=is_system
            )
            setting.updated_by = updated_by
            db.session.add(setting)
        
        db.session.commit()
        return setting
    
    @classmethod
    def get_category_settings(cls, category: str) -> Dict[str, Any]:
        """الحصول على جميع إعدادات فئة معينة"""
        settings = cls.query.filter_by(category=category).all()
        return {setting.key: setting.get_value() for setting in settings}
    
    @classmethod
    def delete_setting(cls, key: str) -> bool:
        """حذف إعداد معين"""
        setting = cls.query.filter_by(key=key).first()
        if setting and not setting.is_system:
            db.session.delete(setting)
            db.session.commit()
            return True
        return False
    
    @classmethod
    def initialize_default_settings(cls):
        """تهيئة الإعدادات الافتراضية"""
        default_settings = [
            # ETA Settings
            ('ETA_BASE_URL', 'https://api.preprod.invoicing.eta.gov.eg/api/v1', 'string', 'eta', 'رابط API مصلحة الضرائب', False, True),
            ('ETA_CLIENT_ID', '', 'string', 'eta', 'معرف العميل لمصلحة الضرائب', True, True),
            ('ETA_CLIENT_SECRET', '', 'string', 'eta', 'كلمة سر العميل لمصلحة الضرائب', True, True),
            ('ETA_ENVIRONMENT', 'sandbox', 'string', 'eta', 'بيئة العمل (sandbox/production)', False, True),
            ('ETA_TIMEOUT', '30', 'integer', 'eta', 'مهلة الاتصال بالثواني', False, True),
            ('ETA_AUTO_SUBMIT', 'false', 'boolean', 'eta', 'الإرسال التلقائي للإيصالات', False, True),
            ('ETA_DEVICE_SERIAL', 'DEVICE001', 'string', 'eta', 'رقم الجهاز المسجل', False, True),
            
            # Company Settings
            ('COMPANY_NAME', 'شركة النظام الضريبي', 'string', 'company', 'اسم الشركة', False, True),
            ('COMPANY_TAX_ID', '', 'string', 'company', 'الرقم الضريبي للشركة', False, True),
            ('COMPANY_ACTIVITY_CODE', '', 'string', 'company', 'كود النشاط', False, True),
            ('COMPANY_BRANCH_ID', '0', 'string', 'company', 'كود الفرع', False, True),
            ('COMPANY_ADDRESS', '', 'string', 'company', 'عنوان الشركة', False, True),
            ('COMPANY_PHONE', '', 'string', 'company', 'هاتف الشركة', False, True),
            ('COMPANY_EMAIL', '', 'string', 'company', 'بريد الشركة الإلكتروني', False, True),
            
            # System Settings
            ('SYSTEM_CURRENCY', 'EGP', 'string', 'system', 'العملة الافتراضية', False, True),
            ('SYSTEM_LANGUAGE', 'ar', 'string', 'system', 'لغة النظام', False, True),
            ('SYSTEM_TIMEZONE', 'Africa/Cairo', 'string', 'system', 'المنطقة الزمنية', False, True),
            ('BACKUP_ENABLED', 'true', 'boolean', 'system', 'تفعيل النسخ الاحتياطي', False, True),
            ('BACKUP_RETENTION_DAYS', '30', 'integer', 'system', 'مدة الاحتفاظ بالنسخ الاحتياطية', False, True),
            
            # Security Settings
            ('SESSION_TIMEOUT', '480', 'integer', 'security', 'مهلة انتهاء الجلسة بالدقائق', False, True),
            ('PASSWORD_MIN_LENGTH', '8', 'integer', 'security', 'الحد الأدنى لطول كلمة المرور', False, True),
            ('LOGIN_ATTEMPTS_LIMIT', '5', 'integer', 'security', 'عدد محاولات تسجيل الدخول المسموحة', False, True),
            
            # Notification Settings
            ('EMAIL_NOTIFICATIONS', 'true', 'boolean', 'notifications', 'تفعيل إشعارات البريد الإلكتروني', False, True),
            ('SMS_NOTIFICATIONS', 'false', 'boolean', 'notifications', 'تفعيل إشعارات الرسائل النصية', False, True),
        ]
        
        for key, value, data_type, category, description, is_encrypted, is_system in default_settings:
            existing = cls.query.filter_by(key=key).first()
            if not existing:
                setting = cls(
                    key=key,
                    value=value,
                    data_type=data_type,
                    category=category,
                    description=description,
                    is_encrypted=is_encrypted,
                    is_system=is_system
                )
                db.session.add(setting)
        
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            print(f"Error initializing default settings: {e}")
    
    def to_dict(self) -> Dict:
        """تحويل الإعداد إلى قاموس"""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.get_value(),
            'data_type': self.data_type,
            'category': self.category,
            'description': self.description,
            'is_encrypted': self.is_encrypted,
            'is_system': self.is_system,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'updated_by': self.updated_by
        }
    
    def __repr__(self):
        return f'<SystemSettings {self.key}={self.value}>'


# Helper functions for easy access
def get_setting(key: str, default: Any = None, category: str = None) -> Any:
    """دالة مساعدة للحصول على إعداد"""
    return SystemSettings.get_setting(key, default, category)


def set_setting(key: str, value: Any, **kwargs) -> SystemSettings:
    """دالة مساعدة لتعيين إعداد"""
    return SystemSettings.set_setting(key, value, **kwargs)


def get_eta_settings() -> Dict[str, Any]:
    """الحصول على جميع إعدادات ETA"""
    return SystemSettings.get_category_settings('eta')


def get_company_settings() -> Dict[str, Any]:
    """الحصول على جميع إعدادات الشركة"""
    return SystemSettings.get_category_settings('company')
