"""
Backup and restore service for SystemTax
"""

import os
import json
import gzip
import shutil
import sqlite3
import subprocess
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from flask import current_app
from app import db
from app.models.user import User
from app.models.account import Account
from app.models.journal import JournalEntry, JournalLine
from app.models.invoice import Invoice, InvoiceItem
from app.models.receipt import Receipt
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.models.notification import Notification
import logging

logger = logging.getLogger(__name__)


class BackupService:
    """Service for creating and managing backups"""
    
    def __init__(self):
        self.backup_dir = Path(current_app.config.get('BACKUP_DIR', 'backups'))
        self.backup_dir.mkdir(exist_ok=True)
        self.max_backups = current_app.config.get('MAX_BACKUPS', 30)
        self.compression_enabled = current_app.config.get('BACKUP_COMPRESSION', True)
    
    def create_full_backup(self, description: str = None) -> Dict:
        """Create a full system backup"""
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"full_backup_{timestamp}"
        backup_path = self.backup_dir / backup_name
        backup_path.mkdir(exist_ok=True)
        
        try:
            # Create backup metadata
            metadata = {
                'backup_name': backup_name,
                'backup_type': 'full',
                'created_at': datetime.now().isoformat(),
                'description': description or f'Full backup created on {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}',
                'version': current_app.config.get('APP_VERSION', '1.0.0'),
                'database_url': current_app.config.get('DATABASE_URL', ''),
                'files': []
            }
            
            # Backup database
            db_backup_path = self._backup_database(backup_path)
            if db_backup_path:
                metadata['files'].append({
                    'type': 'database',
                    'path': str(db_backup_path.relative_to(backup_path)),
                    'size': db_backup_path.stat().st_size
                })
            
            # Backup uploaded files
            files_backup_path = self._backup_files(backup_path)
            if files_backup_path:
                metadata['files'].append({
                    'type': 'files',
                    'path': str(files_backup_path.relative_to(backup_path)),
                    'size': self._get_directory_size(files_backup_path)
                })
            
            # Backup configuration
            config_backup_path = self._backup_configuration(backup_path)
            if config_backup_path:
                metadata['files'].append({
                    'type': 'configuration',
                    'path': str(config_backup_path.relative_to(backup_path)),
                    'size': config_backup_path.stat().st_size
                })
            
            # Save metadata
            metadata_path = backup_path / 'metadata.json'
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            # Compress backup if enabled
            if self.compression_enabled:
                compressed_path = self._compress_backup(backup_path)
                if compressed_path:
                    shutil.rmtree(backup_path)
                    backup_path = compressed_path
                    metadata['compressed'] = True
                    metadata['compressed_size'] = backup_path.stat().st_size
            
            # Clean up old backups
            self._cleanup_old_backups()
            
            # Log backup creation
            logger.info(f"Full backup created successfully: {backup_name}")
            
            return {
                'success': True,
                'backup_name': backup_name,
                'backup_path': str(backup_path),
                'metadata': metadata
            }
            
        except Exception as e:
            logger.error(f"Failed to create full backup: {e}")
            # Clean up failed backup
            if backup_path.exists():
                shutil.rmtree(backup_path)
            
            return {
                'success': False,
                'error': str(e)
            }
    
    def create_incremental_backup(self, since: datetime = None) -> Dict:
        """Create an incremental backup"""
        
        if not since:
            # Get last backup date
            since = self._get_last_backup_date()
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"incremental_backup_{timestamp}"
        backup_path = self.backup_dir / backup_name
        backup_path.mkdir(exist_ok=True)
        
        try:
            # Create backup metadata
            metadata = {
                'backup_name': backup_name,
                'backup_type': 'incremental',
                'created_at': datetime.now().isoformat(),
                'since': since.isoformat() if since else None,
                'description': f'Incremental backup since {since.strftime("%Y-%m-%d %H:%M:%S") if since else "beginning"}',
                'version': current_app.config.get('APP_VERSION', '1.0.0'),
                'changes': []
            }
            
            # Backup changed data
            changes = self._backup_incremental_data(backup_path, since)
            metadata['changes'] = changes
            
            # Save metadata
            metadata_path = backup_path / 'metadata.json'
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            # Compress backup if enabled
            if self.compression_enabled:
                compressed_path = self._compress_backup(backup_path)
                if compressed_path:
                    shutil.rmtree(backup_path)
                    backup_path = compressed_path
                    metadata['compressed'] = True
            
            logger.info(f"Incremental backup created successfully: {backup_name}")
            
            return {
                'success': True,
                'backup_name': backup_name,
                'backup_path': str(backup_path),
                'metadata': metadata
            }
            
        except Exception as e:
            logger.error(f"Failed to create incremental backup: {e}")
            if backup_path.exists():
                shutil.rmtree(backup_path)
            
            return {
                'success': False,
                'error': str(e)
            }
    
    def _backup_database(self, backup_path: Path) -> Optional[Path]:
        """Backup database"""
        
        db_url = current_app.config.get('DATABASE_URL', '')
        
        if db_url.startswith('sqlite'):
            # SQLite backup
            db_file = db_url.replace('sqlite:///', '')
            if os.path.exists(db_file):
                backup_db_path = backup_path / 'database.db'
                shutil.copy2(db_file, backup_db_path)
                return backup_db_path
        
        elif db_url.startswith('postgresql'):
            # PostgreSQL backup
            backup_db_path = backup_path / 'database.sql'
            try:
                # Extract connection details from URL
                # postgresql://user:password@host:port/database
                import urllib.parse
                parsed = urllib.parse.urlparse(db_url)
                
                cmd = [
                    'pg_dump',
                    '-h', parsed.hostname,
                    '-p', str(parsed.port or 5432),
                    '-U', parsed.username,
                    '-d', parsed.path[1:],  # Remove leading slash
                    '-f', str(backup_db_path),
                    '--no-password'
                ]
                
                env = os.environ.copy()
                if parsed.password:
                    env['PGPASSWORD'] = parsed.password
                
                subprocess.run(cmd, env=env, check=True)
                return backup_db_path
                
            except subprocess.CalledProcessError as e:
                logger.error(f"PostgreSQL backup failed: {e}")
                return None
        
        elif db_url.startswith('mysql'):
            # MySQL backup
            backup_db_path = backup_path / 'database.sql'
            try:
                import urllib.parse
                parsed = urllib.parse.urlparse(db_url)
                
                cmd = [
                    'mysqldump',
                    '-h', parsed.hostname,
                    '-P', str(parsed.port or 3306),
                    '-u', parsed.username,
                    f'-p{parsed.password}' if parsed.password else '',
                    parsed.path[1:],  # Remove leading slash
                ]
                
                with open(backup_db_path, 'w') as f:
                    subprocess.run(cmd, stdout=f, check=True)
                
                return backup_db_path
                
            except subprocess.CalledProcessError as e:
                logger.error(f"MySQL backup failed: {e}")
                return None
        
        return None
    
    def _backup_files(self, backup_path: Path) -> Optional[Path]:
        """Backup uploaded files and static assets"""
        
        files_to_backup = [
            ('uploads', current_app.config.get('UPLOAD_FOLDER', 'uploads')),
            ('static', 'app/static'),
            ('templates', 'app/templates')
        ]
        
        files_backup_path = backup_path / 'files'
        files_backup_path.mkdir(exist_ok=True)
        
        for folder_name, source_path in files_to_backup:
            if os.path.exists(source_path):
                dest_path = files_backup_path / folder_name
                shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
        
        return files_backup_path if any(files_backup_path.iterdir()) else None
    
    def _backup_configuration(self, backup_path: Path) -> Optional[Path]:
        """Backup configuration files"""
        
        config_data = {
            'app_config': {
                'APP_NAME': current_app.config.get('APP_NAME'),
                'SECRET_KEY': '***HIDDEN***',  # Don't backup secret key
                'DATABASE_URL': '***HIDDEN***',  # Don't backup DB credentials
                'MAIL_SERVER': current_app.config.get('MAIL_SERVER'),
                'MAIL_PORT': current_app.config.get('MAIL_PORT'),
                'MAIL_USE_TLS': current_app.config.get('MAIL_USE_TLS'),
                'MAIL_USERNAME': current_app.config.get('MAIL_USERNAME'),
                'UPLOAD_FOLDER': current_app.config.get('UPLOAD_FOLDER'),
                'MAX_CONTENT_LENGTH': current_app.config.get('MAX_CONTENT_LENGTH'),
            },
            'backup_timestamp': datetime.now().isoformat(),
            'python_version': os.sys.version,
            'flask_version': getattr(__import__('flask'), '__version__', 'unknown')
        }
        
        config_path = backup_path / 'configuration.json'
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        
        return config_path
    
    def _backup_incremental_data(self, backup_path: Path, since: datetime) -> List[Dict]:
        """Backup data that changed since the given date"""
        
        changes = []
        
        # Define models to backup incrementally
        models_to_backup = [
            (User, 'users'),
            (Account, 'accounts'),
            (JournalEntry, 'journal_entries'),
            (JournalLine, 'journal_lines'),
            (Invoice, 'invoices'),
            (InvoiceItem, 'invoice_items'),
            (Receipt, 'receipts'),
            (Customer, 'customers'),
            (Vendor, 'vendors'),
            (Notification, 'notifications')
        ]
        
        for model_class, table_name in models_to_backup:
            try:
                # Get records modified since the given date
                query = model_class.query
                
                if hasattr(model_class, 'updated_at'):
                    query = query.filter(model_class.updated_at >= since)
                elif hasattr(model_class, 'created_at'):
                    query = query.filter(model_class.created_at >= since)
                
                records = query.all()
                
                if records:
                    # Export records to JSON
                    records_data = []
                    for record in records:
                        record_dict = {}
                        for column in model_class.__table__.columns:
                            value = getattr(record, column.name)
                            if isinstance(value, datetime):
                                value = value.isoformat()
                            elif hasattr(value, '__dict__'):
                                value = str(value)
                            record_dict[column.name] = value
                        records_data.append(record_dict)
                    
                    # Save to file
                    table_file = backup_path / f'{table_name}.json'
                    with open(table_file, 'w', encoding='utf-8') as f:
                        json.dump(records_data, f, ensure_ascii=False, indent=2)
                    
                    changes.append({
                        'table': table_name,
                        'records_count': len(records),
                        'file': str(table_file.relative_to(backup_path))
                    })
                    
            except Exception as e:
                logger.error(f"Failed to backup {table_name}: {e}")
        
        return changes
    
    def _compress_backup(self, backup_path: Path) -> Optional[Path]:
        """Compress backup directory"""
        
        try:
            compressed_path = backup_path.with_suffix('.tar.gz')
            shutil.make_archive(
                str(backup_path),
                'gztar',
                str(backup_path.parent),
                str(backup_path.name)
            )
            return compressed_path
            
        except Exception as e:
            logger.error(f"Failed to compress backup: {e}")
            return None
    
    def _cleanup_old_backups(self):
        """Remove old backups beyond the retention limit"""
        
        try:
            # Get all backup files/directories
            backups = []
            for item in self.backup_dir.iterdir():
                if item.name.startswith(('full_backup_', 'incremental_backup_')):
                    backups.append((item.stat().st_mtime, item))
            
            # Sort by modification time (newest first)
            backups.sort(reverse=True)
            
            # Remove old backups
            for i, (mtime, backup_path) in enumerate(backups):
                if i >= self.max_backups:
                    if backup_path.is_dir():
                        shutil.rmtree(backup_path)
                    else:
                        backup_path.unlink()
                    logger.info(f"Removed old backup: {backup_path.name}")
                    
        except Exception as e:
            logger.error(f"Failed to cleanup old backups: {e}")
    
    def _get_last_backup_date(self) -> Optional[datetime]:
        """Get the date of the last backup"""
        
        try:
            backups = []
            for item in self.backup_dir.iterdir():
                if item.name.startswith(('full_backup_', 'incremental_backup_')):
                    backups.append(item.stat().st_mtime)
            
            if backups:
                return datetime.fromtimestamp(max(backups))
            
        except Exception as e:
            logger.error(f"Failed to get last backup date: {e}")
        
        return None
    
    def _get_directory_size(self, path: Path) -> int:
        """Get total size of directory"""
        
        total_size = 0
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                total_size += os.path.getsize(filepath)
        return total_size
    
    def list_backups(self) -> List[Dict]:
        """List all available backups"""
        
        backups = []
        
        for item in self.backup_dir.iterdir():
            if item.name.startswith(('full_backup_', 'incremental_backup_')):
                try:
                    # Try to read metadata
                    metadata_path = None
                    
                    if item.is_dir():
                        metadata_path = item / 'metadata.json'
                    elif item.suffix == '.gz':
                        # Extract metadata from compressed backup
                        import tarfile
                        with tarfile.open(item, 'r:gz') as tar:
                            try:
                                metadata_file = tar.extractfile(f'{item.stem}/metadata.json')
                                if metadata_file:
                                    metadata = json.loads(metadata_file.read().decode('utf-8'))
                                    backups.append({
                                        'path': str(item),
                                        'size': item.stat().st_size,
                                        'compressed': True,
                                        **metadata
                                    })
                                    continue
                            except:
                                pass
                    
                    if metadata_path and metadata_path.exists():
                        with open(metadata_path, 'r', encoding='utf-8') as f:
                            metadata = json.load(f)
                        
                        backups.append({
                            'path': str(item),
                            'size': self._get_directory_size(item) if item.is_dir() else item.stat().st_size,
                            'compressed': False,
                            **metadata
                        })
                    else:
                        # Fallback for backups without metadata
                        backups.append({
                            'backup_name': item.name,
                            'path': str(item),
                            'size': self._get_directory_size(item) if item.is_dir() else item.stat().st_size,
                            'created_at': datetime.fromtimestamp(item.stat().st_mtime).isoformat(),
                            'backup_type': 'full' if 'full_backup_' in item.name else 'incremental',
                            'compressed': item.suffix == '.gz'
                        })
                        
                except Exception as e:
                    logger.error(f"Failed to read backup metadata for {item.name}: {e}")
        
        # Sort by creation date (newest first)
        backups.sort(key=lambda x: x.get('created_at', ''), reverse=True)
        
        return backups
    
    def delete_backup(self, backup_name: str) -> bool:
        """Delete a specific backup"""
        
        try:
            backup_path = self.backup_dir / backup_name
            
            # Also check for compressed version
            if not backup_path.exists():
                backup_path = self.backup_dir / f"{backup_name}.tar.gz"
            
            if backup_path.exists():
                if backup_path.is_dir():
                    shutil.rmtree(backup_path)
                else:
                    backup_path.unlink()
                
                logger.info(f"Deleted backup: {backup_name}")
                return True
            
        except Exception as e:
            logger.error(f"Failed to delete backup {backup_name}: {e}")
        
        return False


class RestoreService:
    """Service for restoring from backups"""

    def __init__(self):
        self.backup_dir = Path(current_app.config.get('BACKUP_DIR', 'backups'))

    def restore_from_backup(self, backup_name: str, restore_options: Dict = None) -> Dict:
        """Restore system from backup"""

        if not restore_options:
            restore_options = {
                'restore_database': True,
                'restore_files': True,
                'restore_configuration': False,
                'overwrite_existing': False
            }

        backup_path = self.backup_dir / backup_name

        # Check for compressed backup
        if not backup_path.exists():
            backup_path = self.backup_dir / f"{backup_name}.tar.gz"
            if backup_path.exists():
                # Extract compressed backup
                extracted_path = self._extract_backup(backup_path)
                if not extracted_path:
                    return {'success': False, 'error': 'Failed to extract backup'}
                backup_path = extracted_path

        if not backup_path.exists():
            return {'success': False, 'error': 'Backup not found'}

        try:
            # Read backup metadata
            metadata_path = backup_path / 'metadata.json'
            if not metadata_path.exists():
                return {'success': False, 'error': 'Invalid backup: metadata not found'}

            with open(metadata_path, 'r', encoding='utf-8') as f:
                metadata = json.load(f)

            restore_results = {
                'success': True,
                'backup_name': backup_name,
                'backup_type': metadata.get('backup_type', 'unknown'),
                'restored_components': []
            }

            # Create restore point before starting
            restore_point = self._create_restore_point()
            restore_results['restore_point'] = restore_point

            # Restore database
            if restore_options.get('restore_database', True):
                db_result = self._restore_database(backup_path, restore_options)
                restore_results['restored_components'].append(db_result)

            # Restore files
            if restore_options.get('restore_files', True):
                files_result = self._restore_files(backup_path, restore_options)
                restore_results['restored_components'].append(files_result)

            # Restore configuration
            if restore_options.get('restore_configuration', False):
                config_result = self._restore_configuration(backup_path, restore_options)
                restore_results['restored_components'].append(config_result)

            # Check if any component failed
            failed_components = [r for r in restore_results['restored_components'] if not r['success']]
            if failed_components:
                restore_results['success'] = False
                restore_results['failed_components'] = failed_components

            logger.info(f"Restore completed for backup: {backup_name}")

            return restore_results

        except Exception as e:
            logger.error(f"Failed to restore from backup {backup_name}: {e}")
            return {'success': False, 'error': str(e)}

    def _extract_backup(self, backup_path: Path) -> Optional[Path]:
        """Extract compressed backup"""

        try:
            import tarfile

            extract_path = backup_path.parent / backup_path.stem

            with tarfile.open(backup_path, 'r:gz') as tar:
                tar.extractall(backup_path.parent)

            return extract_path

        except Exception as e:
            logger.error(f"Failed to extract backup {backup_path}: {e}")
            return None

    def _create_restore_point(self) -> str:
        """Create a restore point before restoration"""

        try:
            backup_service = BackupService()
            result = backup_service.create_full_backup("Pre-restore backup")

            if result['success']:
                return result['backup_name']

        except Exception as e:
            logger.error(f"Failed to create restore point: {e}")

        return None

    def _restore_database(self, backup_path: Path, options: Dict) -> Dict:
        """Restore database from backup"""

        try:
            db_file = backup_path / 'database.db'
            sql_file = backup_path / 'database.sql'

            db_url = current_app.config.get('DATABASE_URL', '')

            if db_url.startswith('sqlite') and db_file.exists():
                # SQLite restore
                current_db = db_url.replace('sqlite:///', '')

                if options.get('overwrite_existing', False):
                    # Backup current database
                    if os.path.exists(current_db):
                        backup_current = f"{current_db}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        shutil.copy2(current_db, backup_current)

                    # Restore from backup
                    shutil.copy2(db_file, current_db)
                else:
                    # Merge data (more complex, requires careful handling)
                    return {'success': False, 'component': 'database', 'error': 'Merge restore not implemented for SQLite'}

                return {'success': True, 'component': 'database', 'message': 'SQLite database restored successfully'}

            elif sql_file.exists():
                # SQL dump restore (PostgreSQL/MySQL)
                if db_url.startswith('postgresql'):
                    return self._restore_postgresql(sql_file, options)
                elif db_url.startswith('mysql'):
                    return self._restore_mysql(sql_file, options)

            return {'success': False, 'component': 'database', 'error': 'No compatible database backup found'}

        except Exception as e:
            logger.error(f"Database restore failed: {e}")
            return {'success': False, 'component': 'database', 'error': str(e)}

    def _restore_postgresql(self, sql_file: Path, options: Dict) -> Dict:
        """Restore PostgreSQL database"""

        try:
            db_url = current_app.config.get('DATABASE_URL', '')
            import urllib.parse
            parsed = urllib.parse.urlparse(db_url)

            if options.get('overwrite_existing', False):
                # Drop and recreate database
                cmd_drop = [
                    'dropdb',
                    '-h', parsed.hostname,
                    '-p', str(parsed.port or 5432),
                    '-U', parsed.username,
                    '--if-exists',
                    parsed.path[1:]
                ]

                cmd_create = [
                    'createdb',
                    '-h', parsed.hostname,
                    '-p', str(parsed.port or 5432),
                    '-U', parsed.username,
                    parsed.path[1:]
                ]

                env = os.environ.copy()
                if parsed.password:
                    env['PGPASSWORD'] = parsed.password

                subprocess.run(cmd_drop, env=env, check=False)  # Don't fail if DB doesn't exist
                subprocess.run(cmd_create, env=env, check=True)

            # Restore from SQL dump
            cmd_restore = [
                'psql',
                '-h', parsed.hostname,
                '-p', str(parsed.port or 5432),
                '-U', parsed.username,
                '-d', parsed.path[1:],
                '-f', str(sql_file)
            ]

            env = os.environ.copy()
            if parsed.password:
                env['PGPASSWORD'] = parsed.password

            subprocess.run(cmd_restore, env=env, check=True)

            return {'success': True, 'component': 'database', 'message': 'PostgreSQL database restored successfully'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'component': 'database', 'error': f'PostgreSQL restore failed: {e}'}

    def _restore_mysql(self, sql_file: Path, options: Dict) -> Dict:
        """Restore MySQL database"""

        try:
            db_url = current_app.config.get('DATABASE_URL', '')
            import urllib.parse
            parsed = urllib.parse.urlparse(db_url)

            cmd = [
                'mysql',
                '-h', parsed.hostname,
                '-P', str(parsed.port or 3306),
                '-u', parsed.username,
                f'-p{parsed.password}' if parsed.password else '',
                parsed.path[1:]
            ]

            with open(sql_file, 'r') as f:
                subprocess.run(cmd, stdin=f, check=True)

            return {'success': True, 'component': 'database', 'message': 'MySQL database restored successfully'}

        except subprocess.CalledProcessError as e:
            return {'success': False, 'component': 'database', 'error': f'MySQL restore failed: {e}'}

    def _restore_files(self, backup_path: Path, options: Dict) -> Dict:
        """Restore files from backup"""

        try:
            files_backup_path = backup_path / 'files'

            if not files_backup_path.exists():
                return {'success': True, 'component': 'files', 'message': 'No files to restore'}

            restored_folders = []

            # Restore uploads
            uploads_backup = files_backup_path / 'uploads'
            if uploads_backup.exists():
                uploads_dir = current_app.config.get('UPLOAD_FOLDER', 'uploads')
                if options.get('overwrite_existing', False):
                    if os.path.exists(uploads_dir):
                        shutil.rmtree(uploads_dir)
                    shutil.copytree(uploads_backup, uploads_dir)
                else:
                    # Merge files
                    if not os.path.exists(uploads_dir):
                        os.makedirs(uploads_dir)
                    for item in uploads_backup.rglob('*'):
                        if item.is_file():
                            rel_path = item.relative_to(uploads_backup)
                            dest_path = Path(uploads_dir) / rel_path
                            dest_path.parent.mkdir(parents=True, exist_ok=True)
                            if not dest_path.exists():
                                shutil.copy2(item, dest_path)

                restored_folders.append('uploads')

            # Note: Static files and templates are usually not restored in production
            # as they are part of the application code

            return {
                'success': True,
                'component': 'files',
                'message': f'Files restored successfully: {", ".join(restored_folders)}'
            }

        except Exception as e:
            logger.error(f"Files restore failed: {e}")
            return {'success': False, 'component': 'files', 'error': str(e)}

    def _restore_configuration(self, backup_path: Path, options: Dict) -> Dict:
        """Restore configuration from backup"""

        try:
            config_file = backup_path / 'configuration.json'

            if not config_file.exists():
                return {'success': True, 'component': 'configuration', 'message': 'No configuration to restore'}

            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # Configuration restore is typically manual or requires restart
            # This is a placeholder for configuration restoration logic

            return {
                'success': True,
                'component': 'configuration',
                'message': 'Configuration backup available (manual restoration required)'
            }

        except Exception as e:
            logger.error(f"Configuration restore failed: {e}")
            return {'success': False, 'component': 'configuration', 'error': str(e)}

    def validate_backup(self, backup_name: str) -> Dict:
        """Validate backup integrity"""

        backup_path = self.backup_dir / backup_name

        # Check for compressed backup
        if not backup_path.exists():
            backup_path = self.backup_dir / f"{backup_name}.tar.gz"
            if backup_path.exists():
                # Extract for validation
                extracted_path = self._extract_backup(backup_path)
                if not extracted_path:
                    return {'valid': False, 'error': 'Failed to extract backup for validation'}
                backup_path = extracted_path

        if not backup_path.exists():
            return {'valid': False, 'error': 'Backup not found'}

        try:
            validation_results = {
                'valid': True,
                'backup_name': backup_name,
                'checks': []
            }

            # Check metadata
            metadata_path = backup_path / 'metadata.json'
            if metadata_path.exists():
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                validation_results['checks'].append({
                    'check': 'metadata',
                    'status': 'passed',
                    'message': 'Metadata file is valid'
                })
            else:
                validation_results['checks'].append({
                    'check': 'metadata',
                    'status': 'failed',
                    'message': 'Metadata file missing'
                })
                validation_results['valid'] = False

            # Check database backup
            db_files = list(backup_path.glob('database.*'))
            if db_files:
                validation_results['checks'].append({
                    'check': 'database',
                    'status': 'passed',
                    'message': f'Database backup found: {db_files[0].name}'
                })
            else:
                validation_results['checks'].append({
                    'check': 'database',
                    'status': 'warning',
                    'message': 'No database backup found'
                })

            # Check files backup
            files_path = backup_path / 'files'
            if files_path.exists():
                validation_results['checks'].append({
                    'check': 'files',
                    'status': 'passed',
                    'message': 'Files backup found'
                })
            else:
                validation_results['checks'].append({
                    'check': 'files',
                    'status': 'warning',
                    'message': 'No files backup found'
                })

            return validation_results

        except Exception as e:
            logger.error(f"Backup validation failed for {backup_name}: {e}")
            return {'valid': False, 'error': str(e)}
