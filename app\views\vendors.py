"""
Vendor views
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required
from app import db
from app.models.vendor import Vendor
from app.forms.vendor import VendorForm, VendorSearchForm
from app.utils.decorators import employee_required
from app.utils.helpers import paginate_query, flash_errors

vendors_bp = Blueprint('vendors', __name__)

@vendors_bp.route('/')
@employee_required
def index():
    """List all vendors"""
    form = VendorSearchForm()
    page = request.args.get('page', 1, type=int)
    
    # Build query
    query = Vendor.query.filter_by(is_active=True)
    
    # Apply search filter
    if request.args.get('search'):
        search_term = request.args.get('search')
        vendors_found = Vendor.search(search_term)
        vendor_ids = [v.id for v in vendors_found]
        query = query.filter(Vendor.id.in_(vendor_ids))
        form.search.data = search_term
    
    # Order by name
    query = query.order_by(Vendor.name)
    
    # Paginate
    vendors = paginate_query(query, page)
    
    return render_template('vendors/index.html', vendors=vendors, form=form)

@vendors_bp.route('/new', methods=['GET', 'POST'])
@employee_required
def new():
    """Create new vendor"""
    form = VendorForm()
    
    if form.validate_on_submit():
        vendor = Vendor(
            name=form.name.data,
            name_en=form.name_en.data,
            tax_id=form.tax_id.data,
            address=form.address.data,
            address_en=form.address_en.data,
            email=form.email.data,
            phone=form.phone.data,
            mobile=form.mobile.data
        )
        vendor.is_active = form.is_active.data
        
        db.session.add(vendor)
        db.session.commit()
        
        flash(f'تم إنشاء المورد {vendor.name} بنجاح.', 'success')
        return redirect(url_for('vendors.detail', vendor_id=vendor.id))
    
    flash_errors(form)
    return render_template('vendors/form.html', form=form, title='إضافة مورد جديد')

@vendors_bp.route('/<vendor_id>')
@employee_required
def detail(vendor_id):
    """View vendor details"""
    vendor = Vendor.query.get_or_404(vendor_id)
    
    return render_template('vendors/detail.html', vendor=vendor)

@vendors_bp.route('/<vendor_id>/edit', methods=['GET', 'POST'])
@employee_required
def edit(vendor_id):
    """Edit vendor"""
    vendor = Vendor.query.get_or_404(vendor_id)
    form = VendorForm()
    
    if form.validate_on_submit():
        vendor.name = form.name.data
        vendor.name_en = form.name_en.data
        vendor.tax_id = form.tax_id.data
        vendor.address = form.address.data
        vendor.address_en = form.address_en.data
        vendor.email = form.email.data
        vendor.phone = form.phone.data
        vendor.mobile = form.mobile.data
        vendor.is_active = form.is_active.data
        
        db.session.commit()
        
        flash(f'تم تحديث بيانات المورد {vendor.name} بنجاح.', 'success')
        return redirect(url_for('vendors.detail', vendor_id=vendor.id))
    
    elif request.method == 'GET':
        form.name.data = vendor.name
        form.name_en.data = vendor.name_en
        form.tax_id.data = vendor.tax_id
        form.address.data = vendor.address
        form.address_en.data = vendor.address_en
        form.email.data = vendor.email
        form.phone.data = vendor.phone
        form.mobile.data = vendor.mobile
        form.is_active.data = vendor.is_active
    
    flash_errors(form)
    return render_template('vendors/form.html', form=form, vendor=vendor, title='تعديل بيانات المورد')

@vendors_bp.route('/<vendor_id>/delete', methods=['POST'])
@employee_required
def delete(vendor_id):
    """Delete vendor"""
    vendor = Vendor.query.get_or_404(vendor_id)
    
    vendor_name = vendor.name
    db.session.delete(vendor)
    db.session.commit()
    
    flash(f'تم حذف المورد {vendor_name} بنجاح.', 'success')
    return redirect(url_for('vendors.index'))

@vendors_bp.route('/api/search')
@employee_required
def api_search():
    """API endpoint for vendor search"""
    term = request.args.get('term', '')
    
    if not term:
        return jsonify([])
    
    vendors = Vendor.search(term)[:20]  # Limit to 20 results
    
    return jsonify([{
        'id': vendor.id,
        'name': vendor.name,
        'tax_id': vendor.tax_id,
        'phone': vendor.phone,
        'email': vendor.email
    } for vendor in vendors])
