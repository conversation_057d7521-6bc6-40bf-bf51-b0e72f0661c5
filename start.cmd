@echo off
echo ========================================
echo        SystemTax - نظام الضرائب
echo ========================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo خطأ: Python غير مثبت على النظام
    echo يرجى تثبيت Python 3.8 أو أحدث
    pause
    exit /b 1
)

:: Activate virtual environment if exists
if exist "venv\Scripts\activate.bat" (
    echo تفعيل البيئة الافتراضية...
    call venv\Scripts\activate.bat
)

:: Install requirements if needed
if not exist "venv\Lib\site-packages\flask" (
    echo تثبيت المتطلبات...
    pip install -r requirements-simple.txt
    if errorlevel 1 (
        echo خطأ في تثبيت المتطلبات، جاري المحاولة مع المتطلبات الكاملة...
        pip install Flask Flask-SQLAlchemy Flask-Login Flask-WTF WTForms bcrypt python-dotenv
    )
)

:: Start the application
echo.
echo بدء تشغيل SystemTax...
echo النظام متاح على: http://localhost:8000
echo المستخدم: admin
echo كلمة المرور: admin123
echo.
echo اضغط Ctrl+C لإيقاف النظام
echo ========================================
echo.

python app.py

:: Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo حدث خطأ في تشغيل النظام
    pause
)
