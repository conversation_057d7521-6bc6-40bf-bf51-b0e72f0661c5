"""
Settings forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, DecimalField, SelectField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Email, Optional

class CompanySettingsForm(FlaskForm):
    company_name = StringField('اسم الشركة', validators=[
        DataRequired(), 
        Length(min=1, max=150)
    ])
    company_name_en = StringField('اسم الشركة بالإنجليزية', validators=[
        Length(max=150)
    ])
    company_tax_id = StringField('الرقم الضريبي', validators=[
        DataRequired(),
        Length(min=1, max=20)
    ])
    company_address = TextAreaField('عنوان الشركة', validators=[
        Length(max=500)
    ])
    company_phone = StringField('هاتف الشركة', validators=[
        Length(max=20)
    ])
    company_email = StringField('بريد الشركة الإلكتروني', validators=[
        Optional(),
        Email(),
        Length(max=100)
    ])
    submit = SubmitField('حفظ إعدادات الشركة')

class InvoiceSettingsForm(FlaskForm):
    invoice_prefix = StringField('بادئة رقم الفاتورة', validators=[
        DataRequired(),
        Length(min=1, max=10)
    ])
    default_tax_rate = DecimalField('معدل الضريبة الافتراضي %', validators=[
        DataRequired(),
        NumberRange(min=0, max=100, message='معدل الضريبة يجب أن يكون بين 0 و 100')
    ], places=2)
    currency_code = StringField('رمز العملة', validators=[
        DataRequired(),
        Length(min=1, max=5)
    ])
    currency_symbol = StringField('رمز العملة', validators=[
        DataRequired(),
        Length(min=1, max=10)
    ])
    submit = SubmitField('حفظ إعدادات الفواتير')

class ReceiptSettingsForm(FlaskForm):
    receipt_prefix = StringField('بادئة رقم الإيصال', validators=[
        DataRequired(),
        Length(min=1, max=10)
    ])
    submit = SubmitField('حفظ إعدادات الإيصالات')

class TaxApiSettingsForm(FlaskForm):
    tax_api_base_url = StringField('رابط API مصلحة الضرائب', validators=[
        DataRequired(),
        Length(min=1, max=200)
    ])
    tax_api_client_id = StringField('معرف العميل', validators=[
        Length(max=100)
    ])
    tax_api_client_secret = StringField('سر العميل', validators=[
        Length(max=100)
    ])
    tax_api_environment = SelectField('البيئة', choices=[
        ('sandbox', 'تجريبية'),
        ('production', 'إنتاج')
    ], default='sandbox')
    submit = SubmitField('حفظ إعدادات API الضرائب')

class SystemSettingForm(FlaskForm):
    """Generic form for individual system settings"""
    key = StringField('المفتاح', validators=[
        DataRequired(),
        Length(min=1, max=100)
    ])
    value = TextAreaField('القيمة', validators=[
        Length(max=1000)
    ])
    description = TextAreaField('الوصف', validators=[
        Length(max=500)
    ])
    submit = SubmitField('حفظ الإعداد')
