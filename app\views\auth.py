"""
Authentication views
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from app import db
from app.models.user import User
from app.forms.auth import LoginForm, RegistrationForm, ChangePasswordForm, UserEditForm
from app.utils.decorators import admin_required

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if current_user.is_authenticated:
        return redirect(url_for('dashboard.index'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        
        if user and user.check_password(form.password.data):
            if not user.is_active:
                flash('حسابك غير نشط. يرجى الاتصال بالمدير.', 'error')
                return render_template('auth/login.html', form=form)
            
            login_user(user, remember=form.remember_me.data)
            next_page = request.args.get('next')
            
            if not next_page or not next_page.startswith('/'):
                next_page = url_for('dashboard.index')
            
            flash(f'مرحباً {user.get_display_name()}!', 'success')
            return redirect(next_page)
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة.', 'error')
    
    return render_template('auth/login.html', form=form)

@auth_bp.route('/logout')
@login_required
def logout():
    """User logout"""
    logout_user()
    flash('تم تسجيل الخروج بنجاح.', 'info')
    return redirect(url_for('auth.login'))

@auth_bp.route('/register', methods=['GET', 'POST'])
@admin_required
def register():
    """Register new user (admin only)"""
    form = RegistrationForm()
    if form.validate_on_submit():
        user = User(
            username=form.username.data,
            email=form.email.data,
            password=form.password.data,
            role=form.role.data
        )
        
        db.session.add(user)
        db.session.commit()
        
        flash(f'تم إنشاء حساب المستخدم {user.username} بنجاح.', 'success')
        return redirect(url_for('auth.users'))
    
    return render_template('auth/register.html', form=form)

@auth_bp.route('/users')
@admin_required
def users():
    """List all users (admin only)"""
    page = request.args.get('page', 1, type=int)
    users = User.query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('auth/users.html', users=users)

@auth_bp.route('/users/<user_id>')
@admin_required
def user_detail(user_id):
    """View user details (admin only)"""
    user = User.query.get_or_404(user_id)
    return render_template('auth/user_detail.html', user=user)

@auth_bp.route('/users/<user_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_user(user_id):
    """Edit user (admin only)"""
    user = User.query.get_or_404(user_id)
    form = UserEditForm(user)
    
    if form.validate_on_submit():
        user.username = form.username.data
        user.email = form.email.data
        user.role = form.role.data
        user.is_active = form.is_active.data
        
        db.session.commit()
        flash(f'تم تحديث بيانات المستخدم {user.username} بنجاح.', 'success')
        return redirect(url_for('auth.user_detail', user_id=user.id))
    
    elif request.method == 'GET':
        form.username.data = user.username
        form.email.data = user.email
        form.role.data = user.role
        form.is_active.data = user.is_active
    
    return render_template('auth/edit_user.html', form=form, user=user)

@auth_bp.route('/users/<user_id>/delete', methods=['POST'])
@admin_required
def delete_user(user_id):
    """Delete user (admin only)"""
    user = User.query.get_or_404(user_id)
    
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص.', 'error')
        return redirect(url_for('auth.users'))
    
    # Check if user has related data
    if user.journal_entries.count() > 0 or user.invoices.count() > 0 or user.receipts.count() > 0:
        flash('لا يمكن حذف هذا المستخدم لأنه مرتبط ببيانات أخرى.', 'error')
        return redirect(url_for('auth.user_detail', user_id=user.id))
    
    username = user.username
    db.session.delete(user)
    db.session.commit()
    
    flash(f'تم حذف المستخدم {username} بنجاح.', 'success')
    return redirect(url_for('auth.users'))

@auth_bp.route('/profile')
@login_required
def profile():
    """View current user profile"""
    return render_template('auth/profile.html', user=current_user)

@auth_bp.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    """Change current user password"""
    form = ChangePasswordForm()
    
    if form.validate_on_submit():
        if not current_user.check_password(form.current_password.data):
            flash('كلمة المرور الحالية غير صحيحة.', 'error')
            return render_template('auth/change_password.html', form=form)
        
        current_user.set_password(form.new_password.data)
        db.session.commit()
        
        flash('تم تغيير كلمة المرور بنجاح.', 'success')
        return redirect(url_for('auth.profile'))
    
    return render_template('auth/change_password.html', form=form)

@auth_bp.route('/users/<user_id>/reset-password', methods=['POST'])
@admin_required
def reset_user_password(user_id):
    """Reset user password (admin only)"""
    user = User.query.get_or_404(user_id)
    
    # Generate temporary password
    import secrets
    import string
    temp_password = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))
    
    user.set_password(temp_password)
    db.session.commit()
    
    flash(f'تم إعادة تعيين كلمة مرور المستخدم {user.username}. كلمة المرور الجديدة: {temp_password}', 'info')
    return redirect(url_for('auth.user_detail', user_id=user.id))
