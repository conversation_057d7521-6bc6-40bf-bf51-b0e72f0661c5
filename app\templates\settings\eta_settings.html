{% extends "base.html" %}

{% block title %}إعدادات مصلحة الضرائب - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1 class="h3 mb-0">إعدادات مصلحة الضرائب المصرية</h1>
                    <p class="text-muted">إدارة إعدادات التكامل مع منظومة الفاتورة والإيصال الإلكتروني</p>
                </div>
                <div>
                    <button type="button" class="btn btn-info" onclick="testConnection()">
                        <i class="fas fa-plug"></i> اختبار الاتصال
                    </button>
                    <a href="{{ url_for('settings.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للإعدادات
                    </a>
                </div>
            </div>

            <form method="POST" action="{{ url_for('settings.save_eta_settings') }}" id="settingsForm">
                <div class="row">
                    <!-- ETA API Settings -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-cog"></i> إعدادات API مصلحة الضرائب
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Base URL -->
                                <div class="mb-3">
                                    <label for="eta_base_url" class="form-label">رابط API *</label>
                                    <select class="form-select" id="eta_base_url" name="eta_base_url" required>
                                        <option value="https://api.preprod.invoicing.eta.gov.eg/api/v1" 
                                                {% for setting in eta_settings if setting.key == 'ETA_BASE_URL' %}
                                                    {{ 'selected' if setting.get_value() == 'https://api.preprod.invoicing.eta.gov.eg/api/v1' else '' }}
                                                {% endfor %}>
                                            بيئة الاختبار (Sandbox)
                                        </option>
                                        <option value="https://api.invoicing.eta.gov.eg/api/v1"
                                                {% for setting in eta_settings if setting.key == 'ETA_BASE_URL' %}
                                                    {{ 'selected' if setting.get_value() == 'https://api.invoicing.eta.gov.eg/api/v1' else '' }}
                                                {% endfor %}>
                                            بيئة الإنتاج (Production)
                                        </option>
                                    </select>
                                    <div class="form-text">اختر بيئة العمل المناسبة</div>
                                </div>

                                <!-- Client ID -->
                                <div class="mb-3">
                                    <label for="eta_client_id" class="form-label">معرف العميل (Client ID) *</label>
                                    <input type="text" class="form-control" id="eta_client_id" name="eta_client_id" 
                                           value="{% for setting in eta_settings if setting.key == 'ETA_CLIENT_ID' %}{{ setting.get_value() }}{% endfor %}"
                                           required>
                                    <div class="form-text">معرف العميل المسجل لدى مصلحة الضرائب</div>
                                </div>

                                <!-- Client Secret -->
                                <div class="mb-3">
                                    <label for="eta_client_secret" class="form-label">كلمة سر العميل (Client Secret) *</label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="eta_client_secret" name="eta_client_secret" 
                                               value="{% for setting in eta_settings if setting.key == 'ETA_CLIENT_SECRET' %}{{ setting.get_value() }}{% endfor %}"
                                               required>
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('eta_client_secret')">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                    <div class="form-text">كلمة سر العميل السرية</div>
                                </div>

                                <!-- Environment -->
                                <div class="mb-3">
                                    <label for="eta_environment" class="form-label">بيئة العمل</label>
                                    <select class="form-select" id="eta_environment" name="eta_environment">
                                        <option value="sandbox" 
                                                {% for setting in eta_settings if setting.key == 'ETA_ENVIRONMENT' %}
                                                    {{ 'selected' if setting.get_value() == 'sandbox' else '' }}
                                                {% endfor %}>
                                            بيئة الاختبار (Sandbox)
                                        </option>
                                        <option value="production"
                                                {% for setting in eta_settings if setting.key == 'ETA_ENVIRONMENT' %}
                                                    {{ 'selected' if setting.get_value() == 'production' else '' }}
                                                {% endfor %}>
                                            بيئة الإنتاج (Production)
                                        </option>
                                    </select>
                                </div>

                                <!-- Timeout -->
                                <div class="mb-3">
                                    <label for="eta_timeout" class="form-label">مهلة الاتصال (ثانية)</label>
                                    <input type="number" class="form-control" id="eta_timeout" name="eta_timeout" 
                                           value="{% for setting in eta_settings if setting.key == 'ETA_TIMEOUT' %}{{ setting.get_value() }}{% endfor %}"
                                           min="10" max="120">
                                    <div class="form-text">مهلة انتظار الاستجابة من مصلحة الضرائب</div>
                                </div>

                                <!-- Device Serial -->
                                <div class="mb-3">
                                    <label for="eta_device_serial" class="form-label">رقم الجهاز المسجل</label>
                                    <input type="text" class="form-control" id="eta_device_serial" name="eta_device_serial" 
                                           value="{% for setting in eta_settings if setting.key == 'ETA_DEVICE_SERIAL' %}{{ setting.get_value() }}{% endfor %}">
                                    <div class="form-text">رقم الجهاز المسجل لدى مصلحة الضرائب</div>
                                </div>

                                <!-- Auto Submit -->
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="eta_auto_submit" name="eta_auto_submit"
                                               {% for setting in eta_settings if setting.key == 'ETA_AUTO_SUBMIT' %}
                                                   {{ 'checked' if setting.get_value() else '' }}
                                               {% endfor %}>
                                        <label class="form-check-label" for="eta_auto_submit">
                                            الإرسال التلقائي للإيصالات
                                        </label>
                                    </div>
                                    <div class="form-text">إرسال الإيصالات تلقائياً عند إنشائها</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Company Information -->
                    <div class="col-lg-6">
                        <div class="card">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-building"></i> معلومات الشركة
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Company Name -->
                                <div class="mb-3">
                                    <label for="company_name" class="form-label">اسم الشركة *</label>
                                    <input type="text" class="form-control" id="company_name" name="company_name" 
                                           value="{% for setting in company_settings if setting.key == 'COMPANY_NAME' %}{{ setting.get_value() }}{% endfor %}"
                                           required>
                                </div>

                                <!-- Tax ID -->
                                <div class="mb-3">
                                    <label for="company_tax_id" class="form-label">الرقم الضريبي *</label>
                                    <input type="text" class="form-control" id="company_tax_id" name="company_tax_id" 
                                           value="{% for setting in company_settings if setting.key == 'COMPANY_TAX_ID' %}{{ setting.get_value() }}{% endfor %}"
                                           required>
                                    <div class="form-text">الرقم الضريبي المسجل لدى مصلحة الضرائب</div>
                                </div>

                                <!-- Activity Code -->
                                <div class="mb-3">
                                    <label for="company_activity_code" class="form-label">كود النشاط *</label>
                                    <input type="text" class="form-control" id="company_activity_code" name="company_activity_code" 
                                           value="{% for setting in company_settings if setting.key == 'COMPANY_ACTIVITY_CODE' %}{{ setting.get_value() }}{% endfor %}"
                                           required>
                                    <div class="form-text">كود النشاط التجاري المسجل</div>
                                </div>

                                <!-- Branch ID -->
                                <div class="mb-3">
                                    <label for="company_branch_id" class="form-label">كود الفرع</label>
                                    <input type="text" class="form-control" id="company_branch_id" name="company_branch_id" 
                                           value="{% for setting in company_settings if setting.key == 'COMPANY_BRANCH_ID' %}{{ setting.get_value() }}{% endfor %}">
                                    <div class="form-text">كود الفرع (0 للمقر الرئيسي)</div>
                                </div>

                                <!-- Address -->
                                <div class="mb-3">
                                    <label for="company_address" class="form-label">عنوان الشركة</label>
                                    <textarea class="form-control" id="company_address" name="company_address" rows="3">{% for setting in company_settings if setting.key == 'COMPANY_ADDRESS' %}{{ setting.get_value() }}{% endfor %}</textarea>
                                </div>

                                <!-- Phone -->
                                <div class="mb-3">
                                    <label for="company_phone" class="form-label">هاتف الشركة</label>
                                    <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                           value="{% for setting in company_settings if setting.key == 'COMPANY_PHONE' %}{{ setting.get_value() }}{% endfor %}">
                                </div>

                                <!-- Email -->
                                <div class="mb-3">
                                    <label for="company_email" class="form-label">بريد الشركة الإلكتروني</label>
                                    <input type="email" class="form-control" id="company_email" name="company_email" 
                                           value="{% for setting in company_settings if setting.key == 'COMPANY_EMAIL' %}{{ setting.get_value() }}{% endfor %}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Connection Test Modal -->
<div class="modal fade" id="connectionTestModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اختبار الاتصال مع مصلحة الضرائب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="connectionTestResult">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري الاختبار...</span>
                        </div>
                        <p class="mt-2">جاري اختبار الاتصال...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        icon.className = 'fas fa-eye';
    }
}

function testConnection() {
    const modal = new bootstrap.Modal(document.getElementById('connectionTestModal'));
    modal.show();
    
    fetch('{{ url_for("settings.test_eta_connection") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        const resultDiv = document.getElementById('connectionTestResult');
        
        if (data.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ${data.message}
                </div>
                <div class="mt-3">
                    <h6>تفاصيل الاتصال:</h6>
                    <ul class="list-unstyled">
                        <li><strong>البيئة:</strong> ${data.data.environment}</li>
                        <li><strong>المصادقة:</strong> ${data.data.authenticated ? 'نجحت' : 'فشلت'}</li>
                        <li><strong>أنواع المستندات:</strong> ${data.data.document_types_count || 0}</li>
                    </ul>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> ${data.message}
                </div>
                <div class="mt-3">
                    <h6>تفاصيل الخطأ:</h6>
                    <ul class="list-unstyled">
                        <li><strong>البيئة:</strong> ${data.data.environment}</li>
                        <li><strong>المصادقة:</strong> ${data.data.authenticated ? 'نجحت' : 'فشلت'}</li>
                        ${data.data.error ? `<li><strong>الخطأ:</strong> ${data.data.error}</li>` : ''}
                    </ul>
                </div>
            `;
        }
    })
    .catch(error => {
        document.getElementById('connectionTestResult').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> حدث خطأ في الاتصال
            </div>
        `;
    });
}

function resetForm() {
    if (confirm('هل تريد إعادة تعيين جميع الإعدادات؟')) {
        document.getElementById('settingsForm').reset();
    }
}

// Form validation
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    const requiredFields = ['eta_base_url', 'eta_client_id', 'eta_client_secret', 'company_name', 'company_tax_id', 'company_activity_code'];
    let isValid = true;
    
    requiredFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
    }
});
</script>
{% endblock %}
