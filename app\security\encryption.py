"""
Encryption and security utilities for SystemTax
"""

import os
import base64
import hashlib
import secrets
import json
from datetime import datetime, timedelta
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from flask import current_app
import logging

logger = logging.getLogger(__name__)


class EncryptionService:
    """Service for encrypting and decrypting sensitive data"""
    
    def __init__(self):
        self.encryption_key = self._get_or_create_key()
        self.fernet = Fernet(self.encryption_key)
    
    def _get_or_create_key(self):
        """Get or create encryption key"""
        
        # Try to get key from environment
        key_env = current_app.config.get('ENCRYPTION_KEY')
        if key_env:
            return key_env.encode()
        
        # Try to get key from file
        key_file = current_app.config.get('ENCRYPTION_KEY_FILE', 'encryption.key')
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        
        # Generate new key
        key = Fernet.generate_key()
        
        # Save key to file
        try:
            with open(key_file, 'wb') as f:
                f.write(key)
            logger.info(f"Generated new encryption key: {key_file}")
        except Exception as e:
            logger.warning(f"Could not save encryption key to file: {e}")
        
        return key
    
    def encrypt(self, data: str) -> str:
        """Encrypt string data"""
        
        if not data:
            return data
        
        try:
            encrypted_data = self.fernet.encrypt(data.encode('utf-8'))
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt string data"""
        
        if not encrypted_data:
            return encrypted_data
        
        try:
            decoded_data = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self.fernet.decrypt(decoded_data)
            return decrypted_data.decode('utf-8')
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise
    
    def encrypt_file(self, file_path: str, output_path: str = None) -> str:
        """Encrypt a file"""
        
        if not output_path:
            output_path = file_path + '.encrypted'
        
        try:
            with open(file_path, 'rb') as infile:
                file_data = infile.read()
            
            encrypted_data = self.fernet.encrypt(file_data)
            
            with open(output_path, 'wb') as outfile:
                outfile.write(encrypted_data)
            
            logger.info(f"File encrypted: {file_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"File encryption failed: {e}")
            raise
    
    def decrypt_file(self, encrypted_file_path: str, output_path: str = None) -> str:
        """Decrypt a file"""
        
        if not output_path:
            output_path = encrypted_file_path.replace('.encrypted', '')
        
        try:
            with open(encrypted_file_path, 'rb') as infile:
                encrypted_data = infile.read()
            
            decrypted_data = self.fernet.decrypt(encrypted_data)
            
            with open(output_path, 'wb') as outfile:
                outfile.write(decrypted_data)
            
            logger.info(f"File decrypted: {encrypted_file_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"File decryption failed: {e}")
            raise


class PasswordSecurity:
    """Password security utilities"""
    
    @staticmethod
    def generate_salt() -> str:
        """Generate a random salt"""
        return secrets.token_hex(32)
    
    @staticmethod
    def hash_password(password: str, salt: str = None) -> tuple:
        """Hash password with salt"""
        
        if not salt:
            salt = PasswordSecurity.generate_salt()
        
        # Use PBKDF2 with SHA-256
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt.encode('utf-8'),
            iterations=100000,
        )
        
        password_hash = base64.urlsafe_b64encode(
            kdf.derive(password.encode('utf-8'))
        ).decode('utf-8')
        
        return password_hash, salt
    
    @staticmethod
    def verify_password(password: str, password_hash: str, salt: str) -> bool:
        """Verify password against hash"""
        
        try:
            computed_hash, _ = PasswordSecurity.hash_password(password, salt)
            return secrets.compare_digest(password_hash, computed_hash)
        except Exception as e:
            logger.error(f"Password verification failed: {e}")
            return False
    
    @staticmethod
    def generate_secure_password(length: int = 16) -> str:
        """Generate a secure random password"""
        
        import string
        
        # Define character sets
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special = "!@#$%^&*"
        
        # Ensure at least one character from each set
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits),
            secrets.choice(special)
        ]
        
        # Fill the rest randomly
        all_chars = lowercase + uppercase + digits + special
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))
        
        # Shuffle the password
        secrets.SystemRandom().shuffle(password)
        
        return ''.join(password)
    
    @staticmethod
    def check_password_strength(password: str) -> dict:
        """Check password strength"""
        
        import re
        
        score = 0
        feedback = []
        
        # Length check
        if len(password) >= 12:
            score += 2
        elif len(password) >= 8:
            score += 1
        else:
            feedback.append("كلمة المرور قصيرة جداً (يجب أن تكون 8 أحرف على الأقل)")
        
        # Character variety checks
        if re.search(r'[a-z]', password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على أحرف صغيرة")
        
        if re.search(r'[A-Z]', password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على أحرف كبيرة")
        
        if re.search(r'\d', password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على أرقام")
        
        if re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            score += 1
        else:
            feedback.append("يجب أن تحتوي على رموز خاصة")
        
        # Common patterns check
        if re.search(r'(.)\1{2,}', password):
            score -= 1
            feedback.append("تجنب تكرار الأحرف")
        
        if re.search(r'(012|123|234|345|456|567|678|789|890)', password):
            score -= 1
            feedback.append("تجنب الأرقام المتتالية")
        
        # Determine strength
        if score >= 6:
            strength = "قوية جداً"
            color = "success"
        elif score >= 4:
            strength = "قوية"
            color = "info"
        elif score >= 2:
            strength = "متوسطة"
            color = "warning"
        else:
            strength = "ضعيفة"
            color = "danger"
        
        return {
            'score': score,
            'strength': strength,
            'color': color,
            'feedback': feedback
        }


class TokenSecurity:
    """Token generation and validation utilities"""
    
    @staticmethod
    def generate_token(length: int = 32) -> str:
        """Generate a secure random token"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def generate_api_key() -> str:
        """Generate an API key"""
        return f"stx_{secrets.token_urlsafe(32)}"
    
    @staticmethod
    def generate_csrf_token() -> str:
        """Generate a CSRF token"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    def hash_token(token: str) -> str:
        """Hash a token for storage"""
        return hashlib.sha256(token.encode('utf-8')).hexdigest()
    
    @staticmethod
    def verify_token(token: str, token_hash: str) -> bool:
        """Verify token against hash"""
        computed_hash = TokenSecurity.hash_token(token)
        return secrets.compare_digest(token_hash, computed_hash)


class DataMasking:
    """Data masking utilities for sensitive information"""
    
    @staticmethod
    def mask_email(email: str) -> str:
        """Mask email address"""
        if not email or '@' not in email:
            return email
        
        local, domain = email.split('@', 1)
        
        if len(local) <= 2:
            masked_local = '*' * len(local)
        else:
            masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        
        return f"{masked_local}@{domain}"
    
    @staticmethod
    def mask_phone(phone: str) -> str:
        """Mask phone number"""
        if not phone:
            return phone
        
        # Remove non-digit characters
        digits = ''.join(filter(str.isdigit, phone))
        
        if len(digits) <= 4:
            return '*' * len(digits)
        
        return digits[:2] + '*' * (len(digits) - 4) + digits[-2:]
    
    @staticmethod
    def mask_credit_card(card_number: str) -> str:
        """Mask credit card number"""
        if not card_number:
            return card_number
        
        # Remove non-digit characters
        digits = ''.join(filter(str.isdigit, card_number))
        
        if len(digits) <= 4:
            return '*' * len(digits)
        
        return '*' * (len(digits) - 4) + digits[-4:]
    
    @staticmethod
    def mask_national_id(national_id: str) -> str:
        """Mask national ID"""
        if not national_id:
            return national_id
        
        if len(national_id) <= 4:
            return '*' * len(national_id)
        
        return national_id[:2] + '*' * (len(national_id) - 4) + national_id[-2:]


class SecurityAudit:
    """Security audit utilities"""
    
    @staticmethod
    def check_file_permissions(file_path: str) -> dict:
        """Check file permissions"""
        
        try:
            stat_info = os.stat(file_path)
            permissions = oct(stat_info.st_mode)[-3:]
            
            # Check for secure permissions
            secure = permissions in ['600', '640', '644', '755']
            
            return {
                'file': file_path,
                'permissions': permissions,
                'secure': secure,
                'owner_readable': bool(stat_info.st_mode & 0o400),
                'owner_writable': bool(stat_info.st_mode & 0o200),
                'owner_executable': bool(stat_info.st_mode & 0o100),
                'group_readable': bool(stat_info.st_mode & 0o040),
                'group_writable': bool(stat_info.st_mode & 0o020),
                'group_executable': bool(stat_info.st_mode & 0o010),
                'other_readable': bool(stat_info.st_mode & 0o004),
                'other_writable': bool(stat_info.st_mode & 0o002),
                'other_executable': bool(stat_info.st_mode & 0o001)
            }
            
        except Exception as e:
            logger.error(f"Failed to check file permissions for {file_path}: {e}")
            return {'file': file_path, 'error': str(e)}
    
    @staticmethod
    def scan_directory_permissions(directory: str) -> list:
        """Scan directory for insecure file permissions"""
        
        issues = []
        
        try:
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_path = os.path.join(root, file)
                    perm_info = SecurityAudit.check_file_permissions(file_path)
                    
                    if not perm_info.get('secure', False):
                        issues.append(perm_info)
                        
        except Exception as e:
            logger.error(f"Failed to scan directory {directory}: {e}")
        
        return issues
    
    @staticmethod
    def check_environment_variables() -> dict:
        """Check for sensitive data in environment variables"""
        
        sensitive_patterns = [
            'password', 'secret', 'key', 'token', 'api',
            'database_url', 'db_pass', 'mail_password'
        ]
        
        issues = []
        secure_vars = []
        
        for var_name, var_value in os.environ.items():
            var_lower = var_name.lower()
            
            # Check if variable name suggests sensitive data
            is_sensitive = any(pattern in var_lower for pattern in sensitive_patterns)
            
            if is_sensitive:
                if var_value and len(var_value) > 0:
                    secure_vars.append({
                        'name': var_name,
                        'masked_value': DataMasking.mask_credit_card(var_value),
                        'length': len(var_value)
                    })
                else:
                    issues.append({
                        'name': var_name,
                        'issue': 'Empty sensitive environment variable'
                    })
        
        return {
            'secure_variables': secure_vars,
            'issues': issues
        }


# Global encryption service instance
encryption_service = None

def get_encryption_service():
    """Get global encryption service instance"""
    global encryption_service
    
    if encryption_service is None:
        encryption_service = EncryptionService()
    
    return encryption_service


# Convenience functions
def encrypt_data(data: str) -> str:
    """Encrypt data using global service"""
    return get_encryption_service().encrypt(data)

def decrypt_data(encrypted_data: str) -> str:
    """Decrypt data using global service"""
    return get_encryption_service().decrypt(encrypted_data)

def hash_password(password: str) -> tuple:
    """Hash password"""
    return PasswordSecurity.hash_password(password)

def verify_password(password: str, password_hash: str, salt: str) -> bool:
    """Verify password"""
    return PasswordSecurity.verify_password(password, password_hash, salt)

def generate_token(length: int = 32) -> str:
    """Generate secure token"""
    return TokenSecurity.generate_token(length)

def mask_sensitive_data(data: str, data_type: str) -> str:
    """Mask sensitive data based on type"""

    if data_type == 'email':
        return DataMasking.mask_email(data)
    elif data_type == 'phone':
        return DataMasking.mask_phone(data)
    elif data_type == 'credit_card':
        return DataMasking.mask_credit_card(data)
    elif data_type == 'national_id':
        return DataMasking.mask_national_id(data)
    else:
        return data


class SecurityMonitor:
    """Security monitoring and intrusion detection"""

    def __init__(self):
        self.failed_attempts = {}
        self.suspicious_activities = []
        self.max_failed_attempts = 5
        self.lockout_duration = 300  # 5 minutes

    def record_failed_login(self, username: str, ip_address: str) -> dict:
        """Record failed login attempt"""

        key = f"{username}:{ip_address}"
        current_time = datetime.now()

        if key not in self.failed_attempts:
            self.failed_attempts[key] = {
                'count': 0,
                'first_attempt': current_time,
                'last_attempt': current_time,
                'locked_until': None
            }

        attempt_info = self.failed_attempts[key]
        attempt_info['count'] += 1
        attempt_info['last_attempt'] = current_time

        # Check if account should be locked
        if attempt_info['count'] >= self.max_failed_attempts:
            attempt_info['locked_until'] = current_time + timedelta(seconds=self.lockout_duration)

            # Log security event
            self.log_security_event(
                'account_lockout',
                f"Account {username} locked due to {attempt_info['count']} failed attempts from {ip_address}",
                {'username': username, 'ip_address': ip_address, 'attempts': attempt_info['count']}
            )

        return {
            'locked': attempt_info['locked_until'] is not None and current_time < attempt_info['locked_until'],
            'attempts': attempt_info['count'],
            'locked_until': attempt_info['locked_until']
        }

    def is_account_locked(self, username: str, ip_address: str) -> bool:
        """Check if account is locked"""

        key = f"{username}:{ip_address}"

        if key not in self.failed_attempts:
            return False

        attempt_info = self.failed_attempts[key]

        if attempt_info['locked_until'] is None:
            return False

        if datetime.now() >= attempt_info['locked_until']:
            # Lockout period expired
            attempt_info['locked_until'] = None
            attempt_info['count'] = 0
            return False

        return True

    def record_successful_login(self, username: str, ip_address: str):
        """Record successful login and reset failed attempts"""

        key = f"{username}:{ip_address}"

        if key in self.failed_attempts:
            del self.failed_attempts[key]

        self.log_security_event(
            'successful_login',
            f"User {username} logged in successfully from {ip_address}",
            {'username': username, 'ip_address': ip_address}
        )

    def detect_suspicious_activity(self, user_id: int, activity: str, details: dict) -> bool:
        """Detect suspicious activity patterns"""

        suspicious = False
        reasons = []

        # Check for unusual login times
        if activity == 'login':
            current_hour = datetime.now().hour
            if current_hour < 6 or current_hour > 22:  # Outside business hours
                suspicious = True
                reasons.append('login_outside_hours')

        # Check for multiple rapid actions
        if activity in ['create_invoice', 'create_receipt', 'delete_record']:
            recent_activities = [
                a for a in self.suspicious_activities
                if a['user_id'] == user_id
                and a['activity'] == activity
                and (datetime.now() - a['timestamp']).seconds < 60
            ]

            if len(recent_activities) > 10:  # More than 10 actions per minute
                suspicious = True
                reasons.append('rapid_actions')

        # Check for privilege escalation attempts
        if activity == 'access_denied':
            recent_denials = [
                a for a in self.suspicious_activities
                if a['user_id'] == user_id
                and a['activity'] == 'access_denied'
                and (datetime.now() - a['timestamp']).seconds < 300
            ]

            if len(recent_denials) > 5:  # More than 5 access denials in 5 minutes
                suspicious = True
                reasons.append('privilege_escalation_attempt')

        if suspicious:
            self.log_security_event(
                'suspicious_activity',
                f"Suspicious activity detected for user {user_id}: {activity}",
                {
                    'user_id': user_id,
                    'activity': activity,
                    'reasons': reasons,
                    'details': details
                }
            )

        # Record activity
        self.suspicious_activities.append({
            'user_id': user_id,
            'activity': activity,
            'timestamp': datetime.now(),
            'suspicious': suspicious,
            'reasons': reasons,
            'details': details
        })

        # Clean old activities (keep only last hour)
        cutoff_time = datetime.now() - timedelta(hours=1)
        self.suspicious_activities = [
            a for a in self.suspicious_activities
            if a['timestamp'] > cutoff_time
        ]

        return suspicious

    def log_security_event(self, event_type: str, message: str, details: dict = None):
        """Log security event"""

        from app.models.security_log import SecurityLog
        from app import db

        try:
            log_entry = SecurityLog(
                event_type=event_type,
                message=message,
                details=json.dumps(details) if details else None,
                ip_address=details.get('ip_address') if details else None,
                user_id=details.get('user_id') if details else None
            )

            db.session.add(log_entry)
            db.session.commit()

            logger.warning(f"Security event: {event_type} - {message}")

        except Exception as e:
            logger.error(f"Failed to log security event: {e}")

    def get_security_summary(self, hours: int = 24) -> dict:
        """Get security summary for the last N hours"""

        from app.models.security_log import SecurityLog
        from datetime import datetime, timedelta

        cutoff_time = datetime.now() - timedelta(hours=hours)

        try:
            # Get security events from database
            events = SecurityLog.query.filter(
                SecurityLog.created_at >= cutoff_time
            ).all()

            # Categorize events
            event_counts = {}
            recent_events = []

            for event in events:
                event_type = event.event_type
                event_counts[event_type] = event_counts.get(event_type, 0) + 1

                recent_events.append({
                    'type': event_type,
                    'message': event.message,
                    'timestamp': event.created_at,
                    'ip_address': event.ip_address,
                    'user_id': event.user_id
                })

            # Sort recent events by timestamp
            recent_events.sort(key=lambda x: x['timestamp'], reverse=True)

            return {
                'period_hours': hours,
                'total_events': len(events),
                'event_counts': event_counts,
                'recent_events': recent_events[:20],  # Last 20 events
                'failed_logins': event_counts.get('failed_login', 0),
                'successful_logins': event_counts.get('successful_login', 0),
                'account_lockouts': event_counts.get('account_lockout', 0),
                'suspicious_activities': event_counts.get('suspicious_activity', 0)
            }

        except Exception as e:
            logger.error(f"Failed to get security summary: {e}")
            return {
                'error': str(e),
                'period_hours': hours,
                'total_events': 0
            }


# Global security monitor instance
security_monitor = None

def get_security_monitor():
    """Get global security monitor instance"""
    global security_monitor

    if security_monitor is None:
        security_monitor = SecurityMonitor()

    return security_monitor
