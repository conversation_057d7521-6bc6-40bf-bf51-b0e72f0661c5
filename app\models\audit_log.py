"""
Audit logging models for tracking all system changes
"""

from app import db
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean, Index, JSON
from sqlalchemy.orm import relationship
import json


class AuditLog(db.Model):
    """Audit log for tracking all system changes"""
    
    __tablename__ = 'audit_logs'
    
    id = Column(Integer, primary_key=True)
    
    # Action information
    action = Column(String(50), nullable=False, comment='نوع العملية')
    table_name = Column(String(100), nullable=False, comment='اسم الجدول')
    record_id = Column(Integer, nullable=True, comment='معرف السجل')
    
    # Change tracking
    old_values = Column(Text, nullable=True, comment='القيم القديمة (JSON)')
    new_values = Column(Text, nullable=True, comment='القيم الجديدة (JSON)')
    changed_fields = Column(Text, nullable=True, comment='الحقول المتغيرة')
    
    # Context information
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='المستخدم')
    username = Column(String(80), nullable=True, comment='اسم المستخدم')
    ip_address = Column(String(45), nullable=True, comment='عنوان IP')
    user_agent = Column(Text, nullable=True, comment='معلومات المتصفح')
    
    # Request information
    request_path = Column(String(500), nullable=True, comment='مسار الطلب')
    request_method = Column(String(10), nullable=True, comment='طريقة الطلب')
    session_id = Column(String(100), nullable=True, comment='معرف الجلسة')
    
    # Additional metadata
    description = Column(Text, nullable=True, comment='وصف العملية')
    tags = Column(String(500), nullable=True, comment='علامات للتصنيف')
    is_sensitive = Column(Boolean, default=False, comment='عملية حساسة')
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, comment='تاريخ العملية')
    
    # Relationships
    user = relationship('User', backref='audit_logs')
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_audit_logs_action', 'action'),
        Index('idx_audit_logs_table_name', 'table_name'),
        Index('idx_audit_logs_record_id', 'record_id'),
        Index('idx_audit_logs_user_id', 'user_id'),
        Index('idx_audit_logs_created_at', 'created_at'),
        Index('idx_audit_logs_table_record', 'table_name', 'record_id'),
        Index('idx_audit_logs_sensitive', 'is_sensitive'),
    )
    
    def __repr__(self):
        return f'<AuditLog {self.action} on {self.table_name}:{self.record_id}>'
    
    @property
    def old_values_dict(self):
        """Get old values as dictionary"""
        if self.old_values:
            try:
                return json.loads(self.old_values)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    @property
    def new_values_dict(self):
        """Get new values as dictionary"""
        if self.new_values:
            try:
                return json.loads(self.new_values)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    @property
    def changed_fields_list(self):
        """Get changed fields as list"""
        if self.changed_fields:
            try:
                return json.loads(self.changed_fields)
            except (json.JSONDecodeError, TypeError):
                return []
        return []
    
    def get_field_changes(self):
        """Get detailed field changes"""
        old_vals = self.old_values_dict
        new_vals = self.new_values_dict
        changes = []
        
        for field in self.changed_fields_list:
            old_val = old_vals.get(field)
            new_val = new_vals.get(field)
            
            changes.append({
                'field': field,
                'old_value': old_val,
                'new_value': new_val,
                'changed': old_val != new_val
            })
        
        return changes
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'action': self.action,
            'table_name': self.table_name,
            'record_id': self.record_id,
            'old_values': self.old_values_dict,
            'new_values': self.new_values_dict,
            'changed_fields': self.changed_fields_list,
            'user_id': self.user_id,
            'username': self.username,
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'request_path': self.request_path,
            'request_method': self.request_method,
            'session_id': self.session_id,
            'description': self.description,
            'tags': self.tags,
            'is_sensitive': self.is_sensitive,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'field_changes': self.get_field_changes()
        }
    
    @classmethod
    def log_action(cls, action, table_name, record_id=None, old_values=None, 
                   new_values=None, changed_fields=None, user_id=None, 
                   username=None, ip_address=None, user_agent=None,
                   request_path=None, request_method=None, session_id=None,
                   description=None, tags=None, is_sensitive=False):
        """Create a new audit log entry"""
        
        log_entry = cls(
            action=action,
            table_name=table_name,
            record_id=record_id,
            old_values=json.dumps(old_values) if old_values else None,
            new_values=json.dumps(new_values) if new_values else None,
            changed_fields=json.dumps(changed_fields) if changed_fields else None,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            request_path=request_path,
            request_method=request_method,
            session_id=session_id,
            description=description,
            tags=tags,
            is_sensitive=is_sensitive
        )
        
        db.session.add(log_entry)
        
        try:
            db.session.commit()
            return log_entry
        except Exception as e:
            db.session.rollback()
            # Log to application logger as fallback
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to create audit log: {e}")
            return None
    
    @classmethod
    def get_record_history(cls, table_name, record_id, limit=50):
        """Get audit history for a specific record"""
        
        return cls.query.filter(
            cls.table_name == table_name,
            cls.record_id == record_id
        ).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_user_activity(cls, user_id, hours=24, limit=100):
        """Get audit logs for a specific user"""
        
        from datetime import datetime, timedelta
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        return cls.query.filter(
            cls.user_id == user_id,
            cls.created_at >= cutoff_time
        ).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_table_activity(cls, table_name, hours=24, limit=100):
        """Get audit logs for a specific table"""
        
        from datetime import datetime, timedelta
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        return cls.query.filter(
            cls.table_name == table_name,
            cls.created_at >= cutoff_time
        ).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_sensitive_operations(cls, hours=24, limit=100):
        """Get sensitive operations"""
        
        from datetime import datetime, timedelta
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        return cls.query.filter(
            cls.is_sensitive == True,
            cls.created_at >= cutoff_time
        ).order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def search_logs(cls, search_term, table_name=None, action=None, 
                    user_id=None, date_from=None, date_to=None, limit=100):
        """Search audit logs"""
        
        query = cls.query
        
        # Text search in description and values
        if search_term:
            search_filter = db.or_(
                cls.description.ilike(f'%{search_term}%'),
                cls.old_values.ilike(f'%{search_term}%'),
                cls.new_values.ilike(f'%{search_term}%')
            )
            query = query.filter(search_filter)
        
        # Filter by table
        if table_name:
            query = query.filter(cls.table_name == table_name)
        
        # Filter by action
        if action:
            query = query.filter(cls.action == action)
        
        # Filter by user
        if user_id:
            query = query.filter(cls.user_id == user_id)
        
        # Date range filter
        if date_from:
            query = query.filter(cls.created_at >= date_from)
        
        if date_to:
            query = query.filter(cls.created_at <= date_to)
        
        return query.order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_statistics(cls, hours=24):
        """Get audit statistics"""
        
        from datetime import datetime, timedelta
        from sqlalchemy import func
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        # Total operations
        total_operations = cls.query.filter(cls.created_at >= cutoff_time).count()
        
        # Operations by action
        action_stats = db.session.query(
            cls.action,
            func.count(cls.id).label('count')
        ).filter(
            cls.created_at >= cutoff_time
        ).group_by(cls.action).all()
        
        # Operations by table
        table_stats = db.session.query(
            cls.table_name,
            func.count(cls.id).label('count')
        ).filter(
            cls.created_at >= cutoff_time
        ).group_by(cls.table_name).all()
        
        # Operations by user
        user_stats = db.session.query(
            cls.username,
            func.count(cls.id).label('count')
        ).filter(
            cls.created_at >= cutoff_time,
            cls.username.isnot(None)
        ).group_by(cls.username).all()
        
        # Sensitive operations
        sensitive_count = cls.query.filter(
            cls.created_at >= cutoff_time,
            cls.is_sensitive == True
        ).count()
        
        return {
            'total_operations': total_operations,
            'sensitive_operations': sensitive_count,
            'actions': {stat.action: stat.count for stat in action_stats},
            'tables': {stat.table_name: stat.count for stat in table_stats},
            'users': {stat.username: stat.count for stat in user_stats}
        }
    
    @classmethod
    def cleanup_old_logs(cls, days=365):
        """Clean up old audit logs"""
        
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Keep sensitive operations longer
        sensitive_cutoff = datetime.utcnow() - timedelta(days=days * 2)
        
        # Delete non-sensitive old logs
        deleted_count = cls.query.filter(
            cls.created_at < cutoff_date,
            cls.is_sensitive == False
        ).delete()
        
        # Delete very old sensitive logs
        deleted_sensitive = cls.query.filter(
            cls.created_at < sensitive_cutoff,
            cls.is_sensitive == True
        ).delete()
        
        db.session.commit()
        
        return deleted_count + deleted_sensitive


class DataChangeLog(db.Model):
    """Detailed data change tracking for specific fields"""
    
    __tablename__ = 'data_change_logs'
    
    id = Column(Integer, primary_key=True)
    
    # Reference to audit log
    audit_log_id = Column(Integer, ForeignKey('audit_logs.id'), nullable=False, comment='مرجع سجل التدقيق')
    
    # Field information
    field_name = Column(String(100), nullable=False, comment='اسم الحقل')
    field_type = Column(String(50), nullable=True, comment='نوع الحقل')
    
    # Values
    old_value = Column(Text, nullable=True, comment='القيمة القديمة')
    new_value = Column(Text, nullable=True, comment='القيمة الجديدة')
    
    # Change metadata
    change_type = Column(String(20), nullable=False, comment='نوع التغيير')  # created, updated, deleted
    is_sensitive = Column(Boolean, default=False, comment='حقل حساس')
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, comment='تاريخ التغيير')
    
    # Relationships
    audit_log = relationship('AuditLog', backref='field_changes')
    
    # Indexes
    __table_args__ = (
        Index('idx_data_change_logs_audit_log_id', 'audit_log_id'),
        Index('idx_data_change_logs_field_name', 'field_name'),
        Index('idx_data_change_logs_change_type', 'change_type'),
        Index('idx_data_change_logs_sensitive', 'is_sensitive'),
    )
    
    def __repr__(self):
        return f'<DataChangeLog {self.field_name}: {self.old_value} -> {self.new_value}>'
    
    @property
    def has_changed(self):
        """Check if value actually changed"""
        return self.old_value != self.new_value
    
    @property
    def is_addition(self):
        """Check if this is a new field addition"""
        return self.change_type == 'created' or (self.old_value is None and self.new_value is not None)
    
    @property
    def is_deletion(self):
        """Check if this is a field deletion"""
        return self.change_type == 'deleted' or (self.old_value is not None and self.new_value is None)
    
    @property
    def is_modification(self):
        """Check if this is a field modification"""
        return self.change_type == 'updated' and self.has_changed
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'audit_log_id': self.audit_log_id,
            'field_name': self.field_name,
            'field_type': self.field_type,
            'old_value': self.old_value,
            'new_value': self.new_value,
            'change_type': self.change_type,
            'is_sensitive': self.is_sensitive,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'has_changed': self.has_changed,
            'is_addition': self.is_addition,
            'is_deletion': self.is_deletion,
            'is_modification': self.is_modification
        }


class AuditService:
    """Service for managing audit logging"""

    @staticmethod
    def log_create(table_name, record_id, new_values, user_id=None,
                   username=None, description=None, is_sensitive=False, **context):
        """Log record creation"""

        return AuditLog.log_action(
            action='CREATE',
            table_name=table_name,
            record_id=record_id,
            new_values=new_values,
            user_id=user_id,
            username=username,
            description=description or f'تم إنشاء سجل جديد في {table_name}',
            is_sensitive=is_sensitive,
            **context
        )

    @staticmethod
    def log_update(table_name, record_id, old_values, new_values,
                   user_id=None, username=None, description=None,
                   is_sensitive=False, **context):
        """Log record update"""

        # Determine changed fields
        changed_fields = []
        if old_values and new_values:
            for key in new_values:
                if key in old_values and old_values[key] != new_values[key]:
                    changed_fields.append(key)
                elif key not in old_values:
                    changed_fields.append(key)

        audit_log = AuditLog.log_action(
            action='UPDATE',
            table_name=table_name,
            record_id=record_id,
            old_values=old_values,
            new_values=new_values,
            changed_fields=changed_fields,
            user_id=user_id,
            username=username,
            description=description or f'تم تحديث سجل في {table_name}',
            is_sensitive=is_sensitive,
            **context
        )

        # Create detailed field change logs
        if audit_log and changed_fields:
            AuditService._create_field_change_logs(
                audit_log.id, changed_fields, old_values, new_values
            )

        return audit_log

    @staticmethod
    def log_delete(table_name, record_id, old_values, user_id=None,
                   username=None, description=None, is_sensitive=False, **context):
        """Log record deletion"""

        return AuditLog.log_action(
            action='DELETE',
            table_name=table_name,
            record_id=record_id,
            old_values=old_values,
            user_id=user_id,
            username=username,
            description=description or f'تم حذف سجل من {table_name}',
            is_sensitive=is_sensitive,
            **context
        )

    @staticmethod
    def log_view(table_name, record_id=None, user_id=None, username=None,
                 description=None, **context):
        """Log record view/access"""

        return AuditLog.log_action(
            action='VIEW',
            table_name=table_name,
            record_id=record_id,
            user_id=user_id,
            username=username,
            description=description or f'تم عرض سجل من {table_name}',
            is_sensitive=False,
            **context
        )

    @staticmethod
    def log_export(table_name, record_count, export_format, user_id=None,
                   username=None, description=None, **context):
        """Log data export"""

        return AuditLog.log_action(
            action='EXPORT',
            table_name=table_name,
            new_values={'record_count': record_count, 'format': export_format},
            user_id=user_id,
            username=username,
            description=description or f'تم تصدير {record_count} سجل من {table_name}',
            is_sensitive=True,
            **context
        )

    @staticmethod
    def log_import(table_name, record_count, import_format, user_id=None,
                   username=None, description=None, **context):
        """Log data import"""

        return AuditLog.log_action(
            action='IMPORT',
            table_name=table_name,
            new_values={'record_count': record_count, 'format': import_format},
            user_id=user_id,
            username=username,
            description=description or f'تم استيراد {record_count} سجل إلى {table_name}',
            is_sensitive=True,
            **context
        )

    @staticmethod
    def log_login(user_id, username, ip_address, user_agent, success=True, **context):
        """Log user login attempt"""

        action = 'LOGIN_SUCCESS' if success else 'LOGIN_FAILED'
        description = f'تسجيل دخول {"ناجح" if success else "فاشل"} للمستخدم {username}'

        return AuditLog.log_action(
            action=action,
            table_name='users',
            record_id=user_id,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            user_agent=user_agent,
            description=description,
            is_sensitive=not success,
            **context
        )

    @staticmethod
    def log_logout(user_id, username, ip_address, **context):
        """Log user logout"""

        return AuditLog.log_action(
            action='LOGOUT',
            table_name='users',
            record_id=user_id,
            user_id=user_id,
            username=username,
            ip_address=ip_address,
            description=f'تسجيل خروج للمستخدم {username}',
            is_sensitive=False,
            **context
        )

    @staticmethod
    def log_permission_change(target_user_id, old_permissions, new_permissions,
                             user_id=None, username=None, **context):
        """Log permission changes"""

        return AuditLog.log_action(
            action='PERMISSION_CHANGE',
            table_name='users',
            record_id=target_user_id,
            old_values={'permissions': old_permissions},
            new_values={'permissions': new_permissions},
            user_id=user_id,
            username=username,
            description=f'تم تغيير صلاحيات المستخدم {target_user_id}',
            is_sensitive=True,
            **context
        )

    @staticmethod
    def _create_field_change_logs(audit_log_id, changed_fields, old_values, new_values):
        """Create detailed field change logs"""

        sensitive_fields = [
            'password', 'password_hash', 'salt', 'email', 'phone',
            'national_id', 'tax_number', 'bank_account', 'salary'
        ]

        for field in changed_fields:
            old_val = old_values.get(field) if old_values else None
            new_val = new_values.get(field) if new_values else None

            # Determine change type
            if old_val is None and new_val is not None:
                change_type = 'created'
            elif old_val is not None and new_val is None:
                change_type = 'deleted'
            else:
                change_type = 'updated'

            # Check if field is sensitive
            is_sensitive = any(sensitive_field in field.lower() for sensitive_field in sensitive_fields)

            # Mask sensitive values
            if is_sensitive:
                from app.security.encryption import mask_sensitive_data
                if old_val:
                    old_val = mask_sensitive_data(str(old_val), 'credit_card')
                if new_val:
                    new_val = mask_sensitive_data(str(new_val), 'credit_card')

            change_log = DataChangeLog(
                audit_log_id=audit_log_id,
                field_name=field,
                old_value=str(old_val) if old_val is not None else None,
                new_value=str(new_val) if new_val is not None else None,
                change_type=change_type,
                is_sensitive=is_sensitive
            )

            db.session.add(change_log)

        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to create field change logs: {e}")

    @staticmethod
    def get_audit_trail(table_name, record_id, include_field_changes=True):
        """Get complete audit trail for a record"""

        audit_logs = AuditLog.get_record_history(table_name, record_id)

        if include_field_changes:
            for log in audit_logs:
                log.detailed_changes = DataChangeLog.query.filter_by(
                    audit_log_id=log.id
                ).all()

        return audit_logs

    @staticmethod
    def generate_audit_report(date_from, date_to, table_name=None,
                             user_id=None, action=None, include_sensitive=False):
        """Generate comprehensive audit report"""

        # Get audit logs for the period
        logs = AuditLog.search_logs(
            search_term=None,
            table_name=table_name,
            action=action,
            user_id=user_id,
            date_from=date_from,
            date_to=date_to,
            limit=10000
        )

        # Filter sensitive operations if not included
        if not include_sensitive:
            logs = [log for log in logs if not log.is_sensitive]

        # Generate statistics
        stats = AuditLog.get_statistics(
            hours=int((date_to - date_from).total_seconds() / 3600)
        )

        # Group by categories
        report = {
            'period': {
                'from': date_from.isoformat(),
                'to': date_to.isoformat()
            },
            'statistics': stats,
            'logs': [log.to_dict() for log in logs],
            'summary': {
                'total_operations': len(logs),
                'unique_users': len(set(log.user_id for log in logs if log.user_id)),
                'unique_tables': len(set(log.table_name for log in logs)),
                'sensitive_operations': len([log for log in logs if log.is_sensitive])
            }
        }

        return report
