"""
User model for authentication and authorization
"""

import uuid
from datetime import datetime
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from app import db

class User(UserMixin, db.Model):
    __tablename__ = 'users'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    username = db.Column(db.String(50), unique=True, nullable=False, index=True)
    email = db.Column(db.String(100), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='employee')
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    journal_entries = db.relationship('JournalEntry', foreign_keys='JournalEntry.created_by', backref='creator', lazy='dynamic')
    invoices = db.relationship('Invoice', backref='creator', lazy='dynamic')
    receipts = db.relationship('Receipt', backref='creator', lazy='dynamic')
    
    def __init__(self, username, email, password, role='employee'):
        self.username = username
        self.email = email
        self.set_password(password)
        self.role = role
    
    def set_password(self, password):
        """Hash and set password"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """Check if provided password matches hash"""
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        """Check if user is admin"""
        return self.role == 'admin'
    
    def is_accountant(self):
        """Check if user is accountant"""
        return self.role in ['admin', 'accountant']
    
    def can_access(self, resource):
        """Check if user can access specific resource"""
        permissions = {
            'admin': ['all'],
            'accountant': ['accounts', 'journal', 'invoices', 'receipts', 'reports', 'customers', 'vendors'],
            'employee': ['invoices', 'receipts', 'customers']
        }
        
        user_permissions = permissions.get(self.role, [])
        return 'all' in user_permissions or resource in user_permissions
    
    def get_display_name(self):
        """Get display name for user"""
        return self.username
    
    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'role': self.role,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @staticmethod
    def create_admin_user(username='admin', email='<EMAIL>', password='admin123'):
        """Create default admin user"""
        existing_admin = User.query.filter_by(username=username).first()
        if existing_admin:
            return existing_admin
        
        admin_user = User(
            username=username,
            email=email,
            password=password,
            role='admin'
        )
        
        db.session.add(admin_user)
        db.session.commit()
        return admin_user
    
    def __repr__(self):
        return f'<User {self.username}>'
