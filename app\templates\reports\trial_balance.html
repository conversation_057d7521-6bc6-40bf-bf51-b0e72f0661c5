{% extends "base.html" %}

{% block title %}ميزان المراجعة - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-balance-scale me-3"></i>
                ميزان المراجعة
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('reports.index') }}">التقارير</a></li>
                    <li class="breadcrumb-item active">ميزان المراجعة</li>
                </ol>
            </nav>
        </div>
        <div>
            <div class="btn-group">
                <button type="button" class="btn btn-info" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
                <button type="button" class="btn btn-danger" onclick="window.print()">
                    <i class="fas fa-file-pdf me-2"></i>
                    PDF
                </button>
                <button type="button" class="btn btn-success" onclick="alert('سيتم تنفيذ تصدير Excel قريباً')">
                    <i class="fas fa-file-excel me-2"></i>
                    Excel
                </button>
            </div>
            <a href="{{ url_for('reports.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للتقارير
            </a>
        </div>
    </div>
</div>

<!-- Report Parameters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>
            معايير التقرير
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                {{ form.date_from.label(class="form-label") }}
                {{ form.date_from(class="form-control") }}
            </div>
            <div class="col-md-3">
                {{ form.date_to.label(class="form-label") }}
                {{ form.date_to(class="form-control") }}
            </div>
            <div class="col-md-3">
                {{ form.account_type.label(class="form-label") }}
                {{ form.account_type(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ form.show_zero_balances.label(class="form-label") }}
                <div class="form-check">
                    {{ form.show_zero_balances(class="form-check-input") }}
                    {{ form.show_zero_balances.label(class="form-check-label") }}
                </div>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Report Header -->
<div class="card mb-4" id="report-content">
    <div class="card-header text-center bg-light">
        <h3 class="mb-1">{{ company_name or 'اسم الشركة' }}</h3>
        <h4 class="mb-1">ميزان المراجعة</h4>
        <h5 class="text-muted">
            من {{ date_from|date }} إلى {{ date_to|date }}
        </h5>
    </div>
    
    <div class="card-body">
        {% if trial_balance_data %}
        <div class="table-responsive">
            <table class="table table-bordered table-hover">
                <thead class="table-dark">
                    <tr>
                        <th rowspan="2" class="text-center align-middle">رمز الحساب</th>
                        <th rowspan="2" class="text-center align-middle">اسم الحساب</th>
                        <th colspan="2" class="text-center">الرصيد الافتتاحي</th>
                        <th colspan="2" class="text-center">الحركة خلال الفترة</th>
                        <th colspan="2" class="text-center">الرصيد الختامي</th>
                    </tr>
                    <tr>
                        <th class="text-center">مدين</th>
                        <th class="text-center">دائن</th>
                        <th class="text-center">مدين</th>
                        <th class="text-center">دائن</th>
                        <th class="text-center">مدين</th>
                        <th class="text-center">دائن</th>
                    </tr>
                </thead>
                <tbody>
                    {% for account_type, accounts in trial_balance_data.items() %}
                    <!-- Account Type Header -->
                    <tr class="table-secondary">
                        <td colspan="8" class="fw-bold">
                            <i class="fas fa-{{ account_type_icons[account_type] }} me-2"></i>
                            {{ account_type_names[account_type] }}
                        </td>
                    </tr>
                    
                    {% for account in accounts %}
                    <tr>
                        <td class="text-center">{{ account.code }}</td>
                        <td>
                            {% if account.level > 1 %}
                                {{ '&nbsp;' * (account.level - 1) * 4 }}
                            {% endif %}
                            {{ account.name }}
                        </td>
                        <td class="text-end">
                            {% if account.opening_balance_debit %}
                                {{ account.opening_balance_debit|currency }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end">
                            {% if account.opening_balance_credit %}
                                {{ account.opening_balance_credit|currency }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end">
                            {% if account.period_debit %}
                                {{ account.period_debit|currency }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end">
                            {% if account.period_credit %}
                                {{ account.period_credit|currency }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end">
                            {% if account.closing_balance_debit %}
                                {{ account.closing_balance_debit|currency }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td class="text-end">
                            {% if account.closing_balance_credit %}
                                {{ account.closing_balance_credit|currency }}
                            {% else %}
                                -
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                    
                    <!-- Account Type Totals -->
                    <tr class="table-light fw-bold">
                        <td colspan="2" class="text-end">إجمالي {{ account_type_names[account_type] }}</td>
                        <td class="text-end">{{ totals[account_type].opening_debit|currency }}</td>
                        <td class="text-end">{{ totals[account_type].opening_credit|currency }}</td>
                        <td class="text-end">{{ totals[account_type].period_debit|currency }}</td>
                        <td class="text-end">{{ totals[account_type].period_credit|currency }}</td>
                        <td class="text-end">{{ totals[account_type].closing_debit|currency }}</td>
                        <td class="text-end">{{ totals[account_type].closing_credit|currency }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
                
                <!-- Grand Totals -->
                <tfoot class="table-dark">
                    <tr class="fw-bold">
                        <td colspan="2" class="text-center">الإجمالي العام</td>
                        <td class="text-end">{{ grand_totals.opening_debit|currency }}</td>
                        <td class="text-end">{{ grand_totals.opening_credit|currency }}</td>
                        <td class="text-end">{{ grand_totals.period_debit|currency }}</td>
                        <td class="text-end">{{ grand_totals.period_credit|currency }}</td>
                        <td class="text-end">{{ grand_totals.closing_debit|currency }}</td>
                        <td class="text-end">{{ grand_totals.closing_credit|currency }}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        <!-- Balance Verification -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            التحقق من التوازن
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm mb-0">
                            <tr>
                                <td><strong>إجمالي المدين:</strong></td>
                                <td class="text-end">{{ grand_totals.closing_debit|currency }}</td>
                            </tr>
                            <tr>
                                <td><strong>إجمالي الدائن:</strong></td>
                                <td class="text-end">{{ grand_totals.closing_credit|currency }}</td>
                            </tr>
                            <tr class="table-light">
                                <td><strong>الفرق:</strong></td>
                                <td class="text-end">
                                    {% set difference = grand_totals.closing_debit - grand_totals.closing_credit %}
                                    <span class="text-{{ 'success' if difference == 0 else 'danger' }}">
                                        {{ difference|currency }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                        
                        {% if difference == 0 %}
                        <div class="alert alert-success mt-2 mb-0">
                            <i class="fas fa-check me-2"></i>
                            الميزان متوازن
                        </div>
                        {% else %}
                        <div class="alert alert-danger mt-2 mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الميزان غير متوازن - يرجى مراجعة القيود
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات التقرير
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm mb-0">
                            <tr>
                                <td><strong>عدد الحسابات:</strong></td>
                                <td class="text-end">{{ report_info.total_accounts }}</td>
                            </tr>
                            <tr>
                                <td><strong>الحسابات النشطة:</strong></td>
                                <td class="text-end">{{ report_info.active_accounts }}</td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ التقرير:</strong></td>
                                <td class="text-end">{{ report_info.generated_at|datetime }}</td>
                            </tr>
                            <tr>
                                <td><strong>أنشأ بواسطة:</strong></td>
                                <td class="text-end">{{ current_user.username }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
            <h5>لا توجد بيانات</h5>
            <p class="text-muted">لا توجد حسابات أو قيود في الفترة المحددة.</p>
            <a href="{{ url_for('journal.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة قيد جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Report Actions -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-cogs me-2"></i>
            إجراءات التقرير
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>تصدير التقرير:</h6>
                <div class="btn-group w-100">
                    <button type="button" class="btn btn-outline-danger" onclick="window.print()">
                        <i class="fas fa-file-pdf me-2"></i>
                        PDF
                    </button>
                    <button type="button" class="btn btn-outline-success" onclick="alert('سيتم تنفيذ تصدير Excel قريباً')">
                        <i class="fas fa-file-excel me-2"></i>
                        Excel
                    </button>
                    <a href="{{ url_for('reports.trial_balance_csv') }}?{{ request.query_string.decode() }}" 
                       class="btn btn-outline-info">
                        <i class="fas fa-file-csv me-2"></i>
                        CSV
                    </a>
                </div>
            </div>
            
            <div class="col-md-6">
                <h6>إجراءات أخرى:</h6>
                <div class="btn-group w-100">
                    <button type="button" class="btn btn-outline-primary" onclick="emailReport()">
                        <i class="fas fa-envelope me-2"></i>
                        إرسال بريد
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="saveTemplate()">
                        <i class="fas fa-save me-2"></i>
                        حفظ كقالب
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="scheduleReport()">
                        <i class="fas fa-clock me-2"></i>
                        جدولة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function emailReport() {
    const modal = new bootstrap.Modal(document.getElementById('emailModal'));
    modal.show();
}

function saveTemplate() {
    const templateName = prompt('اسم القالب:');
    if (templateName) {
        fetch('/reports/save-template', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                name: templateName,
                type: 'trial_balance',
                parameters: {
                    date_from: '{{ date_from }}',
                    date_to: '{{ date_to }}',
                    account_type: '{{ form.account_type.data }}',
                    show_zero_balances: {{ 'true' if form.show_zero_balances.data else 'false' }}
                }
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم حفظ القالب بنجاح');
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        });
    }
}

function scheduleReport() {
    alert('ميزة الجدولة ستكون متاحة قريباً');
}

// Print styles
window.addEventListener('beforeprint', function() {
    document.body.classList.add('printing');
});

window.addEventListener('afterprint', function() {
    document.body.classList.remove('printing');
});
</script>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .page-header,
    .card:not(#report-content),
    .btn,
    .breadcrumb {
        display: none !important;
    }
    
    #report-content {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        color: black !important;
    }
}

.table th {
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
}

.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
}
</style>
{% endblock %}
