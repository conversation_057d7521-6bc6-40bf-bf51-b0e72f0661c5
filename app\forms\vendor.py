"""
Vendor forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Length, Email, Optional
from app.models.vendor import Vendor

class VendorForm(FlaskForm):
    name = StringField('اسم المورد', validators=[
        DataRequired(), 
        Length(min=1, max=150)
    ])
    name_en = StringField('الاسم بالإنجليزية', validators=[
        Length(max=150)
    ])
    tax_id = StringField('الرقم الضريبي', validators=[
        Length(max=20)
    ])
    address = TextAreaField('العنوان', validators=[
        Length(max=500)
    ])
    address_en = TextAreaField('العنوان بالإنجليزية', validators=[
        Length(max=500)
    ])
    email = StringField('البريد الإلكتروني', validators=[
        Optional(), 
        Email(),
        Length(max=100)
    ])
    phone = StringField('الهاتف', validators=[
        Length(max=20)
    ])
    mobile = StringField('الموبايل', validators=[
        Length(max=20)
    ])
    is_active = BooleanField('نشط', default=True)
    submit = SubmitField('حفظ')

class VendorSearchForm(FlaskForm):
    search = StringField('البحث', validators=[Length(max=100)])
    submit = SubmitField('بحث')
