"""
Notification system models
"""

from app import db
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, ForeignKey, Enum
from sqlalchemy.orm import relationship
import enum


class NotificationType(enum.Enum):
    """Notification types"""
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"
    INVOICE_DUE = "invoice_due"
    PAYMENT_RECEIVED = "payment_received"
    LOW_BALANCE = "low_balance"
    SYSTEM_UPDATE = "system_update"
    BACKUP_COMPLETE = "backup_complete"
    TAX_DEADLINE = "tax_deadline"


class NotificationChannel(enum.Enum):
    """Notification delivery channels"""
    IN_APP = "in_app"
    EMAIL = "email"
    SMS = "sms"
    PUSH = "push"


class Notification(db.Model):
    """Notification model for system notifications"""
    
    __tablename__ = 'notifications'
    
    id = Column(Integer, primary_key=True)
    
    # Notification content
    title = Column(String(200), nullable=False, comment='عنوان الإشعار')
    message = Column(Text, nullable=False, comment='محتوى الإشعار')
    type = Column(Enum(NotificationType), nullable=False, default=NotificationType.INFO, comment='نوع الإشعار')
    
    # Recipient information
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='المستخدم المستهدف')
    role = Column(String(50), nullable=True, comment='الدور المستهدف')
    is_global = Column(Boolean, default=False, comment='إشعار عام لجميع المستخدمين')
    
    # Delivery channels
    channels = Column(String(200), nullable=False, default='in_app', comment='قنوات التوصيل')
    
    # Status tracking
    is_read = Column(Boolean, default=False, comment='تم القراءة')
    is_sent = Column(Boolean, default=False, comment='تم الإرسال')
    read_at = Column(DateTime, nullable=True, comment='تاريخ القراءة')
    sent_at = Column(DateTime, nullable=True, comment='تاريخ الإرسال')
    
    # Scheduling
    scheduled_at = Column(DateTime, nullable=True, comment='موعد الإرسال المجدول')
    expires_at = Column(DateTime, nullable=True, comment='تاريخ انتهاء الصلاحية')
    
    # Metadata
    data = Column(Text, nullable=True, comment='بيانات إضافية (JSON)')
    action_url = Column(String(500), nullable=True, comment='رابط الإجراء')
    action_text = Column(String(100), nullable=True, comment='نص الإجراء')
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')
    created_by_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='أنشأ بواسطة')
    
    # Relationships
    user = relationship('User', foreign_keys=[user_id], backref='notifications')
    created_by = relationship('User', foreign_keys=[created_by_id])
    
    def __repr__(self):
        return f'<Notification {self.title}>'
    
    @property
    def is_expired(self):
        """Check if notification is expired"""
        if self.expires_at:
            return datetime.utcnow() > self.expires_at
        return False
    
    @property
    def is_scheduled(self):
        """Check if notification is scheduled for future delivery"""
        if self.scheduled_at:
            return datetime.utcnow() < self.scheduled_at
        return False
    
    @property
    def can_be_sent(self):
        """Check if notification can be sent"""
        return not self.is_sent and not self.is_expired and not self.is_scheduled
    
    def mark_as_read(self):
        """Mark notification as read"""
        self.is_read = True
        self.read_at = datetime.utcnow()
        db.session.commit()
    
    def mark_as_sent(self):
        """Mark notification as sent"""
        self.is_sent = True
        self.sent_at = datetime.utcnow()
        db.session.commit()
    
    def get_channels_list(self):
        """Get list of delivery channels"""
        if self.channels:
            return [channel.strip() for channel in self.channels.split(',')]
        return ['in_app']
    
    def has_channel(self, channel):
        """Check if notification uses specific channel"""
        return channel in self.get_channels_list()
    
    def get_icon(self):
        """Get icon for notification type"""
        icons = {
            NotificationType.INFO: 'info-circle',
            NotificationType.SUCCESS: 'check-circle',
            NotificationType.WARNING: 'exclamation-triangle',
            NotificationType.ERROR: 'times-circle',
            NotificationType.INVOICE_DUE: 'file-invoice-dollar',
            NotificationType.PAYMENT_RECEIVED: 'money-bill-wave',
            NotificationType.LOW_BALANCE: 'wallet',
            NotificationType.SYSTEM_UPDATE: 'sync-alt',
            NotificationType.BACKUP_COMPLETE: 'database',
            NotificationType.TAX_DEADLINE: 'calendar-exclamation'
        }
        return icons.get(self.type, 'bell')
    
    def get_color(self):
        """Get color class for notification type"""
        colors = {
            NotificationType.INFO: 'info',
            NotificationType.SUCCESS: 'success',
            NotificationType.WARNING: 'warning',
            NotificationType.ERROR: 'danger',
            NotificationType.INVOICE_DUE: 'warning',
            NotificationType.PAYMENT_RECEIVED: 'success',
            NotificationType.LOW_BALANCE: 'danger',
            NotificationType.SYSTEM_UPDATE: 'info',
            NotificationType.BACKUP_COMPLETE: 'success',
            NotificationType.TAX_DEADLINE: 'warning'
        }
        return colors.get(self.type, 'info')
    
    def to_dict(self):
        """Convert notification to dictionary"""
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'type': self.type.value,
            'icon': self.get_icon(),
            'color': self.get_color(),
            'is_read': self.is_read,
            'action_url': self.action_url,
            'action_text': self.action_text,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'read_at': self.read_at.isoformat() if self.read_at else None
        }


class NotificationTemplate(db.Model):
    """Notification templates for automated notifications"""
    
    __tablename__ = 'notification_templates'
    
    id = Column(Integer, primary_key=True)
    
    # Template identification
    name = Column(String(100), nullable=False, unique=True, comment='اسم القالب')
    description = Column(Text, nullable=True, comment='وصف القالب')
    type = Column(Enum(NotificationType), nullable=False, comment='نوع الإشعار')
    
    # Template content
    title_template = Column(String(200), nullable=False, comment='قالب العنوان')
    message_template = Column(Text, nullable=False, comment='قالب الرسالة')
    
    # Default settings
    default_channels = Column(String(200), nullable=False, default='in_app', comment='القنوات الافتراضية')
    target_role = Column(String(50), nullable=True, comment='الدور المستهدف')
    is_global = Column(Boolean, default=False, comment='إشعار عام')
    
    # Automation settings
    is_active = Column(Boolean, default=True, comment='نشط')
    trigger_event = Column(String(100), nullable=True, comment='حدث التشغيل')
    conditions = Column(Text, nullable=True, comment='شروط التشغيل (JSON)')
    
    # Scheduling
    delay_minutes = Column(Integer, default=0, comment='تأخير بالدقائق')
    expires_after_hours = Column(Integer, nullable=True, comment='انتهاء الصلاحية بالساعات')
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')
    created_by_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='أنشأ بواسطة')
    
    # Relationships
    created_by = relationship('User', backref='notification_templates')
    
    def __repr__(self):
        return f'<NotificationTemplate {self.name}>'
    
    def render_title(self, context=None):
        """Render title template with context"""
        if context:
            try:
                return self.title_template.format(**context)
            except KeyError:
                pass
        return self.title_template
    
    def render_message(self, context=None):
        """Render message template with context"""
        if context:
            try:
                return self.message_template.format(**context)
            except KeyError:
                pass
        return self.message_template
    
    def create_notification(self, context=None, user_id=None, **kwargs):
        """Create notification from template"""
        from datetime import timedelta
        
        notification = Notification(
            title=self.render_title(context),
            message=self.render_message(context),
            type=self.type,
            channels=self.default_channels,
            user_id=user_id or kwargs.get('user_id'),
            role=self.target_role,
            is_global=self.is_global,
            **kwargs
        )
        
        # Set scheduling
        if self.delay_minutes > 0:
            notification.scheduled_at = datetime.utcnow() + timedelta(minutes=self.delay_minutes)
        
        # Set expiration
        if self.expires_after_hours:
            notification.expires_at = datetime.utcnow() + timedelta(hours=self.expires_after_hours)
        
        return notification


class NotificationPreference(db.Model):
    """User notification preferences"""
    
    __tablename__ = 'notification_preferences'
    
    id = Column(Integer, primary_key=True)
    
    # User and notification type
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False, comment='المستخدم')
    notification_type = Column(Enum(NotificationType), nullable=False, comment='نوع الإشعار')
    
    # Channel preferences
    in_app_enabled = Column(Boolean, default=True, comment='الإشعارات داخل التطبيق')
    email_enabled = Column(Boolean, default=True, comment='الإشعارات بالبريد الإلكتروني')
    sms_enabled = Column(Boolean, default=False, comment='الإشعارات بالرسائل النصية')
    push_enabled = Column(Boolean, default=True, comment='الإشعارات المنبثقة')
    
    # Timing preferences
    quiet_hours_start = Column(String(5), nullable=True, comment='بداية ساعات الهدوء (HH:MM)')
    quiet_hours_end = Column(String(5), nullable=True, comment='نهاية ساعات الهدوء (HH:MM)')
    
    # Tracking
    created_at = Column(DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')
    
    # Relationships
    user = relationship('User', backref='notification_preferences')
    
    __table_args__ = (
        db.UniqueConstraint('user_id', 'notification_type', name='unique_user_notification_type'),
    )
    
    def __repr__(self):
        return f'<NotificationPreference {self.user_id}:{self.notification_type.value}>'
    
    def get_enabled_channels(self):
        """Get list of enabled channels for this preference"""
        channels = []
        if self.in_app_enabled:
            channels.append('in_app')
        if self.email_enabled:
            channels.append('email')
        if self.sms_enabled:
            channels.append('sms')
        if self.push_enabled:
            channels.append('push')
        return channels
    
    def is_in_quiet_hours(self):
        """Check if current time is in quiet hours"""
        if not self.quiet_hours_start or not self.quiet_hours_end:
            return False
        
        from datetime import time
        now = datetime.now().time()
        start = time.fromisoformat(self.quiet_hours_start)
        end = time.fromisoformat(self.quiet_hours_end)
        
        if start <= end:
            return start <= now <= end
        else:  # Quiet hours span midnight
            return now >= start or now <= end


class NotificationLog(db.Model):
    """Log of sent notifications for tracking and debugging"""
    
    __tablename__ = 'notification_logs'
    
    id = Column(Integer, primary_key=True)
    
    # Notification reference
    notification_id = Column(Integer, ForeignKey('notifications.id'), nullable=False, comment='الإشعار')
    
    # Delivery details
    channel = Column(Enum(NotificationChannel), nullable=False, comment='قناة التوصيل')
    recipient = Column(String(200), nullable=False, comment='المستلم')
    status = Column(String(50), nullable=False, comment='حالة التوصيل')
    
    # Response details
    response_code = Column(String(10), nullable=True, comment='رمز الاستجابة')
    response_message = Column(Text, nullable=True, comment='رسالة الاستجابة')
    external_id = Column(String(100), nullable=True, comment='المعرف الخارجي')
    
    # Timing
    sent_at = Column(DateTime, default=datetime.utcnow, comment='تاريخ الإرسال')
    delivered_at = Column(DateTime, nullable=True, comment='تاريخ التوصيل')
    
    # Relationships
    notification = relationship('Notification', backref='delivery_logs')
    
    def __repr__(self):
        return f'<NotificationLog {self.notification_id}:{self.channel.value}>'
    
    @property
    def is_delivered(self):
        """Check if notification was delivered successfully"""
        return self.status in ['delivered', 'read']
    
    @property
    def is_failed(self):
        """Check if notification delivery failed"""
        return self.status in ['failed', 'bounced', 'rejected']
