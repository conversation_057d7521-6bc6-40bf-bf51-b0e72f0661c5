#!/usr/bin/env python3
"""
إصلاح بيانات جدول الحسابات
Fix accounts table data
"""

import sqlite3

def fix_accounts_data():
    """إصلاح بيانات الحسابات"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 إصلاح بيانات الحسابات...")
        
        # إصلاح البيانات - نسخ من account_type إلى type
        cursor.execute("UPDATE accounts SET type = account_type WHERE type IS NULL OR type = ''")
        
        # حذف الحسابات الموجودة
        cursor.execute("DELETE FROM accounts")
        
        # إدراج الحسابات الافتراضية
        default_accounts = [
            ('1000', 'الأصول', 'Assets', 'Asset', None),
            ('1100', 'الأصول المتداولة', 'Current Assets', 'Asset', 1),
            ('1110', 'النقدية', 'Cash', 'Asset', 2),
            ('1120', 'البنوك', 'Banks', 'Asset', 2),
            ('1130', 'العملاء', 'Accounts Receivable', 'Asset', 2),
            ('1140', 'المخزون', 'Inventory', 'Asset', 2),
            ('2000', 'الخصوم', 'Liabilities', 'Liability', None),
            ('2100', 'الخصوم المتداولة', 'Current Liabilities', 'Liability', 7),
            ('2110', 'الموردين', 'Accounts Payable', 'Liability', 8),
            ('2120', 'الضرائب المستحقة', 'Taxes Payable', 'Liability', 8),
            ('3000', 'حقوق الملكية', 'Equity', 'Equity', None),
            ('3100', 'رأس المال', 'Capital', 'Equity', 11),
            ('3200', 'الأرباح المحتجزة', 'Retained Earnings', 'Equity', 11),
            ('4000', 'الإيرادات', 'Revenue', 'Income', None),
            ('4100', 'إيرادات المبيعات', 'Sales Revenue', 'Income', 14),
            ('4200', 'إيرادات أخرى', 'Other Income', 'Income', 14),
            ('5000', 'المصروفات', 'Expenses', 'Expense', None),
            ('5100', 'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 'Expense', 17),
            ('5200', 'مصروفات التشغيل', 'Operating Expenses', 'Expense', 17),
            ('5300', 'مصروفات أخرى', 'Other Expenses', 'Expense', 17)
        ]
        
        # إدراج الحسابات الجديدة
        for code, name, name_en, acc_type, parent_id in default_accounts:
            try:
                cursor.execute("""
                    INSERT INTO accounts (code, name, name_en, type, parent_id, is_active)
                    VALUES (?, ?, ?, ?, ?, 1)
                """, (code, name, name_en, acc_type, parent_id))
                print(f"✅ تم إدراج الحساب: {code} - {name}")
            except Exception as e:
                print(f"❌ خطأ في إدراج الحساب {code}: {e}")
        
        conn.commit()
        conn.close()
        
        print("🎉 تم إصلاح بيانات الحسابات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح البيانات: {e}")
        return False

if __name__ == "__main__":
    fix_accounts_data()
