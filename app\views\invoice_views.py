"""
Invoice management views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort, make_response
from flask_login import login_required, current_user
from sqlalchemy import or_, and_, func, desc
from sqlalchemy.orm import joinedload
from app import db
from app.models.invoice import Invoice, InvoiceLine
from app.models.customer import Customer
from app.models.account import Account
from app.forms.invoice_forms import InvoiceForm, InvoiceSearchForm, QuickInvoiceForm, InvoiceBulkActionForm
from app.utils.decorators import permission_required
from app.utils.helpers import get_pagination_params, generate_reference_number
from decimal import Decimal
from datetime import datetime, date, timedelta
import json

# Create blueprint
invoices_bp = Blueprint('invoices', __name__, url_prefix='/invoices')


@invoices_bp.route('/')
@login_required
@permission_required('invoices')
def index():
    """Display invoices list with search and filtering"""
    form = InvoiceSearchForm(request.args)
    page, per_page = get_pagination_params()
    
    # Build query
    query = Invoice.query.options(
        joinedload(Invoice.customer),
        joinedload(Invoice.created_by)
    )
    
    # Apply search filters
    if form.search.data:
        search_term = f"%{form.search.data}%"
        query = query.filter(
            or_(
                Invoice.invoice_number.ilike(search_term),
                Invoice.reference_number.ilike(search_term),
                Invoice.description.ilike(search_term)
            )
        )
        
        # Also search in customer names
        query = query.join(Customer, Invoice.customer_id == Customer.id, isouter=True).filter(
            or_(
                Invoice.invoice_number.ilike(search_term),
                Invoice.reference_number.ilike(search_term),
                Invoice.description.ilike(search_term),
                Customer.name.ilike(search_term),
                Customer.tax_id.ilike(search_term)
            )
        )
    
    if form.status.data:
        query = query.filter(Invoice.status == form.status.data)
    
    if form.customer_id.data:
        query = query.filter(Invoice.customer_id == form.customer_id.data)
    
    if form.date_from.data:
        query = query.filter(Invoice.issue_date >= form.date_from.data)

    if form.date_to.data:
        query = query.filter(Invoice.issue_date <= form.date_to.data)

    # Order by invoice date (newest first)
    query = query.order_by(desc(Invoice.issue_date), desc(Invoice.created_at))
    
    # Paginate
    invoices = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Get statistics
    stats = {
        'total_invoices': Invoice.query.count(),
        'paid_invoices': Invoice.query.filter_by(status='paid').count(),
        'pending_invoices': Invoice.query.filter_by(status='pending').count(),
        'total_sales': db.session.query(func.sum(Invoice.total_amount)).scalar() or Decimal('0')
    }
    
    return render_template(
        'invoices/index.html',
        invoices=invoices,
        form=form,
        stats=stats,
        title='الفواتير الإلكترونية'
    )


@invoices_bp.route('/new')
@login_required
@permission_required('invoices')
def new():
    """Display form for creating new invoice"""
    form = InvoiceForm()
    
    # Pre-fill customer if specified in query params
    customer_id = request.args.get('customer_id')
    if customer_id:
        form.customer_id.data = customer_id
    
    # Auto-generate invoice number
    if not form.invoice_number.data:
        form.invoice_number.data = generate_reference_number('INV')
    
    return render_template(
        'invoices/form.html',
        form=form,
        title='فاتورة جديدة'
    )


@invoices_bp.route('/create', methods=['POST'])
@login_required
@permission_required('invoices')
def create():
    """Create new invoice"""
    form = InvoiceForm()
    
    if form.validate_on_submit():
        try:
            # Create invoice
            invoice = Invoice(
                invoice_number=form.invoice_number.data,
                reference_number=form.reference_number.data or None,
                customer_id=form.customer_id.data,
                issue_date=form.invoice_date.data,
                due_date=form.due_date.data or None,
                currency=form.currency.data or 'EGP',
                description=form.description.data or None,
                notes=form.notes.data or None,
                status='draft',
                created_by_id=current_user.id
            )
            
            db.session.add(invoice)
            db.session.flush()  # Get the ID
            
            # Process invoice lines from form data
            lines_data = []
            subtotal = Decimal('0')
            total_discount = Decimal('0')
            total_tax = Decimal('0')
            
            for key, value in request.form.items():
                if key.startswith('lines-') and key.endswith('-description'):
                    index = key.split('-')[1]
                    description = value
                    quantity = Decimal(request.form.get(f'lines-{index}-quantity', '0'))
                    unit_price = Decimal(request.form.get(f'lines-{index}-unit_price', '0'))
                    discount_rate = Decimal(request.form.get(f'lines-{index}-discount_rate', '0'))
                    tax_rate = Decimal(request.form.get(f'lines-{index}-tax_rate', '14'))
                    
                    if description and quantity > 0:
                        # Calculate amounts
                        line_subtotal = quantity * unit_price
                        discount_amount = line_subtotal * (discount_rate / 100)
                        line_after_discount = line_subtotal - discount_amount
                        tax_amount = line_after_discount * (tax_rate / 100)
                        total_amount = line_after_discount + tax_amount
                        
                        lines_data.append({
                            'description': description,
                            'quantity': quantity,
                            'unit_price': unit_price,
                            'discount_rate': discount_rate,
                            'discount_amount': discount_amount,
                            'tax_rate': tax_rate,
                            'tax_amount': tax_amount,
                            'total_amount': total_amount
                        })
                        
                        subtotal += line_subtotal
                        total_discount += discount_amount
                        total_tax += tax_amount
            
            # Validate that we have at least 1 line
            if len(lines_data) < 1:
                flash('يجب إضافة بند واحد على الأقل للفاتورة', 'error')
                return render_template('invoices/form.html', form=form, title='فاتورة جديدة')
            
            # Create invoice lines
            for line_data in lines_data:
                line = InvoiceLine(
                    invoice_id=invoice.id,
                    description=line_data['description'],
                    quantity=line_data['quantity'],
                    unit_price=line_data['unit_price'],
                    discount_rate=line_data['discount_rate'],
                    discount_amount=line_data['discount_amount'],
                    tax_rate=line_data['tax_rate'],
                    tax_amount=line_data['tax_amount'],
                    total_amount=line_data['total_amount']
                )
                db.session.add(line)
            
            # Update invoice totals
            invoice.subtotal_amount = subtotal
            invoice.discount_amount = total_discount
            invoice.tax_amount = total_tax
            invoice.total_amount = subtotal - total_discount + total_tax
            
            # Check action
            action = request.form.get('action', 'save')
            if action == 'save_and_send':
                invoice.status = 'pending'
                invoice.sent_at = datetime.utcnow()
            
            db.session.commit()
            
            if action == 'save_and_send':
                flash(f'تم إنشاء وإرسال الفاتورة "{invoice.invoice_number}" بنجاح', 'success')
            else:
                flash(f'تم إنشاء الفاتورة "{invoice.invoice_number}" كمسودة بنجاح', 'success')
            
            return redirect(url_for('invoices.detail', invoice_id=invoice.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء الفاتورة: {str(e)}', 'error')
    
    return render_template(
        'invoices/form.html',
        form=form,
        title='فاتورة جديدة'
    )


@invoices_bp.route('/<uuid:invoice_id>')
@login_required
@permission_required('invoices')
def detail(invoice_id):
    """Display invoice details"""
    invoice = Invoice.query.options(
        joinedload(Invoice.customer),
        joinedload(Invoice.created_by)
    ).get_or_404(invoice_id)
    
    # Get payment history (when payment model is available)
    payments = []
    
    # Get related documents
    related_documents = []
    
    return render_template(
        'invoices/detail.html',
        invoice=invoice,
        payments=payments,
        related_documents=related_documents,
        title=f'فاتورة رقم {invoice.invoice_number}'
    )


@invoices_bp.route('/<uuid:invoice_id>/edit')
@login_required
@permission_required('invoices')
def edit(invoice_id):
    """Display form for editing invoice"""
    invoice = Invoice.query.options(
        joinedload(Invoice.lines),
        joinedload(Invoice.customer)
    ).get_or_404(invoice_id)
    
    if not invoice.can_be_edited():
        flash('لا يمكن تعديل هذه الفاتورة', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    form = InvoiceForm(obj=invoice, invoice=invoice)
    
    return render_template(
        'invoices/form.html',
        form=form,
        invoice=invoice,
        title=f'تعديل الفاتورة: {invoice.invoice_number}'
    )


@invoices_bp.route('/<uuid:invoice_id>/update', methods=['POST'])
@login_required
@permission_required('invoices')
def update(invoice_id):
    """Update invoice"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if not invoice.can_be_edited():
        flash('لا يمكن تعديل هذه الفاتورة', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    form = InvoiceForm(invoice=invoice)
    
    if form.validate_on_submit():
        try:
            # Update invoice details
            invoice.invoice_number = form.invoice_number.data
            invoice.reference_number = form.reference_number.data or None
            invoice.customer_id = form.customer_id.data
            invoice.issue_date = form.invoice_date.data
            invoice.due_date = form.due_date.data or None
            invoice.currency = form.currency.data or 'EGP'
            invoice.description = form.description.data or None
            invoice.notes = form.notes.data or None
            invoice.updated_at = datetime.utcnow()
            
            # Delete existing lines
            InvoiceLine.query.filter_by(invoice_id=invoice.id).delete()
            
            # Process new invoice lines (same logic as create)
            lines_data = []
            subtotal = Decimal('0')
            total_discount = Decimal('0')
            total_tax = Decimal('0')
            
            for key, value in request.form.items():
                if key.startswith('lines-') and key.endswith('-description'):
                    index = key.split('-')[1]
                    description = value
                    quantity = Decimal(request.form.get(f'lines-{index}-quantity', '0'))
                    unit_price = Decimal(request.form.get(f'lines-{index}-unit_price', '0'))
                    discount_rate = Decimal(request.form.get(f'lines-{index}-discount_rate', '0'))
                    tax_rate = Decimal(request.form.get(f'lines-{index}-tax_rate', '14'))
                    
                    if description and quantity > 0:
                        # Calculate amounts
                        line_subtotal = quantity * unit_price
                        discount_amount = line_subtotal * (discount_rate / 100)
                        line_after_discount = line_subtotal - discount_amount
                        tax_amount = line_after_discount * (tax_rate / 100)
                        total_amount = line_after_discount + tax_amount
                        
                        lines_data.append({
                            'description': description,
                            'quantity': quantity,
                            'unit_price': unit_price,
                            'discount_rate': discount_rate,
                            'discount_amount': discount_amount,
                            'tax_rate': tax_rate,
                            'tax_amount': tax_amount,
                            'total_amount': total_amount
                        })
                        
                        subtotal += line_subtotal
                        total_discount += discount_amount
                        total_tax += tax_amount
            
            # Create new invoice lines
            for line_data in lines_data:
                line = InvoiceLine(
                    invoice_id=invoice.id,
                    description=line_data['description'],
                    quantity=line_data['quantity'],
                    unit_price=line_data['unit_price'],
                    discount_rate=line_data['discount_rate'],
                    discount_amount=line_data['discount_amount'],
                    tax_rate=line_data['tax_rate'],
                    tax_amount=line_data['tax_amount'],
                    total_amount=line_data['total_amount']
                )
                db.session.add(line)
            
            # Update invoice totals
            invoice.subtotal_amount = subtotal
            invoice.discount_amount = total_discount
            invoice.tax_amount = total_tax
            invoice.total_amount = subtotal - total_discount + total_tax
            
            db.session.commit()
            
            flash(f'تم تحديث الفاتورة "{invoice.invoice_number}" بنجاح', 'success')
            return redirect(url_for('invoices.detail', invoice_id=invoice.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الفاتورة: {str(e)}', 'error')
    
    return render_template(
        'invoices/form.html',
        form=form,
        invoice=invoice,
        title=f'تعديل الفاتورة: {invoice.invoice_number}'
    )


@invoices_bp.route('/<uuid:invoice_id>/delete', methods=['POST'])
@login_required
@permission_required('invoices')
def delete(invoice_id):
    """Delete invoice"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if not invoice.can_be_deleted():
        flash('لا يمكن حذف هذه الفاتورة', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    try:
        invoice_number = invoice.invoice_number
        
        # Delete lines first
        InvoiceLine.query.filter_by(invoice_id=invoice.id).delete()
        
        # Delete invoice
        db.session.delete(invoice)
        db.session.commit()
        
        flash(f'تم حذف الفاتورة "{invoice_number}" بنجاح', 'success')
        return redirect(url_for('invoices.index'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الفاتورة: {str(e)}', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))


@invoices_bp.route('/<uuid:invoice_id>/pdf')
@login_required
@permission_required('invoices')
def pdf(invoice_id):
    """Generate PDF for invoice"""
    invoice = Invoice.query.options(
        joinedload(Invoice.customer),
        joinedload(Invoice.lines)
    ).get_or_404(invoice_id)
    
    # This would generate PDF using a library like ReportLab or WeasyPrint
    # For now, return a placeholder response
    
    response = make_response("PDF generation not implemented yet")
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'inline; filename=invoice_{invoice.invoice_number}.pdf'
    
    return response


@invoices_bp.route('/bulk_action', methods=['POST'])
@login_required
@permission_required('invoices')
def bulk_action():
    """Execute bulk action on invoices"""
    action = request.form.get('action')
    invoice_ids = request.form.get('invoice_ids', '').split(',')
    
    if not action or not invoice_ids:
        flash('يرجى تحديد إجراء وفواتير', 'error')
        return redirect(url_for('invoices.index'))
    
    try:
        invoices = Invoice.query.filter(Invoice.id.in_(invoice_ids)).all()
        
        if action == 'mark_paid':
            for invoice in invoices:
                if invoice.status in ['pending', 'overdue']:
                    invoice.status = 'paid'
                    invoice.paid_at = datetime.utcnow()
            db.session.commit()
            flash(f'تم تحديد {len(invoices)} فاتورة كمدفوعة بنجاح', 'success')
            
        elif action == 'mark_cancelled':
            for invoice in invoices:
                if invoice.can_be_cancelled():
                    invoice.status = 'cancelled'
                    invoice.cancelled_at = datetime.utcnow()
            db.session.commit()
            flash(f'تم إلغاء {len(invoices)} فاتورة بنجاح', 'success')
            
        elif action == 'delete':
            deletable_invoices = [i for i in invoices if i.can_be_deleted()]
            for invoice in deletable_invoices:
                InvoiceLine.query.filter_by(invoice_id=invoice.id).delete()
                db.session.delete(invoice)
            db.session.commit()
            flash(f'تم حذف {len(deletable_invoices)} فاتورة بنجاح', 'success')
            
            if len(deletable_invoices) < len(invoices):
                flash(f'{len(invoices) - len(deletable_invoices)} فاتورة لم يتم حذفها لعدم إمكانية الحذف', 'warning')
        
        elif action == 'submit_eta':
            # Implement ETA submission
            flash('سيتم تنفيذ إرسال الفواتير لمنظومة الضرائب قريباً', 'info')
        
        elif action == 'export':
            # Implement export functionality
            flash('سيتم تنفيذ التصدير قريباً', 'info')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تنفيذ الإجراء: {str(e)}', 'error')
    
    return redirect(url_for('invoices.index'))


# API Routes
@invoices_bp.route('/<uuid:invoice_id>/submit-eta', methods=['POST'])
@login_required
@permission_required('invoices')
def submit_eta(invoice_id):
    """Submit invoice to Egyptian Tax Authority"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    try:
        # This would implement actual ETA submission
        # For now, simulate the process
        
        invoice.eta_status = 'pending'
        invoice.eta_submitted_at = datetime.utcnow()
        
        # Simulate processing
        import uuid
        invoice.eta_uuid = str(uuid.uuid4())
        invoice.eta_status = 'submitted'
        invoice.eta_response = 'Successfully submitted to ETA'
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إرسال الفاتورة لمنظومة الضرائب بنجاح',
            'eta_uuid': invoice.eta_uuid
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء الإرسال: {str(e)}'
        }), 500


@invoices_bp.route('/<uuid:invoice_id>/send-email', methods=['POST'])
@login_required
@permission_required('invoices')
def send_email(invoice_id):
    """Send invoice via email"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if not invoice.customer or not invoice.customer.email:
        return jsonify({
            'success': False,
            'message': 'العميل ليس لديه بريد إلكتروني'
        }), 400
    
    try:
        # This would implement actual email sending
        # For now, simulate the process
        
        invoice.email_sent_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إرسال الفاتورة بالبريد الإلكتروني بنجاح'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء الإرسال: {str(e)}'
        }), 500


@invoices_bp.route('/<uuid:invoice_id>/mark-paid', methods=['POST'])
@login_required
@permission_required('invoices')
def mark_paid(invoice_id):
    """Mark invoice as paid"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    try:
        if invoice.status in ['pending', 'overdue']:
            invoice.status = 'paid'
            invoice.paid_at = datetime.utcnow()
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': 'تم تحديد الفاتورة كمدفوعة'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'لا يمكن تحديد هذه الفاتورة كمدفوعة'
            }), 400
            
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500


@invoices_bp.route('/export/<format>')
@login_required
@permission_required('invoices')
def export(format):
    """Export invoices in different formats"""
    try:
        # Get filter parameters from request
        search = request.args.get('search', '')
        customer_id = request.args.get('customer_id', '')
        status = request.args.get('status', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')

        # Build query with same filters as index
        query = Invoice.query.options(
            joinedload(Invoice.customer),
            joinedload(Invoice.created_by)
        )

        # Apply filters
        if search:
            query = query.filter(
                or_(
                    Invoice.invoice_number.contains(search),
                    Invoice.notes.contains(search)
                )
            )

        if customer_id:
            query = query.filter(Invoice.customer_id == customer_id)

        if status:
            query = query.filter(Invoice.status == status)

        if date_from:
            from datetime import datetime
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            query = query.filter(Invoice.issue_date >= date_from_obj)

        if date_to:
            from datetime import datetime
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            query = query.filter(Invoice.issue_date <= date_to_obj)

        # Order by date
        query = query.order_by(desc(Invoice.issue_date))

        invoices = query.all()

        if format == 'pdf':
            # For now, return a simple message
            flash('سيتم تنفيذ تصدير PDF قريباً', 'info')
            return redirect(url_for('invoices.index'))

        elif format == 'excel':
            # For now, return a simple message
            flash('سيتم تنفيذ تصدير Excel قريباً', 'info')
            return redirect(url_for('invoices.index'))

        elif format == 'csv':
            # For now, return a simple message
            flash('سيتم تنفيذ تصدير CSV قريباً', 'info')
            return redirect(url_for('invoices.index'))

        else:
            flash('تنسيق التصدير غير مدعوم', 'error')
            return redirect(url_for('invoices.index'))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('invoices.index'))
