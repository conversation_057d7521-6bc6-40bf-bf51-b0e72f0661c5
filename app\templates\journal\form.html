{% extends "base.html" %}

{% block title %}{{ title }} - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-{{ 'plus' if not entry else 'edit' }} me-3"></i>
                {{ title }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('journal.index') }}">دفتر اليومية</a></li>
                    {% if entry %}
                    <li class="breadcrumb-item"><a href="{{ url_for('journal.detail', entry_id=entry.id) }}">{{ entry.reference_number }}</a></li>
                    {% endif %}
                    <li class="breadcrumb-item active">{{ title }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('journal.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<form method="POST" id="journalForm">
    {{ form.hidden_tag() }}
    
    <div class="row">
        <div class="col-lg-8">
            <!-- Entry Details -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        تفاصيل القيد
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.reference_number.label(class="form-label") }}
                            {{ form.reference_number(class="form-control" + (" is-invalid" if form.reference_number.errors else ""), 
                                                   readonly=entry and entry.is_posted) }}
                            {% if form.reference_number.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.reference_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.entry_date.label(class="form-label required") }}
                            {{ form.entry_date(class="form-control" + (" is-invalid" if form.entry_date.errors else ""),
                                              readonly=entry and entry.is_posted) }}
                            {% if form.entry_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.entry_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.description.label(class="form-label required") }}
                            {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""),
                                              readonly=entry and entry.is_posted) }}
                            {% if form.description.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.description.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.description_en.label(class="form-label") }}
                            {{ form.description_en(class="form-control" + (" is-invalid" if form.description_en.errors else ""),
                                                  readonly=entry and entry.is_posted) }}
                            {% if form.description_en.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.description_en.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Journal Lines -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        بنود القيد
                    </h5>
                    {% if not (entry and entry.is_posted) %}
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addJournalLine()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة بند
                    </button>
                    {% endif %}
                </div>
                <div class="card-body">
                    <div id="journal-lines">
                        <!-- Journal lines will be added here -->
                    </div>
                    
                    <!-- Totals -->
                    <div class="row mt-3 pt-3 border-top">
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <strong>إجمالي المدين:</strong>
                                <span id="total-debits" class="text-success fw-bold">0.00 ج.م</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-between">
                                <strong>إجمالي الدائن:</strong>
                                <span id="total-credits" class="text-danger fw-bold">0.00 ج.م</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <strong>الفرق:</strong>
                                <span id="difference" class="fw-bold">0.00 ج.م</span>
                            </div>
                        </div>
                    </div>
                    
                    <div id="balance-alert" class="alert alert-warning mt-3" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        القيد غير متوازن! يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن.
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Actions -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        الإجراءات
                    </h6>
                </div>
                <div class="card-body">
                    {% if not (entry and entry.is_posted) %}
                    <div class="d-grid gap-2">
                        <button type="submit" name="action" value="save" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ كمسودة
                        </button>
                        <button type="submit" name="action" value="save_and_post" class="btn btn-success" id="save-and-post-btn" disabled>
                            <i class="fas fa-check me-2"></i>
                            حفظ وترحيل
                        </button>
                        <a href="{{ url_for('journal.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        هذا القيد مرحل ولا يمكن تعديله.
                    </div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Quick Account Search -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-search me-2"></i>
                        البحث السريع في الحسابات
                    </h6>
                </div>
                <div class="card-body">
                    <input type="text" class="form-control" id="account-search" 
                           placeholder="ابحث عن حساب...">
                    <div id="account-results" class="mt-2"></div>
                </div>
            </div>
            
            <!-- Help -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        مساعدة
                    </h6>
                </div>
                <div class="card-body">
                    <h6>نصائح:</h6>
                    <ul class="small">
                        <li>يجب أن يكون إجمالي المدين = إجمالي الدائن</li>
                        <li>استخدم البحث السريع للعثور على الحسابات</li>
                        <li>يمكن حفظ القيد كمسودة أولاً</li>
                        <li>القيود المرحلة لا يمكن تعديلها</li>
                    </ul>
                    
                    <h6 class="mt-3">اختصارات لوحة المفاتيح:</h6>
                    <ul class="small">
                        <li><kbd>Ctrl + S</kbd>: حفظ</li>
                        <li><kbd>Ctrl + Enter</kbd>: حفظ وترحيل</li>
                        <li><kbd>Ctrl + N</kbd>: بند جديد</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Journal Line Template -->
<template id="journal-line-template">
    <div class="journal-line border rounded p-3 mb-3">
        <div class="d-flex justify-content-between align-items-start mb-2">
            <h6 class="mb-0">بند رقم <span class="line-number">1</span></h6>
            {% if not (entry and entry.is_posted) %}
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeJournalLine(this)">
                <i class="fas fa-trash"></i>
            </button>
            {% endif %}
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <label class="form-label required">الحساب</label>
                <select class="form-select account-select" name="lines-{index}-account_id" required
                        {{ 'disabled' if entry and entry.is_posted else '' }}>
                    <option value="">اختر الحساب</option>
                </select>
            </div>

            <div class="col-md-6 mb-3">
                <label class="form-label">البيان</label>
                <input type="text" class="form-control" name="lines-{index}-description"
                       placeholder="بيان البند (اختياري)"
                       {{ 'readonly' if entry and entry.is_posted else '' }}>
            </div>
        </div>

        <div class="row">
            <div class="col-md-4 mb-3">
                <label class="form-label required">المبلغ</label>
                <input type="number" class="form-control amount-input" name="lines-{index}-amount"
                       step="0.01" min="0" required onchange="calculateTotals()"
                       {{ 'readonly' if entry and entry.is_posted else '' }}>
            </div>

            <div class="col-md-4 mb-3">
                <label class="form-label required">مدين/دائن</label>
                <select class="form-select dc-select" name="lines-{index}-dc" required onchange="calculateTotals()"
                        {{ 'disabled' if entry and entry.is_posted else '' }}>
                    <option value="">اختر</option>
                    <option value="D">مدين</option>
                    <option value="C">دائن</option>
                </select>
            </div>

            <div class="col-md-4 mb-3">
                <label class="form-label">المبلغ المحسوب</label>
                <div class="form-control-plaintext calculated-amount text-primary fw-bold">0.00 ج.م</div>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block extra_js %}
<script>
let lineIndex = 0;

// Initialize form
$(document).ready(function() {
    // Load existing lines if editing
    {% if entry and entry.lines %}
    {% for line in entry.lines %}
    addJournalLine({{ loop.index0 }}, {
        account_id: '{{ line.account_id }}',
        description: '{{ line.description or "" }}',
        amount: {{ line.amount }},
        dc: '{{ line.dc }}'
    });
    {% endfor %}
    {% else %}
    // Add two empty lines for new entry
    addJournalLine();
    addJournalLine();
    {% endif %}
    
    // Load accounts for selects
    loadAccounts();
    
    // Calculate initial totals
    calculateTotals();
    
    // Keyboard shortcuts
    $(document).keydown(function(e) {
        if (e.ctrlKey) {
            switch(e.which) {
                case 83: // Ctrl+S
                    e.preventDefault();
                    $('button[value="save"]').click();
                    break;
                case 13: // Ctrl+Enter
                    e.preventDefault();
                    $('button[value="save_and_post"]').click();
                    break;
                case 78: // Ctrl+N
                    e.preventDefault();
                    addJournalLine();
                    break;
            }
        }
    });
});

function addJournalLine(index = null, data = null) {
    if (index === null) {
        index = lineIndex++;
    } else {
        lineIndex = Math.max(lineIndex, index + 1);
    }
    
    const template = document.getElementById('journal-line-template');
    const clone = template.content.cloneNode(true);
    
    // Update indices
    clone.querySelectorAll('[name*="{index}"]').forEach(element => {
        element.name = element.name.replace('{index}', index);
    });
    
    // Update line number
    clone.querySelector('.line-number').textContent = index + 1;
    
    // Populate data if provided
    if (data) {
        if (data.account_id) {
            clone.querySelector('.account-select').value = data.account_id;
        }
        if (data.description) {
            clone.querySelector('input[name*="description"]').value = data.description;
        }
        if (data.amount) {
            clone.querySelector('.amount-input').value = data.amount;
        }
        if (data.dc) {
            clone.querySelector('.dc-select').value = data.dc;
        }
    }
    
    document.getElementById('journal-lines').appendChild(clone);
    updateLineNumbers();
    calculateTotals();
}

function removeJournalLine(button) {
    const line = button.closest('.journal-line');
    line.remove();
    updateLineNumbers();
    calculateTotals();
}

function updateLineNumbers() {
    document.querySelectorAll('.journal-line').forEach((line, index) => {
        line.querySelector('.line-number').textContent = index + 1;
    });
}

function calculateTotals() {
    let totalDebits = 0;
    let totalCredits = 0;
    
    document.querySelectorAll('.journal-line').forEach(line => {
        const amount = parseFloat(line.querySelector('.amount-input').value) || 0;
        const dc = line.querySelector('.dc-select').value;
        const calculatedElement = line.querySelector('.calculated-amount');
        
        if (amount > 0 && dc) {
            if (dc === 'D') {
                totalDebits += amount;
                calculatedElement.textContent = amount.toFixed(2) + ' ج.م (مدين)';
                calculatedElement.className = 'form-control-plaintext calculated-amount text-success fw-bold';
            } else if (dc === 'C') {
                totalCredits += amount;
                calculatedElement.textContent = amount.toFixed(2) + ' ج.م (دائن)';
                calculatedElement.className = 'form-control-plaintext calculated-amount text-danger fw-bold';
            }
        } else {
            calculatedElement.textContent = '0.00 ج.م';
            calculatedElement.className = 'form-control-plaintext calculated-amount text-muted';
        }
    });
    
    // Update totals display
    document.getElementById('total-debits').textContent = totalDebits.toFixed(2) + ' ج.م';
    document.getElementById('total-credits').textContent = totalCredits.toFixed(2) + ' ج.م';
    
    const difference = Math.abs(totalDebits - totalCredits);
    const differenceElement = document.getElementById('difference');
    const balanceAlert = document.getElementById('balance-alert');
    const saveAndPostBtn = document.getElementById('save-and-post-btn');
    
    differenceElement.textContent = difference.toFixed(2) + ' ج.م';
    
    if (difference < 0.01 && totalDebits > 0) {
        // Balanced
        differenceElement.className = 'fw-bold text-success';
        balanceAlert.style.display = 'none';
        saveAndPostBtn.disabled = false;
    } else {
        // Not balanced
        differenceElement.className = 'fw-bold text-danger';
        balanceAlert.style.display = 'block';
        saveAndPostBtn.disabled = true;
    }
}

function loadAccounts() {
    fetch('/accounts/api/search?limit=1000')
        .then(response => response.json())
        .then(accounts => {
            const options = '<option value="">اختر الحساب</option>' + 
                accounts.map(account => 
                    `<option value="${account.id}">${account.full_name}</option>`
                ).join('');
            
            document.querySelectorAll('.account-select').forEach(select => {
                const currentValue = select.value;
                select.innerHTML = options;
                if (currentValue) {
                    select.value = currentValue;
                }
            });
        })
        .catch(error => console.error('Error loading accounts:', error));
}

// Account search functionality
$('#account-search').on('input', function() {
    const query = $(this).val();
    if (query.length < 2) {
        $('#account-results').empty();
        return;
    }
    
    fetch(`/accounts/api/search?q=${encodeURIComponent(query)}&limit=10`)
        .then(response => response.json())
        .then(accounts => {
            const results = accounts.map(account => 
                `<div class="account-result p-2 border-bottom" style="cursor: pointer;" 
                      onclick="selectAccount('${account.id}', '${account.full_name}')">
                    <strong>${account.code}</strong> - ${account.name}
                    <br><small class="text-muted">${account.type_display}</small>
                </div>`
            ).join('');
            
            $('#account-results').html(results);
        })
        .catch(error => console.error('Error searching accounts:', error));
});

function selectAccount(accountId, accountName) {
    // Find the first empty account select
    const emptySelect = document.querySelector('.account-select:not([value]), .account-select[value=""]');
    if (emptySelect) {
        emptySelect.value = accountId;
        // Add option if not exists
        if (!emptySelect.querySelector(`option[value="${accountId}"]`)) {
            const option = new Option(accountName, accountId);
            emptySelect.add(option);
            emptySelect.value = accountId;
        }
    }
    
    $('#account-search').val('');
    $('#account-results').empty();
}

// Form validation
$('#journalForm').on('submit', function(e) {
    const lines = document.querySelectorAll('.journal-line');
    if (lines.length < 2) {
        e.preventDefault();
        alert('يجب إضافة بندين على الأقل للقيد');
        return false;
    }
    
    // Check if all lines are complete
    let isValid = true;
    lines.forEach(line => {
        const account = line.querySelector('.account-select').value;
        const amount = line.querySelector('.amount-input').value;
        const dc = line.querySelector('.dc-select').value;
        
        if (!account || !amount || !dc) {
            isValid = false;
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('يرجى إكمال جميع بنود القيد');
        return false;
    }
});
</script>
{% endblock %}
