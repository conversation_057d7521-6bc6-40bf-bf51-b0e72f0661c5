"""
Invoice and InvoiceLine models for E-Invoice functionality
"""

import uuid
from datetime import datetime, date
from decimal import Decimal
from app import db

class Invoice(db.Model):
    __tablename__ = 'invoices'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    customer_id = db.Column(db.String(36), db.ForeignKey('customers.id'), nullable=False, index=True)
    invoice_number = db.Column(db.String(30), unique=True, nullable=False, index=True)
    issue_date = db.Column(db.Date, nullable=False, index=True, default=date.today)
    due_date = db.Column(db.Date)
    subtotal = db.Column(db.Numeric(14, 2), nullable=False, default=0)
    tax_amount = db.Column(db.Numeric(14, 2), nullable=False, default=0)
    discount_amount = db.Column(db.Numeric(14, 2), nullable=False, default=0)
    total_amount = db.Column(db.Numeric(14, 2), nullable=False, default=0)
    status = db.Column(db.String(20), nullable=False, default='draft', index=True)
    tax_status = db.Column(db.String(20), nullable=False, default='pending')
    notes = db.Column(db.Text)
    created_by_id = db.Column(db.String(36), db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Egyptian Tax Authority Integration Fields
    eta_uuid = db.Column(db.String(100))  # UUID من مصلحة الضرائب
    eta_internal_id = db.Column(db.String(100))  # Internal ID من مصلحة الضرائب
    eta_submission_uuid = db.Column(db.String(100))  # UUID الإرسال
    eta_long_id = db.Column(db.String(200))  # Long ID من مصلحة الضرائب
    eta_hash_key = db.Column(db.String(500))  # Hash Key للتوقيع الرقمي
    eta_status = db.Column(db.String(50), default='draft')  # draft, submitted, accepted, rejected
    eta_submitted_at = db.Column(db.DateTime)
    document_type_name = db.Column(db.String(50), default='I')  # I for Invoice
    document_type_version = db.Column(db.String(10), default='1.0')

    # Tax Fields
    tax_totals = db.Column(db.JSON)  # إجمالي الضرائب
    discount_amount = db.Column(db.Numeric(14, 2), default=0)
    net_amount = db.Column(db.Numeric(14, 2))
    tax_amount = db.Column(db.Numeric(14, 2), default=0)

    # Relationships
    created_by = db.relationship('User', foreign_keys=[created_by_id], overlaps="creator,invoices")
    lines = db.relationship('InvoiceLine', backref='invoice', lazy='dynamic',
                           cascade='all, delete-orphan')
    tax_transactions = db.relationship('TaxTransaction', backref='invoice', lazy='dynamic')
    receipt_allocations = db.relationship('ReceiptInvoiceAllocation', backref='invoice', lazy='dynamic')
    
    # Status choices
    STATUS_CHOICES = {
        'draft': 'مسودة',
        'sent': 'مرسلة',
        'paid': 'مدفوعة',
        'cancelled': 'ملغية'
    }
    
    TAX_STATUS_CHOICES = {
        'pending': 'في الانتظار',
        'sent': 'مرسلة',
        'accepted': 'مقبولة',
        'rejected': 'مرفوضة'
    }
    
    def __init__(self, customer_id, issue_date=None, due_date=None, notes=None, created_by_id=None):
        self.customer_id = customer_id
        self.issue_date = issue_date or date.today()
        self.due_date = due_date
        self.notes = notes
        self.created_by_id = created_by_id
        self.invoice_number = self.generate_invoice_number()
    
    def generate_invoice_number(self):
        """Generate unique invoice number"""
        from app.models.system_setting import SystemSetting
        
        prefix = SystemSetting.get_value('invoice_prefix', 'INV')
        year = self.issue_date.year if self.issue_date else date.today().year
        
        # Get last invoice number for this year
        last_invoice = Invoice.query.filter(
            Invoice.invoice_number.like(f'{prefix}-{year}-%'),
            Invoice.id != self.id
        ).order_by(Invoice.invoice_number.desc()).first()
        
        if last_invoice:
            try:
                last_num = int(last_invoice.invoice_number.split('-')[-1])
                next_num = last_num + 1
            except:
                next_num = 1
        else:
            next_num = 1
        
        return f"{prefix}-{year}-{next_num:04d}"
    
    def add_line(self, description, unit_price, quantity=1, discount_percent=0, tax_percent=None):
        """Add an invoice line"""
        from app.models.system_setting import SystemSetting
        
        if tax_percent is None:
            tax_percent = float(SystemSetting.get_value('default_tax_rate', 14))
        
        line = InvoiceLine(
            invoice_id=self.id,
            description=description,
            unit_price=unit_price,
            quantity=quantity,
            discount_percent=discount_percent,
            tax_percent=tax_percent
        )
        
        line.calculate_line_total()
        db.session.add(line)
        return line
    
    def calculate_totals(self):
        """Calculate invoice totals"""
        lines = self.lines.all()
        
        self.subtotal = sum(line.get_subtotal() for line in lines)
        self.discount_amount = sum(line.get_discount_amount() for line in lines)
        self.tax_amount = sum(line.get_tax_amount() for line in lines)
        self.total_amount = sum(line.line_total for line in lines)
        
        return {
            'subtotal': float(self.subtotal),
            'discount_amount': float(self.discount_amount),
            'tax_amount': float(self.tax_amount),
            'total_amount': float(self.total_amount)
        }
    
    def get_status_display(self):
        """Get Arabic display for status"""
        return self.STATUS_CHOICES.get(self.status, self.status)
    
    def get_tax_status_display(self):
        """Get Arabic display for tax status"""
        return self.TAX_STATUS_CHOICES.get(self.tax_status, self.tax_status)
    
    def get_paid_amount(self):
        """Get total paid amount from receipts"""
        total = db.session.query(
            db.func.sum(ReceiptInvoiceAllocation.allocated_amount)
        ).filter_by(invoice_id=self.id).scalar()
        return float(total or 0)
    
    def get_outstanding_amount(self):
        """Get outstanding amount (total - paid)"""
        return float(self.total_amount) - self.get_paid_amount()
    
    def is_fully_paid(self):
        """Check if invoice is fully paid"""
        return abs(self.get_outstanding_amount()) < 0.01
    
    def can_be_edited(self):
        """Check if invoice can be edited"""
        return self.status == 'draft'
    
    def can_be_sent_to_tax_authority(self):
        """Check if invoice can be sent to tax authority"""
        return self.status in ['sent', 'paid'] and self.tax_status == 'pending'
    
    def mark_as_sent(self):
        """Mark invoice as sent"""
        if self.status == 'draft':
            self.status = 'sent'
            self.updated_at = datetime.utcnow()
    
    def mark_as_paid(self):
        """Mark invoice as paid"""
        if self.is_fully_paid():
            self.status = 'paid'
            self.updated_at = datetime.utcnow()
    
    def to_dict(self):
        """Convert invoice to dictionary"""
        return {
            'id': self.id,
            'customer_id': self.customer_id,
            'customer_name': self.customer.name if self.customer else None,
            'invoice_number': self.invoice_number,
            'issue_date': self.issue_date.isoformat() if self.issue_date else None,
            'due_date': self.due_date.isoformat() if self.due_date else None,
            'subtotal': float(self.subtotal),
            'tax_amount': float(self.tax_amount),
            'discount_amount': float(self.discount_amount),
            'total_amount': float(self.total_amount),
            'status': self.status,
            'status_display': self.get_status_display(),
            'tax_status': self.tax_status,
            'tax_status_display': self.get_tax_status_display(),
            'notes': self.notes,
            'lines_count': self.lines.count(),
            'paid_amount': self.get_paid_amount(),
            'outstanding_amount': self.get_outstanding_amount(),
            'is_fully_paid': self.is_fully_paid(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Invoice {self.invoice_number}>'


class InvoiceLine(db.Model):
    __tablename__ = 'invoice_lines'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    invoice_id = db.Column(db.String(36), db.ForeignKey('invoices.id'), nullable=False, index=True)
    description = db.Column(db.Text, nullable=False)
    description_en = db.Column(db.Text)
    unit_price = db.Column(db.Numeric(14, 2), nullable=False)
    quantity = db.Column(db.Numeric(10, 3), nullable=False, default=1)
    discount_percent = db.Column(db.Numeric(5, 2), default=0)
    tax_percent = db.Column(db.Numeric(5, 2), default=0)
    line_total = db.Column(db.Numeric(14, 2), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Egyptian Tax Authority Fields
    item_code = db.Column(db.String(100))  # كود الصنف
    unit_type = db.Column(db.String(50), default='EA')  # وحدة القياس
    discount_amount = db.Column(db.Numeric(14, 2), default=0)
    tax_rate = db.Column(db.Numeric(5, 2), default=14.00)  # معدل الضريبة
    tax_amount = db.Column(db.Numeric(14, 2), default=0)
    net_amount = db.Column(db.Numeric(14, 2))
    sales_total = db.Column(db.Numeric(14, 2))
    
    def __init__(self, invoice_id, description, unit_price, quantity=1, 
                 discount_percent=0, tax_percent=0, description_en=None):
        self.invoice_id = invoice_id
        self.description = description
        self.description_en = description_en
        self.unit_price = unit_price
        self.quantity = quantity
        self.discount_percent = discount_percent
        self.tax_percent = tax_percent
        self.calculate_line_total()
    
    def get_subtotal(self):
        """Get subtotal (unit_price * quantity)"""
        return float(self.unit_price * self.quantity)
    
    def get_discount_amount(self):
        """Get discount amount"""
        return float(self.get_subtotal() * self.discount_percent / 100)
    
    def get_amount_after_discount(self):
        """Get amount after discount"""
        return self.get_subtotal() - self.get_discount_amount()
    
    def get_tax_amount(self):
        """Get tax amount"""
        return float(self.get_amount_after_discount() * self.tax_percent / 100)
    
    def calculate_line_total(self):
        """Calculate and set line total"""
        self.line_total = self.get_amount_after_discount() + self.get_tax_amount()
        return self.line_total
    
    def to_dict(self):
        """Convert invoice line to dictionary"""
        return {
            'id': self.id,
            'invoice_id': self.invoice_id,
            'description': self.description,
            'description_en': self.description_en,
            'unit_price': float(self.unit_price),
            'quantity': float(self.quantity),
            'discount_percent': float(self.discount_percent),
            'tax_percent': float(self.tax_percent),
            'subtotal': self.get_subtotal(),
            'discount_amount': self.get_discount_amount(),
            'tax_amount': self.get_tax_amount(),
            'line_total': float(self.line_total),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<InvoiceLine {self.description}: {self.line_total}>'
