{% extends "base.html" %}

{% block title %}دفتر اليومية - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-book me-3"></i>
                دفتر اليومية
            </h1>
            <p class="mb-0 mt-2">إدارة القيود المحاسبية اليومية</p>
        </div>
        <div>
            <a href="{{ url_for('journal.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                قيد جديد
            </a>
            <a href="{{ url_for('journal.ledger') }}" class="btn btn-outline-light">
                <i class="fas fa-book-open me-2"></i>
                دفتر الأستاذ
            </a>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                {{ form.search.label(class="form-label") }}
                {{ form.search(class="form-control", placeholder="البحث في البيان أو رقم المرجع...") }}
            </div>
            <div class="col-md-2">
                {{ form.date_from.label(class="form-label") }}
                {{ form.date_from(class="form-control") }}
            </div>
            <div class="col-md-2">
                {{ form.date_to.label(class="form-label") }}
                {{ form.date_to(class="form-control") }}
            </div>
            <div class="col-md-2">
                {{ form.account_id.label(class="form-label") }}
                {{ form.account_id(class="form-select") }}
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('journal.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Journal Entries Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            القيود المحاسبية
        </h5>
        <span class="badge bg-primary">{{ entries.total if entries else 0 }} قيد</span>
    </div>
    <div class="card-body">
        {% if entries and entries.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>التاريخ</th>
                        <th>رقم المرجع</th>
                        <th>البيان</th>
                        <th>المبلغ</th>
                        <th>الحالة</th>
                        <th>المستخدم</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for entry in entries.items %}
                    <tr class="{{ 'table-warning' if not entry.is_posted else '' }}">
                        <td>
                            <strong>{{ entry.entry_date|date }}</strong>
                            <br><small class="text-muted">{{ entry.created_at|datetime }}</small>
                        </td>
                        <td>
                            <a href="{{ url_for('journal.detail', entry_id=entry.id) }}" 
                               class="text-decoration-none fw-bold">
                                {{ entry.reference_number }}
                            </a>
                        </td>
                        <td>
                            <div>{{ entry.description }}</div>
                            {% if entry.description_en %}
                                <small class="text-muted">{{ entry.description_en }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="fw-bold text-primary">{{ entry.get_total_amount()|currency }}</span>
                        </td>
                        <td>
                            {% if entry.is_posted %}
                                <span class="badge bg-success">مرحل</span>
                            {% else %}
                                <span class="badge bg-warning">مسودة</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ entry.created_by.username if entry.created_by else 'غير محدد' }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('journal.detail', entry_id=entry.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if not entry.is_posted %}
                                <a href="{{ url_for('journal.edit', entry_id=entry.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="POST" action="{{ url_for('journal.post', entry_id=entry.id) }}" 
                                      class="d-inline" onsubmit="return confirm('هل تريد ترحيل هذا القيد؟')">
                                    <button type="submit" class="btn btn-outline-success" title="ترحيل">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                <form method="POST" action="{{ url_for('journal.delete', entry_id=entry.id) }}" 
                                      class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا القيد؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% else %}
                                {% if current_user.is_admin() %}
                                <form method="POST" action="{{ url_for('journal.unpost', entry_id=entry.id) }}" 
                                      class="d-inline" onsubmit="return confirm('هل تريد إلغاء ترحيل هذا القيد؟')">
                                    <button type="submit" class="btn btn-outline-secondary" title="إلغاء الترحيل">
                                        <i class="fas fa-undo"></i>
                                    </button>
                                </form>
                                {% endif %}
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if entries.pages > 1 %}
        <nav aria-label="تصفح الصفحات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if entries.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('journal.index', page=entries.prev_num, **request.args) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in entries.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != entries.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('journal.index', page=page_num, **request.args) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if entries.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('journal.index', page=entries.next_num, **request.args) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-book fa-3x text-muted mb-3"></i>
            <h5>لا توجد قيود محاسبية</h5>
            <p class="text-muted">لم يتم العثور على أي قيود تطابق معايير البحث.</p>
            <a href="{{ url_for('journal.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة قيد جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">إجمالي القيود</h5>
                <h3 class="text-primary">{{ stats.total_entries if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">قيود مرحلة</h5>
                <h3 class="text-success">{{ stats.posted_entries if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">مسودات</h5>
                <h3 class="text-warning">{{ stats.draft_entries if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">إجمالي المبالغ</h5>
                <h3 class="text-info">{{ stats.total_amount|currency if stats else '0.00 ج.م' }}</h3>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-submit search form on account change
    $('#account_id').change(function() {
        $(this).closest('form').submit();
    });
    
    // Set default date range to current month
    if (!$('#date_from').val()) {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        $('#date_from').val(firstDay.toISOString().split('T')[0]);
        $('#date_to').val(today.toISOString().split('T')[0]);
    }
    
    // Highlight search terms
    const searchTerm = $('#search').val();
    if (searchTerm) {
        $('table tbody td').each(function() {
            const text = $(this).text();
            if (text.toLowerCase().includes(searchTerm.toLowerCase())) {
                $(this).html(text.replace(new RegExp(searchTerm, 'gi'), '<mark>$&</mark>'));
            }
        });
    }
});
</script>
{% endblock %}
