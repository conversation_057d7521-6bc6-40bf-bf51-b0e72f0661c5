# SystemTax Makefile

.PHONY: help build up down restart logs shell db-init db-migrate db-upgrade test clean

# Default target
help:
	@echo "SystemTax - نظام محاسبي ويب متكامل"
	@echo ""
	@echo "Available commands:"
	@echo "  build      - Build Docker images"
	@echo "  up         - Start all services"
	@echo "  down       - Stop all services"
	@echo "  restart    - Restart all services"
	@echo "  logs       - Show logs"
	@echo "  shell      - Open shell in web container"
	@echo "  db-init    - Initialize database"
	@echo "  db-migrate - Create new migration"
	@echo "  db-upgrade - Apply migrations"
	@echo "  test       - Run tests"
	@echo "  clean      - Clean up containers and volumes"

# Docker commands
build:
	docker-compose build

up:
	docker-compose up -d

down:
	docker-compose down

restart:
	docker-compose restart

logs:
	docker-compose logs -f

shell:
	docker-compose exec web bash

# Database commands
db-init:
	docker-compose exec web flask db init

db-migrate:
	docker-compose exec web flask db migrate -m "$(msg)"

db-upgrade:
	docker-compose exec web flask db upgrade

# Testing
test:
	docker-compose exec web pytest tests/

# Cleanup
clean:
	docker-compose down -v
	docker system prune -f

# Development setup
dev-setup:
	cp .env.example .env
	@echo "Please edit .env file with your configuration"

# Create admin user
create-admin:
	docker-compose exec web python -c "from app.utils.admin import create_admin_user; create_admin_user()"

# Backup database
backup-db:
	docker-compose exec db pg_dump -U systemtax_user systemtax > backup_$(shell date +%Y%m%d_%H%M%S).sql

# Restore database
restore-db:
	@echo "Usage: make restore-db file=backup_file.sql"
	docker-compose exec -T db psql -U systemtax_user systemtax < $(file)
