"""
Tests for account management functionality
"""

import pytest
from decimal import Decimal
from app import db
from app.models.account import Account
from app.models.journal import JournalEntry, JournalLine
from app.utils.account_helpers import (
    create_default_accounts,
    calculate_trial_balance,
    validate_account_code,
    suggest_account_code
)


class TestAccountModel:
    """Test Account model functionality"""
    
    def test_account_creation(self, app):
        """Test creating a new account"""
        with app.app_context():
            account = Account(
                code='1100',
                name='النقدية',
                name_en='Cash',
                type='Asset'
            )
            db.session.add(account)
            db.session.commit()
            
            assert account.id is not None
            assert account.code == '1100'
            assert account.name == 'النقدية'
            assert account.type == 'Asset'
            assert account.is_active is True
    
    def test_account_hierarchy(self, app):
        """Test account parent-child relationships"""
        with app.app_context():
            # Create parent account
            parent = Account(
                code='1000',
                name='الأصول',
                type='Asset'
            )
            db.session.add(parent)
            db.session.flush()
            
            # Create child account
            child = Account(
                code='1100',
                name='النقدية',
                type='Asset',
                parent_id=parent.id
            )
            db.session.add(child)
            db.session.commit()
            
            # Test relationships
            assert child.parent == parent
            assert child in parent.children
            assert parent.parent is None
    
    def test_account_type_display(self, app):
        """Test account type display names"""
        with app.app_context():
            account = Account(code='1100', name='Test', type='Asset')
            assert account.get_type_display() == 'أصول'
            
            account.type = 'Liability'
            assert account.get_type_display() == 'خصوم'
            
            account.type = 'Equity'
            assert account.get_type_display() == 'رأس المال'
            
            account.type = 'Income'
            assert account.get_type_display() == 'إيرادات'
            
            account.type = 'Expense'
            assert account.get_type_display() == 'مصروفات'
    
    def test_is_leaf_account(self, app):
        """Test leaf account detection"""
        with app.app_context():
            parent = Account(code='1000', name='Parent', type='Asset')
            child = Account(code='1100', name='Child', type='Asset')
            db.session.add_all([parent, child])
            db.session.flush()
            
            child.parent_id = parent.id
            db.session.commit()
            
            assert child.is_leaf_account() is True
            assert parent.is_leaf_account() is False
    
    def test_can_be_deleted(self, app):
        """Test account deletion validation"""
        with app.app_context():
            account = Account(code='1100', name='Test', type='Asset')
            db.session.add(account)
            db.session.commit()
            
            # New account should be deletable
            assert account.can_be_deleted() is True
            
            # Add a child account
            child = Account(
                code='1110',
                name='Child',
                type='Asset',
                parent_id=account.id
            )
            db.session.add(child)
            db.session.commit()
            
            # Account with children should not be deletable
            assert account.can_be_deleted() is False


class TestAccountBalance:
    """Test account balance calculations"""
    
    def test_account_balance_calculation(self, app, test_accounts):
        """Test balance calculation for different account types"""
        with app.app_context():
            cash_account = test_accounts['cash']
            
            # Create a journal entry
            entry = JournalEntry(
                description='Test Entry',
                reference_number='TEST001'
            )
            db.session.add(entry)
            db.session.flush()
            
            # Add debit line
            debit_line = JournalLine(
                journal_id=entry.id,
                account_id=cash_account.id,
                description='Test Debit',
                amount=Decimal('1000.00'),
                dc='D'
            )
            db.session.add(debit_line)
            db.session.commit()
            
            # Check balance
            balance = cash_account.get_balance()
            assert balance == Decimal('1000.00')
    
    def test_total_debits_credits(self, app, test_accounts):
        """Test total debits and credits calculation"""
        with app.app_context():
            cash_account = test_accounts['cash']
            
            # Create journal entries
            entry1 = JournalEntry(description='Entry 1', reference_number='TEST001')
            entry2 = JournalEntry(description='Entry 2', reference_number='TEST002')
            db.session.add_all([entry1, entry2])
            db.session.flush()
            
            # Add lines
            lines = [
                JournalLine(journal_id=entry1.id, account_id=cash_account.id,
                           amount=Decimal('1000.00'), dc='D'),
                JournalLine(journal_id=entry2.id, account_id=cash_account.id,
                           amount=Decimal('500.00'), dc='C')
            ]
            db.session.add_all(lines)
            db.session.commit()
            
            assert cash_account.get_total_debits() == Decimal('1000.00')
            assert cash_account.get_total_credits() == Decimal('500.00')


class TestAccountHelpers:
    """Test account helper functions"""
    
    def test_create_default_accounts(self, app):
        """Test creating default chart of accounts"""
        with app.app_context():
            accounts = create_default_accounts()
            
            assert len(accounts) > 0
            
            # Check that we have accounts of each type
            types = set(account.type for account in accounts)
            expected_types = {'Asset', 'Liability', 'Equity', 'Income', 'Expense'}
            assert types == expected_types
            
            # Check that some specific accounts exist
            codes = [account.code for account in accounts]
            assert '1000' in codes  # Assets
            assert '2000' in codes  # Liabilities
            assert '3000' in codes  # Equity
            assert '4000' in codes  # Income
            assert '5000' in codes  # Expenses
    
    def test_validate_account_code(self, app):
        """Test account code validation"""
        with app.app_context():
            # Valid codes
            assert validate_account_code('1100')[0] is True
            assert validate_account_code('12345')[0] is True
            
            # Invalid codes
            assert validate_account_code('')[0] is False
            assert validate_account_code('ABC')[0] is False
            assert validate_account_code('12.34')[0] is False
            
            # Test uniqueness
            account = Account(code='1100', name='Test', type='Asset')
            db.session.add(account)
            db.session.commit()
            
            assert validate_account_code('1100')[0] is False
            assert validate_account_code('1100', str(account.id))[0] is True
    
    def test_suggest_account_code(self, app):
        """Test account code suggestion"""
        with app.app_context():
            # Test suggestions for different types
            asset_code = suggest_account_code('Asset')
            assert asset_code.startswith('1')
            
            liability_code = suggest_account_code('Liability')
            assert liability_code.startswith('2')
            
            equity_code = suggest_account_code('Equity')
            assert equity_code.startswith('3')
            
            income_code = suggest_account_code('Income')
            assert income_code.startswith('4')
            
            expense_code = suggest_account_code('Expense')
            assert expense_code.startswith('5')
    
    def test_calculate_trial_balance(self, app, test_accounts):
        """Test trial balance calculation"""
        with app.app_context():
            cash_account = test_accounts['cash']
            sales_account = test_accounts['sales']
            
            # Create balanced journal entry
            entry = JournalEntry(description='Sale', reference_number='SALE001')
            db.session.add(entry)
            db.session.flush()
            
            lines = [
                JournalLine(journal_id=entry.id, account_id=cash_account.id,
                           amount=Decimal('1000.00'), dc='D'),
                JournalLine(journal_id=entry.id, account_id=sales_account.id,
                           amount=Decimal('1000.00'), dc='C')
            ]
            db.session.add_all(lines)
            db.session.commit()
            
            trial_balance = calculate_trial_balance()
            
            assert trial_balance['is_balanced'] is True
            assert trial_balance['total_debits'] == trial_balance['total_credits']
            assert len(trial_balance['accounts']) >= 2


class TestAccountViews:
    """Test account views and forms"""
    
    def test_accounts_index_page(self, client, admin_user, auth):
        """Test accounts index page"""
        auth.login()
        response = client.get('/accounts/')
        assert response.status_code == 200
        assert 'دليل الحسابات' in response.get_data(as_text=True)
    
    def test_account_creation_form(self, client, admin_user, auth):
        """Test account creation form"""
        auth.login()
        
        # Get form page
        response = client.get('/accounts/new')
        assert response.status_code == 200
        
        # Submit form
        response = client.post('/accounts/create', data={
            'code': '9999',
            'name': 'حساب تجريبي',
            'name_en': 'Test Account',
            'type': 'Asset',
            'is_active': True
        }, follow_redirects=True)
        
        assert response.status_code == 200
    
    def test_account_search(self, client, admin_user, auth, test_accounts):
        """Test account search functionality"""
        auth.login()
        
        response = client.get('/accounts/?search=نقدية')
        assert response.status_code == 200
        
        response = client.get('/accounts/?type=Asset')
        assert response.status_code == 200
    
    def test_account_tree_view(self, client, admin_user, auth):
        """Test account tree view"""
        auth.login()
        
        response = client.get('/accounts/tree')
        assert response.status_code == 200
        assert 'العرض الهرمي' in response.get_data(as_text=True)
    
    def test_trial_balance_view(self, client, admin_user, auth):
        """Test trial balance view"""
        auth.login()
        
        response = client.get('/accounts/trial-balance')
        assert response.status_code == 200
        assert 'ميزان المراجعة' in response.get_data(as_text=True)
    
    def test_account_api_search(self, client, admin_user, auth, test_accounts):
        """Test account API search endpoint"""
        auth.login()
        
        response = client.get('/accounts/api/search?q=نقدية')
        assert response.status_code == 200
        
        data = response.get_json()
        assert isinstance(data, list)


class TestAccountPermissions:
    """Test account access permissions"""
    
    def test_admin_access(self, client, admin_user, auth):
        """Test admin user access to accounts"""
        auth.login('admin', 'admin123')
        
        response = client.get('/accounts/')
        assert response.status_code == 200
    
    def test_accountant_access(self, client, accountant_user, auth):
        """Test accountant user access to accounts"""
        auth.login('accountant', 'acc123')
        
        response = client.get('/accounts/')
        assert response.status_code == 200
    
    def test_employee_access(self, client, employee_user, auth):
        """Test employee user access to accounts"""
        auth.login('employee', 'emp123')
        
        # Employees should not have access to accounts
        response = client.get('/accounts/')
        assert response.status_code == 302  # Redirect to login or access denied


if __name__ == '__main__':
    pytest.main([__file__])
