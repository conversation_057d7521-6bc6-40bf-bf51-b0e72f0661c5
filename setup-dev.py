#!/usr/bin/env python3
"""
SystemTax Development Setup Script
نص إعداد بيئة التطوير لنظام SystemTax
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def print_status(message, status="info"):
    """Print colored status messages"""
    colors = {
        "info": "\033[94m",      # Blue
        "success": "\033[92m",   # Green
        "warning": "\033[93m",   # Yellow
        "error": "\033[91m",     # Red
        "reset": "\033[0m"       # Reset
    }
    
    icons = {
        "info": "🔧",
        "success": "✅",
        "warning": "⚠️",
        "error": "❌"
    }
    
    color = colors.get(status, colors["info"])
    icon = icons.get(status, "ℹ️")
    reset = colors["reset"]
    
    print(f"{color}{icon} {message}{reset}")

def check_python_version():
    """Check Python version compatibility"""
    version = sys.version_info
    print_status(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 10):
        print_status("Python 3.10+ مطلوب. يرجى تحديث Python", "error")
        return False
    
    if version.minor >= 13:
        print_status("Python 3.13+ detected - using compatible packages", "warning")
    
    return True

def create_env_file():
    """Create .env file if it doesn't exist"""
    if Path(".env").exists():
        print_status(".env file already exists", "success")
        return
    
    env_content = """# SystemTax Development Configuration
FLASK_ENV=development
FLASK_DEBUG=True
SECRET_KEY=dev-secret-key-change-in-production

# Database (SQLite for development)
DATABASE_URL=sqlite:///systemtax.db

# Redis (optional for development)
REDIS_URL=redis://localhost:6379/0

# Application Settings
APP_NAME=SystemTax
APP_VERSION=1.0.0
COMPANY_NAME=شركتك
COMPANY_TAX_ID=*********

# File Upload Settings
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# Pagination
ITEMS_PER_PAGE=20

# Tax Authority API (Egypt) - Development/Sandbox
TAX_API_BASE_URL=https://api.eta.gov.eg
TAX_API_CLIENT_ID=your-client-id
TAX_API_CLIENT_SECRET=your-client-secret
TAX_API_ENVIRONMENT=sandbox

# Email Configuration (optional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print_status("Created .env file", "success")

def install_requirements():
    """Install Python requirements"""
    print_status("Installing Python packages...")
    
    # Choose requirements file based on Python version
    version = sys.version_info
    if version.minor >= 13 and Path("requirements-py313.txt").exists():
        req_file = "requirements-py313.txt"
        print_status("Using Python 3.13+ compatible requirements", "info")
    else:
        req_file = "requirements.txt"
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", req_file
        ], check=True, capture_output=True)
        print_status("Requirements installed successfully", "success")
        return True
    except subprocess.CalledProcessError as e:
        print_status(f"Failed to install requirements: {e}", "error")
        print_status("Try: python -m pip install --upgrade pip", "warning")
        return False

def setup_database():
    """Setup SQLite database for development"""
    print_status("Setting up database...")
    
    try:
        # Create uploads directory
        Path("uploads").mkdir(exist_ok=True)
        
        # Initialize Flask app and database
        os.environ.setdefault("DATABASE_URL", "sqlite:///systemtax.db")
        
        from app import create_app, db
        from app.models.user import User
        from app.models.system_setting import SystemSetting
        
        app = create_app()
        
        with app.app_context():
            # Create all tables
            db.create_all()
            print_status("Database tables created", "success")
            
            # Create admin user if not exists
            admin = User.query.filter_by(username='admin').first()
            if not admin:
                admin = User('admin', '<EMAIL>', 'admin123', 'admin')
                db.session.add(admin)
                db.session.commit()
                print_status("Admin user created (admin/admin123)", "success")
            else:
                print_status("Admin user already exists", "info")
            
            # Initialize system settings
            try:
                SystemSetting.initialize_default_settings()
                print_status("System settings initialized", "success")
            except Exception as e:
                print_status(f"System settings warning: {e}", "warning")
        
        return True
        
    except Exception as e:
        print_status(f"Database setup error: {e}", "error")
        return False

def main():
    """Main setup function"""
    print("=" * 50)
    print("🚀 SystemTax Development Setup")
    print("   نظام محاسبي ويب متكامل")
    print("=" * 50)
    print()
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Create .env file
    create_env_file()
    
    # Install requirements
    if not install_requirements():
        print_status("Setup failed at requirements installation", "error")
        return 1
    
    # Setup database
    if not setup_database():
        print_status("Setup completed with database warnings", "warning")
    else:
        print_status("Setup completed successfully!", "success")
    
    print()
    print("🎉 SystemTax is ready!")
    print("📱 Run: python app.py")
    print("🌐 Open: http://localhost:5000")
    print("👤 Login: admin / admin123")
    print()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
