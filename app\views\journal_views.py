"""
Journal entry management views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from sqlalchemy import or_, and_, func, desc

from app import db
from app.models.journal import JournalEntry, JournalLine
from app.models.account import Account
from app.forms.journal_forms import JournalEntryForm, JournalSearchForm, LedgerForm
from app.utils.decorators import permission_required
from app.utils.helpers import get_pagination_params, generate_reference_number
from decimal import Decimal
from datetime import datetime, date

# Create blueprint
journal_bp = Blueprint('journal', __name__, url_prefix='/journal')


@journal_bp.route('/')
@login_required
@permission_required('journal')
def index():
    """Display journal entries list with search and filtering"""
    form = JournalSearchForm(request.args)
    page, per_page = get_pagination_params()
    
    # Build query
    query = JournalEntry.query
    
    # Apply search filters
    if form.search.data:
        search_term = f"%{form.search.data}%"
        query = query.filter(
            or_(
                JournalEntry.reference_number.ilike(search_term),
                JournalEntry.description.ilike(search_term),
                JournalEntry.description_en.ilike(search_term)
            )
        )
    
    if form.date_from.data:
        query = query.filter(JournalEntry.entry_date >= form.date_from.data)
    
    if form.date_to.data:
        query = query.filter(JournalEntry.entry_date <= form.date_to.data)
    
    if form.account_id.data:
        query = query.join(JournalLine).filter(
            JournalLine.account_id == form.account_id.data
        )
    
    if form.status.data:
        if form.status.data == 'posted':
            query = query.filter(JournalEntry.is_posted == True)
        elif form.status.data == 'draft':
            query = query.filter(JournalEntry.is_posted == False)
    
    # Order by entry date and creation time (newest first)
    query = query.order_by(
        desc(JournalEntry.entry_date),
        desc(JournalEntry.created_at)
    )
    
    # Paginate
    entries = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Get statistics
    stats = {
        'total_entries': JournalEntry.query.count(),
        'posted_entries': JournalEntry.query.filter_by(is_posted=True).count(),
        'draft_entries': JournalEntry.query.filter_by(is_posted=False).count(),
        'total_amount': db.session.query(func.sum(JournalLine.amount)).scalar() or Decimal('0')
    }
    
    return render_template(
        'journal/index.html',
        entries=entries,
        form=form,
        stats=stats,
        title='دفتر اليومية'
    )


@journal_bp.route('/new')
@login_required
@permission_required('journal')
def new():
    """Display form for creating new journal entry"""
    form = JournalEntryForm()
    
    # Pre-fill account if specified in query params
    account_id = request.args.get('account_id')
    
    return render_template(
        'journal/form.html',
        form=form,
        account_id=account_id,
        title='قيد جديد'
    )


@journal_bp.route('/create', methods=['POST'])
@login_required
@permission_required('journal')
def create():
    """Create new journal entry"""
    form = JournalEntryForm()
    
    if form.validate_on_submit():
        try:
            # Create journal entry
            entry = JournalEntry(
                reference_number=form.reference_number.data or generate_reference_number('JE'),
                entry_date=form.entry_date.data,
                description=form.description.data,
                description_en=form.description_en.data or None,
                created_by_id=current_user.id
            )
            
            db.session.add(entry)
            db.session.flush()  # Get the ID
            
            # Process journal lines from form data
            lines_data = []
            for key, value in request.form.items():
                if key.startswith('lines-') and key.endswith('-account_id'):
                    index = key.split('-')[1]
                    account_id = value
                    description = request.form.get(f'lines-{index}-description', '')
                    amount = request.form.get(f'lines-{index}-amount', '')
                    dc = request.form.get(f'lines-{index}-dc', '')
                    
                    if account_id and amount and dc:
                        lines_data.append({
                            'account_id': account_id,
                            'description': description,
                            'amount': Decimal(amount),
                            'dc': dc
                        })
            
            # Validate that we have at least 2 lines
            if len(lines_data) < 2:
                flash('يجب إضافة بندين على الأقل للقيد', 'error')
                return render_template('journal/form.html', form=form, title='قيد جديد')
            
            # Create journal lines
            total_debits = Decimal('0')
            total_credits = Decimal('0')
            
            for line_data in lines_data:
                line = JournalLine(
                    journal_id=entry.id,
                    account_id=line_data['account_id'],
                    description=line_data['description'] or None,
                    amount=line_data['amount'],
                    dc=line_data['dc']
                )
                
                db.session.add(line)
                
                if line_data['dc'] == 'D':
                    total_debits += line_data['amount']
                else:
                    total_credits += line_data['amount']
            
            # Check if balanced
            if abs(total_debits - total_credits) > Decimal('0.01'):
                flash('القيد غير متوازن! يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن', 'error')
                db.session.rollback()
                return render_template('journal/form.html', form=form, title='قيد جديد')
            
            # Check if should post immediately
            action = request.form.get('action', 'save')
            if action == 'save_and_post':
                entry.is_posted = True
                entry.posted_at = datetime.utcnow()
                entry.posted_by = current_user.id
            
            db.session.commit()
            
            if action == 'save_and_post':
                flash(f'تم إنشاء وترحيل القيد "{entry.reference_number}" بنجاح', 'success')
            else:
                flash(f'تم إنشاء القيد "{entry.reference_number}" كمسودة بنجاح', 'success')
            
            return redirect(url_for('journal.detail', entry_id=entry.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء القيد: {str(e)}', 'error')
    
    return render_template(
        'journal/form.html',
        form=form,
        title='قيد جديد'
    )


@journal_bp.route('/<uuid:entry_id>')
@login_required
@permission_required('journal')
def detail(entry_id):
    """Display journal entry details"""
    entry = JournalEntry.query.get_or_404(entry_id)
    
    # Get related entries (entries that affect the same accounts)
    related_entries = []
    if entry.lines:
        account_ids = [line.account_id for line in entry.lines]
        related_entries = JournalEntry.query.join(JournalLine).filter(
            JournalLine.account_id.in_(account_ids),
            JournalEntry.id != entry.id
        ).distinct().limit(5).all()
    
    return render_template(
        'journal/detail.html',
        entry=entry,
        related_entries=related_entries,
        title=f'قيد رقم {entry.reference_number}'
    )


@journal_bp.route('/<uuid:entry_id>/edit')
@login_required
@permission_required('journal')
def edit(entry_id):
    """Display form for editing journal entry"""
    entry = JournalEntry.query.get_or_404(entry_id)
    
    if entry.is_posted and not current_user.is_admin():
        flash('لا يمكن تعديل القيود المرحلة', 'error')
        return redirect(url_for('journal.detail', entry_id=entry.id))
    
    form = JournalEntryForm(obj=entry, entry=entry)
    
    return render_template(
        'journal/form.html',
        form=form,
        entry=entry,
        title=f'تعديل القيد: {entry.reference_number}'
    )


@journal_bp.route('/<uuid:entry_id>/post', methods=['POST'])
@login_required
@permission_required('journal')
def post(entry_id):
    """Post journal entry"""
    entry = JournalEntry.query.get_or_404(entry_id)
    
    if entry.is_posted:
        flash('القيد مرحل بالفعل', 'warning')
        return redirect(url_for('journal.detail', entry_id=entry.id))
    
    # Check if balanced
    total_debits = entry.get_total_debits()
    total_credits = entry.get_total_credits()
    
    if abs(total_debits - total_credits) > Decimal('0.01'):
        flash('لا يمكن ترحيل قيد غير متوازن', 'error')
        return redirect(url_for('journal.detail', entry_id=entry.id))
    
    try:
        entry.is_posted = True
        entry.posted_at = datetime.utcnow()
        entry.posted_by = current_user.id
        
        db.session.commit()
        
        flash(f'تم ترحيل القيد "{entry.reference_number}" بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء ترحيل القيد: {str(e)}', 'error')
    
    return redirect(url_for('journal.detail', entry_id=entry.id))


@journal_bp.route('/<uuid:entry_id>/unpost', methods=['POST'])
@login_required
@permission_required('journal')
def unpost(entry_id):
    """Unpost journal entry (admin only)"""
    if not current_user.is_admin():
        flash('غير مسموح لك بإلغاء ترحيل القيود', 'error')
        return redirect(url_for('journal.detail', entry_id=entry_id))
    
    entry = JournalEntry.query.get_or_404(entry_id)
    
    if not entry.is_posted:
        flash('القيد غير مرحل', 'warning')
        return redirect(url_for('journal.detail', entry_id=entry.id))
    
    try:
        entry.is_posted = False
        entry.posted_at = None
        entry.posted_by = None
        
        db.session.commit()
        
        flash(f'تم إلغاء ترحيل القيد "{entry.reference_number}" بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إلغاء ترحيل القيد: {str(e)}', 'error')
    
    return redirect(url_for('journal.detail', entry_id=entry.id))


@journal_bp.route('/<uuid:entry_id>/delete', methods=['POST'])
@login_required
@permission_required('journal')
def delete(entry_id):
    """Delete journal entry"""
    entry = JournalEntry.query.get_or_404(entry_id)
    
    if entry.is_posted and not current_user.is_admin():
        flash('لا يمكن حذف القيود المرحلة', 'error')
        return redirect(url_for('journal.detail', entry_id=entry.id))
    
    try:
        entry_ref = entry.reference_number
        
        # Delete lines first
        JournalLine.query.filter_by(journal_id=entry.id).delete()
        
        # Delete entry
        db.session.delete(entry)
        db.session.commit()
        
        flash(f'تم حذف القيد "{entry_ref}" بنجاح', 'success')
        return redirect(url_for('journal.index'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف القيد: {str(e)}', 'error')
        return redirect(url_for('journal.detail', entry_id=entry.id))


@journal_bp.route('/ledger')
@login_required
@permission_required('journal')
def ledger():
    """Display general ledger"""
    form = LedgerForm(request.args)

    # Get all accounts for dropdown
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()

    selected_account = None
    selected_account_id = form.account_id.data

    if selected_account_id:
        selected_account = Account.query.get(selected_account_id)

    # Date range
    date_from = form.date_from.data
    date_to = form.date_to.data
    posted_only = form.posted_only.data

    if selected_account:
        # Single account ledger
        query = JournalLine.query.filter_by(account_id=selected_account.id).join(JournalEntry)

        if date_from:
            query = query.filter(JournalEntry.entry_date >= date_from)
        if date_to:
            query = query.filter(JournalEntry.entry_date <= date_to)
        if posted_only:
            query = query.filter(JournalEntry.is_posted == True)

        transactions = query.order_by(JournalEntry.entry_date, JournalEntry.created_at).all()

        # Calculate running balances
        opening_balance = selected_account.get_balance(date_from) if date_from else Decimal('0')
        running_balance = opening_balance

        for transaction in transactions:
            if transaction.dc == 'D':
                running_balance += transaction.amount
            else:
                running_balance -= transaction.amount
            transaction.running_balance = running_balance

        total_debits = sum(t.amount for t in transactions if t.dc == 'D')
        total_credits = sum(t.amount for t in transactions if t.dc == 'C')
        closing_balance = running_balance

        return render_template(
            'journal/ledger.html',
            form=form,
            accounts=accounts,
            selected_account=selected_account,
            selected_account_id=selected_account_id,
            transactions=transactions,
            opening_balance=opening_balance,
            total_debits=total_debits,
            total_credits=total_credits,
            closing_balance=closing_balance,
            date_from=date_from,
            date_to=date_to,
            posted_only=posted_only,
            title='دفتر الأستاذ العام'
        )

    else:
        # All accounts summary
        account_summaries = []

        for account in accounts:
            if not account.is_leaf_account():
                continue

            query = JournalLine.query.filter_by(account_id=account.id).join(JournalEntry)

            if date_from:
                query = query.filter(JournalEntry.entry_date >= date_from)
            if date_to:
                query = query.filter(JournalEntry.entry_date <= date_to)
            if posted_only:
                query = query.filter(JournalEntry.is_posted == True)

            transactions = query.all()

            if transactions:
                total_debits = sum(t.amount for t in transactions if t.dc == 'D')
                total_credits = sum(t.amount for t in transactions if t.dc == 'C')
                balance = total_debits - total_credits

                account_summaries.append({
                    'account': account,
                    'transaction_count': len(transactions),
                    'total_debits': total_debits,
                    'total_credits': total_credits,
                    'balance': balance
                })

        return render_template(
            'journal/ledger.html',
            form=form,
            accounts=accounts,
            account_summaries=account_summaries,
            date_from=date_from,
            date_to=date_to,
            posted_only=posted_only,
            title='دفتر الأستاذ العام'
        )


# API Routes
@journal_bp.route('/<uuid:entry_id>/status')
@login_required
@permission_required('journal')
def status(entry_id):
    """API endpoint to get entry status"""
    entry = JournalEntry.query.get_or_404(entry_id)

    return jsonify({
        'is_posted': entry.is_posted,
        'posted_at': entry.posted_at.isoformat() if entry.posted_at else None,
        'last_updated': datetime.utcnow().isoformat()
    })
