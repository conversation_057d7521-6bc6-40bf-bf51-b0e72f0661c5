@echo off
REM SystemTax Quick Start Script for Windows
REM This script helps you start SystemTax quickly

echo.
echo ========================================
echo    SystemTax - نظام محاسبي ويب متكامل
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.9+ from https://python.org
    pause
    exit /b 1
)

REM Check Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python version: %PYTHON_VERSION%

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat

REM Check if requirements are installed
if not exist "venv\Lib\site-packages\flask" (
    echo 📥 Installing requirements...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Failed to install requirements
        pause
        exit /b 1
    )
)

REM Check if .env file exists
if not exist ".env" (
    echo ⚙️ Creating environment file...
    copy .env.example .env
    echo ⚠️  Please edit .env file with your settings
)

REM Check if database is initialized
python cli.py check >nul 2>&1
if errorlevel 1 (
    echo 🗄️ Initializing database...
    python cli.py init-db
    if errorlevel 1 (
        echo ❌ Failed to initialize database
        pause
        exit /b 1
    )
    
    echo 👤 Creating admin user...
    python cli.py create-admin
)

echo.
echo 🚀 Starting SystemTax...
echo 📖 Open http://localhost:8000 in your browser
echo 👤 Default login: admin / admin123
echo ⚠️  Don't forget to change the admin password!
echo.
echo Press Ctrl+C to stop the server
echo.

REM Start the application
python run.py

pause
