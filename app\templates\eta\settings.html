{% extends "base.html" %}

{% block title %}إعدادات التكامل مع مصلحة الضرائب{% endblock %}

{% block content %}
{% set settings_dict = {} %}
{% for setting in settings %}
    {% set _ = settings_dict.update({setting.setting_key: setting.setting_value}) %}
{% endfor %}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cog me-2"></i>إعدادات التكامل مع مصلحة الضرائب</h2>
                <a href="{{ url_for('eta.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-2"></i>العودة
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server me-2"></i>إعدادات الاتصال
                    </h5>
                </div>
                <div class="card-body">
                    <form id="settingsForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="eta_base_url" class="form-label">رابط API مصلحة الضرائب</label>
                                    <input type="url" class="form-control" id="eta_base_url" name="ETA_BASE_URL"
                                           value="{{ settings_dict.get('ETA_BASE_URL', '') }}"
                                           placeholder="https://api.invoicing.eta.gov.eg">
                                    <div class="form-text">الرابط الأساسي لواجهة برمجة التطبيقات</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="eta_environment" class="form-label">البيئة</label>
                                    <select class="form-select" id="eta_environment" name="ETA_ENVIRONMENT">
                                        <option value="sandbox" {{ 'selected' if settings_dict.get('ETA_ENVIRONMENT') == 'sandbox' else '' }}>
                                            بيئة التجريب (Sandbox)
                                        </option>
                                        <option value="production" {{ 'selected' if settings_dict.get('ETA_ENVIRONMENT') == 'production' else '' }}>
                                            بيئة الإنتاج (Production)
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="eta_client_id" class="form-label">معرف العميل (Client ID)</label>
                                    <input type="text" class="form-control" id="eta_client_id" name="ETA_CLIENT_ID"
                                           value="{{ settings_dict.get('ETA_CLIENT_ID', '') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="eta_client_secret" class="form-label">كلمة سر العميل (Client Secret)</label>
                                    <input type="password" class="form-control" id="eta_client_secret" name="ETA_CLIENT_SECRET"
                                           value="{{ settings_dict.get('ETA_CLIENT_SECRET', '') }}">
                                </div>
                            </div>
                        </div>

                        <hr>

                        <h6 class="mb-3"><i class="fas fa-building me-2"></i>معلومات الشركة</h6>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company_tax_id" class="form-label">الرقم الضريبي للشركة</label>
                                    <input type="text" class="form-control" id="company_tax_id" name="COMPANY_TAX_ID"
                                           value="{{ settings_dict.get('COMPANY_TAX_ID', '') }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company_activity_code" class="form-label">كود النشاط</label>
                                    <input type="text" class="form-control" id="company_activity_code" name="COMPANY_ACTIVITY_CODE"
                                           value="{{ settings_dict.get('COMPANY_ACTIVITY_CODE', '') }}">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="company_branch_id" class="form-label">معرف الفرع</label>
                                    <input type="text" class="form-control" id="company_branch_id" name="COMPANY_BRANCH_ID"
                                           value="{{ settings_dict.get('COMPANY_BRANCH_ID', '') }}"
                                           placeholder="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="eta_auto_submit" class="form-label">الإرسال التلقائي</label>
                                    <select class="form-select" id="eta_auto_submit" name="ETA_AUTO_SUBMIT">
                                        <option value="false" {{ 'selected' if settings_dict.get('ETA_AUTO_SUBMIT') != 'true' else '' }}>
                                            معطل
                                        </option>
                                        <option value="true" {{ 'selected' if settings_dict.get('ETA_AUTO_SUBMIT') == 'true' else '' }}>
                                            مفعل
                                        </option>
                                    </select>
                                    <div class="form-text">إرسال الفواتير تلقائياً عند إنشائها</div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-primary" onclick="saveSettings()">
                                <i class="fas fa-save me-2"></i>حفظ الإعدادات
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="testConnection()">
                                <i class="fas fa-plug me-2"></i>اختبار الاتصال
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>معلومات مهمة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>نصائح:</h6>
                        <ul class="mb-0">
                            <li>تأكد من صحة بيانات الاتصال قبل التفعيل</li>
                            <li>استخدم بيئة التجريب أولاً للاختبار</li>
                            <li>احتفظ بنسخة احتياطية من الإعدادات</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تحذير:</h6>
                        <p class="mb-0">لا تشارك بيانات الاتصال مع أطراف خارجية</p>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>الإعدادات الحالية
                    </h5>
                </div>
                <div class="card-body">
                    {% if settings %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>المفتاح</th>
                                        <th>القيمة</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for setting in settings %}
                                    <tr>
                                        <td><small>{{ setting.setting_key }}</small></td>
                                        <td>
                                            {% if setting.is_encrypted %}
                                                <small class="text-muted">***مشفر***</small>
                                            {% else %}
                                                <small>{{ setting.setting_value[:20] }}{{ '...' if setting.setting_value|length > 20 else '' }}</small>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted">لا توجد إعدادات محفوظة</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function saveSettings() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    btn.disabled = true;

    // Collect form data
    const formData = new FormData(document.getElementById('settingsForm'));
    const settings = {};

    for (let [key, value] of formData.entries()) {
        settings[key] = value;
    }

    fetch('{{ url_for("eta.update_settings") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('success', data.message);
        } else {
            showAlert('danger', data.message);
        }
    })
    .catch(error => {
        showAlert('danger', 'خطأ في حفظ الإعدادات: ' + error.message);
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function testConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';
    btn.disabled = true;

    fetch('{{ url_for("eta.test_connection") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            showAlert('danger', 'خطأ في الاتصال: ' + error.message);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}
</script>
{% endblock %}
