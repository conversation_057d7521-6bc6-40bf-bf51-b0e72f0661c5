"""
Tests for invoice functionality
"""

import pytest
from decimal import Decimal
from datetime import date, datetime, timedelta
from app import db
from app.models.invoice import Invoice, InvoiceLine
from app.models.customer import Customer
from app.models.user import User


class TestInvoiceModel:
    """Test Invoice model functionality"""
    
    def test_invoice_creation(self, app, admin_user, test_customer):
        """Test creating a new invoice"""
        with app.app_context():
            invoice = Invoice(
                invoice_number='INV-2024-001',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                currency='EGP',
                status='draft',
                created_by_id=admin_user.id
            )
            db.session.add(invoice)
            db.session.commit()
            
            assert invoice.id is not None
            assert invoice.invoice_number == 'INV-2024-001'
            assert invoice.customer == test_customer
            assert invoice.status == 'draft'
            assert invoice.created_by == admin_user
    
    def test_invoice_with_lines(self, app, admin_user, test_customer):
        """Test invoice with lines"""
        with app.app_context():
            # Create invoice
            invoice = Invoice(
                invoice_number='INV-2024-002',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                currency='EGP',
                status='draft',
                created_by_id=admin_user.id
            )
            db.session.add(invoice)
            db.session.flush()
            
            # Create invoice lines
            line1 = InvoiceLine(
                invoice_id=invoice.id,
                description='خدمة استشارية',
                quantity=Decimal('2'),
                unit_price=Decimal('500.00'),
                discount_rate=Decimal('0'),
                tax_rate=Decimal('14'),
                discount_amount=Decimal('0'),
                tax_amount=Decimal('140.00'),
                total_amount=Decimal('1140.00')
            )
            
            line2 = InvoiceLine(
                invoice_id=invoice.id,
                description='منتج برمجي',
                quantity=Decimal('1'),
                unit_price=Decimal('1000.00'),
                discount_rate=Decimal('10'),
                tax_rate=Decimal('14'),
                discount_amount=Decimal('100.00'),
                tax_amount=Decimal('126.00'),
                total_amount=Decimal('1026.00')
            )
            
            db.session.add_all([line1, line2])
            
            # Update invoice totals
            invoice.subtotal_amount = Decimal('2000.00')
            invoice.discount_amount = Decimal('100.00')
            invoice.tax_amount = Decimal('266.00')
            invoice.total_amount = Decimal('2166.00')
            
            db.session.commit()
            
            # Test relationships and calculations
            assert len(invoice.lines) == 2
            assert invoice.subtotal_amount == Decimal('2000.00')
            assert invoice.total_amount == Decimal('2166.00')
    
    def test_invoice_status_display(self, app, admin_user, test_customer):
        """Test invoice status display methods"""
        with app.app_context():
            invoice = Invoice(
                invoice_number='INV-2024-003',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                status='pending',
                created_by_id=admin_user.id
            )
            
            assert invoice.get_status_display() == 'معلقة'
            
            invoice.status = 'paid'
            assert invoice.get_status_display() == 'مدفوعة'
    
    def test_invoice_can_be_edited(self, app, admin_user, test_customer):
        """Test invoice edit permissions"""
        with app.app_context():
            # Draft invoice can be edited
            draft_invoice = Invoice(
                invoice_number='INV-2024-004',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                status='draft',
                created_by_id=admin_user.id
            )
            assert draft_invoice.can_be_edited() is True
            
            # Paid invoice cannot be edited
            paid_invoice = Invoice(
                invoice_number='INV-2024-005',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                status='paid',
                created_by_id=admin_user.id
            )
            assert paid_invoice.can_be_edited() is False
    
    def test_invoice_can_be_deleted(self, app, admin_user, test_customer):
        """Test invoice deletion permissions"""
        with app.app_context():
            # Draft invoice can be deleted
            draft_invoice = Invoice(
                invoice_number='INV-2024-006',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                status='draft',
                created_by_id=admin_user.id
            )
            assert draft_invoice.can_be_deleted() is True
            
            # Paid invoice cannot be deleted
            paid_invoice = Invoice(
                invoice_number='INV-2024-007',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                status='paid',
                created_by_id=admin_user.id
            )
            assert paid_invoice.can_be_deleted() is False


class TestInvoiceLineModel:
    """Test InvoiceLine model functionality"""
    
    def test_invoice_line_creation(self, app, admin_user, test_customer):
        """Test creating invoice lines"""
        with app.app_context():
            # Create invoice first
            invoice = Invoice(
                invoice_number='INV-2024-008',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                status='draft',
                created_by_id=admin_user.id
            )
            db.session.add(invoice)
            db.session.flush()
            
            # Create line
            line = InvoiceLine(
                invoice_id=invoice.id,
                description='خدمة تطوير',
                quantity=Decimal('5'),
                unit_price=Decimal('200.00'),
                discount_rate=Decimal('5'),
                tax_rate=Decimal('14'),
                discount_amount=Decimal('50.00'),
                tax_amount=Decimal('133.00'),
                total_amount=Decimal('1083.00')
            )
            db.session.add(line)
            db.session.commit()
            
            assert line.id is not None
            assert line.invoice == invoice
            assert line.quantity == Decimal('5')
            assert line.unit_price == Decimal('200.00')
    
    def test_invoice_line_calculations(self, app, admin_user, test_customer):
        """Test invoice line amount calculations"""
        with app.app_context():
            invoice = Invoice(
                invoice_number='INV-2024-009',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                status='draft',
                created_by_id=admin_user.id
            )
            db.session.add(invoice)
            db.session.flush()
            
            # Test calculation: 3 * 100 = 300, discount 10% = 30, after discount = 270, tax 14% = 37.8, total = 307.8
            line = InvoiceLine(
                invoice_id=invoice.id,
                description='منتج اختبار',
                quantity=Decimal('3'),
                unit_price=Decimal('100.00'),
                discount_rate=Decimal('10'),
                tax_rate=Decimal('14')
            )
            
            # Calculate amounts
            subtotal = line.quantity * line.unit_price  # 300
            discount_amount = subtotal * (line.discount_rate / 100)  # 30
            after_discount = subtotal - discount_amount  # 270
            tax_amount = after_discount * (line.tax_rate / 100)  # 37.8
            total_amount = after_discount + tax_amount  # 307.8
            
            line.discount_amount = discount_amount
            line.tax_amount = tax_amount
            line.total_amount = total_amount
            
            db.session.add(line)
            db.session.commit()
            
            assert line.discount_amount == Decimal('30.00')
            assert line.tax_amount == Decimal('37.80')
            assert line.total_amount == Decimal('307.80')


class TestInvoiceViews:
    """Test invoice views and forms"""
    
    def test_invoices_index_page(self, client, admin_user, auth):
        """Test invoices index page"""
        auth.login()
        response = client.get('/invoices/')
        assert response.status_code == 200
        assert 'الفواتير الإلكترونية' in response.get_data(as_text=True)
    
    def test_invoice_creation_form(self, client, admin_user, auth):
        """Test invoice creation form"""
        auth.login()
        
        # Get form page
        response = client.get('/invoices/new')
        assert response.status_code == 200
        assert 'فاتورة جديدة' in response.get_data(as_text=True)
    
    def test_invoice_creation(self, client, admin_user, auth, test_customer):
        """Test creating invoice through form"""
        auth.login()
        
        # Submit invoice form
        response = client.post('/invoices/create', data={
            'invoice_number': 'TEST-001',
            'customer_id': str(test_customer.id),
            'invoice_date': date.today().isoformat(),
            'currency': 'EGP',
            'description': 'فاتورة اختبار',
            'lines-0-description': 'خدمة اختبار',
            'lines-0-quantity': '1',
            'lines-0-unit_price': '1000.00',
            'lines-0-discount_rate': '0',
            'lines-0-tax_rate': '14',
            'action': 'save'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        
        # Check if invoice was created
        invoice = Invoice.query.filter_by(invoice_number='TEST-001').first()
        assert invoice is not None
        assert invoice.customer == test_customer
    
    def test_invoice_search(self, client, admin_user, auth, test_customer):
        """Test invoice search functionality"""
        auth.login()
        
        # Create test invoice
        with client.application.app_context():
            invoice = Invoice(
                invoice_number='SEARCH-001',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                status='draft',
                created_by_id=admin_user.id
            )
            db.session.add(invoice)
            db.session.commit()
        
        response = client.get('/invoices/?search=SEARCH-001')
        assert response.status_code == 200
        assert 'SEARCH-001' in response.get_data(as_text=True)
    
    def test_invoice_detail_page(self, client, admin_user, auth, test_customer):
        """Test invoice detail page"""
        auth.login()
        
        # Create test invoice
        with client.application.app_context():
            invoice = Invoice(
                invoice_number='DETAIL-001',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                status='draft',
                created_by_id=admin_user.id
            )
            db.session.add(invoice)
            db.session.commit()
            invoice_id = invoice.id
        
        response = client.get(f'/invoices/{invoice_id}')
        assert response.status_code == 200
        assert 'DETAIL-001' in response.get_data(as_text=True)


class TestInvoicePermissions:
    """Test invoice access permissions"""
    
    def test_admin_access(self, client, admin_user, auth):
        """Test admin user access to invoices"""
        auth.login('admin', 'admin123')
        
        response = client.get('/invoices/')
        assert response.status_code == 200
    
    def test_accountant_access(self, client, accountant_user, auth):
        """Test accountant user access to invoices"""
        auth.login('accountant', 'acc123')
        
        response = client.get('/invoices/')
        assert response.status_code == 200
    
    def test_employee_access(self, client, employee_user, auth):
        """Test employee user access to invoices"""
        auth.login('employee', 'emp123')
        
        # Employees should not have access to invoices
        response = client.get('/invoices/')
        assert response.status_code == 302  # Redirect to login or access denied


class TestInvoiceBusinessLogic:
    """Test invoice business logic"""
    
    def test_invoice_totals_calculation(self, app, admin_user, test_customer):
        """Test invoice totals calculation"""
        with app.app_context():
            invoice = Invoice(
                invoice_number='CALC-001',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                status='draft',
                created_by_id=admin_user.id
            )
            db.session.add(invoice)
            db.session.flush()
            
            # Add multiple lines with different calculations
            lines = [
                {
                    'description': 'خدمة 1',
                    'quantity': Decimal('2'),
                    'unit_price': Decimal('500.00'),
                    'discount_rate': Decimal('0'),
                    'tax_rate': Decimal('14')
                },
                {
                    'description': 'خدمة 2',
                    'quantity': Decimal('1'),
                    'unit_price': Decimal('1000.00'),
                    'discount_rate': Decimal('10'),
                    'tax_rate': Decimal('14')
                }
            ]
            
            total_subtotal = Decimal('0')
            total_discount = Decimal('0')
            total_tax = Decimal('0')
            
            for line_data in lines:
                subtotal = line_data['quantity'] * line_data['unit_price']
                discount = subtotal * (line_data['discount_rate'] / 100)
                after_discount = subtotal - discount
                tax = after_discount * (line_data['tax_rate'] / 100)
                total = after_discount + tax
                
                line = InvoiceLine(
                    invoice_id=invoice.id,
                    description=line_data['description'],
                    quantity=line_data['quantity'],
                    unit_price=line_data['unit_price'],
                    discount_rate=line_data['discount_rate'],
                    tax_rate=line_data['tax_rate'],
                    discount_amount=discount,
                    tax_amount=tax,
                    total_amount=total
                )
                db.session.add(line)
                
                total_subtotal += subtotal
                total_discount += discount
                total_tax += tax
            
            # Update invoice totals
            invoice.subtotal_amount = total_subtotal
            invoice.discount_amount = total_discount
            invoice.tax_amount = total_tax
            invoice.total_amount = total_subtotal - total_discount + total_tax
            
            db.session.commit()
            
            # Verify calculations
            assert invoice.subtotal_amount == Decimal('2000.00')  # 1000 + 1000
            assert invoice.discount_amount == Decimal('100.00')   # 0 + 100
            assert invoice.tax_amount == Decimal('266.00')        # 140 + 126
            assert invoice.total_amount == Decimal('2166.00')     # 2000 - 100 + 266
    
    def test_invoice_due_date_calculation(self, app, admin_user, test_customer):
        """Test invoice due date calculation based on customer payment terms"""
        with app.app_context():
            # Set customer payment terms
            test_customer.payment_terms = 30
            db.session.commit()
            
            invoice_date = date.today()
            expected_due_date = invoice_date + timedelta(days=30)
            
            invoice = Invoice(
                invoice_number='DUE-001',
                customer_id=test_customer.id,
                invoice_date=invoice_date,
                due_date=expected_due_date,
                status='draft',
                created_by_id=admin_user.id
            )
            db.session.add(invoice)
            db.session.commit()
            
            assert invoice.due_date == expected_due_date


if __name__ == '__main__':
    pytest.main([__file__])
