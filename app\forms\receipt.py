"""
Receipt forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, DateField, SelectField, DecimalField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from datetime import date
from app.models.customer import Customer

class ReceiptForm(FlaskForm):
    customer_id = SelectField('العميل', coerce=str, validators=[DataRequired()])
    receipt_date = DateField('تاريخ الإيصال', validators=[DataRequired()], default=date.today)
    amount_received = DecimalField('المبلغ المستلم', validators=[
        DataRequired(), 
        NumberRange(min=0.01, message='المبلغ يجب أن يكون أكبر من صفر')
    ], places=2)
    payment_method = SelectField('طريقة الدفع', choices=[
        ('cash', 'نقدي'),
        ('bank', 'تحويل بنكي'),
        ('check', 'شيك'),
        ('card', 'بطاقة ائتمان')
    ], default='cash', validators=[DataRequired()])
    reference_number = StringField('رقم المرجع', validators=[Length(max=50)])
    notes = TextAreaField('ملاحظات', validators=[Length(max=1000)])
    submit = SubmitField('حفظ الإيصال')
    
    def __init__(self, *args, **kwargs):
        super(ReceiptForm, self).__init__(*args, **kwargs)
        
        # Populate customer choices
        self.customer_id.choices = [('', 'اختر العميل')]
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        
        for customer in customers:
            self.customer_id.choices.append((
                customer.id, 
                customer.name
            ))

class ReceiptSearchForm(FlaskForm):
    search = StringField('البحث', validators=[Length(max=100)])
    customer_id = SelectField('العميل', coerce=str)
    payment_method = SelectField('طريقة الدفع', choices=[
        ('', 'جميع الطرق'),
        ('cash', 'نقدي'),
        ('bank', 'تحويل بنكي'),
        ('check', 'شيك'),
        ('card', 'بطاقة ائتمان')
    ])
    date_from = DateField('من تاريخ')
    date_to = DateField('إلى تاريخ')
    submit = SubmitField('بحث')
    
    def __init__(self, *args, **kwargs):
        super(ReceiptSearchForm, self).__init__(*args, **kwargs)
        
        # Populate customer choices
        self.customer_id.choices = [('', 'جميع العملاء')]
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        
        for customer in customers:
            self.customer_id.choices.append((
                customer.id, 
                customer.name
            ))

class ReceiptAllocationForm(FlaskForm):
    """Form for allocating receipt amounts to invoices"""
    invoice_id = SelectField('الفاتورة', coerce=str, validators=[DataRequired()])
    allocated_amount = DecimalField('المبلغ المخصص', validators=[
        DataRequired(), 
        NumberRange(min=0.01, message='المبلغ يجب أن يكون أكبر من صفر')
    ], places=2)
    submit = SubmitField('تخصيص المبلغ')
    
    def __init__(self, customer_id=None, *args, **kwargs):
        super(ReceiptAllocationForm, self).__init__(*args, **kwargs)
        
        # Populate invoice choices for the specific customer
        self.invoice_id.choices = [('', 'اختر الفاتورة')]
        
        if customer_id:
            from app.models.invoice import Invoice
            invoices = Invoice.query.filter_by(
                customer_id=customer_id
            ).filter(
                Invoice.status.in_(['sent', 'draft'])
            ).order_by(Invoice.issue_date.desc()).all()
            
            for invoice in invoices:
                outstanding = invoice.get_outstanding_amount()
                if outstanding > 0:
                    self.invoice_id.choices.append((
                        invoice.id, 
                        f"{invoice.invoice_number} - {outstanding:.2f} ج.م"
                    ))
