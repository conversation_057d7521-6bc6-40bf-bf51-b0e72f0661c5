"""
Tests for report functionality
"""

import pytest
from decimal import Decimal
from datetime import date, datetime, timedelta
from app import db
from app.models.account import Account
from app.models.journal import JournalEntry, JournalLine
from app.models.invoice import Invoice
from app.models.receipt import Receipt
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.models.user import User


class TestReportViews:
    """Test report views and functionality"""
    
    def test_reports_index_page(self, client, admin_user, auth):
        """Test reports index page"""
        auth.login()
        response = client.get('/reports/')
        assert response.status_code == 200
        assert 'التقارير المالية' in response.get_data(as_text=True)
    
    def test_trial_balance_page(self, client, admin_user, auth):
        """Test trial balance page"""
        auth.login()
        response = client.get('/reports/trial-balance')
        assert response.status_code == 200
        assert 'ميزان المراجعة' in response.get_data(as_text=True)
    
    def test_income_statement_page(self, client, admin_user, auth):
        """Test income statement page"""
        auth.login()
        response = client.get('/reports/income-statement')
        assert response.status_code == 200
        assert 'قائمة الدخل' in response.get_data(as_text=True)
    
    def test_balance_sheet_page(self, client, admin_user, auth):
        """Test balance sheet page"""
        auth.login()
        response = client.get('/reports/balance-sheet')
        assert response.status_code == 200
        assert 'الميزانية العمومية' in response.get_data(as_text=True)
    
    def test_tax_report_page(self, client, admin_user, auth):
        """Test tax report page"""
        auth.login()
        response = client.get('/reports/tax-report')
        assert response.status_code == 200
        assert 'التقرير الضريبي' in response.get_data(as_text=True)


class TestTrialBalanceReport:
    """Test Trial Balance report generation"""
    
    def test_trial_balance_with_data(self, client, admin_user, auth, sample_accounts):
        """Test trial balance generation with sample data"""
        auth.login()
        
        with client.application.app_context():
            # Create some journal entries for testing
            cash_account = Account.query.filter_by(code='1001').first()
            revenue_account = Account.query.filter_by(code='4001').first()
            
            if cash_account and revenue_account:
                # Create a journal entry
                entry = JournalEntry(
                    entry_number='JE-001',
                    entry_date=date.today(),
                    description='Test entry for trial balance',
                    created_by_id=admin_user.id
                )
                db.session.add(entry)
                db.session.flush()
                
                # Add lines
                debit_line = JournalEntryLine(
                    entry_id=entry.id,
                    account_id=cash_account.id,
                    description='Cash received',
                    debit_amount=Decimal('1000.00'),
                    credit_amount=Decimal('0.00')
                )
                
                credit_line = JournalEntryLine(
                    entry_id=entry.id,
                    account_id=revenue_account.id,
                    description='Revenue earned',
                    debit_amount=Decimal('0.00'),
                    credit_amount=Decimal('1000.00')
                )
                
                db.session.add_all([debit_line, credit_line])
                
                # Post the entry
                entry.is_posted = True
                entry.posted_at = datetime.utcnow()
                
                db.session.commit()
        
        # Test trial balance with parameters
        response = client.get('/reports/trial-balance', query_string={
            'date_from': date.today().isoformat(),
            'date_to': date.today().isoformat(),
            'show_zero_balances': 'true'
        })
        
        assert response.status_code == 200
        data = response.get_data(as_text=True)
        assert 'ميزان المراجعة' in data
    
    def test_trial_balance_filtering(self, client, admin_user, auth):
        """Test trial balance with account type filtering"""
        auth.login()
        
        response = client.get('/reports/trial-balance', query_string={
            'date_from': date.today().isoformat(),
            'date_to': date.today().isoformat(),
            'account_type': 'Asset'
        })
        
        assert response.status_code == 200
    
    def test_trial_balance_date_validation(self, client, admin_user, auth):
        """Test trial balance with invalid date range"""
        auth.login()
        
        # Test with end date before start date
        response = client.get('/reports/trial-balance', query_string={
            'date_from': date.today().isoformat(),
            'date_to': (date.today() - timedelta(days=1)).isoformat()
        })
        
        assert response.status_code == 200
        # Should show validation error


class TestIncomeStatementReport:
    """Test Income Statement report generation"""
    
    def test_income_statement_with_data(self, client, admin_user, auth, sample_accounts):
        """Test income statement generation with sample data"""
        auth.login()
        
        with client.application.app_context():
            # Create revenue and expense entries
            revenue_account = Account.query.filter_by(type='Income').first()
            expense_account = Account.query.filter_by(type='Expense').first()
            cash_account = Account.query.filter_by(type='Asset').first()
            
            if revenue_account and expense_account and cash_account:
                # Revenue entry
                revenue_entry = JournalEntry(
                    entry_number='JE-REV-001',
                    entry_date=date.today(),
                    description='Revenue entry',
                    created_by_id=admin_user.id,
                    is_posted=True,
                    posted_at=datetime.utcnow()
                )
                db.session.add(revenue_entry)
                db.session.flush()
                
                # Revenue lines
                db.session.add_all([
                    JournalEntryLine(
                        entry_id=revenue_entry.id,
                        account_id=cash_account.id,
                        description='Cash from sales',
                        debit_amount=Decimal('5000.00'),
                        credit_amount=Decimal('0.00')
                    ),
                    JournalEntryLine(
                        entry_id=revenue_entry.id,
                        account_id=revenue_account.id,
                        description='Sales revenue',
                        debit_amount=Decimal('0.00'),
                        credit_amount=Decimal('5000.00')
                    )
                ])
                
                # Expense entry
                expense_entry = JournalEntry(
                    entry_number='JE-EXP-001',
                    entry_date=date.today(),
                    description='Expense entry',
                    created_by_id=admin_user.id,
                    is_posted=True,
                    posted_at=datetime.utcnow()
                )
                db.session.add(expense_entry)
                db.session.flush()
                
                # Expense lines
                db.session.add_all([
                    JournalEntryLine(
                        entry_id=expense_entry.id,
                        account_id=expense_account.id,
                        description='Office expenses',
                        debit_amount=Decimal('1000.00'),
                        credit_amount=Decimal('0.00')
                    ),
                    JournalEntryLine(
                        entry_id=expense_entry.id,
                        account_id=cash_account.id,
                        description='Cash paid',
                        debit_amount=Decimal('0.00'),
                        credit_amount=Decimal('1000.00')
                    )
                ])
                
                db.session.commit()
        
        # Test income statement
        response = client.get('/reports/income-statement', query_string={
            'date_from': date.today().isoformat(),
            'date_to': date.today().isoformat(),
            'show_details': 'true'
        })
        
        assert response.status_code == 200
        data = response.get_data(as_text=True)
        assert 'قائمة الدخل' in data
    
    def test_income_statement_with_comparison(self, client, admin_user, auth):
        """Test income statement with comparison period"""
        auth.login()
        
        response = client.get('/reports/income-statement', query_string={
            'date_from': date.today().replace(day=1).isoformat(),
            'date_to': date.today().isoformat(),
            'comparison_period': 'previous_month'
        })
        
        assert response.status_code == 200


class TestReportPermissions:
    """Test report access permissions"""
    
    def test_admin_access(self, client, admin_user, auth):
        """Test admin user access to reports"""
        auth.login('admin', 'admin123')
        
        response = client.get('/reports/')
        assert response.status_code == 200
    
    def test_accountant_access(self, client, accountant_user, auth):
        """Test accountant user access to reports"""
        auth.login('accountant', 'acc123')
        
        response = client.get('/reports/')
        assert response.status_code == 200
    
    def test_employee_access(self, client, employee_user, auth):
        """Test employee user access to reports"""
        auth.login('employee', 'emp123')
        
        # Employees should not have access to reports
        response = client.get('/reports/')
        assert response.status_code == 302  # Redirect to login or access denied


class TestReportForms:
    """Test report form validation"""
    
    def test_trial_balance_form_validation(self, app):
        """Test trial balance form validation"""
        from app.forms.report_forms import TrialBalanceForm
        
        with app.app_context():
            # Valid form
            form_data = {
                'date_from': date.today(),
                'date_to': date.today(),
                'account_type': 'Asset',
                'show_zero_balances': True
            }
            form = TrialBalanceForm(data=form_data)
            assert form.validate()
            
            # Invalid date range
            form_data['date_to'] = date.today() - timedelta(days=1)
            form = TrialBalanceForm(data=form_data)
            assert not form.validate()
    
    def test_income_statement_form_validation(self, app):
        """Test income statement form validation"""
        from app.forms.report_forms import IncomeStatementForm
        
        with app.app_context():
            # Valid form
            form_data = {
                'date_from': date.today().replace(day=1),
                'date_to': date.today(),
                'comparison_period': 'previous_month',
                'show_details': True
            }
            form = IncomeStatementForm(data=form_data)
            assert form.validate()
    
    def test_tax_report_form_validation(self, app):
        """Test tax report form validation"""
        from app.forms.report_forms import TaxReportForm
        
        with app.app_context():
            # Valid form
            form_data = {
                'date_from': date.today().replace(day=1),
                'date_to': date.today(),
                'report_type': 'monthly',
                'include_details': True
            }
            form = TaxReportForm(data=form_data)
            assert form.validate()
            
            # Missing report type
            form_data['report_type'] = ''
            form = TaxReportForm(data=form_data)
            assert not form.validate()


class TestReportHelperFunctions:
    """Test report helper functions"""
    
    def test_revenue_calculation(self, app, admin_user, sample_accounts):
        """Test revenue calculation for period"""
        from app.views.report_views import get_revenue_for_period
        
        with app.app_context():
            # Create revenue entry
            revenue_account = Account.query.filter_by(type='Income').first()
            cash_account = Account.query.filter_by(type='Asset').first()
            
            if revenue_account and cash_account:
                entry = JournalEntry(
                    entry_number='REV-TEST-001',
                    entry_date=date.today(),
                    description='Test revenue',
                    created_by_id=admin_user.id,
                    is_posted=True,
                    posted_at=datetime.utcnow()
                )
                db.session.add(entry)
                db.session.flush()
                
                db.session.add_all([
                    JournalEntryLine(
                        entry_id=entry.id,
                        account_id=cash_account.id,
                        description='Cash',
                        debit_amount=Decimal('2000.00'),
                        credit_amount=Decimal('0.00')
                    ),
                    JournalEntryLine(
                        entry_id=entry.id,
                        account_id=revenue_account.id,
                        description='Revenue',
                        debit_amount=Decimal('0.00'),
                        credit_amount=Decimal('2000.00')
                    )
                ])
                
                db.session.commit()
                
                # Test revenue calculation
                revenue = get_revenue_for_period(date.today(), date.today())
                assert revenue >= Decimal('2000.00')
    
    def test_expense_calculation(self, app, admin_user, sample_accounts):
        """Test expense calculation for period"""
        from app.views.report_views import get_expenses_for_period
        
        with app.app_context():
            # Create expense entry
            expense_account = Account.query.filter_by(type='Expense').first()
            cash_account = Account.query.filter_by(type='Asset').first()
            
            if expense_account and cash_account:
                entry = JournalEntry(
                    entry_number='EXP-TEST-001',
                    entry_date=date.today(),
                    description='Test expense',
                    created_by_id=admin_user.id,
                    is_posted=True,
                    posted_at=datetime.utcnow()
                )
                db.session.add(entry)
                db.session.flush()
                
                db.session.add_all([
                    JournalEntryLine(
                        entry_id=entry.id,
                        account_id=expense_account.id,
                        description='Expense',
                        debit_amount=Decimal('500.00'),
                        credit_amount=Decimal('0.00')
                    ),
                    JournalEntryLine(
                        entry_id=entry.id,
                        account_id=cash_account.id,
                        description='Cash',
                        debit_amount=Decimal('0.00'),
                        credit_amount=Decimal('500.00')
                    )
                ])
                
                db.session.commit()
                
                # Test expense calculation
                expenses = get_expenses_for_period(date.today(), date.today())
                assert expenses >= Decimal('500.00')
    
    def test_financial_ratios_calculation(self, app):
        """Test financial ratios calculation"""
        from app.views.report_views import calculate_financial_ratios
        
        with app.app_context():
            # Sample income statement data
            income_data = {
                'total_revenue': Decimal('10000.00'),
                'total_operating_expenses': Decimal('6000.00'),
                'operating_profit': Decimal('4000.00'),
                'net_income': Decimal('3500.00')
            }
            
            ratios = calculate_financial_ratios(income_data)
            
            assert ratios['net_profit_margin'] == 35.0  # 3500/10000 * 100
            assert ratios['operating_profit_margin'] == 40.0  # 4000/10000 * 100
            assert ratios['expense_ratio'] == 60.0  # 6000/10000 * 100


if __name__ == '__main__':
    pytest.main([__file__])
