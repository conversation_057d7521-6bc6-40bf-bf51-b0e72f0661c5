"""
Basic application tests for SystemTax
"""

import pytest
from app import create_app, db
from app.models.user import User
from app.models.system_setting import SystemSetting

@pytest.fixture
def app():
    """Create application for testing"""
    app = create_app('testing')
    
    with app.app_context():
        db.create_all()
        
        # Initialize basic settings
        SystemSetting.initialize_default_settings()
        
        yield app
        
        db.session.remove()
        db.drop_all()

@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()

@pytest.fixture
def admin_user(app):
    """Create admin user for testing"""
    with app.app_context():
        user = User(
            username='testadmin',
            email='<EMAIL>',
            password='testpass123',
            role='admin'
        )
        db.session.add(user)
        db.session.commit()
        return user

def test_app_creation(app):
    """Test that app is created successfully"""
    assert app is not None
    assert app.config['TESTING'] is True

def test_login_page(client):
    """Test login page loads"""
    response = client.get('/auth/login')
    assert response.status_code == 200
    assert 'تسجيل الدخول' in response.get_data(as_text=True)

def test_login_redirect(client):
    """Test that protected pages redirect to login"""
    response = client.get('/')
    assert response.status_code == 302
    assert '/auth/login' in response.location

def test_admin_login(client, admin_user):
    """Test admin user login"""
    response = client.post('/auth/login', data={
        'username': 'testadmin',
        'password': 'testpass123',
        'csrf_token': 'test'  # In real tests, you'd need proper CSRF token
    }, follow_redirects=True)
    
    # Should redirect to dashboard after successful login
    assert response.status_code == 200

def test_user_creation(app):
    """Test user model creation"""
    with app.app_context():
        user = User(
            username='testuser',
            email='<EMAIL>',
            password='password123',
            role='employee'
        )
        
        assert user.username == 'testuser'
        assert user.email == '<EMAIL>'
        assert user.role == 'employee'
        assert user.check_password('password123')
        assert not user.check_password('wrongpassword')

def test_system_settings(app):
    """Test system settings functionality"""
    with app.app_context():
        # Test setting a value
        SystemSetting.set_value('test_key', 'test_value', 'Test description')
        
        # Test getting the value
        value = SystemSetting.get_value('test_key')
        assert value == 'test_value'
        
        # Test default value
        default_value = SystemSetting.get_value('nonexistent_key', 'default')
        assert default_value == 'default'

def test_company_info_settings(app):
    """Test company information settings"""
    with app.app_context():
        company_info = SystemSetting.get_company_info()
        
        assert 'name' in company_info
        assert 'tax_id' in company_info
        assert 'address' in company_info
        assert 'phone' in company_info
        assert 'email' in company_info

def test_invoice_settings(app):
    """Test invoice settings"""
    with app.app_context():
        invoice_settings = SystemSetting.get_invoice_settings()
        
        assert 'prefix' in invoice_settings
        assert 'default_tax_rate' in invoice_settings
        assert 'currency_code' in invoice_settings
        assert 'currency_symbol' in invoice_settings

def test_user_permissions(app):
    """Test user permission system"""
    with app.app_context():
        # Admin user
        admin = User(username='admin', email='<EMAIL>', 
                    password='pass', role='admin')
        assert admin.is_admin()
        assert admin.can_access('accounts')
        assert admin.can_access('journal')
        assert admin.can_access('reports')
        
        # Accountant user
        accountant = User(username='accountant', email='<EMAIL>',
                         password='pass', role='accountant')
        assert not accountant.is_admin()
        assert accountant.can_access('accounts')
        assert accountant.can_access('journal')
        assert accountant.can_access('reports')
        
        # Employee user
        employee = User(username='employee', email='<EMAIL>',
                       password='pass', role='employee')
        assert not employee.is_admin()
        assert employee.can_access('customers')
        assert employee.can_access('invoices')
        assert not employee.can_access('journal')

def test_password_hashing(app):
    """Test password hashing functionality"""
    with app.app_context():
        user = User(username='test', email='<EMAIL>', 
                   password='mypassword', role='employee')
        
        # Password should be hashed
        assert user.password_hash != 'mypassword'
        
        # Should be able to verify correct password
        assert user.check_password('mypassword')
        
        # Should reject incorrect password
        assert not user.check_password('wrongpassword')

def test_user_display_name(app):
    """Test user display name functionality"""
    with app.app_context():
        user = User(username='testuser', email='<EMAIL>',
                   password='pass', role='employee')
        
        # Should return username if no full name
        assert user.get_display_name() == 'testuser'

def test_database_connection(app):
    """Test database connection"""
    with app.app_context():
        # Should be able to query database
        result = db.session.execute('SELECT 1').scalar()
        assert result == 1

def test_error_pages(client):
    """Test error page handling"""
    # Test 404 page
    response = client.get('/nonexistent-page')
    assert response.status_code == 404

def test_api_endpoints_require_auth(client):
    """Test that API endpoints require authentication"""
    api_endpoints = [
        '/customers/api/search',
        '/accounts/api/search',
        '/invoices/api/search'
    ]
    
    for endpoint in api_endpoints:
        response = client.get(endpoint)
        assert response.status_code == 302  # Redirect to login

def test_static_files(client):
    """Test static file serving"""
    # Test CSS file
    response = client.get('/static/css/style.css')
    assert response.status_code == 200
    
    # Test JS file
    response = client.get('/static/js/app.js')
    assert response.status_code == 200

if __name__ == '__main__':
    pytest.main([__file__])
