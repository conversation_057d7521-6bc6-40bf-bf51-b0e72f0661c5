<!-- Enhanced Card Component -->
<!-- Usage: {% include 'components/enhanced_card.html' with context %} -->

{% macro enhanced_card(title, content, card_type='default', icon=None, actions=None, collapsible=False, card_id=None) %}
<div class="card enhanced-card {{ 'card-' + card_type if card_type != 'default' else '' }} fade-in" 
     {% if card_id %}id="{{ card_id }}"{% endif %}>
    
    {% if title or actions or collapsible %}
    <div class="card-header d-flex justify-content-between align-items-center">
        <div class="d-flex align-items-center">
            {% if icon %}
            <i class="fas fa-{{ icon }} me-2"></i>
            {% endif %}
            <h5 class="mb-0">{{ title }}</h5>
        </div>
        
        <div class="card-actions d-flex align-items-center">
            {% if actions %}
                {% for action in actions %}
                <button type="button" 
                        class="btn btn-sm btn-outline-light me-2" 
                        {% if action.onclick %}onclick="{{ action.onclick }}"{% endif %}
                        {% if action.href %}onclick="window.location.href='{{ action.href }}'"{% endif %}
                        {% if action.confirm %}data-confirm="{{ action.confirm }}"{% endif %}
                        title="{{ action.title or action.text }}">
                    {% if action.icon %}<i class="fas fa-{{ action.icon }}"></i>{% endif %}
                    {% if action.text %}{{ action.text }}{% endif %}
                </button>
                {% endfor %}
            {% endif %}
            
            {% if collapsible %}
            <button type="button" 
                    class="btn btn-sm btn-outline-light" 
                    data-bs-toggle="collapse" 
                    data-bs-target="#{{ card_id or 'card' }}-body"
                    title="طي/توسيع">
                <i class="fas fa-chevron-down"></i>
            </button>
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    <div class="card-body {% if collapsible %}collapse show{% endif %}" 
         {% if collapsible %}id="{{ card_id or 'card' }}-body"{% endif %}>
        {{ content }}
    </div>
</div>
{% endmacro %}

<!-- Statistics Card Component -->
{% macro stat_card(title, value, icon, color='primary', trend=None, subtitle=None) %}
<div class="col-xl-3 col-md-6 mb-4">
    <div class="card border-left-{{ color }} shadow h-100 py-2 stat-card">
        <div class="card-body">
            <div class="row no-gutters align-items-center">
                <div class="col mr-2">
                    <div class="text-xs font-weight-bold text-{{ color }} text-uppercase mb-1">
                        {{ title }}
                    </div>
                    <div class="h5 mb-0 font-weight-bold text-gray-800 stat-value">
                        {{ value }}
                    </div>
                    {% if trend %}
                    <div class="mt-2">
                        <span class="text-{{ 'success' if trend.value >= 0 else 'danger' }} small">
                            <i class="fas fa-arrow-{{ 'up' if trend.value >= 0 else 'down' }}"></i>
                            {{ trend.value|abs }}{{ trend.unit or '%' }}
                        </span>
                        <span class="text-muted small">{{ trend.label or 'من الفترة السابقة' }}</span>
                    </div>
                    {% endif %}
                    {% if subtitle %}
                    <div class="text-muted small mt-1">{{ subtitle }}</div>
                    {% endif %}
                </div>
                <div class="col-auto">
                    <i class="fas fa-{{ icon }} fa-2x text-gray-300 stat-icon"></i>
                </div>
            </div>
        </div>
    </div>
</div>
{% endmacro %}

<!-- Action Button Group Component -->
{% macro action_buttons(buttons, size='md', block=False) %}
<div class="btn-group{% if block %} w-100{% endif %}" role="group">
    {% for button in buttons %}
    <button type="{{ button.type or 'button' }}" 
            class="btn btn-{{ button.color or 'primary' }}{% if size != 'md' %} btn-{{ size }}{% endif %}"
            {% if button.onclick %}onclick="{{ button.onclick }}"{% endif %}
            {% if button.href %}onclick="window.location.href='{{ button.href }}'"{% endif %}
            {% if button.confirm %}data-confirm="{{ button.confirm }}"{% endif %}
            {% if button.loading %}data-loading="true"{% endif %}
            {% if button.disabled %}disabled{% endif %}
            title="{{ button.title or button.text }}">
        {% if button.icon %}<i class="fas fa-{{ button.icon }}{% if button.text %} me-2{% endif %}"></i>{% endif %}
        {{ button.text }}
    </button>
    {% endfor %}
</div>
{% endmacro %}

<!-- Enhanced Table Component -->
{% macro enhanced_table(headers, rows, table_id=None, sortable=True, searchable=True, selectable=False, actions=None) %}
<div class="table-container">
    {% if searchable %}
    <div class="table-search mb-3">
        <div class="input-group">
            <span class="input-group-text">
                <i class="fas fa-search"></i>
            </span>
            <input type="text" 
                   class="form-control" 
                   placeholder="البحث في الجدول..."
                   data-search="#{{ table_id or 'enhanced-table' }}">
        </div>
    </div>
    {% endif %}
    
    <div class="table-responsive">
        <table class="table table-hover enhanced-table" 
               {% if table_id %}id="{{ table_id }}"{% else %}id="enhanced-table"{% endif %}>
            <thead>
                <tr>
                    {% if selectable %}
                    <th width="50">
                        <input type="checkbox" class="form-check-input" id="select-all">
                    </th>
                    {% endif %}
                    
                    {% for header in headers %}
                    <th {% if sortable and header.sortable != False %}data-sort="{{ header.key or loop.index0 }}" class="sortable"{% endif %}>
                        {{ header.title or header }}
                        {% if sortable and header.sortable != False %}
                        <i class="fas fa-sort ms-1"></i>
                        {% endif %}
                    </th>
                    {% endfor %}
                    
                    {% if actions %}
                    <th width="120">الإجراءات</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
                {% for row in rows %}
                <tr {% if selectable %}data-selectable="true"{% endif %}>
                    {% if selectable %}
                    <td>
                        <input type="checkbox" class="form-check-input row-select" value="{{ row.id or loop.index0 }}">
                    </td>
                    {% endif %}
                    
                    {% for cell in row.cells or row %}
                    <td>{{ cell }}</td>
                    {% endfor %}
                    
                    {% if actions %}
                    <td>
                        <div class="btn-group btn-group-sm">
                            {% for action in actions %}
                            <button type="button" 
                                    class="btn btn-outline-{{ action.color or 'primary' }}"
                                    {% if action.onclick %}onclick="{{ action.onclick.replace('{id}', row.id|string) }}"{% endif %}
                                    {% if action.href %}onclick="window.location.href='{{ action.href.replace('{id}', row.id|string) }}'"{% endif %}
                                    title="{{ action.title or action.text }}">
                                <i class="fas fa-{{ action.icon }}"></i>
                            </button>
                            {% endfor %}
                        </div>
                    </td>
                    {% endif %}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    
    {% if selectable %}
    <div class="table-actions mt-3">
        <div class="d-flex justify-content-between align-items-center">
            <div class="selected-count">
                <span id="selected-count">0</span> عنصر محدد
            </div>
            <div class="bulk-actions">
                <button type="button" class="btn btn-sm btn-danger" onclick="bulkDelete()">
                    <i class="fas fa-trash me-1"></i>
                    حذف المحدد
                </button>
                <button type="button" class="btn btn-sm btn-info" onclick="bulkExport()">
                    <i class="fas fa-download me-1"></i>
                    تصدير المحدد
                </button>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<script>
// Enhanced table functionality
document.addEventListener('DOMContentLoaded', function() {
    // Select all functionality
    const selectAllCheckbox = document.getElementById('select-all');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const rowCheckboxes = document.querySelectorAll('.row-select');
            rowCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
                checkbox.closest('tr').classList.toggle('selected', this.checked);
            });
            updateSelectedCount();
        });
    }
    
    // Individual row selection
    const rowCheckboxes = document.querySelectorAll('.row-select');
    rowCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            this.closest('tr').classList.toggle('selected', this.checked);
            updateSelectedCount();
            
            // Update select all checkbox
            const allChecked = Array.from(rowCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(rowCheckboxes).some(cb => cb.checked);
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = someChecked && !allChecked;
            }
        });
    });
    
    function updateSelectedCount() {
        const selectedCount = document.querySelectorAll('.row-select:checked').length;
        const countElement = document.getElementById('selected-count');
        if (countElement) {
            countElement.textContent = selectedCount;
        }
    }
});

function bulkDelete() {
    const selected = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        alert('يرجى تحديد عناصر للحذف');
        return;
    }
    
    if (confirm(`هل تريد حذف ${selected.length} عنصر؟`)) {
        // Implement bulk delete logic
        console.log('Bulk delete:', selected);
    }
}

function bulkExport() {
    const selected = Array.from(document.querySelectorAll('.row-select:checked')).map(cb => cb.value);
    if (selected.length === 0) {
        alert('يرجى تحديد عناصر للتصدير');
        return;
    }
    
    // Implement bulk export logic
    console.log('Bulk export:', selected);
}
</script>
{% endmacro %}

<!-- Loading Spinner Component -->
{% macro loading_spinner(size='md', text=None) %}
<div class="text-center loading-container">
    <div class="spinner-border text-primary{% if size != 'md' %} spinner-border-{{ size }}{% endif %}" role="status">
        <span class="visually-hidden">جاري التحميل...</span>
    </div>
    {% if text %}
    <div class="mt-2 text-muted">{{ text }}</div>
    {% endif %}
</div>
{% endmacro %}

<!-- Alert Component -->
{% macro enhanced_alert(message, type='info', dismissible=True, icon=None) %}
<div class="alert alert-{{ type }}{% if dismissible %} alert-dismissible{% endif %} fade show enhanced-alert" role="alert">
    {% if icon %}
    <i class="fas fa-{{ icon }} me-2"></i>
    {% endif %}
    {{ message }}
    {% if dismissible %}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    {% endif %}
</div>
{% endmacro %}

<!-- Progress Bar Component -->
{% macro progress_bar(value, max_value=100, label=None, color='primary', striped=False, animated=False) %}
<div class="progress-container mb-3">
    {% if label %}
    <div class="d-flex justify-content-between mb-1">
        <span class="progress-label">{{ label }}</span>
        <span class="progress-value">{{ value }}/{{ max_value }}</span>
    </div>
    {% endif %}
    <div class="progress">
        <div class="progress-bar bg-{{ color }}{% if striped %} progress-bar-striped{% endif %}{% if animated %} progress-bar-animated{% endif %}" 
             role="progressbar" 
             style="width: {{ (value / max_value * 100)|round(1) }}%"
             aria-valuenow="{{ value }}" 
             aria-valuemin="0" 
             aria-valuemax="{{ max_value }}">
        </div>
    </div>
</div>
{% endmacro %}
