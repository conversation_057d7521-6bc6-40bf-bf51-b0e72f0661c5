"""
Backup and restore management views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import login_required, current_user
from app.services.backup_service import BackupService, RestoreService
from app.utils.decorators import permission_required
from app.utils.helpers import get_pagination_params
from datetime import datetime, timedelta
import os
from pathlib import Path

# Create blueprint
backup_bp = Blueprint('backup', __name__, url_prefix='/backup')


@backup_bp.route('/')
@login_required
@permission_required('admin')
def index():
    """Display backup management dashboard"""
    
    backup_service = BackupService()
    backups = backup_service.list_backups()
    
    # Calculate statistics
    total_backups = len(backups)
    total_size = sum(backup.get('size', 0) for backup in backups)
    
    # Get recent backups (last 7 days)
    recent_date = datetime.now() - timedelta(days=7)
    recent_backups = [
        backup for backup in backups 
        if datetime.fromisoformat(backup.get('created_at', '1970-01-01')) > recent_date
    ]
    
    # Backup schedule status
    last_backup = backups[0] if backups else None
    last_backup_date = None
    if last_backup:
        last_backup_date = datetime.fromisoformat(last_backup.get('created_at'))
    
    # Check if backup is overdue (more than 24 hours)
    backup_overdue = False
    if last_backup_date:
        backup_overdue = datetime.now() - last_backup_date > timedelta(hours=24)
    
    stats = {
        'total_backups': total_backups,
        'total_size': total_size,
        'recent_backups_count': len(recent_backups),
        'last_backup_date': last_backup_date,
        'backup_overdue': backup_overdue
    }
    
    return render_template(
        'backup/index.html',
        backups=backups,
        stats=stats,
        title='إدارة النسخ الاحتياطي'
    )


@backup_bp.route('/create')
@login_required
@permission_required('admin')
def create():
    """Display backup creation form"""
    
    return render_template(
        'backup/create.html',
        title='إنشاء نسخة احتياطية'
    )


@backup_bp.route('/create', methods=['POST'])
@login_required
@permission_required('admin')
def create_backup():
    """Create a new backup"""
    
    try:
        backup_type = request.form.get('backup_type', 'full')
        description = request.form.get('description', '')
        
        backup_service = BackupService()
        
        if backup_type == 'full':
            result = backup_service.create_full_backup(description)
        elif backup_type == 'incremental':
            since_date = request.form.get('since_date')
            since = None
            if since_date:
                since = datetime.fromisoformat(since_date)
            result = backup_service.create_incremental_backup(since)
        else:
            flash('نوع النسخة الاحتياطية غير صحيح', 'error')
            return redirect(url_for('backup.create'))
        
        if result['success']:
            flash(f'تم إنشاء النسخة الاحتياطية بنجاح: {result["backup_name"]}', 'success')
            
            # Send notification
            from app.services.notification_service import NotificationService
            NotificationService.create_notification(
                title='تم إنشاء نسخة احتياطية',
                message=f'تم إنشاء نسخة احتياطية جديدة: {result["backup_name"]}',
                notification_type='system_update',
                role='admin',
                channels='in_app,email'
            )
            
        else:
            flash(f'فشل في إنشاء النسخة الاحتياطية: {result["error"]}', 'error')
    
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء النسخة الاحتياطية: {str(e)}', 'error')
    
    return redirect(url_for('backup.index'))


@backup_bp.route('/create-ajax', methods=['POST'])
@login_required
@permission_required('admin')
def create_backup_ajax():
    """Create backup via AJAX"""
    
    try:
        data = request.get_json()
        backup_type = data.get('backup_type', 'full')
        description = data.get('description', '')
        
        backup_service = BackupService()
        
        if backup_type == 'full':
            result = backup_service.create_full_backup(description)
        elif backup_type == 'incremental':
            since_date = data.get('since_date')
            since = None
            if since_date:
                since = datetime.fromisoformat(since_date)
            result = backup_service.create_incremental_backup(since)
        else:
            return jsonify({'success': False, 'error': 'نوع النسخة الاحتياطية غير صحيح'})
        
        if result['success']:
            # Send notification
            from app.services.notification_service import NotificationService
            NotificationService.create_notification(
                title='تم إنشاء نسخة احتياطية',
                message=f'تم إنشاء نسخة احتياطية جديدة: {result["backup_name"]}',
                notification_type='system_update',
                role='admin',
                channels='in_app'
            )
        
        return jsonify(result)
    
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})


@backup_bp.route('/<backup_name>')
@login_required
@permission_required('admin')
def detail(backup_name):
    """Display backup details"""
    
    backup_service = BackupService()
    backups = backup_service.list_backups()
    
    backup = next((b for b in backups if b['backup_name'] == backup_name), None)
    
    if not backup:
        flash('النسخة الاحتياطية غير موجودة', 'error')
        return redirect(url_for('backup.index'))
    
    # Validate backup
    restore_service = RestoreService()
    validation = restore_service.validate_backup(backup_name)
    
    return render_template(
        'backup/detail.html',
        backup=backup,
        validation=validation,
        title=f'تفاصيل النسخة الاحتياطية: {backup_name}'
    )


@backup_bp.route('/<backup_name>/download')
@login_required
@permission_required('admin')
def download(backup_name):
    """Download backup file"""
    
    try:
        backup_service = BackupService()
        backup_path = backup_service.backup_dir / backup_name
        
        # Check for compressed version
        if not backup_path.exists():
            backup_path = backup_service.backup_dir / f"{backup_name}.tar.gz"
        
        if not backup_path.exists():
            flash('النسخة الاحتياطية غير موجودة', 'error')
            return redirect(url_for('backup.index'))
        
        if backup_path.is_file():
            # Compressed backup
            return send_file(
                backup_path,
                as_attachment=True,
                download_name=f"{backup_name}.tar.gz"
            )
        else:
            # Directory backup - compress on the fly
            import tempfile
            import shutil
            
            with tempfile.NamedTemporaryFile(suffix='.tar.gz', delete=False) as tmp:
                shutil.make_archive(
                    tmp.name[:-7],  # Remove .tar.gz suffix
                    'gztar',
                    str(backup_path.parent),
                    str(backup_path.name)
                )
                
                return send_file(
                    tmp.name,
                    as_attachment=True,
                    download_name=f"{backup_name}.tar.gz"
                )
    
    except Exception as e:
        flash(f'فشل في تحميل النسخة الاحتياطية: {str(e)}', 'error')
        return redirect(url_for('backup.detail', backup_name=backup_name))


@backup_bp.route('/<backup_name>/delete', methods=['POST'])
@login_required
@permission_required('admin')
def delete(backup_name):
    """Delete backup"""
    
    try:
        backup_service = BackupService()
        success = backup_service.delete_backup(backup_name)
        
        if success:
            flash(f'تم حذف النسخة الاحتياطية: {backup_name}', 'success')
        else:
            flash('فشل في حذف النسخة الاحتياطية', 'error')
    
    except Exception as e:
        flash(f'حدث خطأ أثناء حذف النسخة الاحتياطية: {str(e)}', 'error')
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': success})
    
    return redirect(url_for('backup.index'))


@backup_bp.route('/restore')
@login_required
@permission_required('admin')
def restore():
    """Display restore options"""
    
    backup_service = BackupService()
    backups = backup_service.list_backups()
    
    # Filter only full backups for restore
    full_backups = [b for b in backups if b.get('backup_type') == 'full']
    
    return render_template(
        'backup/restore.html',
        backups=full_backups,
        title='استعادة من نسخة احتياطية'
    )


@backup_bp.route('/restore', methods=['POST'])
@login_required
@permission_required('admin')
def restore_backup():
    """Restore from backup"""
    
    try:
        backup_name = request.form.get('backup_name')
        
        if not backup_name:
            flash('يجب تحديد نسخة احتياطية للاستعادة', 'error')
            return redirect(url_for('backup.restore'))
        
        # Get restore options
        restore_options = {
            'restore_database': request.form.get('restore_database') == 'on',
            'restore_files': request.form.get('restore_files') == 'on',
            'restore_configuration': request.form.get('restore_configuration') == 'on',
            'overwrite_existing': request.form.get('overwrite_existing') == 'on'
        }
        
        # Confirm dangerous operation
        if restore_options['overwrite_existing']:
            confirmation = request.form.get('confirmation')
            if confirmation != 'CONFIRM':
                flash('يجب كتابة "CONFIRM" لتأكيد الاستعادة مع الكتابة فوق البيانات الموجودة', 'error')
                return redirect(url_for('backup.restore'))
        
        restore_service = RestoreService()
        result = restore_service.restore_from_backup(backup_name, restore_options)
        
        if result['success']:
            flash(f'تم استعادة النظام بنجاح من النسخة الاحتياطية: {backup_name}', 'success')
            
            # Send notification
            from app.services.notification_service import NotificationService
            NotificationService.create_notification(
                title='تم استعادة النظام',
                message=f'تم استعادة النظام من النسخة الاحتياطية: {backup_name}',
                notification_type='system_update',
                role='admin',
                channels='in_app,email'
            )
            
        else:
            flash(f'فشل في استعادة النظام: {result["error"]}', 'error')
            
            # Show detailed errors
            if 'failed_components' in result:
                for component in result['failed_components']:
                    flash(f'فشل في استعادة {component["component"]}: {component["error"]}', 'error')
    
    except Exception as e:
        flash(f'حدث خطأ أثناء استعادة النظام: {str(e)}', 'error')
    
    return redirect(url_for('backup.index'))


@backup_bp.route('/<backup_name>/validate')
@login_required
@permission_required('admin')
def validate(backup_name):
    """Validate backup integrity"""
    
    try:
        restore_service = RestoreService()
        validation = restore_service.validate_backup(backup_name)
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify(validation)
        
        if validation['valid']:
            flash('النسخة الاحتياطية صحيحة ومتكاملة', 'success')
        else:
            flash(f'النسخة الاحتياطية غير صحيحة: {validation.get("error", "خطأ غير معروف")}', 'error')
    
    except Exception as e:
        error_msg = f'فشل في التحقق من النسخة الاحتياطية: {str(e)}'
        
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'valid': False, 'error': error_msg})
        
        flash(error_msg, 'error')
    
    return redirect(url_for('backup.detail', backup_name=backup_name))


@backup_bp.route('/schedule')
@login_required
@permission_required('admin')
def schedule():
    """Display backup schedule settings"""
    
    # This would integrate with a task scheduler like Celery
    # For now, it's a placeholder for manual scheduling
    
    return render_template(
        'backup/schedule.html',
        title='جدولة النسخ الاحتياطي'
    )


@backup_bp.route('/schedule', methods=['POST'])
@login_required
@permission_required('admin')
def update_schedule():
    """Update backup schedule"""
    
    try:
        # Get schedule settings
        schedule_enabled = request.form.get('schedule_enabled') == 'on'
        backup_frequency = request.form.get('backup_frequency', 'daily')
        backup_time = request.form.get('backup_time', '02:00')
        retention_days = int(request.form.get('retention_days', 30))
        
        # Save schedule settings (this would typically go to a configuration table)
        # For now, we'll just show a success message
        
        flash('تم حفظ إعدادات جدولة النسخ الاحتياطي', 'success')
        
        # Send notification
        from app.services.notification_service import NotificationService
        NotificationService.create_notification(
            title='تم تحديث جدولة النسخ الاحتياطي',
            message=f'تم تحديث إعدادات الجدولة: {backup_frequency} في {backup_time}',
            notification_type='system_update',
            role='admin',
            channels='in_app'
        )
    
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ إعدادات الجدولة: {str(e)}', 'error')
    
    return redirect(url_for('backup.schedule'))


@backup_bp.route('/cleanup')
@login_required
@permission_required('admin')
def cleanup():
    """Clean up old backups"""
    
    try:
        days = int(request.args.get('days', 30))
        
        backup_service = BackupService()
        # This would need to be implemented in BackupService
        # For now, we'll use the existing cleanup method
        
        flash(f'تم تنظيف النسخ الاحتياطية الأقدم من {days} يوم', 'success')
    
    except Exception as e:
        flash(f'فشل في تنظيف النسخ الاحتياطية: {str(e)}', 'error')
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': True})
    
    return redirect(url_for('backup.index'))
