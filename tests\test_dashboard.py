"""
Tests for dashboard functionality
"""

import pytest
from decimal import Decimal
from datetime import date, datetime, timedelta
from app import db
from app.models.account import Account
from app.models.journal import JournalEntry, JournalLine
from app.models.invoice import Invoice
from app.models.receipt import Receipt
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.models.user import User


class TestDashboardViews:
    """Test dashboard views and functionality"""
    
    def test_dashboard_index_redirect(self, client, admin_user, auth):
        """Test dashboard index redirects to analytics"""
        auth.login()
        response = client.get('/dashboard/')
        assert response.status_code == 302
        assert '/dashboard/analytics' in response.location
    
    def test_dashboard_analytics_page(self, client, admin_user, auth):
        """Test dashboard analytics page"""
        auth.login()
        response = client.get('/dashboard/analytics')
        assert response.status_code == 200
        assert 'لوحة التحليلات المالية' in response.get_data(as_text=True)
    
    def test_dashboard_with_period_filter(self, client, admin_user, auth):
        """Test dashboard with different period filters"""
        auth.login()
        
        # Test different periods
        periods = ['today', 'week', 'month', 'quarter', 'year']
        
        for period in periods:
            response = client.get(f'/dashboard/analytics?period={period}')
            assert response.status_code == 200
    
    def test_dashboard_with_custom_period(self, client, admin_user, auth):
        """Test dashboard with custom date range"""
        auth.login()
        
        today = date.today()
        last_month = today - timedelta(days=30)
        
        response = client.get('/dashboard/analytics', query_string={
            'period': 'custom',
            'date_from': last_month.isoformat(),
            'date_to': today.isoformat()
        })
        
        assert response.status_code == 200
    
    def test_dashboard_refresh_api(self, client, admin_user, auth):
        """Test dashboard refresh API endpoint"""
        auth.login()
        
        response = client.post('/dashboard/refresh', 
                              json={'period': 'month'},
                              headers={'Content-Type': 'application/json'})
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['success'] is True
        assert 'kpis' in data
        assert 'chart_data' in data
    
    def test_dashboard_activity_log(self, client, admin_user, auth):
        """Test dashboard activity log page"""
        auth.login()
        response = client.get('/dashboard/activity-log')
        assert response.status_code == 200
        assert 'سجل الأنشطة' in response.get_data(as_text=True)


class TestDashboardKPIs:
    """Test dashboard KPI calculations"""
    
    def test_kpi_calculation_with_data(self, app, admin_user, sample_accounts):
        """Test KPI calculation with sample data"""
        with app.app_context():
            from app.views.dashboard_views import calculate_kpis
            
            # Create test data
            revenue_account = Account.query.filter_by(type='Income').first()
            expense_account = Account.query.filter_by(type='Expense').first()
            cash_account = Account.query.filter_by(type='Asset').first()
            
            if revenue_account and expense_account and cash_account:
                # Create revenue entry
                revenue_entry = JournalEntry(
                    entry_number='KPI-REV-001',
                    entry_date=date.today(),
                    description='Test revenue for KPI',
                    created_by_id=admin_user.id,
                    is_posted=True,
                    posted_at=datetime.utcnow()
                )
                db.session.add(revenue_entry)
                db.session.flush()
                
                db.session.add_all([
                    JournalEntryLine(
                        entry_id=revenue_entry.id,
                        account_id=cash_account.id,
                        description='Cash from sales',
                        debit_amount=Decimal('10000.00'),
                        credit_amount=Decimal('0.00')
                    ),
                    JournalEntryLine(
                        entry_id=revenue_entry.id,
                        account_id=revenue_account.id,
                        description='Sales revenue',
                        debit_amount=Decimal('0.00'),
                        credit_amount=Decimal('10000.00')
                    )
                ])
                
                # Create expense entry
                expense_entry = JournalEntry(
                    entry_number='KPI-EXP-001',
                    entry_date=date.today(),
                    description='Test expense for KPI',
                    created_by_id=admin_user.id,
                    is_posted=True,
                    posted_at=datetime.utcnow()
                )
                db.session.add(expense_entry)
                db.session.flush()
                
                db.session.add_all([
                    JournalEntryLine(
                        entry_id=expense_entry.id,
                        account_id=expense_account.id,
                        description='Office expenses',
                        debit_amount=Decimal('3000.00'),
                        credit_amount=Decimal('0.00')
                    ),
                    JournalEntryLine(
                        entry_id=expense_entry.id,
                        account_id=cash_account.id,
                        description='Cash paid',
                        debit_amount=Decimal('0.00'),
                        credit_amount=Decimal('3000.00')
                    )
                ])
                
                db.session.commit()
                
                # Calculate KPIs
                today = date.today()
                kpis = calculate_kpis(today, today)
                
                assert kpis['total_revenue'] >= Decimal('10000.00')
                assert kpis['total_expenses'] >= Decimal('3000.00')
                assert kpis['net_profit'] >= Decimal('7000.00')
    
    def test_kpi_calculation_empty_data(self, app):
        """Test KPI calculation with no data"""
        with app.app_context():
            from app.views.dashboard_views import calculate_kpis
            
            today = date.today()
            kpis = calculate_kpis(today, today)
            
            # Should return zero values for empty data
            assert kpis['total_revenue'] == Decimal('0')
            assert kpis['total_expenses'] == Decimal('0')
            assert kpis['net_profit'] == Decimal('0')
    
    def test_kpi_growth_calculation(self, app, admin_user, sample_accounts):
        """Test KPI growth rate calculation"""
        with app.app_context():
            from app.views.dashboard_views import calculate_kpis
            
            today = date.today()
            last_month = today - timedelta(days=30)
            
            # Calculate KPIs with comparison period
            kpis = calculate_kpis(today, today, last_month, last_month)
            
            # Growth rates should be calculated
            assert 'revenue_growth' in kpis
            assert 'profit_growth' in kpis
            assert 'expense_growth' in kpis


class TestDashboardCharts:
    """Test dashboard chart data generation"""
    
    def test_chart_data_generation(self, app, admin_user, sample_accounts):
        """Test chart data generation"""
        with app.app_context():
            from app.views.dashboard_views import get_chart_data
            
            today = date.today()
            chart_data = get_chart_data(today, today)
            
            assert 'revenue_expense' in chart_data
            assert 'profit_distribution' in chart_data
            
            revenue_expense = chart_data['revenue_expense']
            assert 'labels' in revenue_expense
            assert 'revenue' in revenue_expense
            assert 'expenses' in revenue_expense
            
            assert isinstance(chart_data['profit_distribution'], list)
    
    def test_chart_data_with_date_range(self, app):
        """Test chart data generation with date range"""
        with app.app_context():
            from app.views.dashboard_views import get_chart_data
            
            today = date.today()
            start_date = today.replace(day=1)  # Start of month
            
            chart_data = get_chart_data(start_date, today)
            
            # Should have data for the date range
            assert len(chart_data['revenue_expense']['labels']) >= 1


class TestDashboardActivities:
    """Test dashboard recent activities"""
    
    def test_recent_activities_with_data(self, app, admin_user, test_customer):
        """Test recent activities with sample data"""
        with app.app_context():
            from app.views.dashboard_views import get_recent_activities
            
            # Create test invoice
            invoice = Invoice(
                invoice_number='ACT-TEST-001',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                total_amount=Decimal('1000.00'),
                status='pending',
                created_by_id=admin_user.id
            )
            db.session.add(invoice)
            
            # Create test receipt
            receipt = Receipt(
                receipt_number='ACT-REC-001',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('500.00'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            db.session.add(receipt)
            
            db.session.commit()
            
            # Get recent activities
            activities = get_recent_activities(limit=10)
            
            assert len(activities) > 0
            
            # Check activity structure
            for activity in activities:
                assert 'title' in activity
                assert 'description' in activity
                assert 'type_color' in activity
                assert 'icon' in activity
                assert 'created_at' in activity
    
    def test_recent_activities_pagination(self, app):
        """Test recent activities with pagination"""
        with app.app_context():
            from app.views.dashboard_views import get_recent_activities
            
            # Test pagination
            activities_page1 = get_recent_activities(page=1, per_page=5)
            activities_page2 = get_recent_activities(page=2, per_page=5)
            
            # Should return different results (or empty for page 2)
            assert isinstance(activities_page1, list)
            assert isinstance(activities_page2, list)


class TestDashboardAlerts:
    """Test dashboard alerts and notifications"""
    
    def test_dashboard_alerts_generation(self, app, admin_user, test_customer):
        """Test dashboard alerts generation"""
        with app.app_context():
            from app.views.dashboard_views import get_dashboard_alerts
            
            # Create overdue invoice
            overdue_invoice = Invoice(
                invoice_number='OVERDUE-001',
                customer_id=test_customer.id,
                invoice_date=date.today() - timedelta(days=60),
                due_date=date.today() - timedelta(days=30),
                total_amount=Decimal('5000.00'),
                status='sent',
                created_by_id=admin_user.id
            )
            db.session.add(overdue_invoice)
            db.session.commit()
            
            # Get alerts
            alerts = get_dashboard_alerts()
            
            assert isinstance(alerts, list)
            
            # Should have alert for overdue invoice
            overdue_alert = next((alert for alert in alerts if 'متأخرة' in alert['message']), None)
            assert overdue_alert is not None
            assert overdue_alert['type'] == 'warning'
    
    def test_cash_balance_alert(self, app, admin_user, sample_accounts):
        """Test low cash balance alert"""
        with app.app_context():
            from app.views.dashboard_views import get_dashboard_alerts
            
            # This would test low cash balance alert
            # Implementation depends on having cash accounts with low balances
            alerts = get_dashboard_alerts()
            
            assert isinstance(alerts, list)


class TestDashboardPermissions:
    """Test dashboard access permissions"""
    
    def test_admin_access(self, client, admin_user, auth):
        """Test admin user access to dashboard"""
        auth.login('admin', 'admin123')
        
        response = client.get('/dashboard/analytics')
        assert response.status_code == 200
    
    def test_accountant_access(self, client, accountant_user, auth):
        """Test accountant user access to dashboard"""
        auth.login('accountant', 'acc123')
        
        response = client.get('/dashboard/analytics')
        assert response.status_code == 200
    
    def test_employee_access(self, client, employee_user, auth):
        """Test employee user access to dashboard"""
        auth.login('employee', 'emp123')
        
        # Employees should not have access to dashboard
        response = client.get('/dashboard/analytics')
        assert response.status_code == 302  # Redirect to login or access denied


class TestDashboardForms:
    """Test dashboard form validation"""
    
    def test_dashboard_filter_form_validation(self, app):
        """Test dashboard filter form validation"""
        from app.forms.dashboard_forms import DashboardFilterForm
        
        with app.app_context():
            # Valid form
            form_data = {
                'period': 'month',
                'compare_with_previous': True
            }
            form = DashboardFilterForm(data=form_data)
            assert form.validate()
            
            # Custom period without dates
            form_data = {
                'period': 'custom',
                'compare_with_previous': True
            }
            form = DashboardFilterForm(data=form_data)
            assert not form.validate()
    
    def test_export_dashboard_form_validation(self, app):
        """Test export dashboard form validation"""
        from app.forms.dashboard_forms import ExportDashboardForm
        
        with app.app_context():
            # Valid form
            form_data = {
                'export_format': 'pdf',
                'include_charts': True,
                'include_kpis': True,
                'date_range': 'current'
            }
            form = ExportDashboardForm(data=form_data)
            assert form.validate()
            
            # Missing export format
            form_data = {
                'include_charts': True,
                'date_range': 'current'
            }
            form = ExportDashboardForm(data=form_data)
            assert not form.validate()


class TestDashboardHelperFunctions:
    """Test dashboard helper functions"""
    
    def test_get_comparison_dates(self, app):
        """Test comparison dates calculation"""
        with app.app_context():
            from app.views.dashboard_views import get_comparison_dates
            
            today = date.today()
            start_date = today.replace(day=1)
            
            comp_from, comp_to = get_comparison_dates(start_date, today)
            
            assert comp_from < start_date
            assert comp_to < start_date
            assert (comp_to - comp_from) == (today - start_date)
    
    def test_get_period_display(self, app):
        """Test period display text generation"""
        with app.app_context():
            from app.views.dashboard_views import get_period_display
            
            today = date.today()
            
            # Test different periods
            assert get_period_display('today', today, today) == 'اليوم'
            assert get_period_display('month', today, today) == 'هذا الشهر'
            assert get_period_display('year', today, today) == 'هذا العام'
            
            # Test custom period
            custom_display = get_period_display('custom', today, today)
            assert 'من' in custom_display and 'إلى' in custom_display


if __name__ == '__main__':
    pytest.main([__file__])
