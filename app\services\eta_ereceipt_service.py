"""
خدمة الإيصال الإلكتروني المتوافقة مع ETA eReceipt API v1.2
ETA eReceipt Service compatible with ETA eReceipt API v1.2
Based on: https://sdk.invoicing.eta.gov.eg/ereceiptapi/
"""

import json
import hashlib
import hmac
import base64
import requests
from datetime import datetime, timezone
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Any
from app import db
from app.models.receipt import Receipt, ReceiptItem, ReceiptTaxTotal, ReceiptItemTax
from app.models.eta_integration import ETASettings, ETAErrorLog
from app.utils.logger import get_logger
from app.services.qr_service import QRCodeService

logger = get_logger(__name__)


class ETAeReceiptService:
    """خدمة الإيصال الإلكتروني لمصلحة الضرائب المصرية"""
    
    def __init__(self):
        self.base_url = ETASettings.get_setting('ETA_BASE_URL', 'https://api.invoicing.eta.gov.eg/api/v1')
        self.client_id = ETASettings.get_setting('ETA_CLIENT_ID')
        self.client_secret = ETASettings.get_setting('ETA_CLIENT_SECRET')
        self.environment = ETASettings.get_setting('ETA_ENVIRONMENT', 'sandbox')
        self.timeout = int(ETASettings.get_setting('ETA_TIMEOUT', '30'))
        self.access_token = None
        self.token_expires_at = None
        
        # Company Information
        self.company_tax_id = ETASettings.get_setting('COMPANY_TAX_ID')
        self.company_activity_code = ETASettings.get_setting('COMPANY_ACTIVITY_CODE')
        self.company_branch_id = ETASettings.get_setting('COMPANY_BRANCH_ID', '0')
        
        # Device Information (Required for eReceipt)
        self.device_serial_number = ETASettings.get_setting('ETA_DEVICE_SERIAL', 'DEVICE001')

        # QR Code service
        self.qr_service = QRCodeService()
        
    def authenticate(self) -> bool:
        """المصادقة مع ETA API"""
        try:
            url = f"{self.base_url}/connect/token"
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {
                'grant_type': 'client_credentials',
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'scope': 'InvoicingAPI'
            }
            
            response = requests.post(url, headers=headers, data=data, timeout=self.timeout)
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data.get('access_token')
                expires_in = token_data.get('expires_in', 3600)
                self.token_expires_at = datetime.now(timezone.utc).timestamp() + expires_in
                
                logger.info("ETA authentication successful")
                return True
            else:
                logger.error(f"ETA authentication failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"ETA authentication error: {str(e)}")
            return False
    
    def is_token_valid(self) -> bool:
        """التحقق من صحة الرمز المميز"""
        if not self.access_token or not self.token_expires_at:
            return False
        
        # Check if token expires in next 5 minutes
        return datetime.now(timezone.utc).timestamp() < (self.token_expires_at - 300)
    
    def ensure_authenticated(self) -> bool:
        """التأكد من المصادقة"""
        if not self.is_token_valid():
            return self.authenticate()
        return True
    
    def generate_receipt_uuid(self, receipt: Receipt) -> str:
        """توليد UUID للإيصال حسب مواصفات ETA"""
        # Create string to hash according to ETA specifications
        uuid_string = (
            f"{receipt.receipt_number}|"
            f"{receipt.datetime_issued.strftime('%Y-%m-%dT%H:%M:%SZ')}|"
            f"{receipt.total_amount}|"
            f"{self.company_tax_id}"
        )
        
        # Generate SHA256 hash
        sha256_hash = hashlib.sha256(uuid_string.encode('utf-8')).hexdigest()
        return sha256_hash.upper()
    
    def calculate_signature(self, receipt_data: Dict) -> str:
        """حساب التوقيع الرقمي للإيصال"""
        # Create canonical string for signature
        canonical_string = json.dumps(receipt_data, sort_keys=True, separators=(',', ':'))
        
        # Calculate HMAC-SHA256 signature
        signature = hmac.new(
            self.client_secret.encode('utf-8'),
            canonical_string.encode('utf-8'),
            hashlib.sha256
        ).digest()
        
        return base64.b64encode(signature).decode('utf-8')
    
    def format_receipt_for_eta(self, receipt: Receipt) -> Dict:
        """تحويل الإيصال لصيغة ETA eReceipt API v1.2"""
        
        # Generate UUID if not exists
        if not receipt.eta_uuid:
            receipt.eta_uuid = self.generate_receipt_uuid(receipt)
            db.session.commit()
        
        # Header Information
        header = {
            "dateTimeIssued": receipt.datetime_issued.strftime('%Y-%m-%dT%H:%M:%SZ'),
            "receiptNumber": receipt.receipt_number,
            "uuid": receipt.eta_uuid,
            "previousUUID": receipt.previous_uuid or "",
            "referenceOldUUID": receipt.reference_old_uuid or "",
            "currency": receipt.currency or "EGP",
            "exchangeRate": float(receipt.exchange_rate or 1.0),
            "sOrderNameCode": receipt.sales_order_name_code or "",
            "orderdeliveryMode": receipt.order_delivery_mode or ""
        }
        
        # Document Type
        document_type = {
            "receiptType": receipt.document_type_name or "s",
            "typeVersion": receipt.document_type_version or "1.2"
        }
        
        # Seller Information
        seller = {
            "rin": self.company_tax_id,
            "companyTradeName": ETASettings.get_setting('COMPANY_NAME', ''),
            "branchCode": receipt.branch_code or self.company_branch_id,
            "branchAddress": {
                "country": "EG",
                "governate": ETASettings.get_setting('COMPANY_GOVERNATE', ''),
                "regionCity": ETASettings.get_setting('COMPANY_CITY', ''),
                "street": ETASettings.get_setting('COMPANY_ADDRESS', ''),
                "buildingNumber": ETASettings.get_setting('COMPANY_BUILDING', ''),
                "postalCode": ETASettings.get_setting('COMPANY_POSTAL_CODE', ''),
                "floor": ETASettings.get_setting('COMPANY_FLOOR', ''),
                "room": ETASettings.get_setting('COMPANY_ROOM', ''),
                "landmark": ETASettings.get_setting('COMPANY_LANDMARK', ''),
                "additionalInformation": ETASettings.get_setting('COMPANY_ADDITIONAL_INFO', '')
            },
            "deviceSerialNumber": receipt.device_serial_number or self.device_serial_number,
            "activityCode": receipt.activity_code or self.company_activity_code
        }
        
        # Buyer Information
        buyer = {
            "type": receipt.buyer_type or "F",
            "id": receipt.buyer_id or "",
            "name": receipt.buyer_name or "",
            "mobileNumber": receipt.buyer_mobile or "",
            "paymentNumber": receipt.payment_number or ""
        }
        
        # Items List
        items_list = []
        for item in receipt.items:
            item_data = {
                "internalCode": item.internal_code,
                "description": item.description,
                "itemType": item.item_type,
                "itemCode": item.item_code,
                "unitType": item.unit_type,
                "quantity": float(item.quantity),
                "unitPrice": float(item.unit_price),
                "netSale": float(item.net_sale),
                "totalSale": float(item.total_sale),
                "total": float(item.total),
                "commercialDiscountData": [
                    {
                        "amount": float(item.commercial_discount_amount),
                        "rate": float(item.commercial_discount_rate)
                    }
                ] if item.commercial_discount_amount > 0 else [],
                "itemDiscountData": [
                    {
                        "amount": float(item.item_discount_amount),
                        "rate": float(item.item_discount_rate)
                    }
                ] if item.item_discount_amount > 0 else [],
                "valueDifference": float(item.value_difference),
                "taxableItems": []
            }
            
            # Add item taxes
            for tax in item.taxes:
                item_data["taxableItems"].append({
                    "taxType": tax.tax_type,
                    "amount": float(tax.amount),
                    "subType": tax.sub_type,
                    "rate": float(tax.rate) if tax.rate else 0
                })
            
            items_list.append(item_data)
        
        # Tax Totals
        tax_totals = []
        for tax_total in receipt.tax_totals:
            tax_totals.append({
                "taxType": tax_total.tax_type,
                "amount": float(tax_total.amount)
            })
        
        # Payment Method
        payment_method = self.map_payment_method(receipt.payment_method)
        
        # Financial Totals
        total_sales = float(receipt.total_sales or 0)
        total_commercial_discount = float(receipt.total_commercial_discount or 0)
        total_items_discount = float(receipt.total_items_discount or 0)
        net_amount = float(receipt.net_amount or 0)
        fees_amount = float(receipt.fees_amount or 0)
        total_amount = float(receipt.total_amount or 0)
        
        # Build complete receipt structure
        receipt_data = {
            "header": header,
            "documentType": document_type,
            "seller": seller,
            "buyer": buyer,
            "itemData": items_list,
            "totalSales": total_sales,
            "totalCommercialDiscount": total_commercial_discount,
            "totalItemsDiscount": total_items_discount,
            "netAmount": net_amount,
            "feesAmount": fees_amount,
            "totalAmount": total_amount,
            "taxTotals": tax_totals,
            "paymentMethod": payment_method,
            "adjustment": float(receipt.adjustment or 0)
        }
        
        return receipt_data
    
    def map_payment_method(self, payment_method: str) -> str:
        """تحويل طريقة الدفع لرموز ETA"""
        payment_mapping = {
            'cash': 'C',
            'card': 'CC',
            'bank': 'BT',
            'check': 'CH',
            'credit': 'CR'
        }
        return payment_mapping.get(payment_method, 'C')
    
    def submit_receipt(self, receipt: Receipt) -> Tuple[bool, Dict]:
        """إرسال الإيصال لمصلحة الضرائب"""
        try:
            # Ensure authentication
            if not self.ensure_authenticated():
                return False, {"error": "Authentication failed"}
            
            # Format receipt for ETA
            receipt_data = self.format_receipt_for_eta(receipt)
            
            # Submit to ETA
            url = f"{self.base_url}/receipts/submit"
            
            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.post(
                url, 
                headers=headers, 
                json=receipt_data, 
                timeout=self.timeout
            )
            
            response_data = response.json() if response.content else {}
            
            if response.status_code == 200:
                # Update receipt with ETA response
                receipt.eta_status = 'submitted'
                receipt.eta_submitted_at = datetime.now(timezone.utc)
                receipt.eta_response_data = json.dumps(response_data)
                
                # Extract important fields from response
                if 'uuid' in response_data:
                    receipt.eta_uuid = response_data['uuid']
                if 'longId' in response_data:
                    receipt.eta_long_id = response_data['longId']
                if 'internalId' in response_data:
                    receipt.eta_internal_id = response_data['internalId']

                # Generate QR Code for the receipt
                try:
                    qr_success, qr_result = self.qr_service.create_printable_receipt_qr(
                        response_data, receipt.receipt_number
                    )

                    if qr_success:
                        receipt.eta_qr_image_path = qr_result

                        # Also generate QR data string
                        qr_data = self.qr_service.format_eta_qr_data(response_data)
                        receipt.eta_qr_code = qr_data

                        logger.info(f"QR Code generated for receipt {receipt.receipt_number}")
                    else:
                        logger.warning(f"Failed to generate QR Code for receipt {receipt.receipt_number}: {qr_result}")

                except Exception as qr_error:
                    logger.error(f"QR Code generation error for receipt {receipt.receipt_number}: {str(qr_error)}")

                db.session.commit()
                
                logger.info(f"Receipt {receipt.receipt_number} submitted successfully to ETA")
                return True, response_data
            else:
                # Log error
                error_msg = f"ETA submission failed: {response.status_code} - {response.text}"
                logger.error(error_msg)
                
                # Update receipt status
                receipt.eta_status = 'failed'
                receipt.eta_response_data = json.dumps(response_data)
                db.session.commit()
                
                # Log error to database
                ETAErrorLog.log_error(
                    error_type='receipt_submission',
                    error_message=error_msg,
                    request_data=receipt_data,
                    response_data=response_data
                )
                
                return False, response_data
                
        except Exception as e:
            error_msg = f"Receipt submission error: {str(e)}"
            logger.error(error_msg)
            
            # Update receipt status
            receipt.eta_status = 'error'
            db.session.commit()
            
            # Log error to database
            ETAErrorLog.log_error(
                error_type='receipt_submission_exception',
                error_message=error_msg,
                request_data=receipt_data if 'receipt_data' in locals() else None
            )
            
            return False, {"error": str(e)}

    def check_receipt_status(self, receipt: Receipt) -> Tuple[bool, Dict]:
        """التحقق من حالة الإيصال في ETA"""
        try:
            if not receipt.eta_uuid:
                return False, {"error": "Receipt not submitted to ETA"}

            # Ensure authentication
            if not self.ensure_authenticated():
                return False, {"error": "Authentication failed"}

            url = f"{self.base_url}/receipts/{receipt.eta_uuid}/status"

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            response = requests.get(url, headers=headers, timeout=self.timeout)
            response_data = response.json() if response.content else {}

            if response.status_code == 200:
                # Update receipt status based on ETA response
                eta_status = response_data.get('status', 'unknown')
                receipt.eta_status = eta_status
                receipt.eta_response_data = json.dumps(response_data)
                db.session.commit()

                logger.info(f"Receipt {receipt.receipt_number} status checked: {eta_status}")
                return True, response_data
            else:
                logger.error(f"Failed to check receipt status: {response.status_code} - {response.text}")
                return False, response_data

        except Exception as e:
            logger.error(f"Receipt status check error: {str(e)}")
            return False, {"error": str(e)}

    def cancel_receipt(self, receipt: Receipt, reason: str = "") -> Tuple[bool, Dict]:
        """إلغاء الإيصال في ETA"""
        try:
            if not receipt.eta_uuid:
                return False, {"error": "Receipt not submitted to ETA"}

            # Ensure authentication
            if not self.ensure_authenticated():
                return False, {"error": "Authentication failed"}

            url = f"{self.base_url}/receipts/{receipt.eta_uuid}/cancel"

            headers = {
                'Authorization': f'Bearer {self.access_token}',
                'Content-Type': 'application/json'
            }

            data = {
                "reason": reason or "Receipt cancelled by user"
            }

            response = requests.post(url, headers=headers, json=data, timeout=self.timeout)
            response_data = response.json() if response.content else {}

            if response.status_code == 200:
                # Update receipt status
                receipt.eta_status = 'cancelled'
                receipt.eta_response_data = json.dumps(response_data)
                db.session.commit()

                logger.info(f"Receipt {receipt.receipt_number} cancelled successfully")
                return True, response_data
            else:
                logger.error(f"Failed to cancel receipt: {response.status_code} - {response.text}")
                return False, response_data

        except Exception as e:
            logger.error(f"Receipt cancellation error: {str(e)}")
            return False, {"error": str(e)}

    def get_receipt_pdf(self, receipt: Receipt) -> Tuple[bool, bytes]:
        """الحصول على PDF الإيصال من ETA"""
        try:
            if not receipt.eta_uuid:
                return False, b""

            # Ensure authentication
            if not self.ensure_authenticated():
                return False, b""

            url = f"{self.base_url}/receipts/{receipt.eta_uuid}/pdf"

            headers = {
                'Authorization': f'Bearer {self.access_token}'
            }

            response = requests.get(url, headers=headers, timeout=self.timeout)

            if response.status_code == 200:
                logger.info(f"Receipt PDF retrieved for {receipt.receipt_number}")
                return True, response.content
            else:
                logger.error(f"Failed to get receipt PDF: {response.status_code}")
                return False, b""

        except Exception as e:
            logger.error(f"Receipt PDF retrieval error: {str(e)}")
            return False, b""

    def validate_receipt_data(self, receipt: Receipt) -> Tuple[bool, List[str]]:
        """التحقق من صحة بيانات الإيصال قبل الإرسال"""
        errors = []

        # Required fields validation
        if not receipt.receipt_number:
            errors.append("Receipt number is required")

        if not receipt.datetime_issued:
            errors.append("Issue date/time is required")

        if not receipt.total_amount or receipt.total_amount <= 0:
            errors.append("Total amount must be greater than zero")

        if not self.company_tax_id:
            errors.append("Company tax ID is not configured")

        if not self.company_activity_code:
            errors.append("Company activity code is not configured")

        # Buyer validation
        if receipt.buyer_type and receipt.buyer_type not in ['B', 'P', 'F']:
            errors.append("Invalid buyer type. Must be B, P, or F")

        # Items validation
        if not receipt.items:
            errors.append("Receipt must have at least one item")

        for item in receipt.items:
            if not item.internal_code:
                errors.append(f"Item internal code is required")

            if not item.description:
                errors.append(f"Item description is required")

            if not item.item_type or item.item_type not in ['GS1', 'EGS']:
                errors.append(f"Item type must be GS1 or EGS")

            if not item.unit_type:
                errors.append(f"Item unit type is required")

            if item.quantity <= 0:
                errors.append(f"Item quantity must be greater than zero")

            if item.unit_price < 0:
                errors.append(f"Item unit price cannot be negative")

        # Financial validation
        calculated_total = sum(float(item.total) for item in receipt.items)
        if abs(calculated_total - float(receipt.total_amount)) > 0.01:
            errors.append("Total amount does not match sum of item totals")

        return len(errors) == 0, errors

    def auto_submit_receipt(self, receipt: Receipt) -> Tuple[bool, Dict]:
        """إرسال تلقائي للإيصال إذا كان مفعلاً"""
        auto_submit = ETASettings.get_setting('ETA_AUTO_SUBMIT', 'false').lower() == 'true'

        if not auto_submit:
            return True, {"message": "Auto submit is disabled"}

        # Validate receipt data
        is_valid, errors = self.validate_receipt_data(receipt)
        if not is_valid:
            logger.warning(f"Receipt {receipt.receipt_number} validation failed: {errors}")
            return False, {"errors": errors}

        # Submit receipt
        return self.submit_receipt(receipt)

    def resend_receipt(self, receipt: Receipt) -> Tuple[bool, Dict]:
        """إعادة إرسال الإيصال"""
        try:
            # Store old UUID as reference
            if receipt.eta_uuid:
                receipt.reference_old_uuid = receipt.eta_uuid

            # Generate new UUID
            receipt.eta_uuid = self.generate_receipt_uuid(receipt)
            receipt.eta_status = 'pending'
            db.session.commit()

            # Submit with new UUID
            return self.submit_receipt(receipt)

        except Exception as e:
            logger.error(f"Receipt resend error: {str(e)}")
            return False, {"error": str(e)}

    def get_tax_types(self) -> Dict[str, str]:
        """الحصول على أنواع الضرائب المدعومة"""
        return {
            'T1': 'ضريبة القيمة المضافة',
            'T2': 'ضريبة الجدولة',
            'T3': 'ضريبة الترفيه',
            'T4': 'ضريبة الدمغة',
            'T5': 'ضريبة أخرى',
            'T6': 'ضريبة الخدمات',
            'T7': 'ضريبة الإنتاج',
            'T8': 'ضريبة الاستهلاك',
            'T9': 'ضريبة البيئة',
            'T10': 'ضريبة التنمية',
            'T11': 'ضريبة الموارد',
            'T12': 'ضريبة التصدير',
            'T13': 'ضريبة الاستيراد',
            'T14': 'ضريبة النقل',
            'T15': 'ضريبة الاتصالات',
            'T16': 'ضريبة الطاقة',
            'T17': 'ضريبة الصحة',
            'T18': 'ضريبة التعليم',
            'T19': 'ضريبة الأمان',
            'T20': 'ضريبة أخرى متقدمة'
        }

    def get_unit_types(self) -> Dict[str, str]:
        """الحصول على وحدات القياس المدعومة"""
        return {
            'EA': 'قطعة',
            'KG': 'كيلوجرام',
            'GM': 'جرام',
            'LT': 'لتر',
            'MT': 'متر',
            'M2': 'متر مربع',
            'M3': 'متر مكعب',
            'HR': 'ساعة',
            'DY': 'يوم',
            'WK': 'أسبوع',
            'MO': 'شهر',
            'YR': 'سنة',
            'PK': 'عبوة',
            'BX': 'صندوق',
            'CT': 'كرتونة',
            'DZ': 'دستة',
            'PR': 'زوج',
            'ST': 'مجموعة',
            'TN': 'طن'
        }
