{% extends "base.html" %}

{% block title %}العرض الهرمي للحسابات - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-sitemap me-3"></i>
                العرض الهرمي للحسابات
            </h1>
            <p class="mb-0 mt-2">الهيكل التنظيمي لدليل الحسابات</p>
        </div>
        <div>
            <a href="{{ url_for('accounts.index') }}" class="btn btn-outline-light">
                <i class="fas fa-list me-2"></i>
                العرض الجدولي
            </a>
            <a href="{{ url_for('accounts.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                حساب جديد
            </a>
        </div>
    </div>
</div>

<!-- Tree View Controls -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-primary" onclick="expandAll()">
                    <i class="fas fa-expand-arrows-alt me-2"></i>
                    توسيع الكل
                </button>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-secondary" onclick="collapseAll()">
                    <i class="fas fa-compress-arrows-alt me-2"></i>
                    طي الكل
                </button>
            </div>
            <div class="col-md-3">
                <button type="button" class="btn btn-outline-info" onclick="showBalances()">
                    <i class="fas fa-calculator me-2"></i>
                    عرض الأرصدة
                </button>
            </div>
            <div class="col-md-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="showInactive">
                    <label class="form-check-label" for="showInactive">
                        عرض الحسابات غير النشطة
                    </label>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Account Tree -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-tree me-2"></i>
            شجرة الحسابات
        </h5>
    </div>
    <div class="card-body">
        <div id="account-tree">
            {% for account in root_accounts %}
                {{ render_account_tree(account) }}
            {% endfor %}
        </div>
    </div>
</div>

<!-- Account Tree Macro -->
{% macro render_account_tree(account, level=0) %}
<div class="account-node" data-account-id="{{ account.id }}" data-level="{{ level }}">
    <div class="account-item d-flex align-items-center py-2" style="padding-right: {{ level * 30 }}px;">
        <!-- Expand/Collapse Button -->
        {% if account.children.count() > 0 %}
        <button class="btn btn-sm btn-link p-0 me-2 expand-btn" onclick="toggleNode(this)">
            <i class="fas fa-chevron-down"></i>
        </button>
        {% else %}
        <span class="me-4"></span>
        {% endif %}
        
        <!-- Account Icon -->
        <i class="fas fa-{{ 'coins' if account.type == 'Asset' else 'credit-card' if account.type == 'Liability' else 'university' if account.type == 'Equity' else 'chart-line' if account.type == 'Income' else 'shopping-cart' }} me-2 text-{{ 'success' if account.type == 'Asset' else 'danger' if account.type == 'Liability' else 'primary' if account.type == 'Equity' else 'info' if account.type == 'Income' else 'warning' }}"></i>
        
        <!-- Account Info -->
        <div class="flex-grow-1">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong class="text-primary">{{ account.code }}</strong>
                    <span class="ms-2">{{ account.name }}</span>
                    {% if account.name_en %}
                        <small class="text-muted ms-2">({{ account.name_en }})</small>
                    {% endif %}
                    
                    <!-- Account Type Badge -->
                    <span class="badge bg-{{ 'success' if account.type == 'Asset' else 'danger' if account.type == 'Liability' else 'primary' if account.type == 'Equity' else 'info' if account.type == 'Income' else 'warning' }} ms-2">
                        {{ account.get_type_display() }}
                    </span>
                    
                    <!-- Status Badge -->
                    {% if not account.is_active %}
                        <span class="badge bg-secondary ms-1">غير نشط</span>
                    {% endif %}
                </div>
                
                <!-- Balance and Actions -->
                <div class="d-flex align-items-center">
                    <!-- Balance -->
                    {% if account.is_leaf_account() %}
                        <span class="balance fw-bold me-3" style="display: none;">
                            {{ account.get_balance()|currency }}
                        </span>
                    {% endif %}
                    
                    <!-- Actions -->
                    <div class="btn-group btn-group-sm">
                        <a href="{{ url_for('accounts.detail', account_id=account.id) }}" 
                           class="btn btn-outline-primary btn-sm" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ url_for('accounts.edit', account_id=account.id) }}" 
                           class="btn btn-outline-warning btn-sm" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </a>
                        {% if account.can_be_deleted() %}
                        <form method="POST" action="{{ url_for('accounts.delete', account_id=account.id) }}" 
                              class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                            <button type="submit" class="btn btn-outline-danger btn-sm" title="حذف">
                                <i class="fas fa-trash"></i>
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Children -->
    {% if account.children.count() > 0 %}
    <div class="account-children">
        {% for child in account.children.filter_by(is_active=True).order_by(account.children.property.mapper.class_.code) %}
            {{ render_account_tree(child, level + 1) }}
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endmacro %}

<!-- Legend -->
<div class="card mt-4">
    <div class="card-header">
        <h6 class="card-title mb-0">
            <i class="fas fa-info-circle me-2"></i>
            دليل الألوان
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-2">
                <span class="badge bg-success me-2">الأصول</span>
                <i class="fas fa-coins text-success"></i>
            </div>
            <div class="col-md-2">
                <span class="badge bg-danger me-2">الخصوم</span>
                <i class="fas fa-credit-card text-danger"></i>
            </div>
            <div class="col-md-2">
                <span class="badge bg-primary me-2">رأس المال</span>
                <i class="fas fa-university text-primary"></i>
            </div>
            <div class="col-md-2">
                <span class="badge bg-info me-2">الإيرادات</span>
                <i class="fas fa-chart-line text-info"></i>
            </div>
            <div class="col-md-2">
                <span class="badge bg-warning me-2">المصروفات</span>
                <i class="fas fa-shopping-cart text-warning"></i>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.account-node {
    border-left: 2px solid #e9ecef;
    margin-left: 10px;
}

.account-item:hover {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

.expand-btn {
    transition: transform 0.2s ease;
}

.expand-btn.collapsed i {
    transform: rotate(-90deg);
}

.account-children {
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.account-children.collapsed {
    max-height: 0;
}

.balance {
    min-width: 120px;
    text-align: left;
}

@media (max-width: 768px) {
    .account-item {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .account-item > div:last-child {
        margin-top: 0.5rem;
        width: 100%;
        justify-content: space-between;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function toggleNode(button) {
    const icon = button.querySelector('i');
    const children = button.closest('.account-node').querySelector('.account-children');
    
    if (children) {
        if (children.classList.contains('collapsed')) {
            children.classList.remove('collapsed');
            icon.classList.remove('fa-chevron-right');
            icon.classList.add('fa-chevron-down');
            button.classList.remove('collapsed');
        } else {
            children.classList.add('collapsed');
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-right');
            button.classList.add('collapsed');
        }
    }
}

function expandAll() {
    document.querySelectorAll('.account-children').forEach(children => {
        children.classList.remove('collapsed');
    });
    document.querySelectorAll('.expand-btn i').forEach(icon => {
        icon.classList.remove('fa-chevron-right');
        icon.classList.add('fa-chevron-down');
    });
    document.querySelectorAll('.expand-btn').forEach(btn => {
        btn.classList.remove('collapsed');
    });
}

function collapseAll() {
    document.querySelectorAll('.account-children').forEach(children => {
        children.classList.add('collapsed');
    });
    document.querySelectorAll('.expand-btn i').forEach(icon => {
        icon.classList.remove('fa-chevron-down');
        icon.classList.add('fa-chevron-right');
    });
    document.querySelectorAll('.expand-btn').forEach(btn => {
        btn.classList.add('collapsed');
    });
}

function showBalances() {
    const balances = document.querySelectorAll('.balance');
    const isVisible = balances[0] && balances[0].style.display !== 'none';
    
    balances.forEach(balance => {
        balance.style.display = isVisible ? 'none' : 'inline';
    });
}

// Show/hide inactive accounts
document.getElementById('showInactive').addEventListener('change', function() {
    const showInactive = this.checked;
    document.querySelectorAll('.account-node').forEach(node => {
        const hasInactiveBadge = node.querySelector('.badge.bg-secondary');
        if (hasInactiveBadge && !showInactive) {
            node.style.display = 'none';
        } else {
            node.style.display = 'block';
        }
    });
});

// Initialize collapsed state for deep levels
document.addEventListener('DOMContentLoaded', function() {
    // Collapse levels deeper than 2 by default
    document.querySelectorAll('.account-node[data-level]').forEach(node => {
        const level = parseInt(node.dataset.level);
        if (level > 1) {
            const children = node.querySelector('.account-children');
            const button = node.querySelector('.expand-btn');
            if (children && button) {
                children.classList.add('collapsed');
                button.classList.add('collapsed');
                const icon = button.querySelector('i');
                if (icon) {
                    icon.classList.remove('fa-chevron-down');
                    icon.classList.add('fa-chevron-right');
                }
            }
        }
    });
});
</script>
{% endblock %}
