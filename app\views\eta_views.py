"""
واجهات إدارة التكامل مع مصلحة الضرائب المصرية
Egyptian Tax Authority (ETA) Integration Views
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app import db
from app.models.eta_integration import ETATaxTransaction, ETASettings, ETATaxCodes, ETAErrorLog
from app.models.invoice import Invoice
from app.models.receipt import Receipt
from app.services.eta_service import ETAService
from app.utils.decorators import admin_required
import json

eta_bp = Blueprint('eta', __name__, url_prefix='/eta')

@eta_bp.route('/')
@login_required
@admin_required
def index():
    """صفحة إدارة التكامل مع مصلحة الضرائب"""
    # إحصائيات التكامل
    total_transactions = ETATaxTransaction.query.count()
    pending_transactions = ETATaxTransaction.query.filter_by(response_status='pending').count()
    successful_transactions = ETATaxTransaction.query.filter_by(response_status='accepted').count()
    failed_transactions = ETATaxTransaction.query.filter_by(response_status='failed').count()

    # آخر المعاملات
    recent_transactions = ETATaxTransaction.query.order_by(ETATaxTransaction.created_at.desc()).limit(10).all()
    
    # الإعدادات الحالية
    settings = {
        'base_url': ETASettings.get_setting('ETA_BASE_URL'),
        'environment': ETASettings.get_setting('ETA_ENVIRONMENT'),
        'client_id': ETASettings.get_setting('ETA_CLIENT_ID'),
        'auto_submit': ETASettings.get_setting('ETA_AUTO_SUBMIT'),
        'company_tax_id': ETASettings.get_setting('COMPANY_TAX_ID'),
        'company_activity_code': ETASettings.get_setting('COMPANY_ACTIVITY_CODE'),
        'company_branch_id': ETASettings.get_setting('COMPANY_BRANCH_ID')
    }
    
    return render_template('eta/index.html',
                         total_transactions=total_transactions,
                         pending_transactions=pending_transactions,
                         successful_transactions=successful_transactions,
                         failed_transactions=failed_transactions,
                         recent_transactions=recent_transactions,
                         settings=settings)

@eta_bp.route('/settings')
@login_required
@admin_required
def settings():
    """صفحة إعدادات التكامل"""
    all_settings = ETASettings.query.all()
    return render_template('eta/settings.html', settings=all_settings)

@eta_bp.route('/settings', methods=['POST'])
@login_required
@admin_required
def update_settings():
    """تحديث إعدادات التكامل"""
    try:
        settings_data = request.get_json()
        
        for key, value in settings_data.items():
            ETASettings.set_setting(key, value)
        
        return jsonify({'success': True, 'message': 'تم تحديث الإعدادات بنجاح'})
    
    except Exception as e:
        return jsonify({'success': False, 'message': f'خطأ في تحديث الإعدادات: {str(e)}'})

@eta_bp.route('/test-connection')
@login_required
@admin_required
def test_connection():
    """اختبار الاتصال مع مصلحة الضرائب"""
    try:
        eta_service = ETAService()
        
        # اختبار المصادقة
        if eta_service.authenticate():
            # اختبار الحصول على أنواع المستندات
            document_types = eta_service.get_document_types()
            
            return jsonify({
                'success': True,
                'message': 'تم الاتصال بنجاح مع مصلحة الضرائب',
                'document_types_count': len(document_types),
                'token_expires_at': eta_service.token_expires_at.isoformat() if eta_service.token_expires_at else None
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في المصادقة مع مصلحة الضرائب'
            })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في الاتصال: {str(e)}'
        })

@eta_bp.route('/submit-invoice/<invoice_id>')
@login_required
def submit_invoice(invoice_id):
    """إرسال فاتورة إلى مصلحة الضرائب"""
    try:
        invoice = Invoice.query.get_or_404(invoice_id)
        
        # التحقق من أن الفاتورة لم يتم إرسالها من قبل
        if invoice.eta_status in ['submitted', 'accepted']:
            return jsonify({
                'success': False,
                'message': 'تم إرسال هذه الفاتورة مسبقاً'
            })
        
        eta_service = ETAService()
        success, response_data = eta_service.submit_invoice(invoice)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'تم إرسال الفاتورة بنجاح',
                'submission_uuid': response_data.get('submissionId'),
                'eta_status': invoice.eta_status
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في إرسال الفاتورة',
                'error_details': response_data
            })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إرسال الفاتورة: {str(e)}'
        })

@eta_bp.route('/submit-receipt/<receipt_id>')
@login_required
def submit_receipt(receipt_id):
    """إرسال إيصال إلى مصلحة الضرائب"""
    try:
        receipt = Receipt.query.get_or_404(receipt_id)
        
        # التحقق من أن الإيصال لم يتم إرسالها من قبل
        if receipt.eta_status in ['submitted', 'accepted']:
            return jsonify({
                'success': False,
                'message': 'تم إرسال هذا الإيصال مسبقاً'
            })
        
        eta_service = ETAService()
        success, response_data = eta_service.submit_receipt(receipt)
        
        if success:
            return jsonify({
                'success': True,
                'message': 'تم إرسال الإيصال بنجاح',
                'eta_uuid': response_data.get('uuid'),
                'eta_status': receipt.eta_status
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في إرسال الإيصال',
                'error_details': response_data
            })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إرسال الإيصال: {str(e)}'
        })

@eta_bp.route('/transactions')
@login_required
@admin_required
def transactions():
    """عرض جميع معاملات الضرائب"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    transactions = ETATaxTransaction.query.order_by(
        ETATaxTransaction.created_at.desc()
    ).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('eta/transactions.html', transactions=transactions)

@eta_bp.route('/transaction/<transaction_id>')
@login_required
@admin_required
def transaction_details(transaction_id):
    """تفاصيل معاملة ضريبية"""
    transaction = ETATaxTransaction.query.get_or_404(transaction_id)
    return render_template('eta/transaction_details.html', transaction=transaction)

@eta_bp.route('/check-status/<submission_uuid>')
@login_required
def check_submission_status(submission_uuid):
    """التحقق من حالة الإرسال"""
    try:
        eta_service = ETAService()
        success, response_data = eta_service.check_submission_status(submission_uuid)
        
        if success:
            # تحديث حالة المعاملة
            transaction = ETATaxTransaction.query.filter_by(submission_uuid=submission_uuid).first()
            if transaction:
                # تحديث الحالة حسب الاستجابة
                status = response_data.get('status', 'unknown')
                transaction.response_status = status
                transaction.response_data = response_data
                db.session.commit()
            
            return jsonify({
                'success': True,
                'status': status,
                'response_data': response_data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في التحقق من الحالة',
                'error_details': response_data
            })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في التحقق من الحالة: {str(e)}'
        })

@eta_bp.route('/error-logs')
@login_required
@admin_required
def error_logs():
    """عرض سجل الأخطاء"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    errors = ETAErrorLog.query.order_by(
        ETAErrorLog.occurred_at.desc()
    ).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('eta/error_logs.html', errors=errors)

@eta_bp.route('/tax-codes')
@login_required
@admin_required
def tax_codes():
    """إدارة أكواد الضرائب"""
    codes = ETATaxCodes.query.filter_by(is_active=True).all()
    return render_template('eta/tax_codes.html', codes=codes)

@eta_bp.route('/sync-codes')
@login_required
@admin_required
def sync_tax_codes():
    """مزامنة أكواد الضرائب من مصلحة الضرائب"""
    try:
        eta_service = ETAService()
        
        # هنا يمكن إضافة منطق مزامنة الأكواد
        # من API مصلحة الضرائب
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث أكواد الضرائب بنجاح'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في تحديث الأكواد: {str(e)}'
        })
