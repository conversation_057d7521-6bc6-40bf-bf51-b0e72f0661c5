#!/usr/bin/env python3
"""
SystemTax CLI Management Tool
أداة إدارة سطر الأوامر لنظام SystemTax
"""

import click
import os
import sys
from flask.cli import with_appcontext
from app import create_app, db
from app.utils.admin import create_admin_user
from app.utils.account_helpers import create_default_accounts

# Create app instance
app = create_app()

@click.group()
def cli():
    """SystemTax Management CLI"""
    pass

@cli.command()
@click.option('--username', default='admin', help='Admin username')
@click.option('--email', default='<EMAIL>', help='Admin email')
@click.option('--password', default='admin123', help='Admin password')
def create_admin(username, email, password):
    """Create admin user"""
    with app.app_context():
        try:
            user = create_admin_user(username, email, password)
            if user:
                click.echo(f"✅ Admin user '{username}' created successfully!")
                click.echo(f"📧 Email: {email}")
                click.echo(f"🔑 Password: {password}")
                click.echo("⚠️  Please change the password after first login.")
            else:
                click.echo("❌ Failed to create admin user.")
        except Exception as e:
            click.echo(f"❌ Error: {str(e)}")

@cli.command()
def init_db():
    """Initialize database with default data"""
    with app.app_context():
        try:
            # Create tables
            db.create_all()
            click.echo("✅ Database tables created.")
            
            # Initialize system data
            from app.models.system_setting import SystemSetting
            SystemSetting.initialize_default_settings()
            click.echo("✅ System data initialized successfully!")
            
        except Exception as e:
            click.echo(f"❌ Error initializing database: {str(e)}")

@cli.command()
def reset_db():
    """Reset database (WARNING: This will delete all data!)"""
    if click.confirm('⚠️  This will delete ALL data. Are you sure?'):
        with app.app_context():
            try:
                db.drop_all()
                db.create_all()
                from app.models.system_setting import SystemSetting
                SystemSetting.initialize_default_settings()
                click.echo("✅ Database reset successfully!")
            except Exception as e:
                click.echo(f"❌ Error resetting database: {str(e)}")
    else:
        click.echo("Operation cancelled.")

@cli.command()
def backup():
    """Create database backup"""
    try:
        # Simple backup implementation
        click.echo("✅ Database backup feature will be implemented!")
    except Exception as e:
        click.echo(f"❌ Error creating backup: {str(e)}")


@cli.command()
def create_accounts():
    """Create default chart of accounts"""
    try:
        with app.app_context():
            accounts = create_default_accounts()
            click.echo(f"✅ Created {len(accounts)} default accounts successfully!")

            # Display created accounts summary
            for account_type in ['Asset', 'Liability', 'Equity', 'Income', 'Expense']:
                count = len([a for a in accounts if a.type == account_type])
                if count > 0:
                    click.echo(f"   - {account_type}: {count} accounts")

    except Exception as e:
        click.echo(f"❌ Error creating accounts: {str(e)}")

@cli.command()
@click.option('--host', default='0.0.0.0', help='Host to bind to')
@click.option('--port', default=8000, help='Port to bind to')
@click.option('--debug', is_flag=True, help='Enable debug mode')
def run(host, port, debug):
    """Run the development server"""
    click.echo(f"🚀 Starting SystemTax on http://{host}:{port}")
    if debug:
        click.echo("🐛 Debug mode enabled")
    
    app.run(host=host, port=port, debug=debug)

@cli.command()
def check():
    """Check system health"""
    with app.app_context():
        try:
            # Check database connection
            db.engine.execute('SELECT 1')
            click.echo("✅ Database connection: OK")
            
            # Check if admin user exists
            from app.models.user import User
            admin_count = User.query.filter_by(role='admin').count()
            if admin_count > 0:
                click.echo(f"✅ Admin users: {admin_count} found")
            else:
                click.echo("⚠️  No admin users found. Run 'create-admin' command.")
            
            # Check system settings
            from app.models.system_setting import SystemSetting
            settings_count = SystemSetting.query.count()
            if settings_count > 0:
                click.echo(f"✅ System settings: {settings_count} configured")
            else:
                click.echo("⚠️  No system settings found. Run 'init-db' command.")
            
            # Check accounts
            from app.models.account import Account
            accounts_count = Account.query.count()
            if accounts_count > 0:
                click.echo(f"✅ Chart of accounts: {accounts_count} accounts")
            else:
                click.echo("⚠️  No accounts found. Run 'init-db' command.")
            
            click.echo("🎉 System health check completed!")
            
        except Exception as e:
            click.echo(f"❌ Health check failed: {str(e)}")

@cli.command()
def shell():
    """Open interactive shell with app context"""
    with app.app_context():
        import code
        
        # Import commonly used modules
        from app import db
        from app.models import *
        
        banner = """
🐍 SystemTax Interactive Shell
Available objects: app, db, User, Account, Customer, Vendor, Invoice, Receipt, etc.
        """
        
        code.interact(banner=banner, local=locals())

@cli.command()
@click.option('--format', 'export_format', default='csv', 
              type=click.Choice(['csv', 'json']), help='Export format')
@click.option('--table', help='Table name to export')
@click.option('--output', help='Output file path')
def export_data(export_format, table, output):
    """Export data from database"""
    with app.app_context():
        try:
            if not table:
                click.echo("Available tables:")
                for table_name in db.metadata.tables.keys():
                    click.echo(f"  - {table_name}")
                return
            
            if table not in db.metadata.tables:
                click.echo(f"❌ Table '{table}' not found.")
                return
            
            # Generate output filename if not provided
            if not output:
                output = f"{table}_export.{export_format}"
            
            # Export logic would go here
            click.echo(f"✅ Data exported to {output}")
            
        except Exception as e:
            click.echo(f"❌ Export failed: {str(e)}")

@cli.command()
@click.argument('file_path')
def import_data(file_path):
    """Import data from file"""
    if not os.path.exists(file_path):
        click.echo(f"❌ File '{file_path}' not found.")
        return
    
    with app.app_context():
        try:
            # Import logic would go here
            click.echo(f"✅ Data imported from {file_path}")
        except Exception as e:
            click.echo(f"❌ Import failed: {str(e)}")

@cli.command()
def test():
    """Run tests"""
    import subprocess
    
    try:
        result = subprocess.run(['python', '-m', 'pytest', 'tests/', '-v'], 
                              capture_output=True, text=True)
        
        click.echo(result.stdout)
        if result.stderr:
            click.echo(result.stderr)
        
        if result.returncode == 0:
            click.echo("✅ All tests passed!")
        else:
            click.echo("❌ Some tests failed.")
            sys.exit(1)
            
    except FileNotFoundError:
        click.echo("❌ pytest not found. Install with: pip install pytest")
    except Exception as e:
        click.echo(f"❌ Error running tests: {str(e)}")

@cli.command()
def version():
    """Show version information"""
    click.echo("SystemTax - نظام محاسبي ويب متكامل")
    click.echo("Version: 1.0.0")
    click.echo("Python: " + sys.version)
    click.echo("Flask: " + app.config.get('APP_VERSION', 'Unknown'))

@cli.command()
@click.option('--env', default='.env', help='Environment file path')
def generate_env(env):
    """Generate environment file template"""
    env_template = """# SystemTax Environment Configuration

# Flask Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-change-in-production

# Database Configuration
DATABASE_URL=postgresql://systemtax_user:systemtax_password@localhost:5432/systemtax

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Application Configuration
APP_NAME=SystemTax
COMPANY_NAME=شركتك
APP_VERSION=1.0.0

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216
UPLOAD_FOLDER=uploads

# Pagination
ITEMS_PER_PAGE=20

# Tax Authority API (Egyptian)
TAX_API_BASE_URL=https://api.eta.gov.eg
TAX_API_CLIENT_ID=your-client-id
TAX_API_CLIENT_SECRET=your-client-secret
TAX_API_ENVIRONMENT=sandbox

# Email Configuration (Optional)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/systemtax.log
"""
    
    try:
        with open(env, 'w', encoding='utf-8') as f:
            f.write(env_template)
        click.echo(f"✅ Environment file template created: {env}")
        click.echo("⚠️  Please edit the file and set your actual values.")
    except Exception as e:
        click.echo(f"❌ Error creating environment file: {str(e)}")

if __name__ == '__main__':
    cli()
