{% extends "base.html" %}

{% block title %}الفواتير - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-file-invoice me-3"></i>
                الفواتير الإلكترونية
            </h1>
            <p class="mb-0 mt-2">إدارة الفواتير الإلكترونية والتكامل مع منظومة الضرائب المصرية</p>
        </div>
        <div>
            <a href="{{ url_for('invoices.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                فاتورة جديدة
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-light dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('invoices.export', format='pdf') }}">PDF</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('invoices.export', format='excel') }}">Excel</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('invoices.export', format='csv') }}">CSV</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                {{ form.search.label(class="form-label") }}
                {{ form.search(class="form-control", placeholder="البحث في رقم الفاتورة أو العميل...") }}
            </div>
            <div class="col-md-2">
                {{ form.status.label(class="form-label") }}
                {{ form.status(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ form.customer_id.label(class="form-label") }}
                {{ form.customer_id(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ form.date_from.label(class="form-label") }}
                {{ form.date_from(class="form-control") }}
            </div>
            <div class="col-md-2">
                {{ form.date_to.label(class="form-label") }}
                {{ form.date_to(class="form-control") }}
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Invoices Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            قائمة الفواتير
        </h5>
        <span class="badge bg-primary">{{ invoices.total if invoices else 0 }} فاتورة</span>
    </div>
    <div class="card-body">
        {% if invoices and invoices.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all" class="form-check-input">
                        </th>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>التاريخ</th>
                        <th>المبلغ الإجمالي</th>
                        <th>الضريبة</th>
                        <th>الحالة</th>
                        <th>حالة الضرائب</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for invoice in invoices.items %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input invoice-checkbox" 
                                   value="{{ invoice.id }}">
                        </td>
                        <td>
                            <a href="{{ url_for('invoices.detail', invoice_id=invoice.id) }}" 
                               class="text-decoration-none fw-bold">
                                {{ invoice.invoice_number }}
                            </a>
                            {% if invoice.reference_number %}
                                <br><small class="text-muted">مرجع: {{ invoice.reference_number }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title bg-primary rounded-circle">
                                        {{ invoice.customer.name[0] if invoice.customer else 'ع' }}
                                    </div>
                                </div>
                                <div>
                                    {% if invoice.customer %}
                                        <a href="{{ url_for('customers.detail', customer_id=invoice.customer.id) }}" 
                                           class="text-decoration-none">
                                            {{ invoice.customer.name }}
                                        </a>
                                        {% if invoice.customer.tax_id %}
                                            <br><small class="text-muted">{{ invoice.customer.tax_id }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">عميل محذوف</span>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {{ invoice.invoice_date|date }}
                            {% if invoice.due_date %}
                                <br><small class="text-muted">استحقاق: {{ invoice.due_date|date }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="fw-bold">{{ invoice.total_amount|currency }}</span>
                            {% if invoice.discount_amount > 0 %}
                                <br><small class="text-success">خصم: {{ invoice.discount_amount|currency }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="text-info">{{ invoice.tax_amount|currency }}</span>
                            <br><small class="text-muted">{{ invoice.tax_rate }}%</small>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'warning' if invoice.status == 'pending' else 'danger' if invoice.status == 'overdue' else 'secondary' }}">
                                {{ invoice.get_status_display() }}
                            </span>
                        </td>
                        <td>
                            {% if invoice.eta_status %}
                                <span class="badge bg-{{ 'success' if invoice.eta_status == 'submitted' else 'warning' if invoice.eta_status == 'pending' else 'danger' }}">
                                    {{ invoice.get_eta_status_display() }}
                                </span>
                                {% if invoice.eta_uuid %}
                                    <br><small class="text-muted">{{ invoice.eta_uuid[:8] }}...</small>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">غير مرسل</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('invoices.detail', invoice_id=invoice.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if invoice.can_be_edited() %}
                                <a href="{{ url_for('invoices.edit', invoice_id=invoice.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                <a href="{{ url_for('invoices.pdf', invoice_id=invoice.id) }}" 
                                   class="btn btn-outline-info" title="PDF" target="_blank">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                {% if not invoice.eta_status or invoice.eta_status == 'failed' %}
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="submitToETA('{{ invoice.id }}')" title="إرسال للضرائب">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                                {% endif %}
                                {% if invoice.can_be_deleted() %}
                                <form method="POST" action="{{ url_for('invoices.delete', invoice_id=invoice.id) }}" 
                                      class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذه الفاتورة؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Bulk Actions -->
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <select class="form-select me-2" id="bulk-action" style="width: auto;">
                        <option value="">اختر إجراء</option>
                        <option value="submit_eta">إرسال للضرائب</option>
                        <option value="generate_pdf">إنتاج PDF</option>
                        <option value="send_email">إرسال بريد</option>
                        <option value="mark_paid">تحديد كمدفوع</option>
                        <option value="export">تصدير</option>
                        <option value="delete">حذف</option>
                    </select>
                    <button type="button" class="btn btn-outline-secondary" onclick="executeBulkAction()">
                        تنفيذ
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <small class="text-muted">
                    <span id="selected-count">0</span> فاتورة محددة
                </small>
            </div>
        </div>

        <!-- Pagination -->
        {% if invoices.pages > 1 %}
        <nav aria-label="تصفح الصفحات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if invoices.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('invoices.index', page=invoices.prev_num, **request.args) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in invoices.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != invoices.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('invoices.index', page=page_num, **request.args) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if invoices.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('invoices.index', page=invoices.next_num, **request.args) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
            <h5>لا توجد فواتير</h5>
            <p class="text-muted">لم يتم العثور على أي فواتير تطابق معايير البحث.</p>
            <a href="{{ url_for('invoices.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء فاتورة جديدة
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">إجمالي الفواتير</h5>
                <h3 class="text-primary">{{ stats.total_invoices if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">فواتير مدفوعة</h5>
                <h3 class="text-success">{{ stats.paid_invoices if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">فواتير معلقة</h5>
                <h3 class="text-warning">{{ stats.pending_invoices if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">إجمالي المبيعات</h5>
                <h3 class="text-info">{{ stats.total_sales|currency if stats else '0.00 ج.م' }}</h3>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Select all checkbox functionality
    $('#select-all').change(function() {
        $('.invoice-checkbox').prop('checked', this.checked);
        updateSelectedCount();
    });
    
    $('.invoice-checkbox').change(function() {
        updateSelectedCount();
        
        // Update select all checkbox
        const totalCheckboxes = $('.invoice-checkbox').length;
        const checkedCheckboxes = $('.invoice-checkbox:checked').length;
        
        $('#select-all').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
        $('#select-all').prop('checked', checkedCheckboxes === totalCheckboxes);
    });
    
    // Auto-submit search form on status change
    $('#status, #customer_id').change(function() {
        $(this).closest('form').submit();
    });
});

function updateSelectedCount() {
    const count = $('.invoice-checkbox:checked').length;
    $('#selected-count').text(count);
}

function submitToETA(invoiceId) {
    if (confirm('هل تريد إرسال هذه الفاتورة إلى منظومة الضرائب المصرية؟')) {
        fetch(`/invoices/${invoiceId}/submit-eta`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال الفاتورة بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function executeBulkAction() {
    const action = $('#bulk-action').val();
    const selectedInvoices = $('.invoice-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (!action) {
        alert('يرجى اختيار إجراء');
        return;
    }
    
    if (selectedInvoices.length === 0) {
        alert('يرجى تحديد فاتورة واحدة على الأقل');
        return;
    }
    
    if (confirm(`هل تريد تنفيذ هذا الإجراء على ${selectedInvoices.length} فاتورة؟`)) {
        // Create form and submit
        const form = $('<form>', {
            method: 'POST',
            action: '{{ url_for("invoices.bulk_action") }}'
        });
        
        form.append($('<input>', {
            type: 'hidden',
            name: 'csrf_token',
            value: '{{ csrf_token() }}'
        }));
        
        form.append($('<input>', {
            type: 'hidden',
            name: 'action',
            value: action
        }));
        
        form.append($('<input>', {
            type: 'hidden',
            name: 'invoice_ids',
            value: selectedInvoices.join(',')
        }));
        
        $('body').append(form);
        form.submit();
    }
}
</script>
{% endblock %}

{% block extra_css %}
<style>
.invoice-status-draft { color: #6c757d; }
.invoice-status-pending { color: #ffc107; }
.invoice-status-paid { color: #28a745; }
.invoice-status-overdue { color: #dc3545; }
.invoice-status-cancelled { color: #6c757d; }

.eta-status-pending { color: #ffc107; }
.eta-status-submitted { color: #28a745; }
.eta-status-failed { color: #dc3545; }

.avatar-sm {
    width: 32px;
    height: 32px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
}
</style>
{% endblock %}
