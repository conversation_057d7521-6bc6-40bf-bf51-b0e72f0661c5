{% extends "base.html" %}

{% block title %}تقرير الضريبة المقتطعة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">تقرير الضريبة المقتطعة</h1>
                <div>
                    <a href="{{ url_for('reports.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-2"></i>
                        العودة للتقارير
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-filter me-2"></i>
                فلاتر التقرير
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" 
                           value="{{ request.args.get('date_from', '') }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" 
                           value="{{ request.args.get('date_to', '') }}">
                </div>
                <div class="col-md-3">
                    <label for="tax_type" class="form-label">نوع الضريبة</label>
                    <select class="form-select" id="tax_type" name="tax_type">
                        <option value="">جميع الأنواع</option>
                        <option value="income" {{ 'selected' if request.args.get('tax_type') == 'income' }}>ضريبة الدخل</option>
                        <option value="professional" {{ 'selected' if request.args.get('tax_type') == 'professional' }}>ضريبة مهنية</option>
                        <option value="service" {{ 'selected' if request.args.get('tax_type') == 'service' }}>ضريبة خدمات</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            تطبيق الفلاتر
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Report Content -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-cut me-2"></i>
                الضرائب المقتطعة
            </h5>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="#" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>PDF
                    </a></li>
                    <li><a class="dropdown-item" href="#">
                        <i class="fas fa-file-excel me-2"></i>Excel
                    </a></li>
                </ul>
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                سيتم تنفيذ تقرير الضريبة المقتطعة قريباً. هذا التقرير سيعرض جميع الضرائب المقتطعة من المنبع.
            </div>

            <!-- Placeholder table -->
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>التاريخ</th>
                            <th>رقم المستند</th>
                            <th>الجهة</th>
                            <th>نوع الضريبة</th>
                            <th>المبلغ الأساسي</th>
                            <th>معدل الضريبة</th>
                            <th>مبلغ الضريبة</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td colspan="8" class="text-center text-muted py-4">
                                <i class="fas fa-inbox fa-2x mb-2"></i>
                                <br>
                                لا توجد بيانات للعرض
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Summary -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-primary">إجمالي الضرائب</h5>
                    <h3 class="text-primary">0.00 ج.م</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-success">المدفوع</h5>
                    <h3 class="text-success">0.00 ج.م</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-warning">المستحق</h5>
                    <h3 class="text-warning">0.00 ج.م</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h5 class="card-title text-info">عدد المعاملات</h5>
                    <h3 class="text-info">0</h3>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
