#!/usr/bin/env python3
"""
إنشاء جميع جداول النظام
Create all system tables
"""

import sqlite3
import os
from datetime import datetime
from werkzeug.security import generate_password_hash

def backup_database():
    """إنشاء نسخة احتياطية"""
    db_path = 'instance/systemtax.db'
    if os.path.exists(db_path):
        backup_path = f'instance/systemtax_backup_tables_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return backup_path
    return None

def create_all_tables():
    """إنشاء جميع الجداول المطلوبة"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🏗️ إنشاء جداول النظام...")
        
        # جدول المستخدمين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(80) UNIQUE NOT NULL,
                email VARCHAR(120) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                role VARCHAR(20) DEFAULT 'user',
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ جدول المستخدمين")
        
        # جدول الحسابات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(200) NOT NULL,
                account_type VARCHAR(50) NOT NULL,
                parent_id INTEGER,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES accounts (id)
            )
        """)
        print("✅ جدول الحسابات")
        
        # جدول العملاء
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(200) NOT NULL,
                email VARCHAR(120),
                phone VARCHAR(20),
                address TEXT,
                tax_id VARCHAR(50),
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ جدول العملاء")
        
        # جدول الموردين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS vendors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(200) NOT NULL,
                email VARCHAR(120),
                phone VARCHAR(20),
                address TEXT,
                tax_id VARCHAR(50),
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ جدول الموردين")
        
        # جدول القيود اليومية
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS journal_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_number VARCHAR(50) UNIQUE NOT NULL,
                date DATE NOT NULL,
                description TEXT,
                reference VARCHAR(100),
                total_debit DECIMAL(15,2) DEFAULT 0,
                total_credit DECIMAL(15,2) DEFAULT 0,
                is_posted BOOLEAN DEFAULT 0,
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)
        print("✅ جدول القيود اليومية")
        
        # جدول تفاصيل القيود
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS journal_entry_lines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                journal_entry_id INTEGER NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                debit DECIMAL(15,2) DEFAULT 0,
                credit DECIMAL(15,2) DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id),
                FOREIGN KEY (account_id) REFERENCES accounts (id)
            )
        """)
        print("✅ جدول تفاصيل القيود")
        
        # جدول الفواتير
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number VARCHAR(50) UNIQUE NOT NULL,
                customer_id INTEGER,
                date DATE NOT NULL,
                due_date DATE,
                subtotal DECIMAL(15,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft',
                notes TEXT,
                eta_submission_uuid VARCHAR(100),
                eta_status VARCHAR(50),
                eta_submitted_at DATETIME,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)
        print("✅ جدول الفواتير")
        
        # جدول الإيصالات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS receipts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                receipt_number VARCHAR(50) UNIQUE NOT NULL,
                customer_id INTEGER,
                date DATE NOT NULL,
                subtotal DECIMAL(15,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft',
                notes TEXT,
                eta_uuid VARCHAR(100),
                eta_internal_id VARCHAR(100),
                eta_status VARCHAR(50),
                eta_submitted_at DATETIME,
                qr_code_path VARCHAR(255),
                previous_uuid VARCHAR(100),
                reference_old_uuid VARCHAR(100),
                document_type_name VARCHAR(50) DEFAULT 's',
                document_type_version VARCHAR(10) DEFAULT '1.2',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)
        print("✅ جدول الإيصالات")
        
        # جدول بنود الإيصالات
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS receipt_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                receipt_id INTEGER NOT NULL,
                description VARCHAR(255) NOT NULL,
                quantity DECIMAL(10,3) DEFAULT 1,
                unit_price DECIMAL(15,2) NOT NULL,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (receipt_id) REFERENCES receipts (id)
            )
        """)
        print("✅ جدول بنود الإيصالات")
        
        # جدول المعاملات الضريبية
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS tax_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_type VARCHAR(50) NOT NULL,
                reference_id INTEGER NOT NULL,
                reference_type VARCHAR(50) NOT NULL,
                tax_amount DECIMAL(15,2) NOT NULL,
                tax_rate DECIMAL(5,2) NOT NULL,
                eta_status VARCHAR(50),
                eta_response TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ جدول المعاملات الضريبية")
        
        # جدول إعدادات النظام (إذا لم يكن موجود)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key VARCHAR(100) UNIQUE NOT NULL,
                value TEXT,
                data_type VARCHAR(20) DEFAULT 'string',
                category VARCHAR(50) DEFAULT 'general',
                description TEXT,
                is_encrypted BOOLEAN DEFAULT 0,
                is_system BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100)
            )
        """)
        print("✅ جدول إعدادات النظام")
        
        # إنشاء الفهارس
        print("\n📋 إنشاء الفهارس...")
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_code ON accounts(code)",
            "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)",
            "CREATE INDEX IF NOT EXISTS idx_vendors_name ON vendors(name)",
            "CREATE INDEX IF NOT EXISTS idx_journal_entries_number ON journal_entries(entry_number)",
            "CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries(date)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_number ON invoices(invoice_number)",
            "CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(date)",
            "CREATE INDEX IF NOT EXISTS idx_receipts_number ON receipts(receipt_number)",
            "CREATE INDEX IF NOT EXISTS idx_receipts_date ON receipts(date)",
            "CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(key)",
            "CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category)"
        ]
        
        for index in indexes:
            cursor.execute(index)
        
        print("✅ تم إنشاء جميع الفهارس")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إنشاء جميع الجداول بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

def create_default_user():
    """إنشاء المستخدم الافتراضي"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود المستخدم admin
        cursor.execute("SELECT id FROM users WHERE username = ?", ('admin',))
        admin_exists = cursor.fetchone()
        
        if not admin_exists:
            # إنشاء كلمة مرور مشفرة
            password_hash = generate_password_hash('admin123')
            
            # إدراج المستخدم الافتراضي
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            """, ('admin', '<EMAIL>', password_hash, 'admin', 1))
            
            print("✅ تم إنشاء المستخدم الافتراضي: admin / admin123")
        else:
            print("ℹ️ المستخدم admin موجود مسبقاً")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المستخدم: {e}")
        return False

def create_default_settings():
    """إنشاء الإعدادات الافتراضية"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الإعدادات الافتراضية
        default_settings = [
            ('ETA_BASE_URL', 'https://api.preprod.invoicing.eta.gov.eg/api/v1', 'string', 'eta', 'رابط API مصلحة الضرائب'),
            ('ETA_ENVIRONMENT', 'sandbox', 'string', 'eta', 'بيئة العمل'),
            ('ETA_TIMEOUT', '60', 'integer', 'eta', 'مهلة الاتصال بالثواني'),
            ('ETA_AUTO_SUBMIT', 'false', 'boolean', 'eta', 'الإرسال التلقائي'),
            ('COMPANY_NAME', 'شركتك', 'string', 'company', 'اسم الشركة'),
            ('COMPANY_TAX_ID', '', 'string', 'company', 'الرقم الضريبي'),
            ('COMPANY_ACTIVITY_CODE', '', 'string', 'company', 'كود النشاط'),
            ('COMPANY_BRANCH_ID', '0', 'string', 'company', 'كود الفرع'),
            ('APP_NAME', 'SystemTax', 'string', 'system', 'اسم التطبيق'),
            ('APP_VERSION', '1.0.0', 'string', 'system', 'إصدار التطبيق')
        ]
        
        for key, value, data_type, category, description in default_settings:
            # التحقق من وجود الإعداد
            cursor.execute("SELECT id FROM system_settings WHERE key = ?", (key,))
            setting_exists = cursor.fetchone()
            
            if not setting_exists:
                cursor.execute("""
                    INSERT INTO system_settings (key, value, data_type, category, description, is_system)
                    VALUES (?, ?, ?, ?, ?, 1)
                """, (key, value, data_type, category, description))
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء الإعدادات الافتراضية")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الإعدادات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🏗️ إنشاء جميع جداول النظام")
    print("=" * 50)
    
    # إنشاء نسخة احتياطية
    backup_path = backup_database()
    
    # إنشاء جميع الجداول
    if not create_all_tables():
        print("❌ فشل في إنشاء الجداول")
        return False
    
    # إنشاء المستخدم الافتراضي
    print("\n👤 إنشاء المستخدم الافتراضي...")
    if not create_default_user():
        print("❌ فشل في إنشاء المستخدم")
        return False
    
    # إنشاء الإعدادات الافتراضية
    print("\n⚙️ إنشاء الإعدادات الافتراضية...")
    if not create_default_settings():
        print("❌ فشل في إنشاء الإعدادات")
        return False
    
    print("\n🎉 تم إعداد قاعدة البيانات بنجاح!")
    print("✅ يمكنك الآن تشغيل النظام: python app.py")
    print("🔐 بيانات الدخول: admin / admin123")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ فشل في الإعداد!")
            exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إلغاء العملية")
        exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        exit(1)
