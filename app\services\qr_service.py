"""
خدمة إنشاء QR Code للإيصالات الإلكترونية
QR Code Service for Electronic Receipts
"""

import qrcode
import qrcode.image.svg
from PIL import Image, ImageDraw, ImageFont
import io
import base64
import os
from typing import Optional, Tuple
from app.utils.logger import get_logger

logger = get_logger(__name__)


class QRCodeService:
    """خدمة إنشاء وإدارة QR Code للإيصالات الإلكترونية"""
    
    def __init__(self):
        self.qr_dir = os.path.join('uploads', 'qr_codes')
        self.ensure_qr_directory()
    
    def ensure_qr_directory(self):
        """التأكد من وجود مجلد QR codes"""
        if not os.path.exists(self.qr_dir):
            os.makedirs(self.qr_dir, exist_ok=True)
    
    def generate_qr_code(self, data: str, receipt_number: str, 
                        format: str = 'PNG', size: int = 10) -> Tuple[bool, str]:
        """
        إنشاء QR Code للإيصال الإلكتروني
        
        Args:
            data: البيانات المراد تشفيرها في QR Code
            receipt_number: رقم الإيصال
            format: تنسيق الصورة (PNG, SVG)
            size: حجم QR Code (1-40)
        
        Returns:
            Tuple[bool, str]: (نجح, مسار الملف أو رسالة خطأ)
        """
        try:
            # إنشاء QR Code
            qr = qrcode.QRCode(
                version=1,  # يتم تحديده تلقائياً حسب حجم البيانات
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=size,
                border=4,
            )
            
            qr.add_data(data)
            qr.make(fit=True)
            
            # إنشاء الصورة
            if format.upper() == 'SVG':
                img = qr.make_image(image_factory=qrcode.image.svg.SvgPathImage)
                file_extension = 'svg'
            else:
                img = qr.make_image(fill_color="black", back_color="white")
                file_extension = 'png'
            
            # حفظ الملف
            filename = f"qr_{receipt_number}_{format.lower()}.{file_extension}"
            file_path = os.path.join(self.qr_dir, filename)
            
            img.save(file_path)
            
            logger.info(f"QR Code generated successfully: {file_path}")
            return True, file_path
            
        except Exception as e:
            logger.error(f"Error generating QR Code: {str(e)}")
            return False, str(e)
    
    def generate_qr_with_logo(self, data: str, receipt_number: str, 
                             logo_path: Optional[str] = None) -> Tuple[bool, str]:
        """
        إنشاء QR Code مع شعار الشركة
        
        Args:
            data: البيانات المراد تشفيرها
            receipt_number: رقم الإيصال
            logo_path: مسار شعار الشركة
        
        Returns:
            Tuple[bool, str]: (نجح, مسار الملف أو رسالة خطأ)
        """
        try:
            # إنشاء QR Code أساسي
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_H,  # مستوى خطأ عالي للشعار
                box_size=10,
                border=4,
            )
            
            qr.add_data(data)
            qr.make(fit=True)
            
            # إنشاء صورة QR Code
            qr_img = qr.make_image(fill_color="black", back_color="white").convert('RGB')
            
            # إضافة الشعار إذا كان متوفراً
            if logo_path and os.path.exists(logo_path):
                logo = Image.open(logo_path)
                
                # تحديد حجم الشعار (10% من حجم QR Code)
                qr_width, qr_height = qr_img.size
                logo_size = min(qr_width, qr_height) // 10
                
                # تغيير حجم الشعار
                logo = logo.resize((logo_size, logo_size), Image.Resampling.LANCZOS)
                
                # إضافة خلفية بيضاء للشعار
                logo_bg = Image.new('RGB', (logo_size + 20, logo_size + 20), 'white')
                logo_bg.paste(logo, (10, 10))
                
                # وضع الشعار في وسط QR Code
                logo_pos = ((qr_width - logo_size - 20) // 2, (qr_height - logo_size - 20) // 2)
                qr_img.paste(logo_bg, logo_pos)
            
            # حفظ الملف
            filename = f"qr_{receipt_number}_with_logo.png"
            file_path = os.path.join(self.qr_dir, filename)
            
            qr_img.save(file_path)
            
            logger.info(f"QR Code with logo generated successfully: {file_path}")
            return True, file_path
            
        except Exception as e:
            logger.error(f"Error generating QR Code with logo: {str(e)}")
            return False, str(e)
    
    def generate_qr_base64(self, data: str, format: str = 'PNG') -> Tuple[bool, str]:
        """
        إنشاء QR Code كـ Base64 string للعرض المباشر
        
        Args:
            data: البيانات المراد تشفيرها
            format: تنسيق الصورة
        
        Returns:
            Tuple[bool, str]: (نجح, Base64 string أو رسالة خطأ)
        """
        try:
            # إنشاء QR Code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=8,
                border=4,
            )
            
            qr.add_data(data)
            qr.make(fit=True)
            
            # إنشاء الصورة
            img = qr.make_image(fill_color="black", back_color="white")
            
            # تحويل لـ Base64
            buffer = io.BytesIO()
            img.save(buffer, format=format.upper())
            img_str = base64.b64encode(buffer.getvalue()).decode()
            
            # إنشاء data URL
            data_url = f"data:image/{format.lower()};base64,{img_str}"
            
            logger.info("QR Code Base64 generated successfully")
            return True, data_url
            
        except Exception as e:
            logger.error(f"Error generating QR Code Base64: {str(e)}")
            return False, str(e)
    
    def create_printable_receipt_qr(self, receipt_data: dict, receipt_number: str) -> Tuple[bool, str]:
        """
        إنشاء QR Code جاهز للطباعة مع معلومات الإيصال
        
        Args:
            receipt_data: بيانات الإيصال من ETA
            receipt_number: رقم الإيصال
        
        Returns:
            Tuple[bool, str]: (نجح, مسار الملف أو رسالة خطأ)
        """
        try:
            # استخراج البيانات المهمة للـ QR Code
            qr_data = self.format_eta_qr_data(receipt_data)
            
            # إنشاء QR Code
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_M,
                box_size=8,
                border=4,
            )
            
            qr.add_data(qr_data)
            qr.make(fit=True)
            
            # إنشاء صورة QR Code
            qr_img = qr.make_image(fill_color="black", back_color="white").convert('RGB')
            
            # إنشاء صورة أكبر مع معلومات إضافية
            img_width = 400
            img_height = 500
            final_img = Image.new('RGB', (img_width, img_height), 'white')
            
            # وضع QR Code في الوسط
            qr_size = 300
            qr_img = qr_img.resize((qr_size, qr_size), Image.Resampling.LANCZOS)
            qr_pos = ((img_width - qr_size) // 2, 50)
            final_img.paste(qr_img, qr_pos)
            
            # إضافة نص تحت QR Code
            draw = ImageDraw.Draw(final_img)
            
            try:
                # محاولة استخدام خط عربي
                font_large = ImageFont.truetype("arial.ttf", 16)
                font_small = ImageFont.truetype("arial.ttf", 12)
            except:
                # استخدام الخط الافتراضي
                font_large = ImageFont.load_default()
                font_small = ImageFont.load_default()
            
            # إضافة رقم الإيصال
            text_y = qr_pos[1] + qr_size + 20
            receipt_text = f"Receipt No: {receipt_number}"
            text_bbox = draw.textbbox((0, 0), receipt_text, font=font_large)
            text_width = text_bbox[2] - text_bbox[0]
            text_x = (img_width - text_width) // 2
            draw.text((text_x, text_y), receipt_text, fill="black", font=font_large)
            
            # إضافة معلومات إضافية
            if 'uuid' in receipt_data:
                uuid_text = f"UUID: {receipt_data['uuid'][:16]}..."
                text_y += 30
                text_bbox = draw.textbbox((0, 0), uuid_text, font=font_small)
                text_width = text_bbox[2] - text_bbox[0]
                text_x = (img_width - text_width) // 2
                draw.text((text_x, text_y), uuid_text, fill="gray", font=font_small)
            
            # إضافة تاريخ الإنشاء
            from datetime import datetime
            date_text = f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            text_y += 25
            text_bbox = draw.textbbox((0, 0), date_text, font=font_small)
            text_width = text_bbox[2] - text_bbox[0]
            text_x = (img_width - text_width) // 2
            draw.text((text_x, text_y), date_text, fill="gray", font=font_small)
            
            # حفظ الملف
            filename = f"printable_qr_{receipt_number}.png"
            file_path = os.path.join(self.qr_dir, filename)
            
            final_img.save(file_path, 'PNG', quality=95)
            
            logger.info(f"Printable QR Code generated successfully: {file_path}")
            return True, file_path
            
        except Exception as e:
            logger.error(f"Error generating printable QR Code: {str(e)}")
            return False, str(e)
    
    def format_eta_qr_data(self, receipt_data: dict) -> str:
        """
        تنسيق بيانات الإيصال من ETA لـ QR Code
        
        Args:
            receipt_data: بيانات الإيصال من ETA
        
        Returns:
            str: البيانات المنسقة للـ QR Code
        """
        try:
            # تنسيق البيانات حسب مواصفات مصلحة الضرائب
            qr_parts = []
            
            # UUID الإيصال
            if 'uuid' in receipt_data:
                qr_parts.append(f"UUID:{receipt_data['uuid']}")
            
            # رقم الإيصال
            if 'receiptNumber' in receipt_data:
                qr_parts.append(f"RN:{receipt_data['receiptNumber']}")
            
            # التاريخ
            if 'dateTimeIssued' in receipt_data:
                qr_parts.append(f"DT:{receipt_data['dateTimeIssued']}")
            
            # المبلغ الإجمالي
            if 'totalAmount' in receipt_data:
                qr_parts.append(f"TA:{receipt_data['totalAmount']}")
            
            # الرقم الضريبي للشركة
            if 'seller' in receipt_data and 'rin' in receipt_data['seller']:
                qr_parts.append(f"RIN:{receipt_data['seller']['rin']}")
            
            # ربط البيانات
            qr_data = "|".join(qr_parts)
            
            return qr_data
            
        except Exception as e:
            logger.error(f"Error formatting ETA QR data: {str(e)}")
            # إرجاع البيانات كما هي في حالة الخطأ
            return str(receipt_data)
    
    def get_qr_file_url(self, file_path: str) -> str:
        """
        الحصول على URL للوصول لملف QR Code
        
        Args:
            file_path: مسار الملف
        
        Returns:
            str: URL للوصول للملف
        """
        if file_path and os.path.exists(file_path):
            # تحويل المسار المطلق إلى مسار نسبي للويب
            relative_path = file_path.replace('uploads/', '/uploads/')
            return relative_path
        return ""
    
    def cleanup_old_qr_files(self, days: int = 30):
        """
        تنظيف ملفات QR Code القديمة
        
        Args:
            days: عدد الأيام للاحتفاظ بالملفات
        """
        try:
            import time
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 60 * 60)
            
            for filename in os.listdir(self.qr_dir):
                file_path = os.path.join(self.qr_dir, filename)
                if os.path.isfile(file_path):
                    file_time = os.path.getmtime(file_path)
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        logger.info(f"Deleted old QR file: {filename}")
            
        except Exception as e:
            logger.error(f"Error cleaning up QR files: {str(e)}")
