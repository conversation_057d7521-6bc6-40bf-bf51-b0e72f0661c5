"""
Settings views
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required
from app import db
from app.models.system_setting import SystemSetting
from app.forms.settings import (CompanySettingsForm, InvoiceSettingsForm, 
                               ReceiptSettingsForm, TaxApiSettingsForm, SystemSettingForm)
from app.utils.decorators import admin_required
from app.utils.helpers import flash_errors

settings_bp = Blueprint('settings', __name__)

@settings_bp.route('/')
@admin_required
def index():
    """Settings dashboard"""
    return render_template('settings/index.html')

@settings_bp.route('/company', methods=['GET', 'POST'])
@admin_required
def company():
    """Company settings"""
    form = CompanySettingsForm()
    
    if form.validate_on_submit():
        # Update company settings
        SystemSetting.set_value('company_name', form.company_name.data)
        SystemSetting.set_value('company_name_en', form.company_name_en.data)
        SystemSetting.set_value('company_tax_id', form.company_tax_id.data)
        SystemSetting.set_value('company_address', form.company_address.data)
        SystemSetting.set_value('company_phone', form.company_phone.data)
        SystemSetting.set_value('company_email', form.company_email.data)
        
        flash('تم حفظ إعدادات الشركة بنجاح.', 'success')
        return redirect(url_for('settings.company'))
    
    elif request.method == 'GET':
        # Load current settings
        form.company_name.data = SystemSetting.get_value('company_name', '')
        form.company_name_en.data = SystemSetting.get_value('company_name_en', '')
        form.company_tax_id.data = SystemSetting.get_value('company_tax_id', '')
        form.company_address.data = SystemSetting.get_value('company_address', '')
        form.company_phone.data = SystemSetting.get_value('company_phone', '')
        form.company_email.data = SystemSetting.get_value('company_email', '')
    
    flash_errors(form)
    return render_template('settings/company.html', form=form)

@settings_bp.route('/invoices', methods=['GET', 'POST'])
@admin_required
def invoices():
    """Invoice settings"""
    form = InvoiceSettingsForm()
    
    if form.validate_on_submit():
        # Update invoice settings
        SystemSetting.set_value('invoice_prefix', form.invoice_prefix.data)
        SystemSetting.set_value('default_tax_rate', str(form.default_tax_rate.data))
        SystemSetting.set_value('currency_code', form.currency_code.data)
        SystemSetting.set_value('currency_symbol', form.currency_symbol.data)
        
        flash('تم حفظ إعدادات الفواتير بنجاح.', 'success')
        return redirect(url_for('settings.invoices'))
    
    elif request.method == 'GET':
        # Load current settings
        form.invoice_prefix.data = SystemSetting.get_value('invoice_prefix', 'INV')
        form.default_tax_rate.data = float(SystemSetting.get_value('default_tax_rate', '14'))
        form.currency_code.data = SystemSetting.get_value('currency_code', 'EGP')
        form.currency_symbol.data = SystemSetting.get_value('currency_symbol', 'ج.م')
    
    flash_errors(form)
    return render_template('settings/invoices.html', form=form)

@settings_bp.route('/receipts', methods=['GET', 'POST'])
@admin_required
def receipts():
    """Receipt settings"""
    form = ReceiptSettingsForm()
    
    if form.validate_on_submit():
        # Update receipt settings
        SystemSetting.set_value('receipt_prefix', form.receipt_prefix.data)
        
        flash('تم حفظ إعدادات الإيصالات بنجاح.', 'success')
        return redirect(url_for('settings.receipts'))
    
    elif request.method == 'GET':
        # Load current settings
        form.receipt_prefix.data = SystemSetting.get_value('receipt_prefix', 'REC')
    
    flash_errors(form)
    return render_template('settings/receipts.html', form=form)

@settings_bp.route('/tax-api', methods=['GET', 'POST'])
@admin_required
def tax_api():
    """Tax API settings"""
    form = TaxApiSettingsForm()
    
    if form.validate_on_submit():
        # Update tax API settings
        SystemSetting.set_value('tax_api_base_url', form.tax_api_base_url.data)
        SystemSetting.set_value('tax_api_client_id', form.tax_api_client_id.data)
        SystemSetting.set_value('tax_api_client_secret', form.tax_api_client_secret.data)
        SystemSetting.set_value('tax_api_environment', form.tax_api_environment.data)
        
        flash('تم حفظ إعدادات API الضرائب بنجاح.', 'success')
        return redirect(url_for('settings.tax_api'))
    
    elif request.method == 'GET':
        # Load current settings
        form.tax_api_base_url.data = SystemSetting.get_value('tax_api_base_url', 'https://api.eta.gov.eg')
        form.tax_api_client_id.data = SystemSetting.get_value('tax_api_client_id', '')
        form.tax_api_client_secret.data = SystemSetting.get_value('tax_api_client_secret', '')
        form.tax_api_environment.data = SystemSetting.get_value('tax_api_environment', 'sandbox')
    
    flash_errors(form)
    return render_template('settings/tax_api.html', form=form)

@settings_bp.route('/system')
@admin_required
def system():
    """System settings list"""
    page = request.args.get('page', 1, type=int)
    
    settings = SystemSetting.query.order_by(SystemSetting.key).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('settings/system.html', settings=settings)

@settings_bp.route('/system/new', methods=['GET', 'POST'])
@admin_required
def new_setting():
    """Create new system setting"""
    form = SystemSettingForm()
    
    if form.validate_on_submit():
        # Check if key already exists
        existing = SystemSetting.query.filter_by(key=form.key.data).first()
        if existing:
            flash('هذا المفتاح موجود بالفعل.', 'error')
            return render_template('settings/setting_form.html', form=form, title='إضافة إعداد جديد')
        
        setting = SystemSetting(
            key=form.key.data,
            value=form.value.data,
            description=form.description.data
        )
        
        db.session.add(setting)
        db.session.commit()
        
        flash(f'تم إنشاء الإعداد {setting.key} بنجاح.', 'success')
        return redirect(url_for('settings.system'))
    
    flash_errors(form)
    return render_template('settings/setting_form.html', form=form, title='إضافة إعداد جديد')

@settings_bp.route('/system/<setting_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_setting(setting_id):
    """Edit system setting"""
    setting = SystemSetting.query.get_or_404(setting_id)
    form = SystemSettingForm()
    
    if form.validate_on_submit():
        # Check if key already exists (excluding current setting)
        existing = SystemSetting.query.filter(
            SystemSetting.key == form.key.data,
            SystemSetting.id != setting.id
        ).first()
        if existing:
            flash('هذا المفتاح مستخدم بالفعل.', 'error')
            return render_template('settings/setting_form.html', form=form, setting=setting, title='تعديل الإعداد')
        
        setting.key = form.key.data
        setting.value = form.value.data
        setting.description = form.description.data
        
        db.session.commit()
        
        flash(f'تم تحديث الإعداد {setting.key} بنجاح.', 'success')
        return redirect(url_for('settings.system'))
    
    elif request.method == 'GET':
        form.key.data = setting.key
        form.value.data = setting.value
        form.description.data = setting.description
    
    flash_errors(form)
    return render_template('settings/setting_form.html', form=form, setting=setting, title='تعديل الإعداد')

@settings_bp.route('/system/<setting_id>/delete', methods=['POST'])
@admin_required
def delete_setting(setting_id):
    """Delete system setting"""
    setting = SystemSetting.query.get_or_404(setting_id)
    
    setting_key = setting.key
    db.session.delete(setting)
    db.session.commit()
    
    flash(f'تم حذف الإعداد {setting_key} بنجاح.', 'success')
    return redirect(url_for('settings.system'))

@settings_bp.route('/backup')
@admin_required
def backup():
    """Database backup"""
    try:
        from app.utils.admin import backup_database
        backup_database()
        flash('تم إنشاء نسخة احتياطية من قاعدة البيانات بنجاح.', 'success')
    except Exception as e:
        flash(f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}', 'error')
    
    return redirect(url_for('settings.index'))

@settings_bp.route('/initialize')
@admin_required
def initialize():
    """Initialize system data"""
    try:
        from app.utils.admin import initialize_system_data
        initialize_system_data()
        flash('تم تهيئة بيانات النظام بنجاح.', 'success')
    except Exception as e:
        flash(f'خطأ في تهيئة بيانات النظام: {str(e)}', 'error')
    
    return redirect(url_for('settings.index'))
