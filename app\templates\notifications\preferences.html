{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ url_for('notifications.index') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>
            العودة للإشعارات
        </a>
    </div>

    <form method="POST" class="needs-validation" novalidate>
        {{ csrf_token() }}
        
        <!-- General Settings -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">الإعدادات العامة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="quiet_hours_start" class="form-label">بداية ساعات الهدوء</label>
                            <input type="time" 
                                   class="form-control" 
                                   id="quiet_hours_start" 
                                   name="quiet_hours_start"
                                   value="{{ preferences.get('info', {}).quiet_hours_start or '22:00' }}">
                            <div class="form-text">لن يتم إرسال إشعارات خلال ساعات الهدوء (عدا الإشعارات الطارئة)</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="quiet_hours_end" class="form-label">نهاية ساعات الهدوء</label>
                            <input type="time" 
                                   class="form-control" 
                                   id="quiet_hours_end" 
                                   name="quiet_hours_end"
                                   value="{{ preferences.get('info', {}).quiet_hours_end or '08:00' }}">
                            <div class="form-text">سيتم استئناف الإشعارات بعد انتهاء ساعات الهدوء</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Types Settings -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">إعدادات أنواع الإشعارات</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th>نوع الإشعار</th>
                                <th class="text-center">داخل التطبيق</th>
                                <th class="text-center">البريد الإلكتروني</th>
                                <th class="text-center">الرسائل النصية</th>
                                <th class="text-center">الإشعارات المنبثقة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for type_value, type_name in [
                                ('info', 'معلومات عامة'),
                                ('success', 'عمليات ناجحة'),
                                ('warning', 'تحذيرات'),
                                ('error', 'أخطاء'),
                                ('invoice_due', 'فواتير مستحقة'),
                                ('payment_received', 'مدفوعات مستلمة'),
                                ('low_balance', 'رصيد منخفض'),
                                ('system_update', 'تحديثات النظام'),
                                ('backup_complete', 'اكتمال النسخ الاحتياطي'),
                                ('tax_deadline', 'مواعيد ضريبية')
                            ] %}
                            <tr>
                                <td class="fw-bold">{{ type_name }}</td>
                                <td class="text-center">
                                    <div class="form-check form-switch d-flex justify-content-center">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="{{ type_value }}_in_app"
                                               name="{{ type_value }}_in_app"
                                               {% if preferences.get(type_value, {}).in_app_enabled %}checked{% endif %}>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="form-check form-switch d-flex justify-content-center">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="{{ type_value }}_email"
                                               name="{{ type_value }}_email"
                                               {% if preferences.get(type_value, {}).email_enabled %}checked{% endif %}>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="form-check form-switch d-flex justify-content-center">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="{{ type_value }}_sms"
                                               name="{{ type_value }}_sms"
                                               {% if preferences.get(type_value, {}).sms_enabled %}checked{% endif %}>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="form-check form-switch d-flex justify-content-center">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="{{ type_value }}_push"
                                               name="{{ type_value }}_push"
                                               {% if preferences.get(type_value, {}).push_enabled %}checked{% endif %}>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> 
                    الإشعارات الطارئة (مثل الأخطاء الحرجة) سيتم إرسالها دائماً بغض النظر عن هذه الإعدادات.
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <button type="button" class="btn btn-outline-success w-100 mb-2" onclick="enableAll()">
                            <i class="fas fa-check-circle me-2"></i>
                            تفعيل جميع الإشعارات
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-outline-warning w-100 mb-2" onclick="enableEssentialOnly()">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            الإشعارات الأساسية فقط
                        </button>
                    </div>
                    <div class="col-md-4">
                        <button type="button" class="btn btn-outline-danger w-100 mb-2" onclick="disableAll()">
                            <i class="fas fa-times-circle me-2"></i>
                            إيقاف جميع الإشعارات
                        </button>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> 
                    إيقاف جميع الإشعارات قد يؤدي إلى فقدان معلومات مهمة حول النظام والعمليات المالية.
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">معلومات الاتصال</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   value="{{ current_user.email or '' }}"
                                   readonly>
                            <div class="form-text">لتغيير البريد الإلكتروني، يرجى الذهاب إلى إعدادات الحساب</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" 
                                   class="form-control" 
                                   id="phone" 
                                   value="{{ current_user.phone or '' }}"
                                   readonly>
                            <div class="form-text">لتغيير رقم الهاتف، يرجى الذهاب إلى إعدادات الحساب</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="card shadow mb-4">
            <div class="card-body text-center">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save me-2"></i>
                    حفظ الإعدادات
                </button>
                <button type="button" class="btn btn-outline-secondary btn-lg ms-3" onclick="resetToDefaults()">
                    <i class="fas fa-undo me-2"></i>
                    استعادة الافتراضي
                </button>
            </div>
        </div>
    </form>
</div>

<!-- Reset Confirmation Modal -->
<div class="modal fade" id="resetModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الاستعادة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل تريد استعادة الإعدادات الافتراضية؟</p>
                <p class="text-muted small">سيتم فقدان جميع الإعدادات المخصصة.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="confirmReset()">استعادة</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function enableAll() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

function enableEssentialOnly() {
    // Disable all first
    disableAll();
    
    // Enable essential notifications
    const essentialTypes = ['error', 'invoice_due', 'low_balance', 'tax_deadline'];
    const essentialChannels = ['in_app', 'email'];
    
    essentialTypes.forEach(type => {
        essentialChannels.forEach(channel => {
            const checkbox = document.getElementById(`${type}_${channel}`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    });
}

function disableAll() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

function resetToDefaults() {
    const modal = new bootstrap.Modal(document.getElementById('resetModal'));
    modal.show();
}

function confirmReset() {
    // Set default values
    disableAll();
    
    // Enable default notifications
    const defaultSettings = {
        'info': ['in_app'],
        'success': ['in_app'],
        'warning': ['in_app', 'email'],
        'error': ['in_app', 'email'],
        'invoice_due': ['in_app', 'email'],
        'payment_received': ['in_app'],
        'low_balance': ['in_app', 'email'],
        'system_update': ['in_app'],
        'backup_complete': ['in_app'],
        'tax_deadline': ['in_app', 'email']
    };
    
    Object.keys(defaultSettings).forEach(type => {
        defaultSettings[type].forEach(channel => {
            const checkbox = document.getElementById(`${type}_${channel}`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    });
    
    // Set default quiet hours
    document.getElementById('quiet_hours_start').value = '22:00';
    document.getElementById('quiet_hours_end').value = '08:00';
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('resetModal'));
    modal.hide();
}

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        const forms = document.getElementsByClassName('needs-validation');
        Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Show success message after form submission
{% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
        {% for category, message in messages %}
            {% if category == 'success' %}
                // Show success toast
                const toast = document.createElement('div');
                toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
                toast.style.zIndex = '9999';
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">{{ message }}</div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                `;
                document.body.appendChild(toast);
                
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();
                
                toast.addEventListener('hidden.bs.toast', () => {
                    toast.remove();
                });
            {% endif %}
        {% endfor %}
    {% endif %}
{% endwith %}
</script>
{% endblock %}
