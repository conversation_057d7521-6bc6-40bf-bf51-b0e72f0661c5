"""
Receipt views
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, make_response
from flask_login import login_required
from app import db
from app.models.receipt import Receipt, ReceiptInvoiceAllocation
from app.models.customer import Customer
from app.models.invoice import Invoice
from app.forms.receipt import ReceiptForm, ReceiptSearchForm, ReceiptAllocationForm
from app.utils.decorators import employee_required
from app.utils.helpers import paginate_query, flash_errors, get_current_user_id
from app.utils.pdf_generator import generate_receipt_pdf

receipts_bp = Blueprint('receipts', __name__)

@receipts_bp.route('/')
@employee_required
def index():
    """List all receipts"""
    form = ReceiptSearchForm()
    page = request.args.get('page', 1, type=int)
    
    # Build query
    query = Receipt.query
    
    # Apply filters
    if request.args.get('search'):
        search_term = request.args.get('search')
        query = query.filter(
            db.or_(
                Receipt.receipt_number.ilike(f'%{search_term}%'),
                Receipt.notes.ilike(f'%{search_term}%'),
                Receipt.reference_number.ilike(f'%{search_term}%')
            )
        )
        form.search.data = search_term
    
    if request.args.get('customer_id'):
        customer_id = request.args.get('customer_id')
        query = query.filter(Receipt.customer_id == customer_id)
        form.customer_id.data = customer_id
    
    if request.args.get('payment_method'):
        payment_method = request.args.get('payment_method')
        query = query.filter(Receipt.payment_method == payment_method)
        form.payment_method.data = payment_method
    
    if request.args.get('date_from'):
        from datetime import datetime
        try:
            date_from = datetime.strptime(request.args.get('date_from'), '%Y-%m-%d').date()
            query = query.filter(Receipt.receipt_date >= date_from)
            form.date_from.data = date_from
        except:
            pass
    
    if request.args.get('date_to'):
        from datetime import datetime
        try:
            date_to = datetime.strptime(request.args.get('date_to'), '%Y-%m-%d').date()
            query = query.filter(Receipt.receipt_date <= date_to)
            form.date_to.data = date_to
        except:
            pass
    
    # Order by receipt date (newest first)
    query = query.order_by(Receipt.receipt_date.desc())
    
    # Paginate
    receipts = paginate_query(query, page)
    
    return render_template('receipts/index.html', receipts=receipts, form=form)

@receipts_bp.route('/new', methods=['GET', 'POST'])
@employee_required
def new():
    """Create new receipt"""
    form = ReceiptForm()
    
    if form.validate_on_submit():
        receipt = Receipt(
            customer_id=form.customer_id.data,
            receipt_date=form.receipt_date.data,
            amount_received=form.amount_received.data,
            payment_method=form.payment_method.data,
            reference_number=form.reference_number.data,
            notes=form.notes.data,
            created_by=get_current_user_id()
        )
        
        db.session.add(receipt)
        db.session.flush()  # Get the ID
        
        # Generate receipt number
        receipt.receipt_number = receipt.generate_receipt_number()
        
        # Auto-allocate to oldest invoices
        receipt.auto_allocate_to_oldest_invoices()
        
        db.session.commit()
        
        flash(f'تم إنشاء الإيصال {receipt.receipt_number} بنجاح.', 'success')
        return redirect(url_for('receipts.detail', receipt_id=receipt.id))
    
    flash_errors(form)
    return render_template('receipts/form.html', form=form, title='إنشاء إيصال جديد')

@receipts_bp.route('/<receipt_id>')
@employee_required
def detail(receipt_id):
    """View receipt details"""
    receipt = Receipt.query.get_or_404(receipt_id)
    
    # Get allocations
    allocations = receipt.allocations.all()
    
    return render_template('receipts/detail.html',
                         receipt=receipt,
                         allocations=allocations)

@receipts_bp.route('/<receipt_id>/edit', methods=['GET', 'POST'])
@employee_required
def edit(receipt_id):
    """Edit receipt"""
    receipt = Receipt.query.get_or_404(receipt_id)
    form = ReceiptForm()
    
    if form.validate_on_submit():
        receipt.customer_id = form.customer_id.data
        receipt.receipt_date = form.receipt_date.data
        receipt.amount_received = form.amount_received.data
        receipt.payment_method = form.payment_method.data
        receipt.reference_number = form.reference_number.data
        receipt.notes = form.notes.data
        
        db.session.commit()
        
        flash(f'تم تحديث الإيصال {receipt.receipt_number} بنجاح.', 'success')
        return redirect(url_for('receipts.detail', receipt_id=receipt.id))
    
    elif request.method == 'GET':
        form.customer_id.data = receipt.customer_id
        form.receipt_date.data = receipt.receipt_date
        form.amount_received.data = receipt.amount_received
        form.payment_method.data = receipt.payment_method
        form.reference_number.data = receipt.reference_number
        form.notes.data = receipt.notes
    
    flash_errors(form)
    return render_template('receipts/form.html', form=form, receipt=receipt, title='تعديل الإيصال')

@receipts_bp.route('/<receipt_id>/allocate', methods=['GET', 'POST'])
@employee_required
def allocate(receipt_id):
    """Allocate receipt to invoices"""
    receipt = Receipt.query.get_or_404(receipt_id)
    form = ReceiptAllocationForm(customer_id=receipt.customer_id)
    
    if form.validate_on_submit():
        invoice_id = form.invoice_id.data
        allocated_amount = form.allocated_amount.data
        
        # Check if amount is valid
        unallocated = receipt.get_unallocated_amount()
        if allocated_amount > unallocated:
            flash('المبلغ المخصص أكبر من المبلغ المتاح.', 'error')
            return render_template('receipts/allocate.html', form=form, receipt=receipt)
        
        # Check invoice outstanding amount
        invoice = Invoice.query.get(invoice_id)
        outstanding = invoice.get_outstanding_amount()
        if allocated_amount > outstanding:
            flash('المبلغ المخصص أكبر من المبلغ المستحق على الفاتورة.', 'error')
            return render_template('receipts/allocate.html', form=form, receipt=receipt)
        
        # Create allocation
        receipt.allocate_to_invoice(invoice_id, allocated_amount)
        db.session.commit()
        
        flash(f'تم تخصيص {allocated_amount:.2f} ج.م للفاتورة {invoice.invoice_number}.', 'success')
        return redirect(url_for('receipts.detail', receipt_id=receipt.id))
    
    flash_errors(form)
    return render_template('receipts/allocate.html', form=form, receipt=receipt)

@receipts_bp.route('/<receipt_id>/pdf')
@employee_required
def pdf(receipt_id):
    """Generate receipt PDF"""
    receipt = Receipt.query.get_or_404(receipt_id)
    
    try:
        pdf_buffer = generate_receipt_pdf(receipt)
        
        response = make_response(pdf_buffer.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'inline; filename=receipt_{receipt.receipt_number}.pdf'
        
        return response
    
    except Exception as e:
        flash(f'خطأ في إنتاج ملف PDF: {str(e)}', 'error')
        return redirect(url_for('receipts.detail', receipt_id=receipt.id))

@receipts_bp.route('/<receipt_id>/delete', methods=['POST'])
@employee_required
def delete(receipt_id):
    """Delete receipt"""
    receipt = Receipt.query.get_or_404(receipt_id)
    
    receipt_number = receipt.receipt_number
    db.session.delete(receipt)
    db.session.commit()
    
    flash(f'تم حذف الإيصال {receipt_number} بنجاح.', 'success')
    return redirect(url_for('receipts.index'))

@receipts_bp.route('/allocation/<allocation_id>/delete', methods=['POST'])
@employee_required
def delete_allocation(allocation_id):
    """Delete receipt allocation"""
    allocation = ReceiptInvoiceAllocation.query.get_or_404(allocation_id)
    receipt_id = allocation.receipt_id
    
    db.session.delete(allocation)
    db.session.commit()
    
    flash('تم حذف التخصيص بنجاح.', 'success')
    return redirect(url_for('receipts.detail', receipt_id=receipt_id))
