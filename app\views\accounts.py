"""
Accounts views for Chart of Accounts management
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required
from app import db
from app.models.account import Account
from app.forms.account import AccountForm, AccountSearchForm
from app.utils.decorators import accountant_required
from app.utils.helpers import paginate_query, flash_errors

accounts_bp = Blueprint('accounts', __name__)

@accounts_bp.route('/')
@accountant_required
def index():
    """List all accounts"""
    form = AccountSearchForm()
    page = request.args.get('page', 1, type=int)
    
    # Build query
    query = Account.query
    
    # Apply filters
    if request.args.get('search'):
        search_term = request.args.get('search')
        query = query.filter(
            db.or_(
                Account.code.ilike(f'%{search_term}%'),
                Account.name.ilike(f'%{search_term}%'),
                Account.name_en.ilike(f'%{search_term}%')
            )
        )
        form.search.data = search_term
    
    if request.args.get('type'):
        account_type = request.args.get('type')
        query = query.filter(Account.type == account_type)
        form.type.data = account_type
    
    # Order by code
    query = query.order_by(Account.code)
    
    # Paginate
    accounts = paginate_query(query, page)
    
    return render_template('accounts/index.html', accounts=accounts, form=form)

@accounts_bp.route('/tree')
@accountant_required
def tree():
    """Display accounts in tree structure"""
    # Get root accounts (no parent)
    root_accounts = Account.get_root_accounts()
    
    return render_template('accounts/tree.html', root_accounts=root_accounts)

@accounts_bp.route('/api/tree')
@accountant_required
def api_tree():
    """API endpoint for accounts tree data"""
    def build_tree_node(account):
        children = []
        for child in account.children.filter_by(is_active=True).order_by(Account.code):
            children.append(build_tree_node(child))
        
        return {
            'id': account.id,
            'code': account.code,
            'name': account.name,
            'type': account.type,
            'balance': account.get_balance(),
            'is_leaf': account.is_leaf_account(),
            'children': children
        }
    
    root_accounts = Account.get_root_accounts()
    tree_data = []
    
    for account in root_accounts:
        tree_data.append(build_tree_node(account))
    
    return jsonify(tree_data)

@accounts_bp.route('/new', methods=['GET', 'POST'])
@accountant_required
def new():
    """Create new account"""
    form = AccountForm()
    
    if form.validate_on_submit():
        account = Account(
            code=form.code.data,
            name=form.name.data,
            name_en=form.name_en.data,
            type=form.type.data,
            parent_id=form.parent_id.data if form.parent_id.data else None
        )
        account.is_active = form.is_active.data
        
        db.session.add(account)
        db.session.commit()
        
        flash(f'تم إنشاء الحساب {account.code} - {account.name} بنجاح.', 'success')
        return redirect(url_for('accounts.detail', account_id=account.id))
    
    flash_errors(form)
    return render_template('accounts/form.html', form=form, title='إنشاء حساب جديد')

@accounts_bp.route('/<account_id>')
@accountant_required
def detail(account_id):
    """View account details"""
    account = Account.query.get_or_404(account_id)
    
    # Get account balance
    balance = account.get_balance()
    
    # Get child accounts
    children = account.children.filter_by(is_active=True).order_by(Account.code).all()
    
    # Get recent transactions (journal lines)
    recent_transactions = account.journal_lines.join(
        account.journal_lines.property.mapper.class_.journal_entry
    ).order_by(
        account.journal_lines.property.mapper.class_.journal_entry.property.mapper.class_.entry_date.desc()
    ).limit(10).all()
    
    return render_template('accounts/detail.html', 
                         account=account, 
                         balance=balance,
                         children=children,
                         recent_transactions=recent_transactions)

@accounts_bp.route('/<account_id>/edit', methods=['GET', 'POST'])
@accountant_required
def edit(account_id):
    """Edit account"""
    account = Account.query.get_or_404(account_id)
    form = AccountForm(account)
    
    if form.validate_on_submit():
        account.code = form.code.data
        account.name = form.name.data
        account.name_en = form.name_en.data
        account.type = form.type.data
        account.parent_id = form.parent_id.data if form.parent_id.data else None
        account.is_active = form.is_active.data
        
        db.session.commit()
        
        flash(f'تم تحديث الحساب {account.code} - {account.name} بنجاح.', 'success')
        return redirect(url_for('accounts.detail', account_id=account.id))
    
    elif request.method == 'GET':
        form.code.data = account.code
        form.name.data = account.name
        form.name_en.data = account.name_en
        form.type.data = account.type
        form.parent_id.data = account.parent_id
        form.is_active.data = account.is_active
    
    flash_errors(form)
    return render_template('accounts/form.html', form=form, account=account, title='تعديل الحساب')

@accounts_bp.route('/<account_id>/delete', methods=['POST'])
@accountant_required
def delete(account_id):
    """Delete account"""
    account = Account.query.get_or_404(account_id)
    
    # Check if account has children
    if account.children.count() > 0:
        flash('لا يمكن حذف هذا الحساب لأنه يحتوي على حسابات فرعية.', 'error')
        return redirect(url_for('accounts.detail', account_id=account.id))
    
    # Check if account has transactions
    if account.journal_lines.count() > 0:
        flash('لا يمكن حذف هذا الحساب لأنه يحتوي على قيود محاسبية.', 'error')
        return redirect(url_for('accounts.detail', account_id=account.id))
    
    account_name = f"{account.code} - {account.name}"
    db.session.delete(account)
    db.session.commit()
    
    flash(f'تم حذف الحساب {account_name} بنجاح.', 'success')
    return redirect(url_for('accounts.index'))

@accounts_bp.route('/<account_id>/balance')
@accountant_required
def balance(account_id):
    """Get account balance"""
    account = Account.query.get_or_404(account_id)
    as_of_date = request.args.get('as_of_date')
    
    if as_of_date:
        from datetime import datetime
        try:
            as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
        except:
            as_of_date = None
    
    balance = account.get_balance(as_of_date)
    
    return jsonify({
        'account_id': account.id,
        'account_code': account.code,
        'account_name': account.name,
        'balance': float(balance),
        'as_of_date': as_of_date.isoformat() if as_of_date else None
    })

@accounts_bp.route('/<account_id>/transactions')
@accountant_required
def transactions(account_id):
    """View account transactions"""
    account = Account.query.get_or_404(account_id)
    page = request.args.get('page', 1, type=int)
    
    # Get transactions (journal lines) for this account
    query = account.journal_lines.join(
        account.journal_lines.property.mapper.class_.journal_entry
    ).order_by(
        account.journal_lines.property.mapper.class_.journal_entry.property.mapper.class_.entry_date.desc(),
        account.journal_lines.property.mapper.class_.journal_entry.property.mapper.class_.created_at.desc()
    )
    
    transactions = paginate_query(query, page)
    
    # Calculate running balance
    running_balance = 0
    for transaction in reversed(transactions.items):
        if account.type in ['Asset', 'Expense']:
            if transaction.dc == 'D':
                running_balance += float(transaction.amount)
            else:
                running_balance -= float(transaction.amount)
        else:  # Liability, Equity, Income
            if transaction.dc == 'C':
                running_balance += float(transaction.amount)
            else:
                running_balance -= float(transaction.amount)
        
        transaction.running_balance = running_balance
    
    return render_template('accounts/transactions.html', 
                         account=account, 
                         transactions=transactions)

@accounts_bp.route('/api/search')
@accountant_required
def api_search():
    """API endpoint for account search"""
    term = request.args.get('term', '')
    account_type = request.args.get('type', '')
    leaf_only = request.args.get('leaf_only', 'false').lower() == 'true'
    
    query = Account.query.filter_by(is_active=True)
    
    if term:
        query = query.filter(
            db.or_(
                Account.code.ilike(f'%{term}%'),
                Account.name.ilike(f'%{term}%')
            )
        )
    
    if account_type:
        query = query.filter(Account.type == account_type)
    
    accounts = query.order_by(Account.code).limit(20).all()
    
    if leaf_only:
        accounts = [acc for acc in accounts if acc.is_leaf_account()]
    
    return jsonify([{
        'id': account.id,
        'code': account.code,
        'name': account.name,
        'type': account.type,
        'full_name': f"{account.code} - {account.name}",
        'is_leaf': account.is_leaf_account()
    } for account in accounts])

@accounts_bp.route('/trial-balance')
@accountant_required
def trial_balance():
    """Generate trial balance report"""
    as_of_date = request.args.get('as_of_date')
    
    if as_of_date:
        from datetime import datetime
        try:
            as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
        except:
            as_of_date = None
    
    # Get all leaf accounts with balances
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
    
    trial_balance_data = []
    total_debits = 0
    total_credits = 0
    
    for account in accounts:
        if account.is_leaf_account():
            balance = account.get_balance(as_of_date)
            
            if balance != 0:
                if account.type in ['Asset', 'Expense']:
                    debit_balance = balance if balance > 0 else 0
                    credit_balance = abs(balance) if balance < 0 else 0
                else:  # Liability, Equity, Income
                    credit_balance = balance if balance > 0 else 0
                    debit_balance = abs(balance) if balance < 0 else 0
                
                trial_balance_data.append({
                    'account': account,
                    'debit_balance': debit_balance,
                    'credit_balance': credit_balance
                })
                
                total_debits += debit_balance
                total_credits += credit_balance
    
    return render_template('accounts/trial_balance.html',
                         trial_balance_data=trial_balance_data,
                         total_debits=total_debits,
                         total_credits=total_credits,
                         as_of_date=as_of_date)
