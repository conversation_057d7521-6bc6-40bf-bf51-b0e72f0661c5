# 🚀 متطلبات تشغيل SystemTax على السيرفر
# SystemTax Server Deployment Requirements

## 📋 **متطلبات الأجهزة (Hardware Requirements)**

### **الحد الأدنى (Minimum):**
- **المعالج (CPU):** 2 cores, 2.0 GHz
- **الذاكرة (RAM):** 4 GB
- **التخزين (Storage):** 20 GB SSD
- **الشبكة (Network):** 100 Mbps

### **المُوصى به (Recommended):**
- **المعالج (CPU):** 4 cores, 2.5 GHz+
- **الذاكرة (RAM):** 8 GB+
- **التخزين (Storage):** 50 GB+ SSD
- **الشبكة (Network):** 1 Gbps

### **للإنتاج الثقيل (Production Heavy Load):**
- **المعالج (CPU):** 8+ cores, 3.0 GHz+
- **الذاكرة (RAM):** 16 GB+
- **التخزين (Storage):** 100 GB+ NVMe SSD
- **الشبكة (Network):** 1 Gbps+

---

## 🖥️ **متطلبات نظام التشغيل (OS Requirements)**

### **أنظمة التشغيل المدعومة:**
- **Ubuntu 20.04 LTS** أو أحدث ✅ (مُوصى به)
- **Ubuntu 22.04 LTS** ✅ (مُوصى به)
- **CentOS 8** أو أحدث ✅
- **RHEL 8** أو أحدث ✅
- **Debian 11** أو أحدث ✅
- **Amazon Linux 2** ✅

### **متطلبات النظام:**
- **Kernel:** Linux 3.10+
- **Architecture:** x86_64 (AMD64)
- **Root Access:** مطلوب للتثبيت الأولي

---

## 🐳 **متطلبات Docker (Docker Requirements)**

### **إصدارات مطلوبة:**
- **Docker:** 20.10.0+ ✅
- **Docker Compose:** 2.0.0+ ✅

### **تثبيت Docker على Ubuntu:**
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت المتطلبات
sudo apt install -y apt-transport-https ca-certificates curl gnupg lsb-release

# إضافة مفتاح Docker GPG
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg

# إضافة مستودع Docker
echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null

# تثبيت Docker
sudo apt update
sudo apt install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# تشغيل Docker
sudo systemctl start docker
sudo systemctl enable docker

# إضافة المستخدم لمجموعة Docker
sudo usermod -aG docker $USER
```

### **تثبيت Docker على CentOS/RHEL:**
```bash
# تثبيت المتطلبات
sudo yum install -y yum-utils

# إضافة مستودع Docker
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo

# تثبيت Docker
sudo yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin

# تشغيل Docker
sudo systemctl start docker
sudo systemctl enable docker

# إضافة المستخدم لمجموعة Docker
sudo usermod -aG docker $USER
```

---

## 🌐 **متطلبات الشبكة (Network Requirements)**

### **المنافذ المطلوبة (Required Ports):**
- **80** - HTTP (Nginx)
- **443** - HTTPS (Nginx) - للإنتاج
- **8000** - تطبيق SystemTax (داخلي)
- **5432** - PostgreSQL (داخلي)
- **6379** - Redis (داخلي)

### **إعداد Firewall:**
```bash
# Ubuntu/Debian (UFW)
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

---

## 📁 **هيكل الملفات على السيرفر (Server File Structure)**

```
/opt/systemtax/
├── docker-compose.yml
├── Dockerfile
├── .env
├── app/
├── database/
├── nginx/
├── uploads/
├── backups/
├── logs/
└── ssl/ (للإنتاج)
```

---

## ⚙️ **خطوات التشغيل (Deployment Steps)**

### **1. تحضير السيرفر:**
```bash
# إنشاء مستخدم للتطبيق
sudo adduser systemtax
sudo usermod -aG docker systemtax
sudo su - systemtax

# إنشاء مجلد التطبيق
sudo mkdir -p /opt/systemtax
sudo chown systemtax:systemtax /opt/systemtax
cd /opt/systemtax
```

### **2. رفع ملفات التطبيق:**
```bash
# نسخ ملفات المشروع
scp -r SystemTax/* user@server:/opt/systemtax/

# أو استخدام Git
git clone https://github.com/your-repo/SystemTax.git /opt/systemtax
```

### **3. إعداد متغيرات البيئة:**
```bash
# إنشاء ملف .env
cat > .env << EOF
# Database Configuration
DATABASE_URL=********************************************************/systemtax

# Redis Configuration
REDIS_URL=redis://:REDIS_PASSWORD_HERE@redis:6379/0

# Flask Configuration
SECRET_KEY=VERY_STRONG_SECRET_KEY_HERE
FLASK_ENV=production

# Security
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true

# Company Information
COMPANY_NAME=اسم شركتك
COMPANY_TAX_ID=*********

# ETA Configuration
ETA_ENVIRONMENT=production
ETA_CLIENT_ID=your_eta_client_id
ETA_CLIENT_SECRET=your_eta_client_secret
EOF

# تأمين الملف
chmod 600 .env
```

### **4. تشغيل التطبيق:**
```bash
# جعل سكريبت التشغيل قابل للتنفيذ
chmod +x docker-start.sh

# تشغيل النظام
./docker-start.sh start
```

---

## 🔒 **إعدادات الأمان (Security Configuration)**

### **1. SSL/TLS للإنتاج:**
```bash
# تثبيت Certbot
sudo apt install -y certbot

# الحصول على شهادة SSL
sudo certbot certonly --standalone -d your-domain.com

# نسخ الشهادات
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem /opt/systemtax/nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem /opt/systemtax/nginx/ssl/key.pem
sudo chown systemtax:systemtax /opt/systemtax/nginx/ssl/*
```

### **2. تحديث كلمات المرور:**
```bash
# تغيير كلمات مرور قاعدة البيانات
# تحديث docker-compose.yml و .env

# تغيير كلمة مرور Redis
# تحديث docker-compose.yml و .env

# تغيير SECRET_KEY
# تحديث .env
```

---

## 📊 **مراقبة النظام (System Monitoring)**

### **1. مراقبة الحاويات:**
```bash
# حالة الحاويات
docker-compose ps

# استخدام الموارد
docker stats

# سجلات التطبيق
docker-compose logs -f web
```

### **2. مراقبة قاعدة البيانات:**
```bash
# الاتصال بقاعدة البيانات
docker-compose exec db psql -U systemtax_user systemtax

# حجم قاعدة البيانات
docker-compose exec db psql -U systemtax_user systemtax -c "SELECT pg_size_pretty(pg_database_size('systemtax'));"
```

---

## 🔄 **النسخ الاحتياطي (Backup Strategy)**

### **1. نسخ احتياطي يومي:**
```bash
# إنشاء سكريبت النسخ الاحتياطي
cat > /opt/systemtax/backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/systemtax/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# إنشاء مجلد النسخ الاحتياطي
mkdir -p $BACKUP_DIR

# نسخ احتياطي لقاعدة البيانات
docker-compose exec -T db pg_dump -U systemtax_user systemtax > $BACKUP_DIR/db_backup_$DATE.sql

# نسخ احتياطي للملفات المرفوعة
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz uploads/

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
EOF

chmod +x /opt/systemtax/backup.sh

# إضافة مهمة cron للنسخ الاحتياطي اليومي
echo "0 2 * * * /opt/systemtax/backup.sh" | crontab -
```

---

## 🔧 **استكشاف الأخطاء (Troubleshooting)**

### **مشاكل شائعة وحلولها:**

#### **1. فشل في بدء الحاويات:**
```bash
# فحص السجلات
docker-compose logs

# إعادة بناء الصور
docker-compose build --no-cache

# تنظيف Docker
docker system prune -f
```

#### **2. مشاكل قاعدة البيانات:**
```bash
# إعادة تشغيل قاعدة البيانات
docker-compose restart db

# فحص حالة قاعدة البيانات
docker-compose exec db pg_isready -U systemtax_user
```

#### **3. مشاكل الذاكرة:**
```bash
# فحص استخدام الذاكرة
free -h
docker stats

# زيادة swap إذا لزم الأمر
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

---

## 📞 **الدعم والصيانة (Support & Maintenance)**

### **تحديثات دورية:**
- **تحديث النظام:** شهرياً
- **تحديث Docker:** عند توفر إصدارات جديدة
- **تحديث التطبيق:** حسب الحاجة
- **مراجعة الأمان:** ربع سنوياً

### **مراقبة الأداء:**
- **استخدام CPU:** < 70%
- **استخدام RAM:** < 80%
- **مساحة القرص:** < 85%
- **زمن الاستجابة:** < 2 ثانية

---

## ✅ **قائمة التحقق النهائية (Final Checklist)**

- [ ] تثبيت Docker و Docker Compose
- [ ] إعداد Firewall والمنافذ
- [ ] رفع ملفات التطبيق
- [ ] إعداد متغيرات البيئة (.env)
- [ ] تشغيل النظام
- [ ] اختبار الوصول للتطبيق
- [ ] إعداد SSL (للإنتاج)
- [ ] إعداد النسخ الاحتياطي
- [ ] اختبار استعادة النسخ الاحتياطي
- [ ] مراقبة الأداء
- [ ] توثيق كلمات المرور

---

## 🎯 **معلومات الاتصال بالنظام**

بعد التشغيل الناجح:
- **URL:** http://your-server-ip:8000
- **Admin Login:** admin / admin123
- **Database:** PostgreSQL على المنفذ 5432
- **Cache:** Redis على المنفذ 6379

**🔐 تذكر تغيير كلمة مرور المدير الافتراضية فور تسجيل الدخول!**
