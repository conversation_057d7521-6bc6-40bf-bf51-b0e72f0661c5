"""
Pytest configuration and fixtures for SystemTax tests
"""

import pytest
import tempfile
import os
from app import create_app, db
from app.models.user import User
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.models.account import Account
from app.models.system_setting import SystemSetting

@pytest.fixture(scope='session')
def app():
    """Create application for testing session"""
    # Create temporary database
    db_fd, db_path = tempfile.mkstemp()
    
    app = create_app({
        'TESTING': True,
        'SQLALCHEMY_DATABASE_URI': f'sqlite:///{db_path}',
        'WTF_CSRF_ENABLED': False,
        'SECRET_KEY': 'test-secret-key'
    })
    
    with app.app_context():
        db.create_all()
        
        # Initialize system settings
        SystemSetting.initialize_default_settings()
        
        # Create default accounts
        create_test_accounts()
        
        yield app
        
        db.session.remove()
        db.drop_all()
    
    os.close(db_fd)
    os.unlink(db_path)

@pytest.fixture
def client(app):
    """Create test client"""
    return app.test_client()

@pytest.fixture
def runner(app):
    """Create test CLI runner"""
    return app.test_cli_runner()

@pytest.fixture
def admin_user(app):
    """Create admin user"""
    with app.app_context():
        user = User(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            role='admin'
        )
        db.session.add(user)
        db.session.commit()
        return user

@pytest.fixture
def accountant_user(app):
    """Create accountant user"""
    with app.app_context():
        user = User(
            username='accountant',
            email='<EMAIL>',
            password='acc123',
            role='accountant'
        )
        db.session.add(user)
        db.session.commit()
        return user

@pytest.fixture
def employee_user(app):
    """Create employee user"""
    with app.app_context():
        user = User(
            username='employee',
            email='<EMAIL>',
            password='emp123',
            role='employee'
        )
        db.session.add(user)
        db.session.commit()
        return user

@pytest.fixture
def test_customer(app):
    """Create test customer"""
    with app.app_context():
        customer = Customer(
            name='عميل تجريبي',
            name_en='Test Customer',
            tax_id='*********',
            address='عنوان تجريبي',
            email='<EMAIL>',
            phone='0*********0'
        )
        db.session.add(customer)
        db.session.commit()
        return customer

@pytest.fixture
def test_vendor(app):
    """Create test vendor"""
    with app.app_context():
        vendor = Vendor(
            name='مورد تجريبي',
            name_en='Test Vendor',
            tax_id='*********',
            address='عنوان المورد',
            email='<EMAIL>',
            phone='01*********'
        )
        db.session.add(vendor)
        db.session.commit()
        return vendor

@pytest.fixture
def authenticated_client(client, admin_user):
    """Create authenticated client with admin user"""
    with client.session_transaction() as sess:
        sess['_user_id'] = admin_user.id
        sess['_fresh'] = True
    return client

def create_test_accounts():
    """Create basic test accounts"""
    accounts = [
        ('1000', 'الأصول', 'Assets', 'Asset', None),
        ('1100', 'النقدية', 'Cash', 'Asset', '1000'),
        ('1200', 'العملاء', 'Accounts Receivable', 'Asset', '1000'),
        ('2000', 'الخصوم', 'Liabilities', 'Liability', None),
        ('2100', 'الموردين', 'Accounts Payable', 'Liability', '2000'),
        ('3000', 'رأس المال', 'Equity', 'Equity', None),
        ('4000', 'الإيرادات', 'Revenue', 'Income', None),
        ('4100', 'مبيعات', 'Sales', 'Income', '4000'),
        ('5000', 'المصروفات', 'Expenses', 'Expense', None),
        ('5100', 'مصروفات عمومية', 'General Expenses', 'Expense', '5000'),
    ]
    
    created_accounts = {}
    
    for code, name, name_en, account_type, parent_code in accounts:
        parent_id = None
        if parent_code and parent_code in created_accounts:
            parent_id = created_accounts[parent_code].id
        
        account = Account(
            code=code,
            name=name,
            name_en=name_en,
            type=account_type,
            parent_id=parent_id
        )
        
        db.session.add(account)
        db.session.flush()
        created_accounts[code] = account
    
    db.session.commit()

@pytest.fixture
def test_accounts(app):
    """Get test accounts"""
    with app.app_context():
        return {
            'cash': Account.query.filter_by(code='1100').first(),
            'receivables': Account.query.filter_by(code='1200').first(),
            'payables': Account.query.filter_by(code='2100').first(),
            'sales': Account.query.filter_by(code='4100').first(),
            'expenses': Account.query.filter_by(code='5100').first(),
        }

class AuthActions:
    """Helper class for authentication actions in tests"""
    
    def __init__(self, client):
        self._client = client
    
    def login(self, username='admin', password='admin123'):
        """Login user"""
        return self._client.post('/auth/login', data={
            'username': username,
            'password': password
        })
    
    def logout(self):
        """Logout user"""
        return self._client.get('/auth/logout')

@pytest.fixture
def auth(client):
    """Authentication helper"""
    return AuthActions(client)

# Custom markers for different test categories
def pytest_configure(config):
    """Configure pytest markers"""
    config.addinivalue_line(
        "markers", "unit: mark test as unit test"
    )
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "api: mark test as API test"
    )
    config.addinivalue_line(
        "markers", "auth: mark test as authentication test"
    )

# Test data factories
class TestDataFactory:
    """Factory for creating test data"""
    
    @staticmethod
    def create_user(username='testuser', email='<EMAIL>', 
                   password='testpass', role='employee'):
        """Create test user"""
        user = User(
            username=username,
            email=email,
            password=password,
            role=role
        )
        db.session.add(user)
        db.session.commit()
        return user
    
    @staticmethod
    def create_customer(name='Test Customer', tax_id='*********'):
        """Create test customer"""
        customer = Customer(
            name=name,
            tax_id=tax_id,
            email='<EMAIL>'
        )
        db.session.add(customer)
        db.session.commit()
        return customer
    
    @staticmethod
    def create_account(code='9999', name='Test Account', 
                      account_type='Asset'):
        """Create test account"""
        account = Account(
            code=code,
            name=name,
            type=account_type
        )
        db.session.add(account)
        db.session.commit()
        return account

@pytest.fixture
def factory():
    """Test data factory"""
    return TestDataFactory
