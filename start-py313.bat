@echo off
chcp 65001 >nul
title SystemTax - نظام محاسبي ويب متكامل

echo ========================================
echo    SystemTax - نظام محاسبي ويب متكامل
echo ========================================
echo.

:: Check Python version
python --version 2>nul
if errorlevel 1 (
    echo ❌ Python غير مثبت. يرجى تثبيت Python 3.10+ أولاً
    echo    تحميل Python من: https://python.org/downloads/
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo 🐍 Python version: %PYTHON_VERSION%

:: Check if virtual environment exists
if not exist "venv" (
    echo 🔧 إنشاء البيئة الافتراضية...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
)

echo 🔄 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

echo 📦 تثبيت المتطلبات...
:: Try Python 3.13 compatible requirements first
if exist "requirements-py313.txt" (
    pip install -r requirements-py313.txt
) else (
    :: Fallback to original requirements
    pip install -r requirements.txt
)

if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    echo.
    echo 💡 جرب الحلول التالية:
    echo    1. تحديث pip: python -m pip install --upgrade pip
    echo    2. تثبيت Microsoft Visual C++ Build Tools
    echo    3. استخدام Docker بدلاً من ذلك: docker-compose up
    echo.
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح

:: Check if .env exists
if not exist ".env" (
    echo 📝 إنشاء ملف الإعدادات...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo ✅ تم إنشاء ملف .env من .env.example
    ) else (
        echo 🔧 إنشاء ملف .env أساسي...
        (
            echo # SystemTax Configuration
            echo FLASK_ENV=development
            echo SECRET_KEY=dev-secret-key-change-in-production
            echo DATABASE_URL=sqlite:///systemtax.db
            echo REDIS_URL=redis://localhost:6379/0
            echo APP_NAME=SystemTax
            echo COMPANY_NAME=شركتك
        ) > .env
        echo ✅ تم إنشاء ملف .env أساسي
    )
)

:: Initialize database
echo 🗄️ إعداد قاعدة البيانات...
python -c "
try:
    from app import create_app, db
    from app.models import *
    app = create_app()
    with app.app_context():
        db.create_all()
        print('✅ تم إنشاء قاعدة البيانات')
        
        # Create admin user
        from app.models.user import User
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User('admin', '<EMAIL>', 'admin123', 'admin')
            db.session.add(admin)
            db.session.commit()
            print('✅ تم إنشاء المستخدم الإداري (admin/admin123)')
        else:
            print('✅ المستخدم الإداري موجود بالفعل')
            
except Exception as e:
    print(f'⚠️ خطأ في إعداد قاعدة البيانات: {e}')
    print('💡 سيتم إنشاء قاعدة البيانات عند أول تشغيل')
"

echo.
echo 🚀 بدء تشغيل SystemTax...
echo 📱 الرابط: http://localhost:5000
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo ⏹️ لإيقاف الخادم اضغط Ctrl+C
echo.

:: Start the application
python app.py

echo.
echo 👋 شكراً لاستخدام SystemTax
pause
