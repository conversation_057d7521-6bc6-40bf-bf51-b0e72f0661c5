{% extends "base.html" %}

{% block title %}الصفحة غير موجودة - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <h1 class="display-1 text-primary">404</h1>
                <h2 class="mb-4">الصفحة غير موجودة</h2>
                <p class="lead mb-4">عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها.</p>
                
                <div class="mb-4">
                    <i class="fas fa-search fa-5x text-muted"></i>
                </div>
                
                <div class="d-flex justify-content-center gap-3">
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        العودة للخلف
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .error-page {
        padding: 4rem 0;
    }
    
    .display-1 {
        font-size: 8rem;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}
