{% extends "base.html" %}

{% block title %}فاتورة رقم {{ invoice.invoice_number }} - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-file-invoice me-3"></i>
                فاتورة رقم {{ invoice.invoice_number }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('invoices.index') }}">الفواتير</a></li>
                    <li class="breadcrumb-item active">{{ invoice.invoice_number }}</li>
                </ol>
            </nav>
        </div>
        <div>
            {% if invoice.can_be_edited() %}
            <a href="{{ url_for('invoices.edit', invoice_id=invoice.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
            {% endif %}
            <a href="{{ url_for('invoices.pdf', invoice_id=invoice.id) }}" class="btn btn-info" target="_blank">
                <i class="fas fa-file-pdf me-2"></i>
                PDF
            </a>
            <a href="{{ url_for('invoices.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Invoice Details -->
    <div class="col-lg-8">
        <!-- Invoice Header -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الفاتورة
                </h5>
                <div>
                    <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'warning' if invoice.status == 'pending' else 'danger' if invoice.status == 'overdue' else 'secondary' }} fs-6">
                        {{ invoice.get_status_display() }}
                    </span>
                    {% if invoice.eta_status %}
                    <span class="badge bg-{{ 'success' if invoice.eta_status == 'submitted' else 'warning' if invoice.eta_status == 'pending' else 'danger' }} fs-6 ms-2">
                        {{ invoice.get_eta_status_display() }}
                    </span>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الفاتورة:</strong></td>
                                <td>{{ invoice.invoice_number }}</td>
                            </tr>
                            {% if invoice.reference_number %}
                            <tr>
                                <td><strong>الرقم المرجعي:</strong></td>
                                <td>{{ invoice.reference_number }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>تاريخ الفاتورة:</strong></td>
                                <td>{{ invoice.invoice_date|date }}</td>
                            </tr>
                            {% if invoice.due_date %}
                            <tr>
                                <td><strong>تاريخ الاستحقاق:</strong></td>
                                <td>{{ invoice.due_date|date }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>العملة:</strong></td>
                                <td>{{ invoice.currency }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ invoice.created_at|datetime }}</td>
                            </tr>
                            {% if invoice.updated_at %}
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ invoice.updated_at|datetime }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>أنشأ بواسطة:</strong></td>
                                <td>{{ invoice.created_by.username if invoice.created_by else 'غير محدد' }}</td>
                            </tr>
                            {% if invoice.eta_uuid %}
                            <tr>
                                <td><strong>UUID الضرائب:</strong></td>
                                <td><code>{{ invoice.eta_uuid }}</code></td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                {% if invoice.description %}
                <div class="mt-3">
                    <strong>الوصف:</strong>
                    <p>{{ invoice.description }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Customer Information -->
        {% if invoice.customer %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>
                    معلومات العميل
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>اسم العميل:</strong></td>
                                <td>
                                    <a href="{{ url_for('customers.detail', customer_id=invoice.customer.id) }}">
                                        {{ invoice.customer.name }}
                                    </a>
                                </td>
                            </tr>
                            {% if invoice.customer.tax_id %}
                            <tr>
                                <td><strong>الرقم الضريبي:</strong></td>
                                <td>{{ invoice.customer.tax_id }}</td>
                            </tr>
                            {% endif %}
                            {% if invoice.customer.email %}
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td><a href="mailto:{{ invoice.customer.email }}">{{ invoice.customer.email }}</a></td>
                            </tr>
                            {% endif %}
                            {% if invoice.customer.phone %}
                            <tr>
                                <td><strong>الهاتف:</strong></td>
                                <td><a href="tel:{{ invoice.customer.phone }}">{{ invoice.customer.phone }}</a></td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        {% if invoice.customer.address %}
                        <p><strong>العنوان:</strong><br>
                        {{ invoice.customer.address }}
                        {% if invoice.customer.city %}, {{ invoice.customer.city }}{% endif %}
                        {% if invoice.customer.state %}, {{ invoice.customer.state }}{% endif %}
                        {% if invoice.customer.postal_code %} {{ invoice.customer.postal_code }}{% endif %}
                        </p>
                        {% endif %}
                        
                        <p><strong>رصيد العميل:</strong> 
                        <span class="fw-bold text-{{ 'success' if invoice.customer.get_balance() >= 0 else 'danger' }}">
                            {{ invoice.customer.get_balance()|currency }}
                        </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Invoice Lines -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    بنود الفاتورة
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>الصنف/الخدمة</th>
                                <th class="text-center">الكمية</th>
                                <th class="text-center">السعر</th>
                                <th class="text-center">الخصم</th>
                                <th class="text-center">الضريبة</th>
                                <th class="text-center">الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for line in invoice.lines %}
                            <tr>
                                <td>{{ line.description }}</td>
                                <td class="text-center">{{ line.quantity }}</td>
                                <td class="text-center">{{ line.unit_price|currency }}</td>
                                <td class="text-center">
                                    {% if line.discount_rate > 0 %}
                                        {{ line.discount_rate }}%
                                        <br><small class="text-muted">({{ line.discount_amount|currency }})</small>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {{ line.tax_rate }}%
                                    <br><small class="text-muted">({{ line.tax_amount|currency }})</small>
                                </td>
                                <td class="text-center">
                                    <strong>{{ line.total_amount|currency }}</strong>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="5" class="text-end">المجموع الفرعي:</th>
                                <th class="text-center">{{ invoice.subtotal_amount|currency }}</th>
                            </tr>
                            {% if invoice.discount_amount > 0 %}
                            <tr>
                                <th colspan="5" class="text-end">إجمالي الخصم:</th>
                                <th class="text-center text-success">{{ invoice.discount_amount|currency }}</th>
                            </tr>
                            {% endif %}
                            <tr>
                                <th colspan="5" class="text-end">إجمالي الضريبة:</th>
                                <th class="text-center text-info">{{ invoice.tax_amount|currency }}</th>
                            </tr>
                            <tr class="table-primary">
                                <th colspan="5" class="text-end">الإجمالي النهائي:</th>
                                <th class="text-center">
                                    <strong class="fs-5">{{ invoice.total_amount|currency }}</strong>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- Notes -->
        {% if invoice.notes %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات
                </h5>
            </div>
            <div class="card-body">
                <p>{{ invoice.notes|nl2br }}</p>
            </div>
        </div>
        {% endif %}

        <!-- Payment History -->
        {% if payments %}
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill me-2"></i>
                    سجل المدفوعات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                                <th>المرجع</th>
                                <th>الملاحظات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>{{ payment.payment_date|date }}</td>
                                <td>{{ payment.amount|currency }}</td>
                                <td>{{ payment.payment_method }}</td>
                                <td>{{ payment.reference_number or '-' }}</td>
                                <td>{{ payment.notes or '-' }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if invoice.can_be_edited() %}
                    <a href="{{ url_for('invoices.edit', invoice_id=invoice.id) }}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الفاتورة
                    </a>
                    {% endif %}
                    
                    <a href="{{ url_for('invoices.pdf', invoice_id=invoice.id) }}" class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-file-pdf me-2"></i>
                        عرض PDF
                    </a>
                    
                    {% if invoice.customer and invoice.customer.email %}
                    <button type="button" class="btn btn-outline-primary" onclick="sendInvoiceEmail()">
                        <i class="fas fa-envelope me-2"></i>
                        إرسال بريد
                    </button>
                    {% endif %}
                    
                    {% if not invoice.eta_status or invoice.eta_status == 'failed' %}
                    <button type="button" class="btn btn-outline-success" onclick="submitToETA()">
                        <i class="fas fa-paper-plane me-2"></i>
                        إرسال للضرائب
                    </button>
                    {% endif %}
                    
                    {% if invoice.status != 'paid' %}
                    <button type="button" class="btn btn-outline-success" onclick="markAsPaid()">
                        <i class="fas fa-check me-2"></i>
                        تحديد كمدفوع
                    </button>
                    {% endif %}
                    
                    <a href="{{ url_for('receipts.new') }}?invoice_id={{ invoice.id }}" class="btn btn-outline-secondary">
                        <i class="fas fa-receipt me-2"></i>
                        إيصال دفع
                    </a>
                    
                    {% if invoice.can_be_deleted() %}
                    <hr>
                    <form method="POST" action="{{ url_for('invoices.delete', invoice_id=invoice.id) }}" 
                          onsubmit="return confirm('هل أنت متأكد من حذف هذه الفاتورة؟')">
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash me-2"></i>
                            حذف الفاتورة
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Invoice Summary -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    ملخص الفاتورة
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ invoice.total_amount|currency }}</h4>
                        <small>إجمالي الفاتورة</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ invoice.paid_amount|currency }}</h4>
                        <small>المبلغ المدفوع</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-12">
                        <h4 class="text-{{ 'success' if invoice.remaining_amount <= 0 else 'warning' }}">
                            {{ invoice.remaining_amount|currency }}
                        </h4>
                        <small>المبلغ المتبقي</small>
                    </div>
                </div>
                
                {% if invoice.due_date %}
                <hr>
                <div class="text-center">
                    {% set days_until_due = (invoice.due_date - today()).days %}
                    {% if days_until_due < 0 %}
                        <span class="badge bg-danger">متأخر {{ -days_until_due }} يوم</span>
                    {% elif days_until_due == 0 %}
                        <span class="badge bg-warning">مستحق اليوم</span>
                    {% else %}
                        <span class="badge bg-info">مستحق خلال {{ days_until_due }} يوم</span>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>

        <!-- ETA Status -->
        {% if invoice.eta_status %}
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-globe me-2"></i>
                    حالة منظومة الضرائب
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <span class="badge bg-{{ 'success' if invoice.eta_status == 'submitted' else 'warning' if invoice.eta_status == 'pending' else 'danger' }} fs-6">
                        {{ invoice.get_eta_status_display() }}
                    </span>
                </div>
                
                {% if invoice.eta_uuid %}
                <hr>
                <p><strong>UUID:</strong><br>
                <code>{{ invoice.eta_uuid }}</code></p>
                {% endif %}
                
                {% if invoice.eta_submitted_at %}
                <p><strong>تاريخ الإرسال:</strong><br>
                {{ invoice.eta_submitted_at|datetime }}</p>
                {% endif %}
                
                {% if invoice.eta_response %}
                <p><strong>استجابة النظام:</strong><br>
                <small>{{ invoice.eta_response }}</small></p>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Related Documents -->
        {% if related_documents %}
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>
                    مستندات مرتبطة
                </h6>
            </div>
            <div class="card-body">
                {% for doc in related_documents %}
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-{{ 'receipt' if doc.type == 'receipt' else 'file-invoice' }} me-2"></i>
                    <a href="{{ doc.url }}" class="text-decoration-none">
                        {{ doc.number }}
                    </a>
                    <small class="text-muted ms-auto">{{ doc.date|date }}</small>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function submitToETA() {
    if (confirm('هل تريد إرسال هذه الفاتورة إلى منظومة الضرائب المصرية؟')) {
        fetch(`/invoices/{{ invoice.id }}/submit-eta`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال الفاتورة بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function sendInvoiceEmail() {
    if (confirm('هل تريد إرسال الفاتورة بالبريد الإلكتروني للعميل؟')) {
        fetch(`/invoices/{{ invoice.id }}/send-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال الفاتورة بالبريد الإلكتروني بنجاح');
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function markAsPaid() {
    if (confirm('هل تريد تحديد هذه الفاتورة كمدفوعة؟')) {
        fetch(`/invoices/{{ invoice.id }}/mark-paid`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تحديد الفاتورة كمدفوعة');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
{% endblock %}
