"""
Receipt and ReceiptInvoiceAllocation models for payment tracking
"""

import uuid
from datetime import datetime, date
from decimal import Decimal
from app import db

class Receipt(db.Model):
    __tablename__ = 'receipts'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    customer_id = db.Column(db.String(36), db.Foreign<PERSON>ey('customers.id'), index=True)
    vendor_id = db.Column(db.String(36), db.Foreign<PERSON>ey('vendors.id'), index=True)
    invoice_id = db.Column(db.String(36), db.<PERSON><PERSON>ey('invoices.id'), index=True)
    account_id = db.Column(db.String(36), db.Foreign<PERSON>ey('accounts.id'), index=True)
    journal_entry_id = db.Column(db.String(36), db.ForeignKey('journal_entries.id'), index=True)
    receipt_number = db.Column(db.String(30), unique=True, nullable=False, index=True)
    receipt_date = db.Column(db.Date, nullable=False, index=True, default=date.today)
    receipt_type = db.Column(db.String(20), nullable=False, default='receipt', index=True)
    amount = db.Column(db.Numeric(14, 2), nullable=False)
    currency = db.Column(db.String(3), default='EGP')
    payment_method = db.Column(db.String(20), default='cash')
    reference_number = db.Column(db.String(50))
    description = db.Column(db.Text)
    notes = db.Column(db.Text)
    created_by_id = db.Column(db.String(36), db.ForeignKey('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Egyptian Tax Authority Integration Fields (ETA eReceipt API v1.2)
    eta_uuid = db.Column(db.String(100))  # SHA256 UUID for ETA
    eta_internal_id = db.Column(db.String(100))  # Internal ID من مصلحة الضرائب
    eta_submission_uuid = db.Column(db.String(100))  # UUID الإرسال
    eta_long_id = db.Column(db.String(200))  # Long ID من مصلحة الضرائب
    eta_hash_key = db.Column(db.String(500))  # Hash Key للتوقيع الرقمي
    eta_status = db.Column(db.String(50), default='draft')  # draft, submitted, accepted, rejected
    eta_submitted_at = db.Column(db.DateTime)

    # QR Code fields
    eta_qr_code = db.Column(db.Text)  # QR Code data from ETA
    eta_qr_image_path = db.Column(db.String(500))  # Path to generated QR image

    # ETA eReceipt v1.2 Required Fields
    previous_uuid = db.Column(db.String(100))  # Previous receipt UUID
    reference_old_uuid = db.Column(db.String(100))  # For resent receipts
    document_type_name = db.Column(db.String(50), default='s')  # 's' for Sale Receipt
    document_type_version = db.Column(db.String(10), default='1.2')

    # Header Information (ETA Required)
    datetime_issued = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    exchange_rate = db.Column(db.Numeric(10, 5), default=1.0)

    # Seller Information (ETA Required)
    branch_code = db.Column(db.String(50))
    device_serial_number = db.Column(db.String(100))
    activity_code = db.Column(db.String(10))

    # Buyer Information (ETA Required)
    buyer_type = db.Column(db.String(1))  # B, P, F
    buyer_id = db.Column(db.String(30))
    buyer_name = db.Column(db.String(100))
    buyer_mobile = db.Column(db.String(30))
    payment_number = db.Column(db.String(30))

    # Financial Information (ETA Required)
    total_sales = db.Column(db.Numeric(14, 5), default=0)
    total_commercial_discount = db.Column(db.Numeric(14, 5), default=0)
    total_items_discount = db.Column(db.Numeric(14, 5), default=0)
    net_amount = db.Column(db.Numeric(14, 5), default=0)
    fees_amount = db.Column(db.Numeric(14, 5), default=0)  # Reserved for future use
    total_amount = db.Column(db.Numeric(14, 5), default=0)
    adjustment = db.Column(db.Numeric(14, 5), default=0)  # Reserved for future use

    # Order Information (ETA Optional)
    sales_order_name_code = db.Column(db.String(200))
    order_delivery_mode = db.Column(db.String(30))
    gross_weight = db.Column(db.Numeric(10, 5))
    net_weight = db.Column(db.Numeric(10, 5))
    
    # Relationships
    customer = db.relationship('Customer', foreign_keys=[customer_id], overlaps="receipts")
    vendor = db.relationship('Vendor', foreign_keys=[vendor_id])
    invoice = db.relationship('Invoice', foreign_keys=[invoice_id])
    account = db.relationship('Account', foreign_keys=[account_id])
    journal_entry = db.relationship('JournalEntry', foreign_keys=[journal_entry_id])
    created_by = db.relationship('User', foreign_keys=[created_by_id], overlaps="creator,receipts")
    allocations = db.relationship('ReceiptInvoiceAllocation', backref='receipt', lazy='dynamic',
                                 cascade='all, delete-orphan')
    
    # Receipt type choices
    RECEIPT_TYPES = {
        'receipt': 'إيصال قبض (من عميل)',
        'payment': 'إيصال دفع (لمورد)'
    }

    # Payment method choices
    PAYMENT_METHODS = {
        'cash': 'نقدي',
        'bank': 'تحويل بنكي',
        'check': 'شيك',
        'card': 'بطاقة ائتمان'
    }
    
    def __init__(self, receipt_type='receipt', receipt_date=None, amount=None,
                 customer_id=None, vendor_id=None, invoice_id=None, account_id=None,
                 payment_method='cash', reference_number=None, description=None,
                 notes=None, created_by_id=None, currency='EGP'):
        self.receipt_type = receipt_type
        self.customer_id = customer_id
        self.vendor_id = vendor_id
        self.invoice_id = invoice_id
        self.account_id = account_id
        self.amount = amount or 0
        self.currency = currency
        self.receipt_date = receipt_date or date.today()
        self.payment_method = payment_method
        self.reference_number = reference_number
        self.description = description
        self.notes = notes
        self.created_by_id = created_by_id
        self.receipt_number = self.generate_receipt_number()
    
    def generate_receipt_number(self):
        """Generate unique receipt number"""
        from app.models.system_setting import SystemSetting
        
        prefix = SystemSetting.get_value('receipt_prefix', 'REC')
        year = self.receipt_date.year if self.receipt_date else date.today().year
        
        # Get last receipt number for this year
        last_receipt = Receipt.query.filter(
            Receipt.receipt_number.like(f'{prefix}-{year}-%'),
            Receipt.id != self.id
        ).order_by(Receipt.receipt_number.desc()).first()
        
        if last_receipt:
            try:
                last_num = int(last_receipt.receipt_number.split('-')[-1])
                next_num = last_num + 1
            except:
                next_num = 1
        else:
            next_num = 1
        
        return f"{prefix}-{year}-{next_num:04d}"
    
    def get_payment_method_display(self):
        """Get Arabic display for payment method"""
        return self.PAYMENT_METHODS.get(self.payment_method, self.payment_method)
    
    def get_allocated_amount(self):
        """Get total allocated amount to invoices"""
        total = self.allocations.with_entities(
            db.func.sum(ReceiptInvoiceAllocation.allocated_amount)
        ).scalar()
        return float(total or 0)
    
    def get_unallocated_amount(self):
        """Get unallocated amount"""
        return float(self.amount_received) - self.get_allocated_amount()
    
    def is_fully_allocated(self):
        """Check if receipt is fully allocated"""
        return abs(self.get_unallocated_amount()) < 0.01
    
    def allocate_to_invoice(self, invoice_id, amount):
        """Allocate amount to specific invoice"""
        from app.models.invoice import Invoice
        
        # Check if allocation already exists
        existing = self.allocations.filter_by(invoice_id=invoice_id).first()
        if existing:
            existing.allocated_amount += amount
        else:
            allocation = ReceiptInvoiceAllocation(
                receipt_id=self.id,
                invoice_id=invoice_id,
                allocated_amount=amount
            )
            db.session.add(allocation)
        
        # Update invoice status if fully paid
        invoice = Invoice.query.get(invoice_id)
        if invoice and invoice.is_fully_paid():
            invoice.mark_as_paid()
    
    def auto_allocate_to_oldest_invoices(self):
        """Automatically allocate to oldest unpaid invoices"""
        from app.models.invoice import Invoice
        
        remaining_amount = self.get_unallocated_amount()
        if remaining_amount <= 0:
            return
        
        # Get unpaid invoices for this customer, ordered by date
        unpaid_invoices = Invoice.query.filter_by(
            customer_id=self.customer_id
        ).filter(
            Invoice.status.in_(['sent', 'draft'])
        ).order_by(Invoice.issue_date).all()
        
        for invoice in unpaid_invoices:
            if remaining_amount <= 0:
                break
            
            outstanding = invoice.get_outstanding_amount()
            if outstanding > 0:
                allocation_amount = min(remaining_amount, outstanding)
                self.allocate_to_invoice(invoice.id, allocation_amount)
                remaining_amount -= allocation_amount
    
    def to_dict(self):
        """Convert receipt to dictionary"""
        return {
            'id': self.id,
            'customer_id': self.customer_id,
            'customer_name': self.customer.name if self.customer else None,
            'receipt_number': self.receipt_number,
            'receipt_date': self.receipt_date.isoformat() if self.receipt_date else None,
            'amount_received': float(self.amount_received),
            'payment_method': self.payment_method,
            'payment_method_display': self.get_payment_method_display(),
            'reference_number': self.reference_number,
            'notes': self.notes,
            'allocated_amount': self.get_allocated_amount(),
            'unallocated_amount': self.get_unallocated_amount(),
            'is_fully_allocated': self.is_fully_allocated(),
            'allocations_count': self.allocations.count(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Receipt {self.receipt_number}: {self.amount_received}>'


class ReceiptInvoiceAllocation(db.Model):
    __tablename__ = 'receipt_invoice_allocations'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    receipt_id = db.Column(db.String(36), db.ForeignKey('receipts.id'), nullable=False, index=True)
    invoice_id = db.Column(db.String(36), db.ForeignKey('invoices.id'), nullable=False, index=True)
    allocated_amount = db.Column(db.Numeric(14, 2), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def __init__(self, receipt_id, invoice_id, allocated_amount):
        self.receipt_id = receipt_id
        self.invoice_id = invoice_id
        self.allocated_amount = allocated_amount
    
    def to_dict(self):
        """Convert allocation to dictionary"""
        return {
            'id': self.id,
            'receipt_id': self.receipt_id,
            'invoice_id': self.invoice_id,
            'invoice_number': self.invoice.invoice_number if self.invoice else None,
            'allocated_amount': float(self.allocated_amount),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<ReceiptInvoiceAllocation {self.allocated_amount}>'


class ReceiptItem(db.Model):
    """بنود الإيصال الإلكتروني - متوافق مع ETA eReceipt API v1.2"""
    __tablename__ = 'receipt_items'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    receipt_id = db.Column(db.String(36), db.ForeignKey('receipts.id'), nullable=False, index=True)

    # Item Information (ETA Required)
    internal_code = db.Column(db.String(50), nullable=False)  # Internal product code
    description = db.Column(db.String(500), nullable=False)  # Item description
    item_type = db.Column(db.String(30), nullable=False)  # GS1 or EGS
    item_code = db.Column(db.String(100), nullable=False)  # GS1/EGS code
    unit_type = db.Column(db.String(30), nullable=False)  # Unit type code
    quantity = db.Column(db.Numeric(10, 5), nullable=False)  # Quantity
    unit_price = db.Column(db.Numeric(14, 5), nullable=False)  # Unit price

    # Financial Information (ETA Required)
    net_sale = db.Column(db.Numeric(14, 5), nullable=False)  # After discount
    total_sale = db.Column(db.Numeric(14, 5), nullable=False)  # Before discount
    total = db.Column(db.Numeric(14, 5), nullable=False)  # Final total with taxes

    # Discount Information (ETA Optional)
    commercial_discount_amount = db.Column(db.Numeric(14, 5), default=0)
    commercial_discount_rate = db.Column(db.Numeric(5, 2), default=0)
    item_discount_amount = db.Column(db.Numeric(14, 5), default=0)
    item_discount_rate = db.Column(db.Numeric(5, 2), default=0)
    additional_commercial_discount = db.Column(db.Numeric(14, 5), default=0)
    additional_item_discount = db.Column(db.Numeric(14, 5), default=0)

    # Tax Information (ETA Optional)
    value_difference = db.Column(db.Numeric(14, 5), default=0)

    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    receipt = db.relationship('Receipt', backref='items')

    def to_dict(self):
        """Convert item to dictionary"""
        return {
            'id': self.id,
            'receipt_id': self.receipt_id,
            'internal_code': self.internal_code,
            'description': self.description,
            'item_type': self.item_type,
            'item_code': self.item_code,
            'unit_type': self.unit_type,
            'quantity': float(self.quantity),
            'unit_price': float(self.unit_price),
            'net_sale': float(self.net_sale),
            'total_sale': float(self.total_sale),
            'total': float(self.total),
            'commercial_discount_amount': float(self.commercial_discount_amount),
            'commercial_discount_rate': float(self.commercial_discount_rate),
            'item_discount_amount': float(self.item_discount_amount),
            'item_discount_rate': float(self.item_discount_rate),
            'additional_commercial_discount': float(self.additional_commercial_discount),
            'additional_item_discount': float(self.additional_item_discount),
            'value_difference': float(self.value_difference),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<ReceiptItem {self.internal_code}: {self.description[:50]}>'


class ReceiptTaxTotal(db.Model):
    """إجمالي الضرائب للإيصال - متوافق مع ETA eReceipt API v1.2"""
    __tablename__ = 'receipt_tax_totals'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    receipt_id = db.Column(db.String(36), db.ForeignKey('receipts.id'), nullable=False, index=True)

    # Tax Information (ETA Required)
    tax_type = db.Column(db.String(30), nullable=False)  # T1-T20
    amount = db.Column(db.Numeric(14, 5), nullable=False)  # Tax amount

    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    receipt = db.relationship('Receipt', backref='tax_totals')

    def to_dict(self):
        """Convert tax total to dictionary"""
        return {
            'id': self.id,
            'receipt_id': self.receipt_id,
            'tax_type': self.tax_type,
            'amount': float(self.amount),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<ReceiptTaxTotal {self.tax_type}: {self.amount}>'


class ReceiptItemTax(db.Model):
    """ضرائب بنود الإيصال - متوافق مع ETA eReceipt API v1.2"""
    __tablename__ = 'receipt_item_taxes'

    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    receipt_item_id = db.Column(db.String(36), db.ForeignKey('receipt_items.id'), nullable=False, index=True)

    # Tax Information (ETA Required)
    tax_type = db.Column(db.String(30), nullable=False)  # T1-T20
    amount = db.Column(db.Numeric(14, 5), nullable=False)  # Tax amount
    sub_type = db.Column(db.String(50), nullable=False)  # Tax subtype
    rate = db.Column(db.Numeric(5, 2))  # Tax rate (optional)

    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    # Relationships
    receipt_item = db.relationship('ReceiptItem', backref='taxes')

    def to_dict(self):
        """Convert item tax to dictionary"""
        return {
            'id': self.id,
            'receipt_item_id': self.receipt_item_id,
            'tax_type': self.tax_type,
            'amount': float(self.amount),
            'sub_type': self.sub_type,
            'rate': float(self.rate) if self.rate else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<ReceiptItemTax {self.tax_type}: {self.amount}>'
