# 🚀 دليل التشغيل السريع - SystemTax
# Quick Start Guide - SystemTax

## 📋 **قبل البدء**

### **✅ تأكد من توفر:**
- **Docker Desktop** مثبت ومشغل
- **4GB RAM** متاحة على الأقل
- **20GB** مساحة فارغة على القرص
- **اتصال إنترنت** لتحميل الصور

---

## ⚡ **التشغيل السريع (5 دقائق)**

### **🪟 على Windows:**

#### **1. تحميل المشروع:**
```cmd
git clone https://github.com/your-repo/SystemTax.git
cd SystemTax
```

#### **2. تشغيل النظام:**
```cmd
docker-start.bat start
```

#### **3. انتظار التشغيل:**
- سيستغرق **2-5 دقائق** في المرة الأولى
- ستظهر رسالة "SystemTax is now running!" عند الانتهاء

#### **4. الوصول للنظام:**
- **افتح المتصفح:** http://localhost:8000
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

---

### **🐧 على Linux/Mac:**

#### **1. تحميل المشروع:**
```bash
git clone https://github.com/your-repo/SystemTax.git
cd SystemTax
```

#### **2. تشغيل النظام:**
```bash
chmod +x docker-start.sh
./docker-start.sh start
```

#### **3. الوصول للنظام:**
- **افتح المتصفح:** http://localhost:8000
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

---

## 🔧 **الإعداد الأولي (10 دقائق)**

### **1. تغيير كلمة مرور المدير:**
1. سجل دخول بالبيانات الافتراضية
2. انتقل لـ **الإعدادات** → **المستخدمين**
3. اختر المستخدم **admin**
4. غيّر كلمة المرور لكلمة قوية

### **2. إعداد معلومات الشركة:**
1. انتقل لـ **الإعدادات** → **معلومات الشركة**
2. أدخل:
   - اسم الشركة
   - الرقم الضريبي
   - العنوان
   - بيانات الاتصال
3. احفظ الإعدادات

### **3. إعداد دليل الحسابات:**
1. انتقل لـ **الحسابات** → **دليل الحسابات**
2. راجع الحسابات الافتراضية
3. أضف حسابات إضافية حسب الحاجة

### **4. إضافة العملاء الأوائل:**
1. انتقل لـ **العملاء** → **إضافة عميل جديد**
2. أدخل بيانات العملاء
3. احفظ البيانات

---

## 🏛️ **ربط مصلحة الضرائب (اختياري)**

### **1. الحصول على بيانات API:**
- سجل في منظومة الفاتورة الإلكترونية
- احصل على **Client ID** و **Client Secret**

### **2. إعداد التكامل:**
1. انتقل لـ **مصلحة الضرائب** → **الإعدادات**
2. أدخل:
   - **Client ID**
   - **Client Secret**
   - **البيئة:** sandbox (للتجريب) أو production (للإنتاج)
3. اضغط **اختبار الاتصال**
4. إذا نجح الاختبار، فعّل **الإرسال التلقائي**

---

## 📊 **إنشاء أول فاتورة**

### **1. إنشاء الفاتورة:**
1. انتقل لـ **الفواتير** → **فاتورة جديدة**
2. اختر العميل
3. أضف الأصناف والكميات
4. احفظ الفاتورة

### **2. إرسال للضرائب:**
1. من صفحة الفاتورة، اضغط **إرسال لمصلحة الضرائب**
2. تابع حالة الإرسال في **مصلحة الضرائب** → **المعاملات**

---

## 🛠️ **أوامر مفيدة**

### **🪟 Windows:**
```cmd
# تشغيل النظام
docker-start.bat start

# إيقاف النظام
docker-start.bat stop

# إعادة تشغيل
docker-start.bat restart

# عرض السجلات
docker-start.bat logs

# حالة النظام
docker-start.bat status
```

### **🐧 Linux/Mac:**
```bash
# تشغيل النظام
./docker-start.sh start

# إيقاف النظام
./docker-start.sh stop

# إعادة تشغيل
./docker-start.sh restart

# عرض السجلات
./docker-start.sh logs

# نسخ احتياطي
./docker-start.sh backup
```

---

## ❓ **مشاكل شائعة وحلولها**

### **🔴 "Docker is not running"**
**الحل:**
1. تأكد من تشغيل Docker Desktop
2. انتظر حتى يكتمل التشغيل (أيقونة خضراء)
3. أعد تشغيل الأمر

### **🔴 "Port 8000 is already in use"**
**الحل:**
```cmd
# إيقاف النظام أولاً
docker-start.bat stop

# ثم إعادة التشغيل
docker-start.bat start
```

### **🔴 "Cannot connect to database"**
**الحل:**
```cmd
# إعادة تشغيل قاعدة البيانات
docker-compose restart db

# فحص السجلات
docker-compose logs db
```

### **🔴 صفحة فارغة أو خطأ 500**
**الحل:**
```cmd
# فحص سجلات التطبيق
docker-start.bat logs

# إعادة بناء الصورة
docker-start.bat build
```

---

## 📞 **الحصول على المساعدة**

### **📋 الوثائق:**
- **[متطلبات التشغيل](DEPLOYMENT_REQUIREMENTS.md)** - للنشر على السيرفر
- **[دليل المستخدم](docs/user-guide.md)** - شرح مفصل للاستخدام
- **[دليل المطور](docs/developer-guide.md)** - للتطوير والتخصيص

### **🆘 الدعم:**
- **GitHub Issues:** للبلاغات والمشاكل
- **GitHub Discussions:** للأسئلة والمناقشات
- **البريد الإلكتروني:** <EMAIL>

---

## 🎯 **الخطوات التالية**

بعد التشغيل الناجح:

1. **📚 اقرأ دليل المستخدم** لفهم جميع الميزات
2. **🔒 راجع إعدادات الأمان** قبل الاستخدام الفعلي
3. **💾 اعد النسخ الاحتياطي** بانتظام
4. **📊 استكشف التقارير** المتاحة
5. **🏛️ اختبر التكامل مع مصلحة الضرائب** في البيئة التجريبية

---

<div align="center">

**🎉 مبروك! SystemTax يعمل الآن بنجاح! 🎉**

**إذا واجهت أي مشاكل، لا تتردد في طلب المساعدة**

</div>
