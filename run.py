#!/usr/bin/env python3
"""
SystemTax Application Runner
Entry point for running the SystemTax application
"""

import os
import sys
import click
from app import create_app

def main():
    """Main entry point for SystemTax application"""
    
    # Check Python version
    if sys.version_info < (3, 9):
        print("❌ Error: SystemTax requires Python 3.9 or higher")
        print(f"Current version: {sys.version}")
        sys.exit(1)
    
    # Create application
    try:
        app = create_app()
    except Exception as e:
        print(f"❌ Error creating application: {str(e)}")
        sys.exit(1)
    
    # Check if running in development mode
    if app.config.get('FLASK_ENV') == 'development':
        print("🚀 Starting SystemTax in development mode...")
        print("📖 Open http://localhost:8000 in your browser")
        print("👤 Default login: admin / admin123")
        print("⚠️  Don't forget to change the admin password!")
        print("")
    
    # Run the application
    try:
        app.run(
            host=os.getenv('HOST', '0.0.0.0'),
            port=int(os.getenv('PORT', 8000)),
            debug=app.config.get('DEBUG', False)
        )
    except KeyboardInterrupt:
        print("\n👋 SystemTax stopped by user")
    except Exception as e:
        print(f"❌ Error running application: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
