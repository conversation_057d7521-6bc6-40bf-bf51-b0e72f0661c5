#!/usr/bin/env python3
"""
سكريبت التثبيت التلقائي لنظام SystemTax
Automatic Installation Script for SystemTax
"""

import os
import sys
import subprocess
import sqlite3
from pathlib import Path

def print_header():
    """طباعة رأس البرنامج"""
    print("=" * 60)
    print("🚀 مرحباً بك في معالج تثبيت SystemTax")
    print("   SystemTax Installation Wizard")
    print("=" * 60)
    print()

def check_python_version():
    """التحقق من إصدار Python"""
    print("🐍 التحقق من إصدار Python...")
    
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"   الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المتطلبات...")
    
    try:
        # تحديث pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ تم تحديث pip")
        
        # تثبيت المتطلبات
        if os.path.exists("requirements.txt"):
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                          check=True, capture_output=True)
            print("✅ تم تثبيت جميع المتطلبات")
        else:
            print("⚠️ ملف requirements.txt غير موجود")
            return False
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("\n📁 إنشاء المجلدات...")
    
    directories = [
        "instance",
        "logs", 
        "uploads",
        "uploads/qr_codes",
        "uploads/documents",
        "backups"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")
    
    return True

def setup_database():
    """إعداد قاعدة البيانات"""
    print("\n🗄️ إعداد قاعدة البيانات...")
    
    try:
        # إنشاء قاعدة بيانات SQLite
        db_path = "instance/systemtax.db"
        
        if os.path.exists(db_path):
            print("ℹ️ قاعدة البيانات موجودة مسبقاً")
            return True
        
        # إنشاء قاعدة بيانات فارغة
        conn = sqlite3.connect(db_path)
        conn.execute("CREATE TABLE IF NOT EXISTS _install_check (id INTEGER)")
        conn.commit()
        conn.close()
        
        print(f"✅ تم إنشاء قاعدة البيانات: {db_path}")
        
        # تشغيل migration
        if os.path.exists("migrate_receipts_eta.py"):
            print("🔄 تشغيل migration...")
            try:
                subprocess.run([sys.executable, "migrate_receipts_eta.py"], 
                              check=True, capture_output=True, cwd=".")
                print("✅ تم تشغيل migration بنجاح")
            except subprocess.CalledProcessError:
                print("⚠️ تحذير: migration فشل، سيتم إنشاء الجداول عند أول تشغيل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def create_env_file():
    """إنشاء ملف .env"""
    print("\n⚙️ إعداد ملف البيئة...")
    
    if os.path.exists(".env"):
        print("ℹ️ ملف .env موجود مسبقاً")
        return True
    
    if os.path.exists(".env.example"):
        try:
            # نسخ .env.example إلى .env
            with open(".env.example", "r", encoding="utf-8") as src:
                content = src.read()
            
            with open(".env", "w", encoding="utf-8") as dst:
                dst.write(content)
            
            print("✅ تم إنشاء ملف .env من .env.example")
            print("⚠️ يرجى تعديل .env بالبيانات الفعلية")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء ملف .env: {e}")
            return False
    else:
        print("⚠️ ملف .env.example غير موجود")
        return False

def initialize_system():
    """تهيئة النظام"""
    print("\n🏗️ تهيئة النظام...")
    
    try:
        # تشغيل التطبيق لتهيئة قاعدة البيانات
        init_script = """
from app import create_app, db
from app.models.system_settings import SystemSettings

app = create_app()
with app.app_context():
    try:
        db.create_all()
        SystemSettings.initialize_default_settings()
        print("✅ تم تهيئة النظام بنجاح")
    except Exception as e:
        print(f"❌ خطأ في التهيئة: {e}")
"""
        
        result = subprocess.run([sys.executable, "-c", init_script], 
                               capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تهيئة النظام")
            return True
        else:
            print(f"⚠️ تحذير في التهيئة: {result.stderr}")
            return True  # نعتبرها نجحت حتى لو كان هناك تحذيرات
            
    except Exception as e:
        print(f"❌ خطأ في تهيئة النظام: {e}")
        return False

def test_installation():
    """اختبار التثبيت"""
    print("\n🧪 اختبار التثبيت...")
    
    try:
        # اختبار استيراد التطبيق
        test_script = """
try:
    from app import create_app
    app = create_app()
    print("✅ التطبيق يعمل بنجاح")
except Exception as e:
    print(f"❌ خطأ في التطبيق: {e}")
    exit(1)
"""
        
        result = subprocess.run([sys.executable, "-c", test_script], 
                               capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التثبيت نجح")
            return True
        else:
            print(f"❌ اختبار التثبيت فشل: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التثبيت: {e}")
        return False

def print_success_message():
    """طباعة رسالة النجاح"""
    print("\n" + "=" * 60)
    print("🎉 تم تثبيت SystemTax بنجاح!")
    print("   SystemTax installed successfully!")
    print("=" * 60)
    print()
    print("📋 الخطوات التالية:")
    print("   1. تعديل ملف .env بالبيانات الفعلية")
    print("   2. تشغيل النظام: python app.py")
    print("   3. فتح المتصفح: http://localhost:8000")
    print("   4. تسجيل الدخول: admin / admin123")
    print()
    print("📚 للمساعدة:")
    print("   - دليل المستخدم: USER_GUIDE.md")
    print("   - تقرير الإنتاج: PRODUCTION_READY_REPORT.md")
    print()
    print("🚀 مبروك! SystemTax جاهز للاستخدام!")
    print("=" * 60)

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # التحقق من Python
    if not check_python_version():
        return False
    
    # تثبيت المتطلبات
    if not install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        return False
    
    # إنشاء المجلدات
    if not create_directories():
        print("❌ فشل في إنشاء المجلدات")
        return False
    
    # إعداد قاعدة البيانات
    if not setup_database():
        print("❌ فشل في إعداد قاعدة البيانات")
        return False
    
    # إنشاء ملف .env
    if not create_env_file():
        print("❌ فشل في إنشاء ملف .env")
        return False
    
    # تهيئة النظام
    if not initialize_system():
        print("❌ فشل في تهيئة النظام")
        return False
    
    # اختبار التثبيت
    if not test_installation():
        print("❌ فشل في اختبار التثبيت")
        return False
    
    # رسالة النجاح
    print_success_message()
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ فشل التثبيت!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إلغاء التثبيت بواسطة المستخدم")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        sys.exit(1)
