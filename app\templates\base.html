<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ APP_NAME }}{% endblock %}</title>
    
    <!-- Bootstrap 5 RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #059669;
            --danger-color: #dc2626;
            --warning-color: #d97706;
            --info-color: #0891b2;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8fafc;
        }
        
        .navbar-brand {
            font-weight: bold;
            color: var(--primary-color) !important;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            min-height: calc(100vh - 56px);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0.5rem;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-2px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            margin-left: 0.5rem;
        }
        
        .main-content {
            background-color: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            margin: 1rem;
            padding: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .btn {
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #1d4ed8;
            border-color: #1d4ed8;
            transform: translateY(-1px);
        }
        
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: var(--light-color);
            border: none;
            font-weight: 600;
            color: var(--dark-color);
        }
        
        .alert {
            border: none;
            border-radius: 0.75rem;
            font-weight: 500;
        }
        
        .form-control, .form-select {
            border-radius: 0.5rem;
            border: 1px solid #e2e8f0;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .stats-card.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        
        .stats-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .stats-card.info {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .page-header {
            background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
            color: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
        }
        
        .breadcrumb {
            background: none;
            padding: 0;
        }
        
        .breadcrumb-item + .breadcrumb-item::before {
            content: "←";
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 56px;
                right: -250px;
                width: 250px;
                height: calc(100vh - 56px);
                transition: right 0.3s ease;
                z-index: 1000;
            }
            
            .sidebar.show {
                right: 0;
            }
            
            .main-content {
                margin: 0.5rem;
                padding: 1rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container-fluid">
            <button class="navbar-toggler d-lg-none" type="button" data-bs-toggle="collapse" data-bs-target="#sidebar">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <a class="navbar-brand" href="{{ url_for('dashboard.index') }}">
                <i class="fas fa-calculator me-2"></i>
                {{ APP_NAME }}
            </a>
            
            <div class="navbar-nav ms-auto">
                {% if current_user.is_authenticated %}
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.get_display_name() }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            {% if current_user.is_authenticated %}
            <!-- Sidebar -->
            <nav class="col-lg-2 d-lg-block sidebar collapse" id="sidebar">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('dashboard.index') }}">
                                <i class="fas fa-tachometer-alt"></i>
                                لوحة التحكم
                            </a>
                        </li>
                        
                        {% if current_user.can_access('accounts') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('accounts.index') }}">
                                <i class="fas fa-list"></i>
                                دليل الحسابات
                            </a>
                        </li>
                        {% endif %}
                        
                        {% if current_user.can_access('journal') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('journal.index') }}">
                                <i class="fas fa-book"></i>
                                القيود اليومية
                            </a>
                        </li>
                        {% endif %}
                        
                        {% if current_user.can_access('customers') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('customers.index') }}">
                                <i class="fas fa-users"></i>
                                العملاء
                            </a>
                        </li>
                        {% endif %}
                        
                        {% if current_user.can_access('vendors') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('vendors.index') }}">
                                <i class="fas fa-truck"></i>
                                الموردين
                            </a>
                        </li>
                        {% endif %}
                        
                        {% if current_user.can_access('invoices') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('invoices.index') }}">
                                <i class="fas fa-file-invoice"></i>
                                الفواتير
                            </a>
                        </li>
                        {% endif %}
                        
                        {% if current_user.can_access('receipts') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('receipts.index') }}">
                                <i class="fas fa-receipt"></i>
                                الإيصالات
                            </a>
                        </li>
                        {% endif %}
                        
                        {% if current_user.can_access('reports') %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('reports.index') }}">
                                <i class="fas fa-chart-bar"></i>
                                التقارير
                            </a>
                        </li>
                        {% endif %}
                        
                        {% if current_user.is_admin() %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('eta.index') }}">
                                <i class="fas fa-university"></i>
                                مصلحة الضرائب
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('settings.index') }}">
                                <i class="fas fa-cog"></i>
                                الإعدادات
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>
            {% endif %}

            <!-- Main content -->
            <main class="col-lg-10 ms-sm-auto px-md-4">
                {% if current_user.is_authenticated %}
                <div class="main-content">
                {% endif %}
                
                    <!-- Flash messages -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}

                    <!-- Page content -->
                    {% block content %}{% endblock %}
                
                {% if current_user.is_authenticated %}
                </div>
                {% endif %}
            </main>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Mobile sidebar toggle
        $('.navbar-toggler').click(function() {
            $('#sidebar').toggleClass('show');
        });
        
        // Close sidebar when clicking outside on mobile
        $(document).click(function(e) {
            if (!$(e.target).closest('#sidebar, .navbar-toggler').length) {
                $('#sidebar').removeClass('show');
            }
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
