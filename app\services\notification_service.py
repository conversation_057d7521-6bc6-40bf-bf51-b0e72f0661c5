"""
Notification service for managing and sending notifications
"""

from app import db, mail
from app.models.notification import (
    Notification, NotificationTemplate, NotificationPreference, 
    NotificationLog, NotificationType, NotificationChannel
)
from app.models.user import User
from flask import current_app, render_template_string, url_for
from flask_mail import Message
from datetime import datetime, timedelta
from sqlalchemy import and_, or_
import json
import logging

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for managing notifications"""
    
    @staticmethod
    def create_notification(title, message, notification_type=NotificationType.INFO, 
                          user_id=None, role=None, is_global=False, 
                          channels='in_app', action_url=None, action_text=None,
                          data=None, scheduled_at=None, expires_at=None):
        """Create a new notification"""
        
        notification = Notification(
            title=title,
            message=message,
            type=notification_type,
            user_id=user_id,
            role=role,
            is_global=is_global,
            channels=channels,
            action_url=action_url,
            action_text=action_text,
            data=json.dumps(data) if data else None,
            scheduled_at=scheduled_at,
            expires_at=expires_at
        )
        
        db.session.add(notification)
        db.session.commit()
        
        # Send immediately if not scheduled
        if not scheduled_at or scheduled_at <= datetime.utcnow():
            NotificationService.send_notification(notification.id)
        
        return notification
    
    @staticmethod
    def create_from_template(template_name, context=None, user_id=None, **kwargs):
        """Create notification from template"""
        
        template = NotificationTemplate.query.filter_by(
            name=template_name, 
            is_active=True
        ).first()
        
        if not template:
            logger.error(f"Notification template '{template_name}' not found")
            return None
        
        notification = template.create_notification(
            context=context,
            user_id=user_id,
            **kwargs
        )
        
        db.session.add(notification)
        db.session.commit()
        
        # Send if not scheduled
        if notification.can_be_sent:
            NotificationService.send_notification(notification.id)
        
        return notification
    
    @staticmethod
    def send_notification(notification_id):
        """Send a notification through all configured channels"""
        
        notification = Notification.query.get(notification_id)
        if not notification or not notification.can_be_sent:
            return False
        
        channels = notification.get_channels_list()
        recipients = NotificationService._get_recipients(notification)
        
        success = True
        for recipient in recipients:
            user_channels = NotificationService._get_user_channels(recipient, notification.type, channels)
            
            for channel in user_channels:
                try:
                    if channel == 'in_app':
                        NotificationService._send_in_app(notification, recipient)
                    elif channel == 'email':
                        NotificationService._send_email(notification, recipient)
                    elif channel == 'sms':
                        NotificationService._send_sms(notification, recipient)
                    elif channel == 'push':
                        NotificationService._send_push(notification, recipient)
                        
                except Exception as e:
                    logger.error(f"Failed to send notification {notification_id} via {channel} to {recipient.id}: {e}")
                    success = False
        
        if success:
            notification.mark_as_sent()
        
        return success
    
    @staticmethod
    def _get_recipients(notification):
        """Get list of users who should receive the notification"""
        
        recipients = []
        
        if notification.is_global:
            # Send to all active users
            recipients = User.query.filter_by(is_active=True).all()
        elif notification.user_id:
            # Send to specific user
            user = User.query.get(notification.user_id)
            if user and user.is_active:
                recipients = [user]
        elif notification.role:
            # Send to users with specific role
            recipients = User.query.filter(
                and_(User.role == notification.role, User.is_active == True)
            ).all()
        
        return recipients
    
    @staticmethod
    def _get_user_channels(user, notification_type, default_channels):
        """Get enabled channels for a user and notification type"""
        
        # Get user preferences
        preference = NotificationPreference.query.filter_by(
            user_id=user.id,
            notification_type=notification_type
        ).first()
        
        if preference:
            # Check if in quiet hours
            if preference.is_in_quiet_hours():
                return ['in_app']  # Only in-app during quiet hours
            
            return preference.get_enabled_channels()
        
        # Use default channels if no preference set
        return default_channels
    
    @staticmethod
    def _send_in_app(notification, user):
        """Send in-app notification"""
        
        # Create user-specific notification if global
        if notification.is_global or notification.role:
            user_notification = Notification(
                title=notification.title,
                message=notification.message,
                type=notification.type,
                user_id=user.id,
                channels='in_app',
                action_url=notification.action_url,
                action_text=notification.action_text,
                data=notification.data
            )
            db.session.add(user_notification)
            db.session.commit()
        
        # Log delivery
        log = NotificationLog(
            notification_id=notification.id,
            channel=NotificationChannel.IN_APP,
            recipient=str(user.id),
            status='delivered'
        )
        db.session.add(log)
        db.session.commit()
    
    @staticmethod
    def _send_email(notification, user):
        """Send email notification"""
        
        if not user.email:
            logger.warning(f"User {user.id} has no email address")
            return
        
        try:
            subject = f"[{current_app.config.get('APP_NAME', 'SystemTax')}] {notification.title}"
            
            # Render email template
            html_body = render_template_string("""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>{{ title }}</title>
                <style>
                    body { font-family: Arial, sans-serif; direction: rtl; text-align: right; }
                    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                    .header { background: #4e73df; color: white; padding: 20px; text-align: center; }
                    .content { padding: 20px; background: #f8f9fc; }
                    .footer { padding: 20px; text-align: center; color: #666; }
                    .button { display: inline-block; padding: 10px 20px; background: #4e73df; color: white; text-decoration: none; border-radius: 5px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>{{ title }}</h1>
                    </div>
                    <div class="content">
                        <p>مرحباً {{ user_name }}،</p>
                        <p>{{ message }}</p>
                        {% if action_url %}
                        <p>
                            <a href="{{ action_url }}" class="button">{{ action_text or 'عرض التفاصيل' }}</a>
                        </p>
                        {% endif %}
                    </div>
                    <div class="footer">
                        <p>هذه رسالة تلقائية من نظام SystemTax</p>
                    </div>
                </div>
            </body>
            </html>
            """, 
            title=notification.title,
            message=notification.message,
            user_name=user.username,
            action_url=notification.action_url,
            action_text=notification.action_text
            )
            
            msg = Message(
                subject=subject,
                recipients=[user.email],
                html=html_body,
                sender=current_app.config.get('MAIL_DEFAULT_SENDER')
            )
            
            mail.send(msg)
            
            # Log successful delivery
            log = NotificationLog(
                notification_id=notification.id,
                channel=NotificationChannel.EMAIL,
                recipient=user.email,
                status='sent'
            )
            db.session.add(log)
            db.session.commit()
            
        except Exception as e:
            logger.error(f"Failed to send email to {user.email}: {e}")
            
            # Log failed delivery
            log = NotificationLog(
                notification_id=notification.id,
                channel=NotificationChannel.EMAIL,
                recipient=user.email,
                status='failed',
                response_message=str(e)
            )
            db.session.add(log)
            db.session.commit()
            raise
    
    @staticmethod
    def _send_sms(notification, user):
        """Send SMS notification"""
        
        if not user.phone:
            logger.warning(f"User {user.id} has no phone number")
            return
        
        # SMS implementation would go here
        # This is a placeholder for SMS service integration
        
        # Log delivery
        log = NotificationLog(
            notification_id=notification.id,
            channel=NotificationChannel.SMS,
            recipient=user.phone,
            status='not_implemented'
        )
        db.session.add(log)
        db.session.commit()
    
    @staticmethod
    def _send_push(notification, user):
        """Send push notification"""
        
        # Push notification implementation would go here
        # This is a placeholder for push service integration
        
        # Log delivery
        log = NotificationLog(
            notification_id=notification.id,
            channel=NotificationChannel.PUSH,
            recipient=str(user.id),
            status='not_implemented'
        )
        db.session.add(log)
        db.session.commit()
    
    @staticmethod
    def get_user_notifications(user_id, unread_only=False, limit=50):
        """Get notifications for a user"""
        
        query = Notification.query.filter(
            or_(
                Notification.user_id == user_id,
                and_(Notification.is_global == True, Notification.is_sent == True)
            )
        )
        
        if unread_only:
            query = query.filter(Notification.is_read == False)
        
        query = query.filter(
            or_(
                Notification.expires_at.is_(None),
                Notification.expires_at > datetime.utcnow()
            )
        )
        
        return query.order_by(Notification.created_at.desc()).limit(limit).all()
    
    @staticmethod
    def mark_as_read(notification_id, user_id=None):
        """Mark notification as read"""
        
        query = Notification.query.filter_by(id=notification_id)
        
        if user_id:
            query = query.filter(
                or_(
                    Notification.user_id == user_id,
                    Notification.is_global == True
                )
            )
        
        notification = query.first()
        if notification:
            notification.mark_as_read()
            return True
        
        return False
    
    @staticmethod
    def mark_all_as_read(user_id):
        """Mark all notifications as read for a user"""
        
        notifications = Notification.query.filter(
            or_(
                Notification.user_id == user_id,
                Notification.is_global == True
            ),
            Notification.is_read == False
        ).all()
        
        for notification in notifications:
            notification.is_read = True
            notification.read_at = datetime.utcnow()
        
        db.session.commit()
        return len(notifications)
    
    @staticmethod
    def get_unread_count(user_id):
        """Get count of unread notifications for a user"""
        
        return Notification.query.filter(
            or_(
                Notification.user_id == user_id,
                Notification.is_global == True
            ),
            Notification.is_read == False,
            or_(
                Notification.expires_at.is_(None),
                Notification.expires_at > datetime.utcnow()
            )
        ).count()
    
    @staticmethod
    def send_scheduled_notifications():
        """Send notifications that are scheduled for now"""
        
        scheduled = Notification.query.filter(
            Notification.is_sent == False,
            Notification.scheduled_at <= datetime.utcnow(),
            or_(
                Notification.expires_at.is_(None),
                Notification.expires_at > datetime.utcnow()
            )
        ).all()
        
        sent_count = 0
        for notification in scheduled:
            try:
                if NotificationService.send_notification(notification.id):
                    sent_count += 1
            except Exception as e:
                logger.error(f"Failed to send scheduled notification {notification.id}: {e}")
        
        return sent_count
    
    @staticmethod
    def cleanup_old_notifications(days=30):
        """Clean up old notifications"""
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Delete old read notifications
        deleted = Notification.query.filter(
            Notification.is_read == True,
            Notification.created_at < cutoff_date
        ).delete()
        
        # Delete expired notifications
        expired = Notification.query.filter(
            Notification.expires_at < datetime.utcnow()
        ).delete()
        
        db.session.commit()
        
        return deleted + expired


# Convenience functions for common notification types

def notify_invoice_due(invoice):
    """Send notification for due invoice"""
    return NotificationService.create_from_template(
        'invoice_due',
        context={
            'invoice_number': invoice.invoice_number,
            'customer_name': invoice.customer.name,
            'amount': invoice.total_amount,
            'due_date': invoice.due_date.strftime('%Y-%m-%d')
        },
        action_url=url_for('invoices.detail', id=invoice.id),
        action_text='عرض الفاتورة'
    )

def notify_payment_received(receipt):
    """Send notification for payment received"""
    return NotificationService.create_from_template(
        'payment_received',
        context={
            'receipt_number': receipt.receipt_number,
            'amount': receipt.amount,
            'customer_name': receipt.customer.name if receipt.customer else 'غير محدد'
        },
        action_url=url_for('receipts.detail', id=receipt.id),
        action_text='عرض الإيصال'
    )

def notify_low_balance(account, balance):
    """Send notification for low account balance"""
    return NotificationService.create_notification(
        title='رصيد منخفض',
        message=f'رصيد الحساب {account.name} منخفض: {balance} ج.م',
        notification_type=NotificationType.LOW_BALANCE,
        role='admin',
        channels='in_app,email'
    )

def notify_system_update(version, features):
    """Send notification for system update"""
    return NotificationService.create_notification(
        title=f'تحديث النظام {version}',
        message=f'تم تحديث النظام بميزات جديدة: {features}',
        notification_type=NotificationType.SYSTEM_UPDATE,
        is_global=True,
        channels='in_app'
    )
