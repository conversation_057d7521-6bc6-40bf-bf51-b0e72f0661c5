#!/usr/bin/env python3
"""
إنشاء الجداول المفقودة
Create missing tables
"""

import sqlite3
import os

def create_missing_tables():
    """إنشاء الجداول المفقودة"""
    db_path = 'instance/systemtax.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🏗️ إنشاء الجداول المفقودة...")
        
        # جدول journal_entries
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS journal_entries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_number VARCHAR(50) UNIQUE NOT NULL,
                date DATE NOT NULL,
                description TEXT,
                reference VARCHAR(100),
                total_debit DECIMAL(15,2) DEFAULT 0,
                total_credit DECIMAL(15,2) DEFAULT 0,
                is_posted BOOLEAN DEFAULT 0,
                created_by INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        """)
        print("✅ جدول journal_entries")
        
        # جدول journal_entry_lines
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS journal_entry_lines (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                journal_entry_id INTEGER NOT NULL,
                account_id INTEGER NOT NULL,
                description TEXT,
                debit DECIMAL(15,2) DEFAULT 0,
                credit DECIMAL(15,2) DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id),
                FOREIGN KEY (account_id) REFERENCES accounts (id)
            )
        """)
        print("✅ جدول journal_entry_lines")
        
        # جدول customers
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(200) NOT NULL,
                email VARCHAR(120),
                phone VARCHAR(20),
                address TEXT,
                tax_id VARCHAR(50),
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ جدول customers")
        
        # جدول vendors
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS vendors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name VARCHAR(200) NOT NULL,
                email VARCHAR(120),
                phone VARCHAR(20),
                address TEXT,
                tax_id VARCHAR(50),
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        print("✅ جدول vendors")
        
        # جدول receipts
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS receipts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                receipt_number VARCHAR(50) UNIQUE NOT NULL,
                customer_id INTEGER,
                date DATE NOT NULL,
                subtotal DECIMAL(15,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft',
                notes TEXT,
                eta_uuid VARCHAR(100),
                eta_internal_id VARCHAR(100),
                eta_status VARCHAR(50),
                eta_submitted_at DATETIME,
                qr_code_path VARCHAR(255),
                previous_uuid VARCHAR(100),
                reference_old_uuid VARCHAR(100),
                document_type_name VARCHAR(50) DEFAULT 's',
                document_type_version VARCHAR(10) DEFAULT '1.2',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)
        print("✅ جدول receipts")
        
        # جدول receipt_items
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS receipt_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                receipt_id INTEGER NOT NULL,
                description VARCHAR(255) NOT NULL,
                quantity DECIMAL(10,3) DEFAULT 1,
                unit_price DECIMAL(15,2) NOT NULL,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (receipt_id) REFERENCES receipts (id)
            )
        """)
        print("✅ جدول receipt_items")
        
        # إنشاء الفهارس
        print("\n📋 إنشاء الفهارس...")
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_journal_entry_lines_journal_id ON journal_entry_lines(journal_entry_id)",
            "CREATE INDEX IF NOT EXISTS idx_journal_entry_lines_account_id ON journal_entry_lines(account_id)",
            "CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries(date)",
            "CREATE INDEX IF NOT EXISTS idx_receipts_date ON receipts(date)",
            "CREATE INDEX IF NOT EXISTS idx_receipts_number ON receipts(receipt_number)"
        ]
        
        for index in indexes:
            cursor.execute(index)
        
        print("✅ تم إنشاء جميع الفهارس")
        
        # إدراج بيانات تجريبية
        print("\n📊 إدراج بيانات تجريبية...")
        
        # قيد محاسبي تجريبي
        cursor.execute("""
            INSERT OR IGNORE INTO journal_entries (id, entry_number, date, description, total_debit, total_credit, is_posted)
            VALUES (1, 'JE-2025-0001', '2025-07-15', 'قيد تجريبي', 1000.00, 1000.00, 1)
        """)
        
        # تفاصيل القيد
        cursor.execute("""
            INSERT OR IGNORE INTO journal_entry_lines (journal_entry_id, account_id, description, debit, credit)
            VALUES 
            (1, 1, 'مدين - حساب الأصول', 1000.00, 0.00),
            (1, 2, 'دائن - حساب الإيرادات', 0.00, 1000.00)
        """)
        
        # عميل تجريبي
        cursor.execute("""
            INSERT OR IGNORE INTO customers (id, name, email, phone, is_active)
            VALUES (1, 'عميل تجريبي', '<EMAIL>', '***********', 1)
        """)
        
        # إيصال تجريبي
        cursor.execute("""
            INSERT OR IGNORE INTO receipts (id, receipt_number, customer_id, date, subtotal, tax_amount, total_amount, status)
            VALUES (1, 'REC-2025-0001', 1, '2025-07-15', 100.00, 14.00, 114.00, 'completed')
        """)
        
        # بند الإيصال
        cursor.execute("""
            INSERT OR IGNORE INTO receipt_items (receipt_id, description, quantity, unit_price, tax_rate, tax_amount, total_amount)
            VALUES (1, 'منتج تجريبي', 1.000, 100.00, 14.00, 14.00, 114.00)
        """)
        
        print("✅ تم إدراج البيانات التجريبية")
        
        conn.commit()
        conn.close()
        
        print("\n🎉 تم إنشاء جميع الجداول المفقودة بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False

if __name__ == "__main__":
    create_missing_tables()
