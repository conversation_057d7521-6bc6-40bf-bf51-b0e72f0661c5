"""
Dashboard views
"""

from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from sqlalchemy import func, and_, or_
from app import db, cache
from app.models.user import User
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.models.account import Account
from app.models.invoice import Invoice
from app.models.receipt import Receipt
from app.models.journal import JournalEntry
from app.utils.decorators import employee_required
from app.utils.helpers import format_currency

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@dashboard_bp.route('/dashboard')
@employee_required
def index():
    """Main dashboard"""
    # Get dashboard statistics
    stats = get_dashboard_stats()
    
    # Get recent activities
    recent_invoices = get_recent_invoices()
    recent_receipts = get_recent_receipts()
    recent_journal_entries = get_recent_journal_entries()
    
    # Get charts data
    monthly_sales = get_monthly_sales_data()
    top_customers = get_top_customers_data()
    
    return render_template('dashboard/index.html',
                         stats=stats,
                         recent_invoices=recent_invoices,
                         recent_receipts=recent_receipts,
                         recent_journal_entries=recent_journal_entries,
                         monthly_sales=monthly_sales,
                         top_customers=top_customers)

@dashboard_bp.route('/api/stats')
@employee_required
def api_stats():
    """API endpoint for dashboard statistics"""
    stats = get_dashboard_stats()
    return jsonify(stats)

@dashboard_bp.route('/api/monthly-sales')
@employee_required
def api_monthly_sales():
    """API endpoint for monthly sales data"""
    data = get_monthly_sales_data()
    return jsonify(data)

@dashboard_bp.route('/api/top-customers')
@employee_required
def api_top_customers():
    """API endpoint for top customers data"""
    data = get_top_customers_data()
    return jsonify(data)

@cache.memoize(timeout=300)  # Cache for 5 minutes
def get_dashboard_stats():
    """Get dashboard statistics"""
    today = date.today()
    this_month_start = date(today.year, today.month, 1)
    last_month_start = date(today.year, today.month - 1, 1) if today.month > 1 else date(today.year - 1, 12, 1)
    last_month_end = this_month_start - timedelta(days=1)
    
    # Total counts
    total_customers = Customer.query.filter_by(is_active=True).count()
    total_vendors = Vendor.query.filter_by(is_active=True).count()
    total_invoices = Invoice.query.count()
    total_receipts = Receipt.query.count()
    
    # This month's invoices
    this_month_invoices = Invoice.query.filter(
        Invoice.issue_date >= this_month_start
    ).count()
    
    # Last month's invoices for comparison
    last_month_invoices = Invoice.query.filter(
        and_(Invoice.issue_date >= last_month_start,
             Invoice.issue_date <= last_month_end)
    ).count()
    
    # This month's sales
    this_month_sales = db.session.query(
        func.coalesce(func.sum(Invoice.total_amount), 0)
    ).filter(
        Invoice.issue_date >= this_month_start
    ).scalar() or 0
    
    # Last month's sales for comparison
    last_month_sales = db.session.query(
        func.coalesce(func.sum(Invoice.total_amount), 0)
    ).filter(
        and_(Invoice.issue_date >= last_month_start,
             Invoice.issue_date <= last_month_end)
    ).scalar() or 0
    
    # This month's receipts
    this_month_receipts = db.session.query(
        func.coalesce(func.sum(Receipt.amount_received), 0)
    ).filter(
        Receipt.receipt_date >= this_month_start
    ).scalar() or 0
    
    # Outstanding invoices
    outstanding_amount = db.session.query(
        func.coalesce(func.sum(Invoice.total_amount), 0)
    ).filter(
        Invoice.status.in_(['sent', 'draft'])
    ).scalar() or 0
    
    # Calculate growth percentages
    invoice_growth = calculate_growth_percentage(this_month_invoices, last_month_invoices)
    sales_growth = calculate_growth_percentage(float(this_month_sales), float(last_month_sales))
    
    return {
        'total_customers': total_customers,
        'total_vendors': total_vendors,
        'total_invoices': total_invoices,
        'total_receipts': total_receipts,
        'this_month_invoices': this_month_invoices,
        'this_month_sales': float(this_month_sales),
        'this_month_receipts': float(this_month_receipts),
        'outstanding_amount': float(outstanding_amount),
        'invoice_growth': invoice_growth,
        'sales_growth': sales_growth,
        'formatted_sales': format_currency(this_month_sales),
        'formatted_receipts': format_currency(this_month_receipts),
        'formatted_outstanding': format_currency(outstanding_amount)
    }

def calculate_growth_percentage(current, previous):
    """Calculate growth percentage"""
    if previous == 0:
        return 100 if current > 0 else 0
    
    return round(((current - previous) / previous) * 100, 1)

def get_recent_invoices(limit=5):
    """Get recent invoices"""
    invoices = Invoice.query.order_by(
        Invoice.created_at.desc()
    ).limit(limit).all()
    
    return [invoice.to_dict() for invoice in invoices]

def get_recent_receipts(limit=5):
    """Get recent receipts"""
    receipts = Receipt.query.order_by(
        Receipt.created_at.desc()
    ).limit(limit).all()
    
    return [receipt.to_dict() for receipt in receipts]

def get_recent_journal_entries(limit=5):
    """Get recent journal entries"""
    if not current_user.can_access('journal'):
        return []
    
    entries = JournalEntry.query.order_by(
        JournalEntry.created_at.desc()
    ).limit(limit).all()
    
    return [entry.to_dict() for entry in entries]

@cache.memoize(timeout=3600)  # Cache for 1 hour
def get_monthly_sales_data():
    """Get monthly sales data for the last 12 months"""
    today = date.today()
    months_data = []
    
    for i in range(11, -1, -1):  # Last 12 months
        month_date = today.replace(day=1) - timedelta(days=i*30)
        month_start = date(month_date.year, month_date.month, 1)
        
        # Calculate next month start
        if month_date.month == 12:
            next_month_start = date(month_date.year + 1, 1, 1)
        else:
            next_month_start = date(month_date.year, month_date.month + 1, 1)
        
        # Get sales for this month
        monthly_sales = db.session.query(
            func.coalesce(func.sum(Invoice.total_amount), 0)
        ).filter(
            and_(Invoice.issue_date >= month_start,
                 Invoice.issue_date < next_month_start)
        ).scalar() or 0
        
        months_data.append({
            'month': month_start.strftime('%Y-%m'),
            'month_name': month_start.strftime('%B %Y'),
            'sales': float(monthly_sales)
        })
    
    return months_data

@cache.memoize(timeout=3600)  # Cache for 1 hour
def get_top_customers_data(limit=10):
    """Get top customers by total invoice amount"""
    top_customers = db.session.query(
        Customer.id,
        Customer.name,
        func.coalesce(func.sum(Invoice.total_amount), 0).label('total_amount'),
        func.count(Invoice.id).label('invoice_count')
    ).outerjoin(Invoice).filter(
        Customer.is_active == True
    ).group_by(
        Customer.id, Customer.name
    ).order_by(
        func.coalesce(func.sum(Invoice.total_amount), 0).desc()
    ).limit(limit).all()
    
    return [{
        'customer_id': customer.id,
        'customer_name': customer.name,
        'total_amount': float(customer.total_amount),
        'invoice_count': customer.invoice_count,
        'formatted_amount': format_currency(customer.total_amount)
    } for customer in top_customers]

@dashboard_bp.route('/quick-stats')
@employee_required
def quick_stats():
    """Quick stats widget"""
    today = date.today()
    
    # Today's stats
    today_invoices = Invoice.query.filter(
        Invoice.issue_date == today
    ).count()
    
    today_receipts = Receipt.query.filter(
        Receipt.receipt_date == today
    ).count()
    
    today_sales = db.session.query(
        func.coalesce(func.sum(Invoice.total_amount), 0)
    ).filter(
        Invoice.issue_date == today
    ).scalar() or 0
    
    # Pending invoices
    pending_invoices = Invoice.query.filter(
        Invoice.status == 'draft'
    ).count()
    
    return render_template('dashboard/quick_stats.html',
                         today_invoices=today_invoices,
                         today_receipts=today_receipts,
                         today_sales=float(today_sales),
                         pending_invoices=pending_invoices,
                         formatted_today_sales=format_currency(today_sales))
