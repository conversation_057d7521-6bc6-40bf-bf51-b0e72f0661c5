{% extends "base.html" %}

{% block title %}{{ title }} - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-{{ 'plus' if not customer else 'edit' }} me-3"></i>
                {{ title }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('customers.index') }}">العملاء</a></li>
                    {% if customer %}
                    <li class="breadcrumb-item"><a href="{{ url_for('customers.detail', customer_id=customer.id) }}">{{ customer.name }}</a></li>
                    {% endif %}
                    <li class="breadcrumb-item active">{{ title }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('customers.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<form method="POST" id="customerForm" enctype="multipart/form-data">
    {{ form.hidden_tag() }}
    
    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.name.label(class="form-label required") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.name_en.label(class="form-label") }}
                            {{ form.name_en(class="form-control" + (" is-invalid" if form.name_en.errors else "")) }}
                            {% if form.name_en.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name_en.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.customer_type.label(class="form-label required") }}
                            {{ form.customer_type(class="form-select" + (" is-invalid" if form.customer_type.errors else "")) }}
                            {% if form.customer_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.customer_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.tax_id.label(class="form-label") }}
                            {{ form.tax_id(class="form-control" + (" is-invalid" if form.tax_id.errors else "")) }}
                            {% if form.tax_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.tax_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">الرقم الضريبي أو السجل التجاري</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else "")) }}
                            {% if form.email.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.email.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                            {% if form.phone.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.phone.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.website.label(class="form-label") }}
                        {{ form.website(class="form-control" + (" is-invalid" if form.website.errors else "")) }}
                        {% if form.website.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.website.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        معلومات العنوان
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        {{ form.address.label(class="form-label") }}
                        {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else "")) }}
                        {% if form.address.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            {{ form.city.label(class="form-label") }}
                            {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else "")) }}
                            {% if form.city.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.city.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.state.label(class="form-label") }}
                            {{ form.state(class="form-control" + (" is-invalid" if form.state.errors else "")) }}
                            {% if form.state.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.state.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            {{ form.postal_code.label(class="form-label") }}
                            {{ form.postal_code(class="form-control" + (" is-invalid" if form.postal_code.errors else "")) }}
                            {% if form.postal_code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.postal_code.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.country.label(class="form-label") }}
                        {{ form.country(class="form-select" + (" is-invalid" if form.country.errors else "")) }}
                        {% if form.country.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.country.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Financial Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-dollar-sign me-2"></i>
                        المعلومات المالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.credit_limit.label(class="form-label") }}
                            {{ form.credit_limit(class="form-control" + (" is-invalid" if form.credit_limit.errors else "")) }}
                            {% if form.credit_limit.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.credit_limit.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">الحد الأقصى للائتمان</div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.payment_terms.label(class="form-label") }}
                            {{ form.payment_terms(class="form-control" + (" is-invalid" if form.payment_terms.errors else "")) }}
                            {% if form.payment_terms.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.payment_terms.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">شروط الدفع بالأيام</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.currency.label(class="form-label") }}
                            {{ form.currency(class="form-select" + (" is-invalid" if form.currency.errors else "")) }}
                            {% if form.currency.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.currency.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.account_id.label(class="form-label") }}
                            {{ form.account_id(class="form-select" + (" is-invalid" if form.account_id.errors else "")) }}
                            {% if form.account_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.account_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">الحساب المحاسبي المرتبط</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sticky-note me-2"></i>
                        معلومات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else "")) }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-check">
                        {{ form.is_active(class="form-check-input") }}
                        {{ form.is_active.label(class="form-check-label") }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Actions -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        الإجراءات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ
                        </button>
                        <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        {% if customer %}
                        <hr>
                        <a href="{{ url_for('customers.detail', customer_id=customer.id) }}" class="btn btn-outline-info">
                            <i class="fas fa-eye me-2"></i>
                            عرض التفاصيل
                        </a>
                        {% if customer.can_be_deleted() %}
                        <form method="POST" action="{{ url_for('customers.delete', customer_id=customer.id) }}" 
                              onsubmit="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-trash me-2"></i>
                                حذف العميل
                            </button>
                        </form>
                        {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Help -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        مساعدة
                    </h6>
                </div>
                <div class="card-body">
                    <h6>أنواع العملاء:</h6>
                    <ul class="list-unstyled small">
                        <li><span class="badge bg-success me-2">فرد</span> عميل فردي</li>
                        <li><span class="badge bg-primary me-2">شركة</span> عميل مؤسسي</li>
                    </ul>
                    
                    <h6 class="mt-3">نصائح:</h6>
                    <ul class="small">
                        <li>الرقم الضريبي مطلوب للشركات</li>
                        <li>حدد حد ائتمان مناسب</li>
                        <li>اربط العميل بحساب محاسبي</li>
                        <li>استخدم الملاحظات للمعلومات المهمة</li>
                    </ul>
                </div>
            </div>
            
            {% if customer %}
            <!-- Customer Info -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info me-2"></i>
                        معلومات العميل
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>تاريخ الإنشاء:</strong><br>{{ customer.created_at|datetime }}</p>
                    {% if customer.updated_at %}
                    <p><strong>آخر تحديث:</strong><br>{{ customer.updated_at|datetime }}</p>
                    {% endif %}
                    <p><strong>الرصيد الحالي:</strong><br>{{ customer.get_balance()|currency }}</p>
                    <p><strong>عدد الفواتير:</strong><br>{{ customer.get_invoices_count() }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-generate account based on customer type and name
    $('#customer_type, #name').on('change', function() {
        if ($('#name').val() && $('#customer_type').val() && !$('#account_id').val()) {
            // Suggest creating a new account
            const customerName = $('#name').val();
            const customerType = $('#customer_type').val();
            
            // You can implement auto-account creation logic here
        }
    });
    
    // Validate tax ID format
    $('#tax_id').on('input', function() {
        const taxId = $(this).val();
        const customerType = $('#customer_type').val();
        
        if (customerType === 'company' && taxId.length > 0 && taxId.length < 9) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').text('الرقم الضريبي للشركات يجب أن يكون 9 أرقام على الأقل');
        } else {
            $(this).removeClass('is-invalid');
        }
    });
    
    // Format phone number
    $('#phone').on('input', function() {
        let phone = $(this).val().replace(/\D/g, '');
        if (phone.length > 0) {
            if (phone.length <= 3) {
                phone = phone;
            } else if (phone.length <= 6) {
                phone = phone.slice(0, 3) + '-' + phone.slice(3);
            } else {
                phone = phone.slice(0, 3) + '-' + phone.slice(3, 6) + '-' + phone.slice(6, 10);
            }
            $(this).val(phone);
        }
    });
    
    // Form validation
    $('#customerForm').on('submit', function(e) {
        const name = $('#name').val();
        const customerType = $('#customer_type').val();
        
        if (!name || !customerType) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }
        
        // Validate email format
        const email = $('#email').val();
        if (email && !isValidEmail(email)) {
            e.preventDefault();
            alert('يرجى إدخال بريد إلكتروني صحيح');
            return false;
        }
    });
});

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
</script>
{% endblock %}
