{% extends "base.html" %}

{% block title %}{{ title }} - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-{{ 'plus' if not invoice else 'edit' }} me-3"></i>
                {{ title }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('invoices.index') }}">الفواتير</a></li>
                    {% if invoice %}
                    <li class="breadcrumb-item"><a href="{{ url_for('invoices.detail', invoice_id=invoice.id) }}">{{ invoice.invoice_number }}</a></li>
                    {% endif %}
                    <li class="breadcrumb-item active">{{ title }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('invoices.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<form method="POST" id="invoiceForm">
    {{ form.hidden_tag() }}
    
    <div class="row">
        <div class="col-lg-8">
            <!-- Invoice Header -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الفاتورة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.invoice_number.label(class="form-label required") }}
                            {{ form.invoice_number(class="form-control" + (" is-invalid" if form.invoice_number.errors else "")) }}
                            {% if form.invoice_number.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.invoice_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.reference_number.label(class="form-label") }}
                            {{ form.reference_number(class="form-control" + (" is-invalid" if form.reference_number.errors else "")) }}
                            {% if form.reference_number.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.reference_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.customer_id.label(class="form-label required") }}
                            <div class="input-group">
                                {{ form.customer_id(class="form-select" + (" is-invalid" if form.customer_id.errors else ""), id="customer-select") }}
                                <button type="button" class="btn btn-outline-secondary" onclick="openCustomerModal()">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            {% if form.customer_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.customer_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.invoice_date.label(class="form-label required") }}
                            {{ form.invoice_date(class="form-control" + (" is-invalid" if form.invoice_date.errors else "")) }}
                            {% if form.invoice_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.invoice_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.due_date.label(class="form-label") }}
                            {{ form.due_date(class="form-control" + (" is-invalid" if form.due_date.errors else "")) }}
                            {% if form.due_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.due_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.currency.label(class="form-label") }}
                            {{ form.currency(class="form-select" + (" is-invalid" if form.currency.errors else "")) }}
                            {% if form.currency.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.currency.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else "")) }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Invoice Lines -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        بنود الفاتورة
                    </h5>
                    <button type="button" class="btn btn-sm btn-primary" onclick="addInvoiceLine()">
                        <i class="fas fa-plus me-2"></i>
                        إضافة بند
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="invoice-lines-table">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 30%">الصنف/الخدمة</th>
                                    <th style="width: 15%">الكمية</th>
                                    <th style="width: 15%">السعر</th>
                                    <th style="width: 10%">الخصم %</th>
                                    <th style="width: 15%">الضريبة %</th>
                                    <th style="width: 15%">الإجمالي</th>
                                    <th style="width: 5%"></th>
                                </tr>
                            </thead>
                            <tbody id="invoice-lines">
                                <!-- Invoice lines will be added here dynamically -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Totals -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control", rows="3") }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>المجموع الفرعي:</strong></td>
                                    <td class="text-end"><span id="subtotal">0.00</span> ج.م</td>
                                </tr>
                                <tr>
                                    <td><strong>إجمالي الخصم:</strong></td>
                                    <td class="text-end"><span id="total-discount">0.00</span> ج.م</td>
                                </tr>
                                <tr>
                                    <td><strong>إجمالي الضريبة:</strong></td>
                                    <td class="text-end"><span id="total-tax">0.00</span> ج.م</td>
                                </tr>
                                <tr class="table-primary">
                                    <td><strong>الإجمالي النهائي:</strong></td>
                                    <td class="text-end"><strong><span id="total-amount">0.00</span> ج.م</strong></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Actions -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        الإجراءات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" name="action" value="save" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ كمسودة
                        </button>
                        <button type="submit" name="action" value="save_and_send" class="btn btn-success">
                            <i class="fas fa-paper-plane me-2"></i>
                            حفظ وإرسال
                        </button>
                        <a href="{{ url_for('invoices.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        {% if invoice %}
                        <hr>
                        <a href="{{ url_for('invoices.detail', invoice_id=invoice.id) }}" class="btn btn-outline-info">
                            <i class="fas fa-eye me-2"></i>
                            عرض التفاصيل
                        </a>
                        <a href="{{ url_for('invoices.pdf', invoice_id=invoice.id) }}" class="btn btn-outline-warning" target="_blank">
                            <i class="fas fa-file-pdf me-2"></i>
                            معاينة PDF
                        </a>
                        {% if invoice.can_be_deleted() %}
                        <form method="POST" action="{{ url_for('invoices.delete', invoice_id=invoice.id) }}" 
                              onsubmit="return confirm('هل أنت متأكد من حذف هذه الفاتورة؟')">
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-trash me-2"></i>
                                حذف الفاتورة
                            </button>
                        </form>
                        {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Customer Info -->
            <div class="card mb-3" id="customer-info-card" style="display: none;">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        معلومات العميل
                    </h6>
                </div>
                <div class="card-body" id="customer-info">
                    <!-- Customer info will be loaded here -->
                </div>
            </div>
            
            <!-- Help -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        مساعدة
                    </h6>
                </div>
                <div class="card-body">
                    <h6>نصائح:</h6>
                    <ul class="small">
                        <li>رقم الفاتورة يجب أن يكون فريد</li>
                        <li>اختر العميل من القائمة أو أضف عميل جديد</li>
                        <li>أضف بنود الفاتورة مع الكميات والأسعار</li>
                        <li>تحقق من الضرائب والخصومات</li>
                        <li>احفظ كمسودة للتعديل لاحقاً</li>
                        <li>احفظ وأرسل لإرسالها للعميل ومنظومة الضرائب</li>
                    </ul>
                    
                    <h6 class="mt-3">اختصارات لوحة المفاتيح:</h6>
                    <ul class="small">
                        <li><kbd>Ctrl + S</kbd> - حفظ</li>
                        <li><kbd>Ctrl + Enter</kbd> - حفظ وإرسال</li>
                        <li><kbd>Ctrl + N</kbd> - بند جديد</li>
                        <li><kbd>Esc</kbd> - إلغاء</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Invoice Line Template -->
<template id="invoice-line-template">
    <tr class="invoice-line">
        <td>
            <input type="text" class="form-control item-description" name="lines-{index}-description" 
                   placeholder="وصف الصنف أو الخدمة" required>
        </td>
        <td>
            <input type="number" class="form-control item-quantity" name="lines-{index}-quantity" 
                   value="1" min="0" step="0.01" required>
        </td>
        <td>
            <input type="number" class="form-control item-price" name="lines-{index}-unit_price" 
                   value="0" min="0" step="0.01" required>
        </td>
        <td>
            <input type="number" class="form-control item-discount" name="lines-{index}-discount_rate" 
                   value="0" min="0" max="100" step="0.01">
        </td>
        <td>
            <select class="form-select item-tax" name="lines-{index}-tax_rate">
                <option value="0">0%</option>
                <option value="14" selected>14%</option>
                <option value="10">10%</option>
                <option value="5">5%</option>
            </select>
        </td>
        <td>
            <input type="text" class="form-control item-total" readonly>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeInvoiceLine(this)">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    </tr>
</template>
{% endblock %}

{% block extra_js %}
<script>
let lineIndex = 0;

$(document).ready(function() {
    // Add initial line if creating new invoice
    {% if not invoice %}
    addInvoiceLine();
    {% else %}
    // Load existing lines
    {% for line in invoice.lines %}
    addInvoiceLine({{ loop.index0 }}, {
        description: '{{ line.description }}',
        quantity: {{ line.quantity }},
        unit_price: {{ line.unit_price }},
        discount_rate: {{ line.discount_rate or 0 }},
        tax_rate: {{ line.tax_rate or 14 }}
    });
    {% endfor %}
    {% endif %}
    
    // Customer selection change
    $('#customer-select').change(function() {
        const customerId = $(this).val();
        if (customerId) {
            loadCustomerInfo(customerId);
        } else {
            $('#customer-info-card').hide();
        }
    });
    
    // Auto-calculate due date based on customer payment terms
    $('#customer-select').change(function() {
        const customerId = $(this).val();
        const invoiceDate = $('#invoice_date').val();
        
        if (customerId && invoiceDate) {
            // This would fetch customer payment terms and calculate due date
            // Implementation depends on customer data structure
        }
    });
    
    // Keyboard shortcuts
    $(document).keydown(function(e) {
        if (e.ctrlKey) {
            switch(e.which) {
                case 83: // Ctrl+S
                    e.preventDefault();
                    $('button[name="action"][value="save"]').click();
                    break;
                case 13: // Ctrl+Enter
                    e.preventDefault();
                    $('button[name="action"][value="save_and_send"]').click();
                    break;
                case 78: // Ctrl+N
                    e.preventDefault();
                    addInvoiceLine();
                    break;
            }
        } else if (e.which === 27) { // Esc
            window.location.href = '{{ url_for("invoices.index") }}';
        }
    });
    
    // Form validation
    $('#invoiceForm').on('submit', function(e) {
        if (!validateForm()) {
            e.preventDefault();
            return false;
        }
    });
    
    // Load customer info if editing existing invoice
    {% if invoice and invoice.customer_id %}
    loadCustomerInfo('{{ invoice.customer_id }}');
    {% endif %}
});

function addInvoiceLine(index = null, data = null) {
    if (index === null) {
        index = lineIndex++;
    } else {
        lineIndex = Math.max(lineIndex, index + 1);
    }
    
    const template = document.getElementById('invoice-line-template');
    const clone = template.content.cloneNode(true);
    
    // Replace {index} placeholders
    clone.querySelectorAll('[name*="{index}"]').forEach(element => {
        element.name = element.name.replace('{index}', index);
    });
    
    // Fill data if provided
    if (data) {
        clone.querySelector('.item-description').value = data.description || '';
        clone.querySelector('.item-quantity').value = data.quantity || 1;
        clone.querySelector('.item-price').value = data.unit_price || 0;
        clone.querySelector('.item-discount').value = data.discount_rate || 0;
        clone.querySelector('.item-tax').value = data.tax_rate || 14;
    }
    
    document.getElementById('invoice-lines').appendChild(clone);
    
    // Attach event listeners to new line
    const newRow = document.getElementById('invoice-lines').lastElementChild;
    attachLineEventListeners(newRow);
    
    calculateTotals();
}

function removeInvoiceLine(button) {
    if (document.querySelectorAll('.invoice-line').length > 1) {
        button.closest('tr').remove();
        calculateTotals();
    } else {
        alert('يجب أن تحتوي الفاتورة على بند واحد على الأقل');
    }
}

function attachLineEventListeners(row) {
    const inputs = row.querySelectorAll('.item-quantity, .item-price, .item-discount, .item-tax');
    inputs.forEach(input => {
        input.addEventListener('input', calculateTotals);
        input.addEventListener('change', calculateTotals);
    });
}

function calculateTotals() {
    let subtotal = 0;
    let totalDiscount = 0;
    let totalTax = 0;
    
    document.querySelectorAll('.invoice-line').forEach(row => {
        const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(row.querySelector('.item-price').value) || 0;
        const discountRate = parseFloat(row.querySelector('.item-discount').value) || 0;
        const taxRate = parseFloat(row.querySelector('.item-tax').value) || 0;
        
        const lineSubtotal = quantity * price;
        const lineDiscount = lineSubtotal * (discountRate / 100);
        const lineAfterDiscount = lineSubtotal - lineDiscount;
        const lineTax = lineAfterDiscount * (taxRate / 100);
        const lineTotal = lineAfterDiscount + lineTax;
        
        row.querySelector('.item-total').value = lineTotal.toFixed(2);
        
        subtotal += lineSubtotal;
        totalDiscount += lineDiscount;
        totalTax += lineTax;
    });
    
    const totalAmount = subtotal - totalDiscount + totalTax;
    
    document.getElementById('subtotal').textContent = subtotal.toFixed(2);
    document.getElementById('total-discount').textContent = totalDiscount.toFixed(2);
    document.getElementById('total-tax').textContent = totalTax.toFixed(2);
    document.getElementById('total-amount').textContent = totalAmount.toFixed(2);
}

function loadCustomerInfo(customerId) {
    fetch(`/customers/${customerId}/info`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const customer = data.customer;
                const html = `
                    <p><strong>الاسم:</strong> ${customer.name}</p>
                    ${customer.tax_id ? `<p><strong>الرقم الضريبي:</strong> ${customer.tax_id}</p>` : ''}
                    ${customer.email ? `<p><strong>البريد:</strong> ${customer.email}</p>` : ''}
                    ${customer.phone ? `<p><strong>الهاتف:</strong> ${customer.phone}</p>` : ''}
                    <p><strong>الرصيد:</strong> ${customer.balance} ج.م</p>
                    ${customer.credit_limit ? `<p><strong>حد الائتمان:</strong> ${customer.credit_limit} ج.م</p>` : ''}
                `;
                document.getElementById('customer-info').innerHTML = html;
                document.getElementById('customer-info-card').style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error loading customer info:', error);
        });
}

function validateForm() {
    const customerSelect = document.getElementById('customer-select');
    if (!customerSelect.value) {
        alert('يرجى اختيار العميل');
        customerSelect.focus();
        return false;
    }
    
    const lines = document.querySelectorAll('.invoice-line');
    if (lines.length === 0) {
        alert('يجب إضافة بند واحد على الأقل');
        return false;
    }
    
    // Validate each line
    for (let line of lines) {
        const description = line.querySelector('.item-description').value.trim();
        const quantity = parseFloat(line.querySelector('.item-quantity').value);
        const price = parseFloat(line.querySelector('.item-price').value);
        
        if (!description) {
            alert('يرجى إدخال وصف لجميع البنود');
            line.querySelector('.item-description').focus();
            return false;
        }
        
        if (quantity <= 0) {
            alert('الكمية يجب أن تكون أكبر من صفر');
            line.querySelector('.item-quantity').focus();
            return false;
        }
        
        if (price < 0) {
            alert('السعر لا يمكن أن يكون سالب');
            line.querySelector('.item-price').focus();
            return false;
        }
    }
    
    return true;
}

function openCustomerModal() {
    // This would open a modal to create a new customer
    // For now, redirect to customer creation page
    window.open('{{ url_for("customers.new") }}', '_blank');
}
</script>
{% endblock %}
