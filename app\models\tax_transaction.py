"""
Tax Transaction model for Egyptian Tax Authority integration
"""

import uuid
from datetime import datetime
from app import db

class TaxTransaction(db.Model):
    __tablename__ = 'tax_transactions'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    invoice_id = db.<PERSON>umn(db.String(36), db.<PERSON><PERSON>('invoices.id'), nullable=False, index=True)
    transaction_type = db.Column(db.String(20), nullable=False, default='submit')
    request_data = db.Column(db.JSON)
    response_data = db.Column(db.JSON)
    tax_invoice_number = db.Column(db.String(50), index=True)
    status = db.Column(db.String(20), default='pending')
    error_message = db.Column(db.Text)
    sent_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    response_at = db.Column(db.DateTime)
    
    # Transaction types
    TRANSACTION_TYPES = {
        'submit': 'إرسال فاتورة',
        'cancel': 'إلغاء فاتورة',
        'query': 'استعلام عن فاتورة'
    }
    
    # Status choices
    STATUS_CHOICES = {
        'pending': 'في الانتظار',
        'sent': 'مرسلة',
        'success': 'نجحت',
        'failed': 'فشلت',
        'cancelled': 'ملغية'
    }
    
    def __init__(self, invoice_id, transaction_type='submit', request_data=None):
        self.invoice_id = invoice_id
        self.transaction_type = transaction_type
        self.request_data = request_data
    
    def get_transaction_type_display(self):
        """Get Arabic display for transaction type"""
        return self.TRANSACTION_TYPES.get(self.transaction_type, self.transaction_type)
    
    def get_status_display(self):
        """Get Arabic display for status"""
        return self.STATUS_CHOICES.get(self.status, self.status)
    
    def mark_as_sent(self):
        """Mark transaction as sent"""
        self.status = 'sent'
        self.sent_at = datetime.utcnow()
    
    def mark_as_success(self, response_data=None, tax_invoice_number=None):
        """Mark transaction as successful"""
        self.status = 'success'
        self.response_at = datetime.utcnow()
        if response_data:
            self.response_data = response_data
        if tax_invoice_number:
            self.tax_invoice_number = tax_invoice_number
        
        # Update invoice tax status
        if self.invoice:
            self.invoice.tax_status = 'accepted'
    
    def mark_as_failed(self, error_message=None, response_data=None):
        """Mark transaction as failed"""
        self.status = 'failed'
        self.response_at = datetime.utcnow()
        if error_message:
            self.error_message = error_message
        if response_data:
            self.response_data = response_data
        
        # Update invoice tax status
        if self.invoice:
            self.invoice.tax_status = 'rejected'
    
    def can_retry(self):
        """Check if transaction can be retried"""
        return self.status in ['failed', 'pending']
    
    def get_response_message(self):
        """Get response message from response data"""
        if self.response_data and isinstance(self.response_data, dict):
            return self.response_data.get('message', '')
        return self.error_message or ''
    
    @classmethod
    def create_for_invoice(cls, invoice_id, transaction_type='submit'):
        """Create tax transaction for invoice"""
        from app.models.invoice import Invoice
        
        invoice = Invoice.query.get(invoice_id)
        if not invoice:
            return None
        
        # Prepare request data
        request_data = {
            'invoice_number': invoice.invoice_number,
            'issue_date': invoice.issue_date.isoformat(),
            'customer_name': invoice.customer.name,
            'customer_tax_id': invoice.customer.tax_id,
            'total_amount': float(invoice.total_amount),
            'tax_amount': float(invoice.tax_amount),
            'lines': [line.to_dict() for line in invoice.lines]
        }
        
        transaction = cls(
            invoice_id=invoice_id,
            transaction_type=transaction_type,
            request_data=request_data
        )
        
        db.session.add(transaction)
        return transaction
    
    def to_dict(self):
        """Convert tax transaction to dictionary"""
        return {
            'id': self.id,
            'invoice_id': self.invoice_id,
            'invoice_number': self.invoice.invoice_number if self.invoice else None,
            'transaction_type': self.transaction_type,
            'transaction_type_display': self.get_transaction_type_display(),
            'status': self.status,
            'status_display': self.get_status_display(),
            'tax_invoice_number': self.tax_invoice_number,
            'error_message': self.error_message,
            'response_message': self.get_response_message(),
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'response_at': self.response_at.isoformat() if self.response_at else None,
            'can_retry': self.can_retry()
        }
    
    def __repr__(self):
        return f'<TaxTransaction {self.transaction_type}: {self.status}>'
