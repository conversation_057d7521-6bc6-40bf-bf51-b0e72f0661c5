"""
Admin utility functions
"""

from app import db
from app.models.user import User
from app.models.system_setting import SystemSetting
from app.models.account import Account

def create_admin_user(username='admin', email='<EMAIL>', password='admin123'):
    """Create default admin user"""
    try:
        # Check if admin already exists
        existing_admin = User.query.filter_by(username=username).first()
        if existing_admin:
            print(f"Admin user '{username}' already exists.")
            return existing_admin
        
        # Create admin user
        admin_user = User(
            username=username,
            email=email,
            password=password,
            role='admin'
        )
        
        db.session.add(admin_user)
        db.session.commit()
        
        print(f"Admin user '{username}' created successfully.")
        print(f"Email: {email}")
        print(f"Password: {password}")
        print("Please change the password after first login.")
        
        return admin_user
        
    except Exception as e:
        db.session.rollback()
        print(f"Error creating admin user: {str(e)}")
        return None

def initialize_system_data():
    """Initialize system with default data"""
    try:
        print("Initializing system data...")
        
        # Initialize system settings
        SystemSetting.initialize_default_settings()
        print("✓ System settings initialized")
        
        # Create default accounts if they don't exist
        create_default_accounts()
        print("✓ Default accounts created")
        
        # Create admin user
        create_admin_user()
        print("✓ Admin user created")
        
        db.session.commit()
        print("System initialization completed successfully!")
        
    except Exception as e:
        db.session.rollback()
        print(f"Error initializing system data: {str(e)}")

def create_default_accounts():
    """Create default chart of accounts"""
    default_accounts = [
        # Assets
        ('1000', 'الأصول', 'Assets', 'Asset', None),
        ('1100', 'الأصول المتداولة', 'Current Assets', 'Asset', '1000'),
        ('1110', 'النقدية', 'Cash', 'Asset', '1100'),
        ('1120', 'البنوك', 'Banks', 'Asset', '1100'),
        ('1130', 'العملاء', 'Accounts Receivable', 'Asset', '1100'),
        ('1140', 'المخزون', 'Inventory', 'Asset', '1100'),
        ('1200', 'الأصول الثابتة', 'Fixed Assets', 'Asset', '1000'),
        ('1210', 'الأراضي والمباني', 'Land and Buildings', 'Asset', '1200'),
        ('1220', 'المعدات والآلات', 'Equipment and Machinery', 'Asset', '1200'),
        
        # Liabilities
        ('2000', 'الخصوم', 'Liabilities', 'Liability', None),
        ('2100', 'الخصوم المتداولة', 'Current Liabilities', 'Liability', '2000'),
        ('2110', 'الموردين', 'Accounts Payable', 'Liability', '2100'),
        ('2120', 'الضرائب المستحقة', 'Taxes Payable', 'Liability', '2100'),
        ('2130', 'المرتبات المستحقة', 'Salaries Payable', 'Liability', '2100'),
        ('2200', 'الخصوم طويلة الأجل', 'Long-term Liabilities', 'Liability', '2000'),
        ('2210', 'القروض طويلة الأجل', 'Long-term Loans', 'Liability', '2200'),
        
        # Equity
        ('3000', 'رأس المال', 'Equity', 'Equity', None),
        ('3100', 'رأس المال المدفوع', 'Paid Capital', 'Equity', '3000'),
        ('3200', 'الأرباح المحتجزة', 'Retained Earnings', 'Equity', '3000'),
        
        # Income
        ('4000', 'الإيرادات', 'Revenue', 'Income', None),
        ('4100', 'إيرادات المبيعات', 'Sales Revenue', 'Income', '4000'),
        ('4200', 'إيرادات أخرى', 'Other Revenue', 'Income', '4000'),
        
        # Expenses
        ('5000', 'المصروفات', 'Expenses', 'Expense', None),
        ('5100', 'مصروفات التشغيل', 'Operating Expenses', 'Expense', '5000'),
        ('5110', 'المرتبات والأجور', 'Salaries and Wages', 'Expense', '5100'),
        ('5120', 'الإيجار', 'Rent', 'Expense', '5100'),
        ('5130', 'الكهرباء والمياه', 'Utilities', 'Expense', '5100'),
        ('5140', 'مصروفات الصيانة', 'Maintenance', 'Expense', '5100'),
        ('5200', 'المصروفات الإدارية', 'Administrative Expenses', 'Expense', '5000'),
        ('5300', 'المصروفات المالية', 'Financial Expenses', 'Expense', '5000'),
    ]
    
    # Create accounts with parent relationships
    created_accounts = {}
    
    for code, name, name_en, account_type, parent_code in default_accounts:
        # Check if account already exists
        existing = Account.query.filter_by(code=code).first()
        if existing:
            created_accounts[code] = existing
            continue
        
        # Find parent account
        parent_id = None
        if parent_code and parent_code in created_accounts:
            parent_id = created_accounts[parent_code].id
        
        # Create account
        account = Account(
            code=code,
            name=name,
            name_en=name_en,
            type=account_type,
            parent_id=parent_id
        )
        
        db.session.add(account)
        db.session.flush()  # Get the ID
        created_accounts[code] = account
    
    print(f"Created {len(default_accounts)} default accounts")

def reset_system_data():
    """Reset system data (use with caution!)"""
    try:
        print("WARNING: This will delete all system data!")
        confirm = input("Type 'CONFIRM' to proceed: ")
        
        if confirm != 'CONFIRM':
            print("Operation cancelled.")
            return
        
        print("Resetting system data...")
        
        # Delete all data (in correct order due to foreign keys)
        from app.models import (TaxTransaction, ReceiptInvoiceAllocation, 
                               Receipt, InvoiceLine, Invoice, JournalLine, 
                               JournalEntry, Customer, Vendor, Account, 
                               SystemSetting, User)
        
        TaxTransaction.query.delete()
        ReceiptInvoiceAllocation.query.delete()
        Receipt.query.delete()
        InvoiceLine.query.delete()
        Invoice.query.delete()
        JournalLine.query.delete()
        JournalEntry.query.delete()
        Customer.query.delete()
        Vendor.query.delete()
        Account.query.delete()
        SystemSetting.query.delete()
        User.query.delete()
        
        db.session.commit()
        print("✓ All data deleted")
        
        # Reinitialize
        initialize_system_data()
        
    except Exception as e:
        db.session.rollback()
        print(f"Error resetting system data: {str(e)}")

def backup_database():
    """Create database backup"""
    import subprocess
    import os
    from datetime import datetime
    
    try:
        # Get database URL from environment
        db_url = os.getenv('DATABASE_URL')
        if not db_url:
            print("DATABASE_URL not found in environment")
            return
        
        # Generate backup filename
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"backup_systemtax_{timestamp}.sql"
        
        # Create backup using pg_dump
        cmd = f"pg_dump {db_url} > {backup_file}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"Database backup created: {backup_file}")
        else:
            print(f"Backup failed: {result.stderr}")
            
    except Exception as e:
        print(f"Error creating backup: {str(e)}")

if __name__ == "__main__":
    # This allows running the script directly for admin tasks
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "init":
            initialize_system_data()
        elif command == "create-admin":
            create_admin_user()
        elif command == "reset":
            reset_system_data()
        elif command == "backup":
            backup_database()
        else:
            print("Available commands: init, create-admin, reset, backup")
    else:
        print("Usage: python admin.py <command>")
        print("Available commands: init, create-admin, reset, backup")
