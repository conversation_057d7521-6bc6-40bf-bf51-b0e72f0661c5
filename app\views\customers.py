"""
Customer views
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required
from app import db
from app.models.customer import Customer
from app.forms.customer import CustomerForm, CustomerSearchForm
from app.utils.decorators import employee_required
from app.utils.helpers import paginate_query, flash_errors

customers_bp = Blueprint('customers', __name__)

@customers_bp.route('/')
@employee_required
def index():
    """List all customers"""
    form = CustomerSearchForm()
    page = request.args.get('page', 1, type=int)
    
    # Build query
    query = Customer.query.filter_by(is_active=True)
    
    # Apply search filter
    if request.args.get('search'):
        search_term = request.args.get('search')
        customers_found = Customer.search(search_term)
        customer_ids = [c.id for c in customers_found]
        query = query.filter(Customer.id.in_(customer_ids))
        form.search.data = search_term
    
    # Order by name
    query = query.order_by(Customer.name)
    
    # Paginate
    customers = paginate_query(query, page)
    
    return render_template('customers/index.html', customers=customers, form=form)

@customers_bp.route('/new', methods=['GET', 'POST'])
@employee_required
def new():
    """Create new customer"""
    form = CustomerForm()
    
    if form.validate_on_submit():
        customer = Customer(
            name=form.name.data,
            name_en=form.name_en.data,
            tax_id=form.tax_id.data,
            address=form.address.data,
            address_en=form.address_en.data,
            email=form.email.data,
            phone=form.phone.data,
            mobile=form.mobile.data
        )
        customer.is_active = form.is_active.data
        
        db.session.add(customer)
        db.session.commit()
        
        flash(f'تم إنشاء العميل {customer.name} بنجاح.', 'success')
        return redirect(url_for('customers.detail', customer_id=customer.id))
    
    flash_errors(form)
    return render_template('customers/form.html', form=form, title='إضافة عميل جديد')

@customers_bp.route('/<customer_id>')
@employee_required
def detail(customer_id):
    """View customer details"""
    customer = Customer.query.get_or_404(customer_id)
    
    # Get customer statistics
    total_invoices = customer.get_total_invoices_amount()
    total_receipts = customer.get_total_receipts_amount()
    outstanding_balance = customer.get_outstanding_balance()
    
    # Get recent invoices and receipts
    recent_invoices = customer.get_recent_invoices(10)
    recent_receipts = customer.get_recent_receipts(10)
    
    return render_template('customers/detail.html',
                         customer=customer,
                         total_invoices=total_invoices,
                         total_receipts=total_receipts,
                         outstanding_balance=outstanding_balance,
                         recent_invoices=recent_invoices,
                         recent_receipts=recent_receipts)

@customers_bp.route('/<customer_id>/edit', methods=['GET', 'POST'])
@employee_required
def edit(customer_id):
    """Edit customer"""
    customer = Customer.query.get_or_404(customer_id)
    form = CustomerForm()
    
    if form.validate_on_submit():
        customer.name = form.name.data
        customer.name_en = form.name_en.data
        customer.tax_id = form.tax_id.data
        customer.address = form.address.data
        customer.address_en = form.address_en.data
        customer.email = form.email.data
        customer.phone = form.phone.data
        customer.mobile = form.mobile.data
        customer.is_active = form.is_active.data
        
        db.session.commit()
        
        flash(f'تم تحديث بيانات العميل {customer.name} بنجاح.', 'success')
        return redirect(url_for('customers.detail', customer_id=customer.id))
    
    elif request.method == 'GET':
        form.name.data = customer.name
        form.name_en.data = customer.name_en
        form.tax_id.data = customer.tax_id
        form.address.data = customer.address
        form.address_en.data = customer.address_en
        form.email.data = customer.email
        form.phone.data = customer.phone
        form.mobile.data = customer.mobile
        form.is_active.data = customer.is_active
    
    flash_errors(form)
    return render_template('customers/form.html', form=form, customer=customer, title='تعديل بيانات العميل')

@customers_bp.route('/<customer_id>/delete', methods=['POST'])
@employee_required
def delete(customer_id):
    """Delete customer"""
    customer = Customer.query.get_or_404(customer_id)
    
    # Check if customer has invoices or receipts
    if customer.invoices.count() > 0 or customer.receipts.count() > 0:
        flash('لا يمكن حذف هذا العميل لأنه مرتبط بفواتير أو إيصالات.', 'error')
        return redirect(url_for('customers.detail', customer_id=customer.id))
    
    customer_name = customer.name
    db.session.delete(customer)
    db.session.commit()
    
    flash(f'تم حذف العميل {customer_name} بنجاح.', 'success')
    return redirect(url_for('customers.index'))

@customers_bp.route('/<customer_id>/invoices')
@employee_required
def invoices(customer_id):
    """View customer invoices"""
    customer = Customer.query.get_or_404(customer_id)
    page = request.args.get('page', 1, type=int)
    
    # Get customer invoices
    query = customer.invoices.order_by(customer.invoices.property.mapper.class_.issue_date.desc())
    invoices = paginate_query(query, page)
    
    return render_template('customers/invoices.html', customer=customer, invoices=invoices)

@customers_bp.route('/<customer_id>/receipts')
@employee_required
def receipts(customer_id):
    """View customer receipts"""
    customer = Customer.query.get_or_404(customer_id)
    page = request.args.get('page', 1, type=int)
    
    # Get customer receipts
    query = customer.receipts.order_by(customer.receipts.property.mapper.class_.receipt_date.desc())
    receipts = paginate_query(query, page)
    
    return render_template('customers/receipts.html', customer=customer, receipts=receipts)

@customers_bp.route('/<customer_id>/statement')
@employee_required
def statement(customer_id):
    """Generate customer statement"""
    customer = Customer.query.get_or_404(customer_id)
    
    from_date = request.args.get('from_date')
    to_date = request.args.get('to_date')
    
    # Parse dates
    if from_date:
        from datetime import datetime
        try:
            from_date = datetime.strptime(from_date, '%Y-%m-%d').date()
        except:
            from_date = None
    
    if to_date:
        from datetime import datetime
        try:
            to_date = datetime.strptime(to_date, '%Y-%m-%d').date()
        except:
            to_date = None
    
    # Get transactions (invoices and receipts)
    transactions = []
    
    # Add invoices
    invoices_query = customer.invoices
    if from_date:
        invoices_query = invoices_query.filter(
            customer.invoices.property.mapper.class_.issue_date >= from_date
        )
    if to_date:
        invoices_query = invoices_query.filter(
            customer.invoices.property.mapper.class_.issue_date <= to_date
        )
    
    for invoice in invoices_query.all():
        transactions.append({
            'date': invoice.issue_date,
            'type': 'invoice',
            'reference': invoice.invoice_number,
            'description': f'فاتورة رقم {invoice.invoice_number}',
            'debit': float(invoice.total_amount),
            'credit': 0,
            'object': invoice
        })
    
    # Add receipts
    receipts_query = customer.receipts
    if from_date:
        receipts_query = receipts_query.filter(
            customer.receipts.property.mapper.class_.receipt_date >= from_date
        )
    if to_date:
        receipts_query = receipts_query.filter(
            customer.receipts.property.mapper.class_.receipt_date <= to_date
        )
    
    for receipt in receipts_query.all():
        transactions.append({
            'date': receipt.receipt_date,
            'type': 'receipt',
            'reference': receipt.receipt_number,
            'description': f'إيصال رقم {receipt.receipt_number}',
            'debit': 0,
            'credit': float(receipt.amount_received),
            'object': receipt
        })
    
    # Sort by date
    transactions.sort(key=lambda x: x['date'])
    
    # Calculate running balance
    running_balance = 0
    for transaction in transactions:
        running_balance += transaction['debit'] - transaction['credit']
        transaction['balance'] = running_balance
    
    return render_template('customers/statement.html',
                         customer=customer,
                         transactions=transactions,
                         from_date=from_date,
                         to_date=to_date,
                         final_balance=running_balance)

@customers_bp.route('/api/search')
@employee_required
def api_search():
    """API endpoint for customer search"""
    term = request.args.get('term', '')
    
    if not term:
        return jsonify([])
    
    customers = Customer.search(term)[:20]  # Limit to 20 results
    
    return jsonify([{
        'id': customer.id,
        'name': customer.name,
        'tax_id': customer.tax_id,
        'phone': customer.phone,
        'email': customer.email,
        'outstanding_balance': customer.get_outstanding_balance()
    } for customer in customers])

@customers_bp.route('/api/outstanding')
@employee_required
def api_outstanding():
    """API endpoint for customers with outstanding balances"""
    customers = Customer.query.filter_by(is_active=True).all()
    
    outstanding_customers = []
    for customer in customers:
        balance = customer.get_outstanding_balance()
        if balance > 0:
            outstanding_customers.append({
                'id': customer.id,
                'name': customer.name,
                'outstanding_balance': balance,
                'formatted_balance': f"{balance:,.2f} ج.م"
            })
    
    # Sort by outstanding balance (highest first)
    outstanding_customers.sort(key=lambda x: x['outstanding_balance'], reverse=True)
    
    return jsonify(outstanding_customers)

@customers_bp.route('/export')
@employee_required
def export():
    """Export customers to CSV"""
    import csv
    from io import StringIO
    from flask import make_response
    
    # Get all active customers
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
    
    # Create CSV
    output = StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow([
        'الاسم', 'الاسم بالإنجليزية', 'الرقم الضريبي', 'العنوان', 
        'البريد الإلكتروني', 'الهاتف', 'الموبايل', 'إجمالي الفواتير', 
        'إجمالي الإيصالات', 'الرصيد المستحق'
    ])
    
    # Write data
    for customer in customers:
        writer.writerow([
            customer.name,
            customer.name_en or '',
            customer.tax_id or '',
            customer.address or '',
            customer.email or '',
            customer.phone or '',
            customer.mobile or '',
            customer.get_total_invoices_amount(),
            customer.get_total_receipts_amount(),
            customer.get_outstanding_balance()
        ])
    
    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv; charset=utf-8'
    response.headers['Content-Disposition'] = 'attachment; filename=customers.csv'
    
    return response
