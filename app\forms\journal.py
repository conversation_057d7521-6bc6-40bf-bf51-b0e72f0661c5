"""
Journal Entry forms
"""

from flask_wtf import <PERSON>laskForm
from wtforms import StringField, TextAreaField, DateField, SelectField, DecimalField, FieldList, FormField, SubmitField
from wtforms.validators import DataRequired, Length, NumberRange, ValidationError
from datetime import date
from decimal import Decimal
from app.models.account import Account

class JournalLineForm(FlaskForm):
    account_id = SelectField('الحساب', coerce=str, validators=[DataRequired()])
    description = TextAreaField('الوصف', validators=[Length(max=500)])
    debit_amount = DecimalField('مدين', places=2, default=0)
    credit_amount = DecimalField('دائن', places=2, default=0)
    
    def __init__(self, *args, **kwargs):
        super(JournalLineForm, self).__init__(*args, **kwargs)
        
        # Populate account choices with leaf accounts only
        self.account_id.choices = [('', 'اختر الحساب')]
        accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
        
        for account in accounts:
            if account.can_post_transactions():
                self.account_id.choices.append((
                    account.id, 
                    f"{account.code} - {account.name}"
                ))
    
    def validate(self, extra_validators=None):
        if not super(JournalLineForm, self).validate(extra_validators):
            return False
        
        # Ensure either debit or credit is entered, but not both
        debit = self.debit_amount.data or 0
        credit = self.credit_amount.data or 0
        
        if debit == 0 and credit == 0:
            self.debit_amount.errors.append('يجب إدخال مبلغ في المدين أو الدائن')
            return False
        
        if debit > 0 and credit > 0:
            self.debit_amount.errors.append('لا يمكن إدخال مبلغ في المدين والدائن معاً')
            return False
        
        return True

class JournalEntryForm(FlaskForm):
    entry_date = DateField('تاريخ القيد', validators=[DataRequired()], default=date.today)
    reference_number = StringField('رقم المرجع', validators=[Length(max=50)])
    description = TextAreaField('الوصف', validators=[Length(max=1000)])
    description_en = TextAreaField('الوصف بالإنجليزية', validators=[Length(max=1000)])
    
    # Dynamic lines will be added via JavaScript
    submit = SubmitField('حفظ القيد')
    
    def validate(self, extra_validators=None):
        if not super(JournalEntryForm, self).validate(extra_validators):
            return False
        
        return True

class SimpleJournalEntryForm(FlaskForm):
    """Simplified form for two-line journal entries"""
    entry_date = DateField('تاريخ القيد', validators=[DataRequired()], default=date.today)
    description = TextAreaField('الوصف', validators=[DataRequired(), Length(max=1000)])
    debit_account_id = SelectField('الحساب المدين', coerce=str, validators=[DataRequired()])
    credit_account_id = SelectField('الحساب الدائن', coerce=str, validators=[DataRequired()])
    amount = DecimalField('المبلغ', validators=[
        DataRequired(), 
        NumberRange(min=0.01, message='المبلغ يجب أن يكون أكبر من صفر')
    ], places=2)
    submit = SubmitField('حفظ القيد')
    
    def __init__(self, *args, **kwargs):
        super(SimpleJournalEntryForm, self).__init__(*args, **kwargs)
        
        # Populate account choices with leaf accounts only
        choices = [('', 'اختر الحساب')]
        accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
        
        for account in accounts:
            if account.can_post_transactions():
                choices.append((
                    account.id, 
                    f"{account.code} - {account.name}"
                ))
        
        self.debit_account_id.choices = choices
        self.credit_account_id.choices = choices
    
    def validate_credit_account_id(self, credit_account_id):
        if credit_account_id.data == self.debit_account_id.data:
            raise ValidationError('الحساب المدين والدائن لا يمكن أن يكونا نفس الحساب')

class JournalSearchForm(FlaskForm):
    search = StringField('البحث', validators=[Length(max=100)])
    date_from = DateField('من تاريخ')
    date_to = DateField('إلى تاريخ')
    account_id = SelectField('الحساب', coerce=str)
    submit = SubmitField('بحث')
    
    def __init__(self, *args, **kwargs):
        super(JournalSearchForm, self).__init__(*args, **kwargs)
        
        # Populate account choices
        self.account_id.choices = [('', 'جميع الحسابات')]
        accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
        
        for account in accounts:
            self.account_id.choices.append((
                account.id, 
                f"{account.code} - {account.name}"
            ))
