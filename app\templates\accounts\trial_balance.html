{% extends "base.html" %}

{% block title %}ميزان المراجعة - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-balance-scale me-3"></i>
                ميزان المراجعة
            </h1>
            <p class="mb-0 mt-2">
                {% if as_of_date %}
                    كما في {{ as_of_date|date }}
                {% else %}
                    كما في {{ moment().format('YYYY-MM-DD')|date }}
                {% endif %}
            </p>
        </div>
        <div>
            <button type="button" class="btn btn-outline-light" onclick="window.print()">
                <i class="fas fa-print me-2"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-outline-light" onclick="exportToExcel()">
                <i class="fas fa-file-excel me-2"></i>
                تصدير Excel
            </button>
        </div>
    </div>
</div>

<!-- Date Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-4">
                <label for="as_of_date" class="form-label">كما في تاريخ:</label>
                <input type="date" class="form-control" id="as_of_date" name="as_of_date" 
                       value="{{ as_of_date.isoformat() if as_of_date else '' }}">
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>
                    عرض
                </button>
            </div>
            <div class="col-md-2">
                <a href="{{ url_for('accounts.trial_balance') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-refresh me-2"></i>
                    اليوم
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Trial Balance Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            ميزان المراجعة
            {% if as_of_date %}
                - {{ as_of_date|date }}
            {% endif %}
        </h5>
    </div>
    <div class="card-body">
        {% if trial_balance_data %}
        <div class="table-responsive">
            <table class="table table-bordered" id="trialBalanceTable">
                <thead class="table-light">
                    <tr>
                        <th rowspan="2" class="text-center align-middle">رمز الحساب</th>
                        <th rowspan="2" class="text-center align-middle">اسم الحساب</th>
                        <th rowspan="2" class="text-center align-middle">نوع الحساب</th>
                        <th colspan="2" class="text-center">الأرصدة</th>
                    </tr>
                    <tr>
                        <th class="text-center text-success">مدين</th>
                        <th class="text-center text-danger">دائن</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in trial_balance_data %}
                    <tr>
                        <td class="text-center">
                            <strong class="text-primary">{{ item.account.code }}</strong>
                        </td>
                        <td>
                            <a href="{{ url_for('accounts.detail', account_id=item.account.id) }}" 
                               class="text-decoration-none">
                                {{ item.account.name }}
                            </a>
                            {% if item.account.name_en %}
                                <br><small class="text-muted">{{ item.account.name_en }}</small>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <span class="badge bg-{{ 'success' if item.account.type == 'Asset' else 'danger' if item.account.type == 'Liability' else 'primary' if item.account.type == 'Equity' else 'info' if item.account.type == 'Income' else 'warning' }}">
                                {{ item.account.get_type_display() }}
                            </span>
                        </td>
                        <td class="text-end">
                            {% if item.debit_balance > 0 %}
                                <strong class="text-success">{{ item.debit_balance|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-end">
                            {% if item.credit_balance > 0 %}
                                <strong class="text-danger">{{ item.credit_balance|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <th colspan="3" class="text-center">الإجمالي</th>
                        <th class="text-end">
                            <strong class="text-success">{{ total_debits|currency }}</strong>
                        </th>
                        <th class="text-end">
                            <strong class="text-danger">{{ total_credits|currency }}</strong>
                        </th>
                    </tr>
                    <tr>
                        <th colspan="3" class="text-center">الفرق</th>
                        <th colspan="2" class="text-center">
                            {% set difference = total_debits - total_credits %}
                            {% if difference == 0 %}
                                <span class="badge bg-success fs-6">متوازن ✓</span>
                            {% else %}
                                <span class="badge bg-danger fs-6">غير متوازن ({{ difference|currency }})</span>
                            {% endif %}
                        </th>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- Summary Cards -->
        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card text-center border-success">
                    <div class="card-body">
                        <h5 class="card-title text-success">إجمالي المدين</h5>
                        <h3 class="text-success">{{ total_debits|currency }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-danger">
                    <div class="card-body">
                        <h5 class="card-title text-danger">إجمالي الدائن</h5>
                        <h3 class="text-danger">{{ total_credits|currency }}</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-{{ 'success' if total_debits == total_credits else 'danger' }}">
                    <div class="card-body">
                        <h5 class="card-title">الفرق</h5>
                        <h3 class="text-{{ 'success' if total_debits == total_credits else 'danger' }}">
                            {{ (total_debits - total_credits)|currency }}
                        </h3>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-info">
                    <div class="card-body">
                        <h5 class="card-title text-info">عدد الحسابات</h5>
                        <h3 class="text-info">{{ trial_balance_data|length }}</h3>
                    </div>
                </div>
            </div>
        </div>

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-balance-scale fa-3x text-muted mb-3"></i>
            <h5>لا توجد بيانات</h5>
            <p class="text-muted">لا توجد حسابات بأرصدة في التاريخ المحدد.</p>
            <a href="{{ url_for('journal.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة قيد محاسبي
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Account Type Breakdown -->
{% if trial_balance_data %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع الأرصدة المدينة
                </h6>
            </div>
            <div class="card-body">
                <canvas id="debitChart" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    توزيع الأرصدة الدائنة
                </h6>
            </div>
            <div class="card-body">
                <canvas id="creditChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .page-header .btn,
    .card:first-child,
    .row:last-child {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .badge {
        color: black !important;
        background-color: transparent !important;
        border: 1px solid black !important;
    }
}

.table th {
    background-color: #f8f9fa !important;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6 !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Export to Excel function
function exportToExcel() {
    const table = document.getElementById('trialBalanceTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "ميزان المراجعة"});
    const filename = `trial_balance_${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, filename);
}

// Load XLSX library for Excel export
const script = document.createElement('script');
script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
document.head.appendChild(script);

{% if trial_balance_data %}
// Prepare chart data
const accountTypes = {};
const debitData = {};
const creditData = {};

{% for item in trial_balance_data %}
const accountType = '{{ item.account.get_type_display() }}';
const debitBalance = {{ item.debit_balance }};
const creditBalance = {{ item.credit_balance }};

if (debitBalance > 0) {
    debitData[accountType] = (debitData[accountType] || 0) + debitBalance;
}
if (creditBalance > 0) {
    creditData[accountType] = (creditData[accountType] || 0) + creditBalance;
}
{% endfor %}

// Debit Chart
const debitCtx = document.getElementById('debitChart').getContext('2d');
new Chart(debitCtx, {
    type: 'doughnut',
    data: {
        labels: Object.keys(debitData),
        datasets: [{
            data: Object.values(debitData),
            backgroundColor: [
                '#28a745', // Assets - Green
                '#dc3545', // Liabilities - Red  
                '#007bff', // Equity - Blue
                '#17a2b8', // Income - Cyan
                '#ffc107'  // Expenses - Yellow
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Credit Chart
const creditCtx = document.getElementById('creditChart').getContext('2d');
new Chart(creditCtx, {
    type: 'doughnut',
    data: {
        labels: Object.keys(creditData),
        datasets: [{
            data: Object.values(creditData),
            backgroundColor: [
                '#28a745', // Assets - Green
                '#dc3545', // Liabilities - Red
                '#007bff', // Equity - Blue
                '#17a2b8', // Income - Cyan
                '#ffc107'  // Expenses - Yellow
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}

// Set default date to today
document.addEventListener('DOMContentLoaded', function() {
    const dateInput = document.getElementById('as_of_date');
    if (!dateInput.value) {
        dateInput.value = new Date().toISOString().split('T')[0];
    }
});
</script>
{% endblock %}
