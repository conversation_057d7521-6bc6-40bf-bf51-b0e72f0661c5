"""
Utils package for SystemTax application
"""

from .decorators import (login_required_with_role, admin_required,
                        accountant_required, employee_required, permission_required)
from .helpers import (generate_uuid, format_currency, format_date, format_datetime,
                     parse_date, parse_decimal, allowed_file, save_uploaded_file,
                     get_file_url, paginate_query, flash_errors, get_current_user_id,
                     convert_to_arabic_numerals, convert_to_english_numerals,
                     truncate_text, get_pagination_info)
from .pdf_generator import generate_invoice_pdf, generate_receipt_pdf
from .tax_api import (submit_invoice_to_tax_authority, query_tax_invoice_status,
                     cancel_tax_invoice)
from .admin import (create_admin_user, initialize_system_data, create_default_accounts,
                   reset_system_data, backup_database)

__all__ = [
    'login_required_with_role', 'admin_required', 'accountant_required',
    'employee_required', 'permission_required',
    'generate_uuid', 'format_currency', 'format_date', 'format_datetime',
    'parse_date', 'parse_decimal', 'allowed_file', 'save_uploaded_file',
    'get_file_url', 'paginate_query', 'flash_errors', 'get_current_user_id',
    'convert_to_arabic_numerals', 'convert_to_english_numerals',
    'truncate_text', 'get_pagination_info',
    'generate_invoice_pdf', 'generate_receipt_pdf',
    'submit_invoice_to_tax_authority', 'query_tax_invoice_status',
    'cancel_tax_invoice',
    'create_admin_user', 'initialize_system_data', 'create_default_accounts',
    'reset_system_data', 'backup_database'
]
