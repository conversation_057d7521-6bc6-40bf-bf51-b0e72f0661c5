{% extends "base.html" %}

{% block title %}{{ account.name }} - دليل الحسابات - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-{{ 'coins' if account.type == 'Asset' else 'credit-card' if account.type == 'Liability' else 'university' if account.type == 'Equity' else 'chart-line' if account.type == 'Income' else 'shopping-cart' }} me-3"></i>
                {{ account.name }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('accounts.index') }}">دليل الحسابات</a></li>
                    {% if account.parent %}
                    <li class="breadcrumb-item">
                        <a href="{{ url_for('accounts.detail', account_id=account.parent.id) }}">
                            {{ account.parent.name }}
                        </a>
                    </li>
                    {% endif %}
                    <li class="breadcrumb-item active">{{ account.name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('accounts.edit', account_id=account.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
            <a href="{{ url_for('accounts.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Account Information -->
    <div class="col-lg-8">
        <!-- Basic Info Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رمز الحساب:</strong></td>
                                <td><span class="badge bg-primary fs-6">{{ account.code }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>اسم الحساب:</strong></td>
                                <td>{{ account.name }}</td>
                            </tr>
                            {% if account.name_en %}
                            <tr>
                                <td><strong>الاسم بالإنجليزية:</strong></td>
                                <td>{{ account.name_en }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>نوع الحساب:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if account.type == 'Asset' else 'danger' if account.type == 'Liability' else 'primary' if account.type == 'Equity' else 'info' if account.type == 'Income' else 'warning' }}">
                                        {{ account.get_type_display() }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الحساب الأب:</strong></td>
                                <td>
                                    {% if account.parent %}
                                        <a href="{{ url_for('accounts.detail', account_id=account.parent.id) }}" class="text-decoration-none">
                                            {{ account.parent.code }} - {{ account.parent.name }}
                                        </a>
                                    {% else %}
                                        <span class="text-muted">حساب رئيسي</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if account.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-secondary">غير نشط</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ account.created_at|datetime }}</td>
                            </tr>
                            {% if account.updated_at %}
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ account.updated_at|datetime }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Balance Card -->
        {% if account.is_leaf_account() %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    رصيد الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h3 class="text-primary">{{ balance|currency }}</h3>
                            <p class="mb-0">الرصيد الحالي</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h3 class="text-success">{{ account.get_total_debits()|currency }}</h3>
                            <p class="mb-0">إجمالي المدين</p>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="border rounded p-3">
                            <h3 class="text-danger">{{ account.get_total_credits()|currency }}</h3>
                            <p class="mb-0">إجمالي الدائن</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <a href="{{ url_for('accounts.transactions', account_id=account.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>
                        عرض الحركات
                    </a>
                    <a href="{{ url_for('journal.ledger', account_id=account.id) }}" class="btn btn-outline-info">
                        <i class="fas fa-book me-2"></i>
                        دفتر الأستاذ
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Child Accounts -->
        {% if children %}
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sitemap me-2"></i>
                    الحسابات الفرعية
                </h5>
                <span class="badge bg-primary">{{ children|length }} حساب</span>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رمز الحساب</th>
                                <th>اسم الحساب</th>
                                <th>النوع</th>
                                <th>الرصيد</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for child in children %}
                            <tr>
                                <td><strong class="text-primary">{{ child.code }}</strong></td>
                                <td>
                                    <a href="{{ url_for('accounts.detail', account_id=child.id) }}" class="text-decoration-none">
                                        {{ child.name }}
                                    </a>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if child.type == 'Asset' else 'danger' if child.type == 'Liability' else 'primary' if child.type == 'Equity' else 'info' if child.type == 'Income' else 'warning' }}">
                                        {{ child.get_type_display() }}
                                    </span>
                                </td>
                                <td>
                                    {% if child.is_leaf_account() %}
                                        <span class="fw-bold">{{ child.get_balance()|currency }}</span>
                                    {% else %}
                                        <span class="text-muted">حساب تجميعي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('accounts.detail', account_id=child.id) }}" 
                                           class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('accounts.edit', account_id=child.id) }}" 
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Recent Transactions -->
        {% if recent_transactions %}
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر الحركات
                </h5>
                <a href="{{ url_for('accounts.transactions', account_id=account.id) }}" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>التاريخ</th>
                                <th>البيان</th>
                                <th>مدين</th>
                                <th>دائن</th>
                                <th>المرجع</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for transaction in recent_transactions %}
                            <tr>
                                <td>{{ transaction.journal_entry.entry_date|date }}</td>
                                <td>{{ transaction.description or transaction.journal_entry.description }}</td>
                                <td>
                                    {% if transaction.dc == 'D' %}
                                        <span class="text-success fw-bold">{{ transaction.amount|currency }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if transaction.dc == 'C' %}
                                        <span class="text-danger fw-bold">{{ transaction.amount|currency }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('journal.detail', entry_id=transaction.journal_entry.id) }}" 
                                       class="text-decoration-none">
                                        {{ transaction.journal_entry.reference_number }}
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('accounts.edit', account_id=account.id) }}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الحساب
                    </a>
                    
                    {% if account.is_leaf_account() %}
                    <a href="{{ url_for('journal.new') }}?account_id={{ account.id }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة قيد
                    </a>
                    {% endif %}
                    
                    <a href="{{ url_for('accounts.new') }}?parent_id={{ account.id }}" class="btn btn-outline-success">
                        <i class="fas fa-plus me-2"></i>
                        حساب فرعي
                    </a>
                    
                    {% if account.can_be_deleted() %}
                    <form method="POST" action="{{ url_for('accounts.delete', account_id=account.id) }}" 
                          onsubmit="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash me-2"></i>
                            حذف الحساب
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Account Path -->
        {% if account.get_path() %}
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-route me-2"></i>
                    مسار الحساب
                </h6>
            </div>
            <div class="card-body">
                {% for path_account in account.get_path() %}
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-{{ 'chevron-left' if not loop.last else 'circle' }} me-2 text-muted"></i>
                        {% if path_account.id != account.id %}
                        <a href="{{ url_for('accounts.detail', account_id=path_account.id) }}" class="text-decoration-none">
                            {{ path_account.code }} - {{ path_account.name }}
                        </a>
                        {% else %}
                        <strong>{{ path_account.code }} - {{ path_account.name }}</strong>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Statistics -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    إحصائيات
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ children|length }}</h4>
                        <small>حساب فرعي</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">{{ recent_transactions|length if recent_transactions else 0 }}</h4>
                        <small>حركة حديثة</small>
                    </div>
                </div>
                
                {% if account.is_leaf_account() %}
                <hr>
                <div class="text-center">
                    <h5 class="text-success">{{ balance|currency }}</h5>
                    <small>الرصيد الحالي</small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh balance every 30 seconds for leaf accounts
{% if account.is_leaf_account() %}
setInterval(function() {
    fetch('{{ url_for("accounts.balance", account_id=account.id) }}')
        .then(response => response.json())
        .then(data => {
            // Update balance display if needed
            console.log('Balance updated:', data.balance);
        })
        .catch(error => console.error('Error updating balance:', error));
}, 30000);
{% endif %}
</script>
{% endblock %}
