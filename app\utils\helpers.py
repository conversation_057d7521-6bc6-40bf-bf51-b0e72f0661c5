"""
Helper utility functions
"""

import os
import uuid
from datetime import datetime, date
from decimal import Decimal
from flask import current_app, url_for, request
from werkzeug.utils import secure_filename

def generate_uuid():
    """Generate a new UUID string"""
    return str(uuid.uuid4())

def get_pagination_params():
    """Get pagination parameters from request"""
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    return page, per_page

def generate_reference_number(prefix='REF', length=6):
    """Generate a reference number with prefix"""
    import random
    import string

    # Generate random alphanumeric string
    random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))

    # Add timestamp for uniqueness
    timestamp = datetime.now().strftime('%Y%m%d')

    return f"{prefix}-{timestamp}-{random_part}"

def format_currency(amount, currency_symbol='ج.م'):
    """Format amount as currency"""
    if amount is None:
        return f"0.00 {currency_symbol}"
    
    try:
        amount = float(amount)
        return f"{amount:,.2f} {currency_symbol}"
    except (ValueError, TypeError):
        return f"0.00 {currency_symbol}"

def format_date(date_obj, format_str='%Y-%m-%d'):
    """Format date object to string"""
    if date_obj is None:
        return ''
    
    if isinstance(date_obj, str):
        return date_obj
    
    try:
        return date_obj.strftime(format_str)
    except:
        return str(date_obj)

def format_datetime(datetime_obj, format_str='%Y-%m-%d %H:%M'):
    """Format datetime object to string"""
    if datetime_obj is None:
        return ''
    
    if isinstance(datetime_obj, str):
        return datetime_obj
    
    try:
        return datetime_obj.strftime(format_str)
    except:
        return str(datetime_obj)

def parse_date(date_str):
    """Parse date string to date object"""
    if not date_str:
        return None
    
    if isinstance(date_str, date):
        return date_str
    
    try:
        return datetime.strptime(date_str, '%Y-%m-%d').date()
    except:
        return None

def parse_decimal(value):
    """Parse value to Decimal"""
    if value is None or value == '':
        return Decimal('0')
    
    try:
        return Decimal(str(value))
    except:
        return Decimal('0')

def allowed_file(filename, allowed_extensions=None):
    """Check if file extension is allowed"""
    if allowed_extensions is None:
        allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', {'pdf', 'png', 'jpg', 'jpeg', 'gif'})
    
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_uploaded_file(file, upload_folder=None):
    """Save uploaded file and return filename"""
    if not file or file.filename == '':
        return None
    
    if not allowed_file(file.filename):
        return None
    
    if upload_folder is None:
        upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
    
    # Create upload folder if it doesn't exist
    os.makedirs(upload_folder, exist_ok=True)
    
    # Generate unique filename
    filename = secure_filename(file.filename)
    name, ext = os.path.splitext(filename)
    unique_filename = f"{name}_{generate_uuid()}{ext}"
    
    file_path = os.path.join(upload_folder, unique_filename)
    file.save(file_path)
    
    return unique_filename

def get_file_url(filename, upload_folder=None):
    """Get URL for uploaded file"""
    if not filename:
        return None
    
    if upload_folder is None:
        upload_folder = current_app.config.get('UPLOAD_FOLDER', 'uploads')
    
    return url_for('static', filename=f'uploads/{filename}')

def paginate_query(query, page=1, per_page=20):
    """Paginate SQLAlchemy query"""
    if per_page is None:
        per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    
    return query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

def flash_errors(form):
    """Flash form validation errors"""
    from flask import flash
    
    for field, errors in form.errors.items():
        for error in errors:
            flash(f'{getattr(form, field).label.text}: {error}', 'error')

def get_current_user_id():
    """Get current user ID"""
    from flask_login import current_user
    
    if current_user.is_authenticated:
        return current_user.id
    return None

def convert_to_arabic_numerals(text):
    """Convert English numerals to Arabic numerals"""
    english_to_arabic = {
        '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
        '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
    }
    
    for eng, ar in english_to_arabic.items():
        text = text.replace(eng, ar)
    
    return text

def convert_to_english_numerals(text):
    """Convert Arabic numerals to English numerals"""
    arabic_to_english = {
        '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
        '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
    }
    
    for ar, eng in arabic_to_english.items():
        text = text.replace(ar, eng)
    
    return text

def truncate_text(text, length=50, suffix='...'):
    """Truncate text to specified length"""
    if not text:
        return ''
    
    if len(text) <= length:
        return text
    
    return text[:length - len(suffix)] + suffix

def get_pagination_info(pagination):
    """Get pagination information for templates"""
    return {
        'page': pagination.page,
        'pages': pagination.pages,
        'per_page': pagination.per_page,
        'total': pagination.total,
        'has_prev': pagination.has_prev,
        'prev_num': pagination.prev_num,
        'has_next': pagination.has_next,
        'next_num': pagination.next_num,
        'items': pagination.items
    }
