@echo off
chcp 65001 >nul
title SystemTax - Quick Start

echo ========================================
echo    SystemTax - تشغيل سريع
echo ========================================
echo.

:: Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت
    echo 📥 تحميل Python من: https://python.org/downloads/
    pause
    exit /b 1
)

:: Show Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo 🐍 Python: %PYTHON_VERSION%

:: Create virtual environment if it doesn't exist
if not exist "venv" (
    echo 🔧 إنشاء البيئة الافتراضية...
    python -m venv venv
)

:: Activate virtual environment
echo 🔄 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

:: Run setup script
echo 🚀 إعداد النظام...
python setup-dev.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في الإعداد
    echo 💡 جرب:
    echo    1. python -m pip install --upgrade pip
    echo    2. pip install Flask Flask-SQLAlchemy
    echo    3. python app.py
    pause
    exit /b 1
)

echo.
echo 🎉 تم الإعداد بنجاح!
echo 🌐 فتح المتصفح على: http://localhost:5000
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.
echo ⏹️ لإيقاف الخادم اضغط Ctrl+C
echo.

:: Start the application
python app.py

pause
