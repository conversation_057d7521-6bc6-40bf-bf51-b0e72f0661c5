"""
Invoice views
"""

from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, make_response
from flask_login import login_required, current_user
from app import db
from app.models.invoice import Invoice, InvoiceLine
from app.models.customer import Customer
from app.forms.invoice import InvoiceForm, InvoiceSearchForm, QuickInvoiceForm
from app.utils.decorators import employee_required
from app.utils.helpers import paginate_query, flash_errors, get_current_user_id
from app.utils.pdf_generator import generate_invoice_pdf
from app.utils.tax_api import submit_invoice_to_tax_authority

invoices_bp = Blueprint('invoices', __name__)

@invoices_bp.route('/')
@employee_required
def index():
    """List all invoices"""
    form = InvoiceSearchForm()
    page = request.args.get('page', 1, type=int)
    
    # Build query
    query = Invoice.query
    
    # Apply filters
    if request.args.get('search'):
        search_term = request.args.get('search')
        query = query.filter(
            db.or_(
                Invoice.invoice_number.ilike(f'%{search_term}%'),
                Invoice.notes.ilike(f'%{search_term}%')
            )
        )
        form.search.data = search_term
    
    if request.args.get('customer_id'):
        customer_id = request.args.get('customer_id')
        query = query.filter(Invoice.customer_id == customer_id)
        form.customer_id.data = customer_id
    
    if request.args.get('status'):
        status = request.args.get('status')
        query = query.filter(Invoice.status == status)
        form.status.data = status
    
    if request.args.get('tax_status'):
        tax_status = request.args.get('tax_status')
        query = query.filter(Invoice.tax_status == tax_status)
        form.tax_status.data = tax_status
    
    if request.args.get('date_from'):
        from datetime import datetime
        try:
            date_from = datetime.strptime(request.args.get('date_from'), '%Y-%m-%d').date()
            query = query.filter(Invoice.issue_date >= date_from)
            form.date_from.data = date_from
        except:
            pass
    
    if request.args.get('date_to'):
        from datetime import datetime
        try:
            date_to = datetime.strptime(request.args.get('date_to'), '%Y-%m-%d').date()
            query = query.filter(Invoice.issue_date <= date_to)
            form.date_to.data = date_to
        except:
            pass
    
    # Order by issue date (newest first)
    query = query.order_by(Invoice.issue_date.desc())
    
    # Paginate
    invoices = paginate_query(query, page)
    
    return render_template('invoices/index.html', invoices=invoices, form=form)

@invoices_bp.route('/new', methods=['GET', 'POST'])
@employee_required
def new():
    """Create new invoice"""
    form = InvoiceForm()
    
    if form.validate_on_submit():
        invoice = Invoice(
            customer_id=form.customer_id.data,
            issue_date=form.issue_date.data,
            due_date=form.due_date.data,
            notes=form.notes.data,
            created_by=get_current_user_id()
        )
        
        db.session.add(invoice)
        db.session.flush()  # Get the ID
        
        # Generate invoice number
        invoice.invoice_number = invoice.generate_invoice_number()
        
        db.session.commit()
        
        flash(f'تم إنشاء الفاتورة {invoice.invoice_number} بنجاح.', 'success')
        return redirect(url_for('invoices.edit', invoice_id=invoice.id))
    
    flash_errors(form)
    return render_template('invoices/form.html', form=form, title='إنشاء فاتورة جديدة')

@invoices_bp.route('/quick', methods=['GET', 'POST'])
@employee_required
def quick():
    """Create quick invoice"""
    form = QuickInvoiceForm()
    
    if form.validate_on_submit():
        invoice = Invoice(
            customer_id=form.customer_id.data,
            issue_date=form.issue_date.data,
            due_date=form.due_date.data,
            notes=form.notes.data,
            created_by=get_current_user_id()
        )
        
        db.session.add(invoice)
        db.session.flush()  # Get the ID
        
        # Generate invoice number
        invoice.invoice_number = invoice.generate_invoice_number()
        
        # Add single line
        line = invoice.add_line(
            description=form.description.data,
            unit_price=form.amount.data,
            quantity=1,
            tax_percent=form.tax_percent.data
        )
        
        # Calculate totals
        invoice.calculate_totals()
        
        db.session.commit()
        
        flash(f'تم إنشاء الفاتورة {invoice.invoice_number} بنجاح.', 'success')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    flash_errors(form)
    return render_template('invoices/quick_form.html', form=form, title='إنشاء فاتورة سريعة')

@invoices_bp.route('/<invoice_id>')
@employee_required
def detail(invoice_id):
    """View invoice details"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    # Get invoice lines
    lines = invoice.lines.all()
    
    # Get tax transactions
    tax_transactions = invoice.tax_transactions.order_by(
        invoice.tax_transactions.property.mapper.class_.sent_at.desc()
    ).all()
    
    # Get receipt allocations
    allocations = invoice.receipt_allocations.all()
    
    return render_template('invoices/detail.html',
                         invoice=invoice,
                         lines=lines,
                         tax_transactions=tax_transactions,
                         allocations=allocations)

@invoices_bp.route('/<invoice_id>/edit', methods=['GET', 'POST'])
@employee_required
def edit(invoice_id):
    """Edit invoice"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if not invoice.can_be_edited():
        flash('لا يمكن تعديل هذه الفاتورة.', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    form = InvoiceForm()
    
    if form.validate_on_submit():
        invoice.customer_id = form.customer_id.data
        invoice.issue_date = form.issue_date.data
        invoice.due_date = form.due_date.data
        invoice.notes = form.notes.data
        
        db.session.commit()
        
        flash(f'تم تحديث الفاتورة {invoice.invoice_number} بنجاح.', 'success')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    elif request.method == 'GET':
        form.customer_id.data = invoice.customer_id
        form.issue_date.data = invoice.issue_date
        form.due_date.data = invoice.due_date
        form.notes.data = invoice.notes
    
    flash_errors(form)
    return render_template('invoices/form.html', form=form, invoice=invoice, title='تعديل الفاتورة')

@invoices_bp.route('/<invoice_id>/lines', methods=['GET', 'POST'])
@employee_required
def manage_lines(invoice_id):
    """Manage invoice lines"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if not invoice.can_be_edited():
        flash('لا يمكن تعديل بنود هذه الفاتورة.', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    if request.method == 'POST':
        # Handle AJAX requests for line management
        action = request.json.get('action')
        
        if action == 'add_line':
            line_data = request.json.get('line')
            line = invoice.add_line(
                description=line_data['description'],
                unit_price=line_data['unit_price'],
                quantity=line_data['quantity'],
                discount_percent=line_data.get('discount_percent', 0),
                tax_percent=line_data.get('tax_percent', 14)
            )
            
            invoice.calculate_totals()
            db.session.commit()
            
            return jsonify({
                'success': True,
                'line': line.to_dict(),
                'totals': invoice.calculate_totals()
            })
        
        elif action == 'update_line':
            line_id = request.json.get('line_id')
            line_data = request.json.get('line')
            
            line = InvoiceLine.query.get_or_404(line_id)
            if line.invoice_id != invoice.id:
                return jsonify({'success': False, 'message': 'خطأ في البيانات'})
            
            line.description = line_data['description']
            line.unit_price = line_data['unit_price']
            line.quantity = line_data['quantity']
            line.discount_percent = line_data.get('discount_percent', 0)
            line.tax_percent = line_data.get('tax_percent', 14)
            line.calculate_line_total()
            
            invoice.calculate_totals()
            db.session.commit()
            
            return jsonify({
                'success': True,
                'line': line.to_dict(),
                'totals': invoice.calculate_totals()
            })
        
        elif action == 'delete_line':
            line_id = request.json.get('line_id')
            
            line = InvoiceLine.query.get_or_404(line_id)
            if line.invoice_id != invoice.id:
                return jsonify({'success': False, 'message': 'خطأ في البيانات'})
            
            db.session.delete(line)
            invoice.calculate_totals()
            db.session.commit()
            
            return jsonify({
                'success': True,
                'totals': invoice.calculate_totals()
            })
    
    # GET request - show lines management page
    lines = invoice.lines.all()
    return render_template('invoices/manage_lines.html', invoice=invoice, lines=lines)

@invoices_bp.route('/<invoice_id>/send', methods=['POST'])
@employee_required
def send(invoice_id):
    """Send invoice (mark as sent)"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if invoice.status != 'draft':
        flash('هذه الفاتورة مرسلة بالفعل.', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    if invoice.lines.count() == 0:
        flash('لا يمكن إرسال فاتورة فارغة.', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    invoice.mark_as_sent()
    db.session.commit()
    
    flash(f'تم إرسال الفاتورة {invoice.invoice_number} بنجاح.', 'success')
    return redirect(url_for('invoices.detail', invoice_id=invoice.id))

@invoices_bp.route('/<invoice_id>/submit-tax', methods=['POST'])
@employee_required
def submit_tax(invoice_id):
    """Submit invoice to tax authority"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if not invoice.can_be_sent_to_tax_authority():
        flash('لا يمكن إرسال هذه الفاتورة لمصلحة الضرائب.', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    try:
        result = submit_invoice_to_tax_authority(invoice)
        
        if result['success']:
            flash(f'تم إرسال الفاتورة لمصلحة الضرائب بنجاح. رقم الفاتورة الضريبية: {result.get("tax_invoice_number", "")}', 'success')
        else:
            flash(f'فشل في إرسال الفاتورة لمصلحة الضرائب: {result["message"]}', 'error')
    
    except Exception as e:
        flash(f'خطأ في إرسال الفاتورة لمصلحة الضرائب: {str(e)}', 'error')
    
    return redirect(url_for('invoices.detail', invoice_id=invoice.id))

@invoices_bp.route('/<invoice_id>/pdf')
@employee_required
def pdf(invoice_id):
    """Generate invoice PDF"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    try:
        pdf_buffer = generate_invoice_pdf(invoice)
        
        response = make_response(pdf_buffer.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'inline; filename=invoice_{invoice.invoice_number}.pdf'
        
        return response
    
    except Exception as e:
        flash(f'خطأ في إنتاج ملف PDF: {str(e)}', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))

@invoices_bp.route('/<invoice_id>/delete', methods=['POST'])
@employee_required
def delete(invoice_id):
    """Delete invoice"""
    invoice = Invoice.query.get_or_404(invoice_id)
    
    if not invoice.can_be_edited():
        flash('لا يمكن حذف هذه الفاتورة.', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    if invoice.receipt_allocations.count() > 0:
        flash('لا يمكن حذف هذه الفاتورة لأنها مرتبطة بإيصالات.', 'error')
        return redirect(url_for('invoices.detail', invoice_id=invoice.id))
    
    invoice_number = invoice.invoice_number
    db.session.delete(invoice)
    db.session.commit()
    
    flash(f'تم حذف الفاتورة {invoice_number} بنجاح.', 'success')
    return redirect(url_for('invoices.index'))

@invoices_bp.route('/api/search')
@employee_required
def api_search():
    """API endpoint for invoice search"""
    term = request.args.get('term', '')
    customer_id = request.args.get('customer_id', '')
    status = request.args.get('status', '')
    
    query = Invoice.query
    
    if term:
        query = query.filter(Invoice.invoice_number.ilike(f'%{term}%'))
    
    if customer_id:
        query = query.filter(Invoice.customer_id == customer_id)
    
    if status:
        query = query.filter(Invoice.status == status)
    
    invoices = query.order_by(Invoice.issue_date.desc()).limit(20).all()
    
    return jsonify([{
        'id': invoice.id,
        'invoice_number': invoice.invoice_number,
        'customer_name': invoice.customer.name,
        'issue_date': invoice.issue_date.isoformat(),
        'total_amount': float(invoice.total_amount),
        'status': invoice.status,
        'outstanding_amount': invoice.get_outstanding_amount()
    } for invoice in invoices])
