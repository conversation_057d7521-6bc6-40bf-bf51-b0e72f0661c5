"""
Utility decorators for SystemTax application
"""

from functools import wraps
from flask import abort, flash, redirect, url_for
from flask_login import current_user

def login_required_with_role(roles):
    """
    Decorator to require login and specific roles
    
    Args:
        roles: List of allowed roles or single role string
    """
    if isinstance(roles, str):
        roles = [roles]
    
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة.', 'warning')
                return redirect(url_for('auth.login'))
            
            if not current_user.is_active:
                flash('حسابك غير نشط. يرجى الاتصال بالمدير.', 'error')
                return redirect(url_for('auth.login'))
            
            if current_user.role not in roles and 'admin' not in roles:
                if current_user.role != 'admin':  # Ad<PERSON> can access everything
                    abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def admin_required(f):
    """Decorator to require admin role"""
    return login_required_with_role(['admin'])(f)

def accountant_required(f):
    """Decorator to require accountant or admin role"""
    return login_required_with_role(['admin', 'accountant'])(f)

def employee_required(f):
    """Decorator to require any authenticated user"""
    return login_required_with_role(['admin', 'accountant', 'employee'])(f)

def permission_required(resource):
    """
    Decorator to check if user has permission for specific resource
    
    Args:
        resource: Resource name to check permission for
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                flash('يرجى تسجيل الدخول للوصول إلى هذه الصفحة.', 'warning')
                return redirect(url_for('auth.login'))
            
            if not current_user.can_access(resource):
                abort(403)
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator
