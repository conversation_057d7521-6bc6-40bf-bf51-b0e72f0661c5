"""
Report generation views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, make_response
from flask_login import login_required, current_user
from sqlalchemy import func, and_, or_, desc, asc
from sqlalchemy.orm import joinedload
from app import db
from app.models.account import Account
from app.models.journal import JournalEntry, JournalLine
from app.models.invoice import Invoice
from app.models.receipt import Receipt
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.forms.report_forms import (
    TrialBalanceForm, IncomeStatementForm, BalanceSheetForm,
    TaxReportForm, SalesReportForm, PurchasesReportForm,
    AgingReportForm, JournalReportForm
)
from app.utils.decorators import permission_required
from app.utils.helpers import get_pagination_params
from decimal import Decimal
from datetime import datetime, date, timedelta
from dateutil.relativedelta import relativedelta
import json

# Create blueprint
reports_bp = Blueprint('reports', __name__, url_prefix='/reports')


@reports_bp.route('/')
@login_required
@permission_required('reports')
def index():
    """Display reports dashboard"""
    
    # Get quick stats for current month
    current_month_start = date.today().replace(day=1)
    current_month_end = date.today()
    
    stats = {
        'total_revenue': get_revenue_for_period(current_month_start, current_month_end),
        'total_expenses': get_expenses_for_period(current_month_start, current_month_end),
        'net_profit': Decimal('0'),
        'taxes_due': get_taxes_due_for_period(current_month_start, current_month_end)
    }
    
    stats['net_profit'] = stats['total_revenue'] - stats['total_expenses']
    
    # Get recent reports (placeholder - would come from a reports history table)
    recent_reports = []
    
    return render_template(
        'reports/index.html',
        stats=stats,
        recent_reports=recent_reports,
        title='التقارير المالية'
    )


@reports_bp.route('/trial-balance')
@login_required
@permission_required('reports')
def trial_balance():
    """Generate Trial Balance report"""
    form = TrialBalanceForm(request.args)
    
    if form.validate():
        # Get trial balance data
        trial_balance_data = generate_trial_balance(
            form.date_from.data,
            form.date_to.data,
            form.account_type.data,
            form.show_zero_balances.data
        )
        
        # Calculate totals
        totals = calculate_trial_balance_totals(trial_balance_data)
        grand_totals = calculate_grand_totals(totals)
        
        # Report info
        report_info = {
            'total_accounts': sum(len(accounts) for accounts in trial_balance_data.values()),
            'active_accounts': sum(
                len([acc for acc in accounts if acc.get('is_active', True)]) 
                for accounts in trial_balance_data.values()
            ),
            'generated_at': datetime.now()
        }
        
        # Account type metadata
        account_type_names = {
            'Asset': 'الأصول',
            'Liability': 'الخصوم',
            'Equity': 'حقوق الملكية',
            'Income': 'الإيرادات',
            'Expense': 'المصروفات'
        }
        
        account_type_icons = {
            'Asset': 'building',
            'Liability': 'credit-card',
            'Equity': 'user-tie',
            'Income': 'arrow-up',
            'Expense': 'arrow-down'
        }
        
        return render_template(
            'reports/trial_balance.html',
            form=form,
            trial_balance_data=trial_balance_data,
            totals=totals,
            grand_totals=grand_totals,
            report_info=report_info,
            account_type_names=account_type_names,
            account_type_icons=account_type_icons,
            date_from=form.date_from.data,
            date_to=form.date_to.data,
            company_name='شركة النظام الضريبي',
            title='ميزان المراجعة'
        )
    
    return render_template(
        'reports/trial_balance.html',
        form=form,
        title='ميزان المراجعة'
    )


@reports_bp.route('/income-statement')
@login_required
@permission_required('reports')
def income_statement():
    """Generate Income Statement report"""
    form = IncomeStatementForm(request.args)
    
    if form.validate():
        # Get income statement data
        income_statement_data = generate_income_statement(
            form.date_from.data,
            form.date_to.data,
            form.show_details.data
        )
        
        # Get comparison data if requested
        comparison_data = None
        if form.comparison_period.data:
            comparison_dates = get_comparison_period_dates(
                form.date_from.data,
                form.date_to.data,
                form.comparison_period.data
            )
            if comparison_dates:
                comparison_data = generate_income_statement(
                    comparison_dates[0],
                    comparison_dates[1],
                    form.show_details.data
                )
        
        # Calculate financial ratios
        ratios = calculate_financial_ratios(income_statement_data)
        
        # Calculate growth rates
        growth = {}
        if comparison_data:
            growth = calculate_growth_rates(income_statement_data, comparison_data)
        
        return render_template(
            'reports/income_statement.html',
            form=form,
            income_statement_data=income_statement_data,
            comparison_data=comparison_data,
            ratios=ratios,
            growth=growth,
            date_from=form.date_from.data,
            date_to=form.date_to.data,
            company_name='شركة النظام الضريبي',
            title='قائمة الدخل'
        )
    
    return render_template(
        'reports/income_statement.html',
        form=form,
        title='قائمة الدخل'
    )


@reports_bp.route('/balance-sheet')
@login_required
@permission_required('reports')
def balance_sheet():
    """Generate Balance Sheet report"""
    # Implementation for balance sheet
    return render_template(
        'reports/balance_sheet.html',
        title='الميزانية العمومية'
    )


@reports_bp.route('/cash-flow')
@login_required
@permission_required('reports')
def cash_flow():
    """Generate Cash Flow Statement"""
    # Implementation for cash flow statement
    return render_template(
        'reports/cash_flow.html',
        title='قائمة التدفقات النقدية'
    )


@reports_bp.route('/tax-report')
@login_required
@permission_required('reports')
def tax_report():
    """Generate Tax reports"""
    form = TaxReportForm(request.args)
    
    if form.validate():
        # Generate tax report based on type
        tax_data = generate_tax_report(
            form.report_type.data,
            form.date_from.data,
            form.date_to.data,
            form.include_details.data
        )
        
        return render_template(
            'reports/tax_report.html',
            form=form,
            tax_data=tax_data,
            title='التقرير الضريبي'
        )
    
    return render_template(
        'reports/tax_report.html',
        form=form,
        title='التقرير الضريبي'
    )


@reports_bp.route('/vat-report')
@login_required
@permission_required('reports')
def vat_report():
    """Generate VAT report"""
    # Implementation for VAT report
    return render_template(
        'reports/vat_report.html',
        title='تقرير ضريبة القيمة المضافة'
    )


@reports_bp.route('/withholding-tax')
@login_required
@permission_required('reports')
def withholding_tax():
    """Generate Withholding Tax report"""
    # Implementation for withholding tax report
    return render_template(
        'reports/withholding_tax.html',
        title='تقرير الضريبة المقتطعة'
    )


@reports_bp.route('/sales-report')
@login_required
@permission_required('reports')
def sales_report():
    """Generate Sales report"""
    form = SalesReportForm(request.args)
    
    if form.validate():
        # Generate sales report
        sales_data = generate_sales_report(
            form.date_from.data,
            form.date_to.data,
            form.group_by.data,
            form.customer_id.data,
            form.include_tax.data,
            form.show_details.data
        )
        
        return render_template(
            'reports/sales_report.html',
            form=form,
            sales_data=sales_data,
            title='تقرير المبيعات'
        )
    
    return render_template(
        'reports/sales_report.html',
        form=form,
        title='تقرير المبيعات'
    )


@reports_bp.route('/purchases-report')
@login_required
@permission_required('reports')
def purchases_report():
    """Generate Purchases report"""
    # Implementation for purchases report
    return render_template(
        'reports/purchases_report.html',
        title='تقرير المشتريات'
    )


@reports_bp.route('/aging-report')
@login_required
@permission_required('reports')
def aging_report():
    """Generate Aging report"""
    form = AgingReportForm(request.args)
    
    if form.validate():
        # Generate aging report
        aging_data = generate_aging_report(
            form.as_of_date.data,
            form.report_type.data,
            form.aging_periods.data,
            form.customer_id.data,
            form.vendor_id.data,
            form.show_zero_balances.data
        )
        
        return render_template(
            'reports/aging_report.html',
            form=form,
            aging_data=aging_data,
            title='تقرير أعمار الديون'
        )
    
    return render_template(
        'reports/aging_report.html',
        form=form,
        title='تقرير أعمار الديون'
    )


@reports_bp.route('/journal-report')
@login_required
@permission_required('reports')
def journal_report():
    """Generate Journal Entry report"""
    # Implementation for journal report
    return render_template(
        'reports/journal_report.html',
        title='تقرير القيود اليومية'
    )


# Helper functions for report generation

def generate_trial_balance(date_from, date_to, account_type=None, show_zero_balances=False):
    """Generate trial balance data"""
    
    # Build query for accounts
    query = Account.query.filter(Account.is_active == True)
    
    if account_type:
        query = query.filter(Account.type == account_type)
    
    accounts = query.order_by(Account.code).all()
    
    # Group accounts by type
    trial_balance_data = {}
    
    for account in accounts:
        # Calculate balances for the account
        opening_balance = account.get_balance_as_of(date_from - timedelta(days=1))
        closing_balance = account.get_balance_as_of(date_to)
        
        # Calculate period movement
        period_debit, period_credit = account.get_period_movement(date_from, date_to)
        
        # Skip zero balances if requested
        if not show_zero_balances and (
            opening_balance == 0 and closing_balance == 0 and 
            period_debit == 0 and period_credit == 0
        ):
            continue
        
        # Determine debit/credit presentation based on account type
        if account.type in ['Asset', 'Expense']:
            opening_balance_debit = opening_balance if opening_balance > 0 else Decimal('0')
            opening_balance_credit = abs(opening_balance) if opening_balance < 0 else Decimal('0')
            closing_balance_debit = closing_balance if closing_balance > 0 else Decimal('0')
            closing_balance_credit = abs(closing_balance) if closing_balance < 0 else Decimal('0')
        else:  # Liability, Equity, Income
            opening_balance_debit = abs(opening_balance) if opening_balance < 0 else Decimal('0')
            opening_balance_credit = opening_balance if opening_balance > 0 else Decimal('0')
            closing_balance_debit = abs(closing_balance) if closing_balance < 0 else Decimal('0')
            closing_balance_credit = closing_balance if closing_balance > 0 else Decimal('0')
        
        account_data = {
            'code': account.code,
            'name': account.name,
            'level': account.level,
            'opening_balance_debit': opening_balance_debit,
            'opening_balance_credit': opening_balance_credit,
            'period_debit': period_debit,
            'period_credit': period_credit,
            'closing_balance_debit': closing_balance_debit,
            'closing_balance_credit': closing_balance_credit
        }
        
        if account.type not in trial_balance_data:
            trial_balance_data[account.type] = []
        
        trial_balance_data[account.type].append(account_data)
    
    return trial_balance_data


def calculate_trial_balance_totals(trial_balance_data):
    """Calculate totals for each account type"""
    totals = {}
    
    for account_type, accounts in trial_balance_data.items():
        totals[account_type] = {
            'opening_debit': sum(acc['opening_balance_debit'] for acc in accounts),
            'opening_credit': sum(acc['opening_balance_credit'] for acc in accounts),
            'period_debit': sum(acc['period_debit'] for acc in accounts),
            'period_credit': sum(acc['period_credit'] for acc in accounts),
            'closing_debit': sum(acc['closing_balance_debit'] for acc in accounts),
            'closing_credit': sum(acc['closing_balance_credit'] for acc in accounts)
        }
    
    return totals


def calculate_grand_totals(totals):
    """Calculate grand totals across all account types"""
    grand_totals = {
        'opening_debit': sum(t['opening_debit'] for t in totals.values()),
        'opening_credit': sum(t['opening_credit'] for t in totals.values()),
        'period_debit': sum(t['period_debit'] for t in totals.values()),
        'period_credit': sum(t['period_credit'] for t in totals.values()),
        'closing_debit': sum(t['closing_debit'] for t in totals.values()),
        'closing_credit': sum(t['closing_credit'] for t in totals.values())
    }
    
    return grand_totals


def generate_income_statement(date_from, date_to, show_details=True):
    """Generate income statement data"""
    
    # Get revenue accounts
    revenue_accounts = Account.query.filter(
        Account.type == 'Income',
        Account.is_active == True
    ).all()
    
    # Get expense accounts
    expense_accounts = Account.query.filter(
        Account.type == 'Expense',
        Account.is_active == True
    ).all()
    
    # Calculate revenues
    revenues = []
    total_revenue = Decimal('0')
    
    for account in revenue_accounts:
        balance = account.get_balance_for_period(date_from, date_to)
        if balance != 0 or show_details:
            revenues.append({
                'name': account.name,
                'amount': abs(balance)  # Revenue accounts have credit balances
            })
            total_revenue += abs(balance)
    
    # Calculate expenses
    operating_expenses = []
    total_operating_expenses = Decimal('0')
    
    for account in expense_accounts:
        balance = account.get_balance_for_period(date_from, date_to)
        if balance != 0 or show_details:
            operating_expenses.append({
                'name': account.name,
                'amount': balance  # Expense accounts have debit balances
            })
            total_operating_expenses += balance
    
    # Calculate net income
    net_income = total_revenue - total_operating_expenses
    
    income_statement_data = {
        'revenues': revenues,
        'total_revenue': total_revenue,
        'operating_expenses': operating_expenses,
        'total_operating_expenses': total_operating_expenses,
        'operating_profit': total_revenue - total_operating_expenses,
        'net_income_before_tax': total_revenue - total_operating_expenses,
        'tax_expenses': Decimal('0'),  # Would be calculated based on tax accounts
        'net_income': net_income
    }
    
    return income_statement_data


def get_revenue_for_period(date_from, date_to):
    """Get total revenue for a period"""
    revenue_accounts = Account.query.filter(
        Account.type == 'Income',
        Account.is_active == True
    ).all()
    
    total = Decimal('0')
    for account in revenue_accounts:
        balance = account.get_balance_for_period(date_from, date_to)
        total += abs(balance)
    
    return total


def get_expenses_for_period(date_from, date_to):
    """Get total expenses for a period"""
    expense_accounts = Account.query.filter(
        Account.type == 'Expense',
        Account.is_active == True
    ).all()
    
    total = Decimal('0')
    for account in expense_accounts:
        balance = account.get_balance_for_period(date_from, date_to)
        total += balance
    
    return total


def get_taxes_due_for_period(date_from, date_to):
    """Get taxes due for a period"""
    # This would calculate based on tax accounts and rates
    # For now, return a placeholder
    return Decimal('0')


def calculate_financial_ratios(income_statement_data):
    """Calculate financial ratios"""
    ratios = {}
    
    if income_statement_data['total_revenue'] > 0:
        ratios['gross_profit_margin'] = (
            income_statement_data.get('gross_profit', income_statement_data['total_revenue']) / 
            income_statement_data['total_revenue'] * 100
        )
        ratios['operating_profit_margin'] = (
            income_statement_data['operating_profit'] / 
            income_statement_data['total_revenue'] * 100
        )
        ratios['net_profit_margin'] = (
            income_statement_data['net_income'] / 
            income_statement_data['total_revenue'] * 100
        )
        ratios['expense_ratio'] = (
            income_statement_data['total_operating_expenses'] / 
            income_statement_data['total_revenue'] * 100
        )
    else:
        ratios = {
            'gross_profit_margin': 0,
            'operating_profit_margin': 0,
            'net_profit_margin': 0,
            'expense_ratio': 0
        }
    
    return ratios


def get_comparison_period_dates(date_from, date_to, comparison_period):
    """Get dates for comparison period"""
    if comparison_period == 'previous_month':
        comp_to = date_from - timedelta(days=1)
        comp_from = comp_to.replace(day=1)
        return (comp_from, comp_to)
    
    elif comparison_period == 'previous_year':
        comp_from = date_from - relativedelta(years=1)
        comp_to = date_to - relativedelta(years=1)
        return (comp_from, comp_to)
    
    elif comparison_period == 'same_period_last_year':
        comp_from = date_from - relativedelta(years=1)
        comp_to = date_to - relativedelta(years=1)
        return (comp_from, comp_to)
    
    return None


def calculate_growth_rates(current_data, comparison_data):
    """Calculate growth rates between periods"""
    growth = {}
    
    if comparison_data['total_revenue'] > 0:
        growth['revenue_growth'] = (
            (current_data['total_revenue'] - comparison_data['total_revenue']) /
            comparison_data['total_revenue'] * 100
        )
    else:
        growth['revenue_growth'] = 0
    
    if comparison_data['net_income'] != 0:
        growth['profit_growth'] = (
            (current_data['net_income'] - comparison_data['net_income']) /
            abs(comparison_data['net_income']) * 100
        )
    else:
        growth['profit_growth'] = 0
    
    if comparison_data['total_operating_expenses'] > 0:
        growth['expense_growth'] = (
            (current_data['total_operating_expenses'] - comparison_data['total_operating_expenses']) /
            comparison_data['total_operating_expenses'] * 100
        )
    else:
        growth['expense_growth'] = 0
    
    return growth


# Placeholder functions for other reports
def generate_tax_report(report_type, date_from, date_to, include_details):
    """Generate tax report data"""
    return {}


def generate_sales_report(date_from, date_to, group_by, customer_id, include_tax, show_details):
    """Generate sales report data"""
    return {}


def generate_aging_report(as_of_date, report_type, aging_periods, customer_id, vendor_id, show_zero_balances):
    """Generate aging report data"""
    return {}
