"""
Forms for dashboard and analytics
"""

from flask_wtf import FlaskForm
from wtforms import (
    StringField, SelectField, DateField, BooleanField, 
    TextAreaField, IntegerField, DecimalField
)
from wtforms.validators import DataRequired, Optional, NumberRange, Length
from datetime import date, datetime, timedelta
from dateutil.relativedelta import relativedelta


class DashboardFilterForm(FlaskForm):
    """Form for dashboard filtering and period selection"""
    
    period = SelectField(
        'الفترة',
        choices=[
            ('today', 'اليوم'),
            ('week', 'هذا الأسبوع'),
            ('month', 'هذا الشهر'),
            ('quarter', 'هذا الربع'),
            ('year', 'هذا العام'),
            ('custom', 'فترة مخصصة')
        ],
        default='month',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    date_from = DateField(
        'من تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    compare_with_previous = BooleanField(
        'مقارنة مع الفترة السابقة',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set default dates based on period
        if not self.date_from.data or not self.date_to.data:
            self.set_default_dates()
    
    def set_default_dates(self):
        """Set default dates based on selected period"""
        today = date.today()
        
        if self.period.data == 'today':
            self.date_from.data = today
            self.date_to.data = today
        elif self.period.data == 'week':
            # Start of current week (Monday)
            start_of_week = today - timedelta(days=today.weekday())
            self.date_from.data = start_of_week
            self.date_to.data = today
        elif self.period.data == 'month':
            # Start of current month
            self.date_from.data = today.replace(day=1)
            self.date_to.data = today
        elif self.period.data == 'quarter':
            # Start of current quarter
            quarter_start_month = ((today.month - 1) // 3) * 3 + 1
            self.date_from.data = today.replace(month=quarter_start_month, day=1)
            self.date_to.data = today
        elif self.period.data == 'year':
            # Start of current year
            self.date_from.data = today.replace(month=1, day=1)
            self.date_to.data = today
    
    def get_comparison_dates(self):
        """Get dates for comparison period"""
        if not self.compare_with_previous.data or not self.date_from.data or not self.date_to.data:
            return None, None
        
        period_length = (self.date_to.data - self.date_from.data).days
        
        comp_date_to = self.date_from.data - timedelta(days=1)
        comp_date_from = comp_date_to - timedelta(days=period_length)
        
        return comp_date_from, comp_date_to
    
    def validate(self, extra_validators=None):
        """Custom validation"""
        if not super().validate(extra_validators):
            return False
        
        if self.period.data == 'custom':
            if not self.date_from.data:
                self.date_from.errors.append('تاريخ البداية مطلوب للفترة المخصصة')
                return False
            
            if not self.date_to.data:
                self.date_to.errors.append('تاريخ النهاية مطلوب للفترة المخصصة')
                return False
            
            if self.date_from.data > self.date_to.data:
                self.date_to.errors.append('تاريخ النهاية يجب أن يكون بعد تاريخ البداية')
                return False
        
        return True


class KPIConfigForm(FlaskForm):
    """Form for configuring KPI display preferences"""
    
    show_revenue = BooleanField(
        'عرض الإيرادات',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    show_profit = BooleanField(
        'عرض صافي الربح',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    show_expenses = BooleanField(
        'عرض المصروفات',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    show_cash_balance = BooleanField(
        'عرض الرصيد النقدي',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    show_growth_indicators = BooleanField(
        'عرض مؤشرات النمو',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    currency_format = SelectField(
        'تنسيق العملة',
        choices=[
            ('EGP', 'جنيه مصري (ج.م)'),
            ('USD', 'دولار أمريكي ($)'),
            ('EUR', 'يورو (€)')
        ],
        default='EGP',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    decimal_places = SelectField(
        'عدد الخانات العشرية',
        choices=[
            ('0', '0'),
            ('1', '1'),
            ('2', '2'),
            ('3', '3')
        ],
        default='2',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )


class ChartConfigForm(FlaskForm):
    """Form for configuring chart display preferences"""
    
    chart_type = SelectField(
        'نوع الرسم البياني',
        choices=[
            ('line', 'خطي'),
            ('bar', 'أعمدة'),
            ('area', 'منطقة'),
            ('pie', 'دائري'),
            ('doughnut', 'حلقي')
        ],
        default='line',
        validators=[DataRequired(message='نوع الرسم البياني مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    time_period = SelectField(
        'فترة البيانات',
        choices=[
            ('daily', 'يومي'),
            ('weekly', 'أسبوعي'),
            ('monthly', 'شهري'),
            ('quarterly', 'ربع سنوي'),
            ('yearly', 'سنوي')
        ],
        default='monthly',
        validators=[DataRequired(message='فترة البيانات مطلوبة')],
        render_kw={'class': 'form-select'}
    )
    
    data_points = IntegerField(
        'عدد نقاط البيانات',
        default=12,
        validators=[
            Optional(),
            NumberRange(min=3, max=50, message='عدد نقاط البيانات يجب أن يكون بين 3 و 50')
        ],
        render_kw={'class': 'form-control'}
    )
    
    show_legend = BooleanField(
        'عرض وسيلة الإيضاح',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    show_grid = BooleanField(
        'عرض الشبكة',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    animate_charts = BooleanField(
        'تحريك الرسوم البيانية',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class AlertConfigForm(FlaskForm):
    """Form for configuring dashboard alerts"""
    
    alert_type = SelectField(
        'نوع التنبيه',
        choices=[
            ('revenue_drop', 'انخفاض الإيرادات'),
            ('expense_increase', 'زيادة المصروفات'),
            ('cash_low', 'انخفاض الرصيد النقدي'),
            ('overdue_invoices', 'فواتير متأخرة'),
            ('tax_deadline', 'موعد ضريبي'),
            ('custom', 'تنبيه مخصص')
        ],
        validators=[DataRequired(message='نوع التنبيه مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    threshold_value = DecimalField(
        'القيمة الحدية',
        validators=[
            Optional(),
            NumberRange(min=0, message='القيمة الحدية يجب أن تكون أكبر من صفر')
        ],
        places=2,
        render_kw={'class': 'form-control'}
    )
    
    threshold_percentage = DecimalField(
        'النسبة المئوية الحدية',
        validators=[
            Optional(),
            NumberRange(min=0, max=100, message='النسبة المئوية يجب أن تكون بين 0 و 100')
        ],
        places=1,
        render_kw={'class': 'form-control'}
    )
    
    alert_message = TextAreaField(
        'رسالة التنبيه',
        validators=[
            Optional(),
            Length(max=500, message='رسالة التنبيه يجب ألا تزيد عن 500 حرف')
        ],
        render_kw={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'رسالة التنبيه المخصصة...'
        }
    )
    
    is_active = BooleanField(
        'تنبيه نشط',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    email_notification = BooleanField(
        'إرسال تنبيه بالبريد الإلكتروني',
        default=False,
        render_kw={'class': 'form-check-input'}
    )
    
    sms_notification = BooleanField(
        'إرسال تنبيه برسالة نصية',
        default=False,
        render_kw={'class': 'form-check-input'}
    )


class DashboardLayoutForm(FlaskForm):
    """Form for customizing dashboard layout"""
    
    layout_style = SelectField(
        'نمط التخطيط',
        choices=[
            ('default', 'افتراضي'),
            ('compact', 'مضغوط'),
            ('detailed', 'مفصل'),
            ('minimal', 'بسيط')
        ],
        default='default',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    sidebar_position = SelectField(
        'موضع الشريط الجانبي',
        choices=[
            ('left', 'يسار'),
            ('right', 'يمين'),
            ('hidden', 'مخفي')
        ],
        default='right',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    theme = SelectField(
        'المظهر',
        choices=[
            ('light', 'فاتح'),
            ('dark', 'داكن'),
            ('auto', 'تلقائي')
        ],
        default='light',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    show_quick_actions = BooleanField(
        'عرض الإجراءات السريعة',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    show_recent_activity = BooleanField(
        'عرض النشاط الأخير',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    show_alerts = BooleanField(
        'عرض التنبيهات',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    auto_refresh = BooleanField(
        'التحديث التلقائي',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    refresh_interval = SelectField(
        'فترة التحديث (بالدقائق)',
        choices=[
            ('1', '1'),
            ('5', '5'),
            ('10', '10'),
            ('15', '15'),
            ('30', '30'),
            ('60', '60')
        ],
        default='5',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )


class ExportDashboardForm(FlaskForm):
    """Form for exporting dashboard data"""
    
    export_format = SelectField(
        'تنسيق التصدير',
        choices=[
            ('pdf', 'PDF'),
            ('excel', 'Excel'),
            ('csv', 'CSV'),
            ('json', 'JSON'),
            ('image', 'صورة')
        ],
        validators=[DataRequired(message='تنسيق التصدير مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    include_charts = BooleanField(
        'تضمين الرسوم البيانية',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    include_kpis = BooleanField(
        'تضمين المؤشرات الرئيسية',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    include_tables = BooleanField(
        'تضمين الجداول',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    date_range = SelectField(
        'نطاق التاريخ',
        choices=[
            ('current', 'الفترة الحالية'),
            ('last_month', 'الشهر الماضي'),
            ('last_quarter', 'الربع الماضي'),
            ('last_year', 'السنة الماضية'),
            ('custom', 'فترة مخصصة')
        ],
        default='current',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    custom_date_from = DateField(
        'من تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    custom_date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    email_to = StringField(
        'إرسال إلى (البريد الإلكتروني)',
        validators=[
            Optional(),
            Length(max=255, message='البريد الإلكتروني طويل جداً')
        ],
        render_kw={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        }
    )
    
    include_notes = TextAreaField(
        'ملاحظات إضافية',
        validators=[
            Optional(),
            Length(max=1000, message='الملاحظات يجب ألا تزيد عن 1000 حرف')
        ],
        render_kw={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'ملاحظات أو تعليقات إضافية...'
        }
    )
    
    def validate(self, extra_validators=None):
        """Custom validation"""
        if not super().validate(extra_validators):
            return False
        
        if self.date_range.data == 'custom':
            if not self.custom_date_from.data:
                self.custom_date_from.errors.append('تاريخ البداية مطلوب للفترة المخصصة')
                return False
            
            if not self.custom_date_to.data:
                self.custom_date_to.errors.append('تاريخ النهاية مطلوب للفترة المخصصة')
                return False
            
            if self.custom_date_from.data > self.custom_date_to.data:
                self.custom_date_to.errors.append('تاريخ النهاية يجب أن يكون بعد تاريخ البداية')
                return False
        
        return True
