@echo off
REM SystemTax Docker Startup Script for Windows
REM سكريبت تشغيل SystemTax باستخدام Docker على Windows

echo ==========================================
echo     SystemTax - نظام الضرائب المتكامل
echo ==========================================
echo.

REM Check if Docker is running
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker is not running. Please start Docker Desktop first.
    echo [خطأ] Docker غير مشغل. يرجى تشغيل Docker Desktop أولاً.
    pause
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Docker Compose is not available.
    echo [خطأ] Docker Compose غير متوفر.
    pause
    exit /b 1
)

if "%1"=="start" goto start
if "%1"=="stop" goto stop
if "%1"=="restart" goto restart
if "%1"=="status" goto status
if "%1"=="logs" goto logs
if "%1"=="build" goto build
if "%1"=="cleanup" goto cleanup
goto help

:start
echo [INFO] Starting SystemTax services...
echo [معلومات] بدء تشغيل خدمات SystemTax...

REM Create necessary directories
if not exist "uploads" mkdir uploads
if not exist "instance" mkdir instance
if not exist "nginx\ssl" mkdir nginx\ssl
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups

REM Start services
docker-compose up -d

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] SystemTax is now running!
    echo [نجح] SystemTax يعمل الآن!
    echo.
    echo 🌐 Application URL: http://localhost:8000
    echo 👤 Default Login: admin / admin123
    echo 🗄️  Database: PostgreSQL on localhost:5432
    echo 🔄 Redis Cache: localhost:6379
    echo.
    echo Use 'docker-start.bat logs' to view application logs
    echo Use 'docker-start.bat stop' to stop all services
) else (
    echo [ERROR] Failed to start services. Check Docker Desktop.
    echo [خطأ] فشل في تشغيل الخدمات. تحقق من Docker Desktop.
)
goto end

:stop
echo [INFO] Stopping all services...
echo [معلومات] إيقاف جميع الخدمات...
docker-compose down
echo [SUCCESS] All services stopped.
echo [نجح] تم إيقاف جميع الخدمات.
goto end

:restart
echo [INFO] Restarting services...
echo [معلومات] إعادة تشغيل الخدمات...
docker-compose restart
echo [SUCCESS] Services restarted.
echo [نجح] تم إعادة تشغيل الخدمات.
goto end

:status
echo [INFO] Checking container status...
echo [معلومات] فحص حالة الحاويات...
docker-compose ps
goto end

:logs
echo [INFO] Showing application logs...
echo [معلومات] عرض سجلات التطبيق...
docker-compose logs -f web
goto end

:build
echo [INFO] Building SystemTax Docker image...
echo [معلومات] بناء صورة SystemTax Docker...
docker-compose build --no-cache
echo [SUCCESS] Build completed.
echo [نجح] اكتمل البناء.
goto end

:cleanup
echo [WARNING] This will remove all containers and data.
echo [تحذير] سيؤدي هذا إلى حذف جميع الحاويات والبيانات.
set /p confirm="Are you sure? (y/N): "
if /i "%confirm%"=="y" (
    echo [INFO] Cleaning up containers and volumes...
    echo [معلومات] تنظيف الحاويات والأحجام...
    docker-compose down -v --remove-orphans
    docker system prune -f
    echo [SUCCESS] Cleanup completed.
    echo [نجح] اكتمل التنظيف.
) else (
    echo [INFO] Cleanup cancelled.
    echo [معلومات] تم إلغاء التنظيف.
)
goto end

:help
echo SystemTax Docker Management Script for Windows
echo سكريبت إدارة SystemTax Docker لنظام Windows
echo.
echo Usage: docker-start.bat {start^|stop^|restart^|status^|logs^|build^|cleanup}
echo الاستخدام: docker-start.bat {start^|stop^|restart^|status^|logs^|build^|cleanup}
echo.
echo Commands / الأوامر:
echo   start    - Start all services / تشغيل جميع الخدمات
echo   stop     - Stop all services / إيقاف جميع الخدمات
echo   restart  - Restart all services / إعادة تشغيل جميع الخدمات
echo   status   - Show container status / عرض حالة الحاويات
echo   logs     - View application logs / عرض سجلات التطبيق
echo   build    - Build Docker image / بناء صورة Docker
echo   cleanup  - Remove all containers and data / حذف جميع الحاويات والبيانات
echo.

:end
pause
