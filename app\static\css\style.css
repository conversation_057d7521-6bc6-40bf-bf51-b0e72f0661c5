/* SystemTax Custom Styles */

:root {
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --success-color: #059669;
    --danger-color: #dc2626;
    --warning-color: #d97706;
    --info-color: #0891b2;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --border-radius: 0.75rem;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    --transition: all 0.3s ease;
}

/* Typography */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--light-color);
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: var(--dark-color);
}

/* Layout */
.main-content {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin: 1rem;
    padding: 2rem;
    min-height: calc(100vh - 120px);
}

.page-header {
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #e2e8f0;
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 0.5rem;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    padding: 0.5rem 1rem;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    box-shadow: 0 2px 4px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #047857);
    box-shadow: 0 2px 4px rgba(5, 150, 105, 0.3);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #b91c1c);
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #c2410c);
    box-shadow: 0 2px 4px rgba(217, 119, 6, 0.3);
}

.btn-info {
    background: linear-gradient(135deg, var(--info-color), #0e7490);
    box-shadow: 0 2px 4px rgba(8, 145, 178, 0.3);
}

/* Forms */
.form-control, .form-select {
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    transition: var(--transition);
    padding: 0.75rem;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background-color: var(--light-color);
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
}

.table tbody td {
    padding: 0.75rem 1rem;
    border-color: #f1f5f9;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8fafc;
}

/* Alerts */
.alert {
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    box-shadow: var(--box-shadow);
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    color: #065f46;
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe, #bfdbfe);
    color: #1e40af;
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.stats-card.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stats-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-card.info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-card.danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
}

/* Badges */
.badge {
    border-radius: 0.5rem;
    font-weight: 500;
    padding: 0.5rem 0.75rem;
}

/* Pagination */
.pagination .page-link {
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    border: 1px solid #e2e8f0;
    color: var(--primary-color);
    transition: var(--transition);
}

.pagination .page-link:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Breadcrumb */
.breadcrumb {
    background: none;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: var(--secondary-color);
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb-item a:hover {
    color: #1d4ed8;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Utilities */
.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.border-primary {
    border-color: var(--primary-color) !important;
}

.shadow-sm {
    box-shadow: var(--box-shadow) !important;
}

.rounded-lg {
    border-radius: var(--border-radius) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        margin: 0.5rem;
        padding: 1rem;
    }
    
    .page-header {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .stats-card {
        margin-bottom: 0.5rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin: 0;
        padding: 0;
        box-shadow: none;
    }
    
    .page-header {
        background: none !important;
        color: black !important;
        box-shadow: none;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --light-color: #1e293b;
        --dark-color: #f8fafc;
    }
    
    body {
        background-color: #0f172a;
        color: #f8fafc;
    }
    
    .main-content {
        background-color: #1e293b;
    }
    
    .card {
        background-color: #1e293b;
        color: #f8fafc;
    }
    
    .table {
        color: #f8fafc;
    }
    
    .table thead th {
        background-color: #334155;
    }
    
    .table tbody tr:hover {
        background-color: #334155;
    }
}
