"""
Audit and compliance management views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, make_response
from flask_login import login_required, current_user
from app import db
from app.models.audit_log import AuditLog, DataChangeLog, AuditService
from app.models.security_log import SecurityLog
from app.utils.decorators import permission_required
from app.utils.helpers import get_pagination_params
from datetime import datetime, timedelta, date
import json

# Create blueprint
audit_bp = Blueprint('audit', __name__, url_prefix='/audit')


@audit_bp.route('/')
@login_required
@permission_required('admin')
def index():
    """Audit dashboard"""
    
    # Get recent statistics
    stats_24h = AuditLog.get_statistics(hours=24)
    stats_7d = AuditLog.get_statistics(hours=168)  # 7 days
    
    # Get recent audit logs
    recent_logs = AuditLog.query.order_by(
        AuditLog.created_at.desc()
    ).limit(20).all()
    
    # Get recent security events
    recent_security = SecurityLog.get_recent_events(hours=24, limit=10)
    
    # Get sensitive operations
    sensitive_ops = AuditLog.get_sensitive_operations(hours=24, limit=10)
    
    return render_template(
        'audit/index.html',
        stats_24h=stats_24h,
        stats_7d=stats_7d,
        recent_logs=recent_logs,
        recent_security=recent_security,
        sensitive_ops=sensitive_ops,
        title='لوحة التدقيق والمراجعة'
    )


@audit_bp.route('/logs')
@login_required
@permission_required('admin')
def logs():
    """Audit logs listing"""
    
    page, per_page = get_pagination_params()
    
    # Get filter parameters
    table_name = request.args.get('table_name')
    action = request.args.get('action')
    user_id = request.args.get('user_id')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    search_term = request.args.get('search')
    
    # Convert date strings
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
        except ValueError:
            date_from = None
    
    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            date_to = date_to.replace(hour=23, minute=59, second=59)
        except ValueError:
            date_to = None
    
    # Search logs
    logs = AuditLog.search_logs(
        search_term=search_term,
        table_name=table_name,
        action=action,
        user_id=int(user_id) if user_id else None,
        date_from=date_from,
        date_to=date_to,
        limit=per_page * 10  # Get more for pagination
    )
    
    # Manual pagination (since we're using search method)
    start_idx = (page - 1) * per_page
    end_idx = start_idx + per_page
    paginated_logs = logs[start_idx:end_idx]
    
    # Get unique values for filters
    unique_tables = db.session.query(AuditLog.table_name.distinct()).all()
    unique_actions = db.session.query(AuditLog.action.distinct()).all()
    
    from app.models.user import User
    users = User.query.filter_by(is_active=True).all()
    
    return render_template(
        'audit/logs.html',
        logs=paginated_logs,
        total_logs=len(logs),
        page=page,
        per_page=per_page,
        unique_tables=[t[0] for t in unique_tables],
        unique_actions=[a[0] for a in unique_actions],
        users=users,
        filters={
            'table_name': table_name,
            'action': action,
            'user_id': user_id,
            'date_from': date_from.strftime('%Y-%m-%d') if date_from else '',
            'date_to': date_to.strftime('%Y-%m-%d') if date_to else '',
            'search': search_term
        },
        title='سجلات التدقيق'
    )


@audit_bp.route('/logs/<int:log_id>')
@login_required
@permission_required('admin')
def log_detail(log_id):
    """Audit log details"""
    
    log = AuditLog.query.get_or_404(log_id)
    
    # Get field changes
    field_changes = DataChangeLog.query.filter_by(audit_log_id=log_id).all()
    
    # Get related logs for the same record
    related_logs = []
    if log.record_id:
        related_logs = AuditLog.get_record_history(
            log.table_name, 
            log.record_id, 
            limit=20
        )
        # Remove current log from related
        related_logs = [l for l in related_logs if l.id != log_id]
    
    return render_template(
        'audit/log_detail.html',
        log=log,
        field_changes=field_changes,
        related_logs=related_logs,
        title=f'تفاصيل سجل التدقيق #{log_id}'
    )


@audit_bp.route('/trail/<table_name>/<int:record_id>')
@login_required
@permission_required('admin')
def audit_trail(table_name, record_id):
    """Complete audit trail for a record"""
    
    trail = AuditService.get_audit_trail(table_name, record_id, include_field_changes=True)
    
    return render_template(
        'audit/trail.html',
        trail=trail,
        table_name=table_name,
        record_id=record_id,
        title=f'مسار التدقيق: {table_name}#{record_id}'
    )


@audit_bp.route('/reports')
@login_required
@permission_required('admin')
def reports():
    """Audit reports"""
    
    return render_template(
        'audit/reports.html',
        title='تقارير التدقيق'
    )


@audit_bp.route('/reports/generate', methods=['POST'])
@login_required
@permission_required('admin')
def generate_report():
    """Generate audit report"""
    
    try:
        # Get form parameters
        date_from = datetime.strptime(request.form.get('date_from'), '%Y-%m-%d')
        date_to = datetime.strptime(request.form.get('date_to'), '%Y-%m-%d')
        date_to = date_to.replace(hour=23, minute=59, second=59)
        
        table_name = request.form.get('table_name') or None
        user_id = request.form.get('user_id') or None
        action = request.form.get('action') or None
        include_sensitive = request.form.get('include_sensitive') == 'on'
        report_format = request.form.get('format', 'html')
        
        if user_id:
            user_id = int(user_id)
        
        # Generate report
        report = AuditService.generate_audit_report(
            date_from=date_from,
            date_to=date_to,
            table_name=table_name,
            user_id=user_id,
            action=action,
            include_sensitive=include_sensitive
        )
        
        if report_format == 'json':
            response = make_response(json.dumps(report, ensure_ascii=False, indent=2))
            response.headers['Content-Type'] = 'application/json; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename=audit_report_{date_from.strftime("%Y%m%d")}_{date_to.strftime("%Y%m%d")}.json'
            return response
        
        elif report_format == 'csv':
            # Convert to CSV format
            import csv
            import io
            
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Write headers
            writer.writerow([
                'التاريخ', 'العملية', 'الجدول', 'معرف السجل', 
                'المستخدم', 'عنوان IP', 'الوصف'
            ])
            
            # Write data
            for log in report['logs']:
                writer.writerow([
                    log['created_at'],
                    log['action'],
                    log['table_name'],
                    log['record_id'] or '',
                    log['username'] or '',
                    log['ip_address'] or '',
                    log['description'] or ''
                ])
            
            response = make_response(output.getvalue())
            response.headers['Content-Type'] = 'text/csv; charset=utf-8'
            response.headers['Content-Disposition'] = f'attachment; filename=audit_report_{date_from.strftime("%Y%m%d")}_{date_to.strftime("%Y%m%d")}.csv'
            return response
        
        else:  # HTML format
            return render_template(
                'audit/report.html',
                report=report,
                date_from=date_from,
                date_to=date_to,
                title='تقرير التدقيق'
            )
    
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء التقرير: {str(e)}', 'error')
        return redirect(url_for('audit.reports'))


@audit_bp.route('/security')
@login_required
@permission_required('admin')
def security():
    """Security events dashboard"""
    
    # Get security statistics
    security_summary = SecurityLog.get_event_counts(hours=24)
    
    # Get recent critical events
    critical_events = SecurityLog.get_critical_events(hours=24)
    
    # Get recent events by type
    recent_events = SecurityLog.get_recent_events(hours=24, limit=50)
    
    return render_template(
        'audit/security.html',
        security_summary=security_summary,
        critical_events=critical_events,
        recent_events=recent_events,
        title='الأحداث الأمنية'
    )


@audit_bp.route('/security/<int:event_id>')
@login_required
@permission_required('admin')
def security_detail(event_id):
    """Security event details"""
    
    event = SecurityLog.query.get_or_404(event_id)
    
    # Get related events from same IP or user
    related_events = []
    
    if event.ip_address:
        related_events.extend(
            SecurityLog.get_ip_activity(event.ip_address, hours=24)
        )
    
    if event.user_id:
        related_events.extend(
            SecurityLog.get_user_activity(event.user_id, hours=24)
        )
    
    # Remove duplicates and current event
    seen_ids = set()
    unique_related = []
    for rel_event in related_events:
        if rel_event.id != event_id and rel_event.id not in seen_ids:
            unique_related.append(rel_event)
            seen_ids.add(rel_event.id)
    
    return render_template(
        'audit/security_detail.html',
        event=event,
        related_events=unique_related[:20],  # Limit to 20
        title=f'تفاصيل الحدث الأمني #{event_id}'
    )


@audit_bp.route('/compliance')
@login_required
@permission_required('admin')
def compliance():
    """Compliance dashboard"""
    
    # Get compliance metrics
    today = date.today()
    last_30_days = today - timedelta(days=30)
    
    # Data retention compliance
    old_logs_count = AuditLog.query.filter(
        AuditLog.created_at < datetime.combine(last_30_days, datetime.min.time())
    ).count()
    
    # Security compliance
    failed_logins = SecurityLog.query.filter(
        SecurityLog.event_type == 'failed_login',
        SecurityLog.created_at >= datetime.combine(last_30_days, datetime.min.time())
    ).count()
    
    # Access compliance
    sensitive_access = AuditLog.query.filter(
        AuditLog.is_sensitive == True,
        AuditLog.created_at >= datetime.combine(last_30_days, datetime.min.time())
    ).count()
    
    compliance_metrics = {
        'data_retention': {
            'old_logs_count': old_logs_count,
            'status': 'compliant' if old_logs_count < 1000 else 'warning'
        },
        'security': {
            'failed_logins': failed_logins,
            'status': 'compliant' if failed_logins < 100 else 'warning'
        },
        'access_control': {
            'sensitive_access': sensitive_access,
            'status': 'compliant'
        }
    }
    
    return render_template(
        'audit/compliance.html',
        compliance_metrics=compliance_metrics,
        title='الامتثال والمطابقة'
    )


@audit_bp.route('/cleanup', methods=['POST'])
@login_required
@permission_required('admin')
def cleanup():
    """Clean up old audit logs"""
    
    try:
        days = int(request.form.get('days', 365))
        
        # Clean audit logs
        audit_deleted = AuditLog.cleanup_old_logs(days=days)
        
        # Clean security logs
        security_deleted = SecurityLog.cleanup_old_logs(days=days)
        
        flash(f'تم حذف {audit_deleted} سجل تدقيق و {security_deleted} سجل أمني', 'success')
        
    except Exception as e:
        flash(f'حدث خطأ أثناء التنظيف: {str(e)}', 'error')
    
    return redirect(url_for('audit.index'))


@audit_bp.route('/api/stats')
@login_required
@permission_required('admin')
def api_stats():
    """API endpoint for audit statistics"""
    
    hours = int(request.args.get('hours', 24))
    
    stats = AuditLog.get_statistics(hours=hours)
    security_stats = SecurityLog.get_event_counts(hours=hours)
    
    return jsonify({
        'audit_stats': stats,
        'security_stats': security_stats,
        'period_hours': hours
    })


@audit_bp.route('/api/recent-logs')
@login_required
@permission_required('admin')
def api_recent_logs():
    """API endpoint for recent audit logs"""
    
    limit = int(request.args.get('limit', 20))
    
    logs = AuditLog.query.order_by(
        AuditLog.created_at.desc()
    ).limit(limit).all()
    
    return jsonify({
        'logs': [log.to_dict() for log in logs]
    })


# Context processor for audit navigation
@audit_bp.context_processor
def inject_audit_stats():
    """Inject audit statistics into templates"""
    
    try:
        unresolved_security = SecurityLog.query.filter_by(status='logged').count()
        recent_sensitive = AuditLog.query.filter(
            AuditLog.is_sensitive == True,
            AuditLog.created_at >= datetime.utcnow() - timedelta(hours=24)
        ).count()
        
        return {
            'audit_stats': {
                'unresolved_security': unresolved_security,
                'recent_sensitive': recent_sensitive
            }
        }
    except:
        return {'audit_stats': {'unresolved_security': 0, 'recent_sensitive': 0}}
