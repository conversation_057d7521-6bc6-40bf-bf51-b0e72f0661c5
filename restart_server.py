#!/usr/bin/env python3
"""
إعادة تشغيل خادم SystemTax
Restart SystemTax server
"""

import os
import sys
import time
import subprocess
import signal
import psutil

def find_flask_processes():
    """البحث عن عمليات Flask"""
    flask_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = proc.info['cmdline']
            if cmdline and any('flask' in str(cmd).lower() or 'systemtax' in str(cmd).lower() for cmd in cmdline):
                flask_processes.append(proc.info['pid'])
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return flask_processes

def kill_flask_processes():
    """إنهاء عمليات Flask"""
    processes = find_flask_processes()
    
    if not processes:
        print('🔍 لم يتم العثور على عمليات Flask قيد التشغيل')
        return True
    
    print(f'🔄 إنهاء {len(processes)} عملية Flask...')
    
    for pid in processes:
        try:
            proc = psutil.Process(pid)
            proc.terminate()
            print(f'✅ تم إنهاء العملية {pid}')
        except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
            print(f'⚠️ لا يمكن إنهاء العملية {pid}: {e}')
    
    # انتظار قليل للتأكد من إنهاء العمليات
    time.sleep(2)
    
    # التحقق من إنهاء العمليات
    remaining = find_flask_processes()
    if remaining:
        print(f'⚠️ لا تزال هناك {len(remaining)} عملية قيد التشغيل')
        return False
    else:
        print('✅ تم إنهاء جميع عمليات Flask')
        return True

def start_flask_server():
    """تشغيل خادم Flask"""
    print('🚀 تشغيل خادم SystemTax...')
    
    try:
        # تشغيل الخادم في الخلفية
        process = subprocess.Popen(
            [sys.executable, 'run.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            cwd=os.getcwd()
        )
        
        print(f'✅ تم تشغيل الخادم بمعرف العملية: {process.pid}')
        
        # انتظار قليل للتأكد من بدء التشغيل
        time.sleep(3)
        
        # التحقق من حالة العملية
        if process.poll() is None:
            print('🎉 الخادم يعمل بنجاح!')
            return True
        else:
            stdout, stderr = process.communicate()
            print(f'❌ فشل في تشغيل الخادم:')
            print(f'stdout: {stdout.decode()}')
            print(f'stderr: {stderr.decode()}')
            return False
            
    except Exception as e:
        print(f'❌ خطأ في تشغيل الخادم: {e}')
        return False

def test_server():
    """اختبار الخادم"""
    print('🧪 اختبار الخادم...')
    
    import requests
    
    try:
        # انتظار قليل للتأكد من بدء الخادم
        time.sleep(2)
        
        # اختبار صفحة تسجيل الدخول
        response = requests.get('http://localhost:8000/auth/login', timeout=5)
        
        if response.status_code == 200:
            print('✅ الخادم يستجيب بنجاح!')
            
            # اختبار صفحة Analytics
            analytics_response = requests.get('http://localhost:8000/analytics', timeout=5)
            
            if analytics_response.status_code in [200, 302]:
                print('✅ صفحة Analytics متاحة!')
                return True
            else:
                print(f'⚠️ مشكلة في صفحة Analytics: {analytics_response.status_code}')
                return False
        else:
            print(f'❌ الخادم لا يستجيب: {response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ خطأ في اختبار الخادم: {e}')
        return False

def main():
    """الدالة الرئيسية"""
    print('🔄 إعادة تشغيل SystemTax...')
    print('=' * 40)
    
    # إنهاء العمليات القديمة
    if not kill_flask_processes():
        print('⚠️ قد تكون هناك عمليات لا تزال قيد التشغيل')
    
    print()
    
    # تشغيل الخادم الجديد
    if start_flask_server():
        print()
        
        # اختبار الخادم
        if test_server():
            print()
            print('🎉 تم إعادة تشغيل SystemTax بنجاح!')
            print('✅ فلتر timeago متاح الآن')
            print()
            print('🌐 رابط النظام: http://localhost:8000')
            print('📊 رابط Analytics: http://localhost:8000/analytics')
            print()
            print('🔑 بيانات تسجيل الدخول:')
            print('   👤 المستخدم: admin')
            print('   🔒 كلمة المرور: admin123')
            
            return True
        else:
            print('❌ فشل في اختبار الخادم')
            return False
    else:
        print('❌ فشل في تشغيل الخادم')
        return False

if __name__ == "__main__":
    try:
        success = main()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print('\n⚠️ تم إلغاء العملية')
        exit(1)
    except Exception as e:
        print(f'❌ خطأ غير متوقع: {e}')
        exit(1)
