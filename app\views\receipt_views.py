"""
Receipt management views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort, make_response
from flask_login import login_required, current_user
from sqlalchemy import or_, and_, func, desc
from sqlalchemy.orm import joinedload
from app import db
from app.models.receipt import Receipt
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.models.invoice import Invoice
from app.models.account import Account
from app.forms.receipt_forms import ReceiptForm, ReceiptSearchForm, QuickReceiptForm, ReceiptBulkActionForm
from app.utils.decorators import permission_required
from app.utils.helpers import get_pagination_params, generate_reference_number
from app.services.eta_service import ETAService
from app.services.eta_ereceipt_service import ETAeReceiptService
from decimal import Decimal
from datetime import datetime, date, timedelta

# Create blueprint
receipts_bp = Blueprint('receipts', __name__, url_prefix='/receipts')


@receipts_bp.route('/')
@login_required
@permission_required('receipts')
def index():
    """Display receipts list with search and filtering"""
    form = ReceiptSearchForm(request.args)
    page, per_page = get_pagination_params()
    
    # Build query
    query = Receipt.query.options(
        joinedload(Receipt.customer),
        joinedload(Receipt.vendor),
        joinedload(Receipt.invoice),
        joinedload(Receipt.created_by)
    )
    
    # Apply search filters
    if form.search.data:
        search_term = f"%{form.search.data}%"
        query = query.filter(
            or_(
                Receipt.receipt_number.ilike(search_term),
                Receipt.reference_number.ilike(search_term),
                Receipt.description.ilike(search_term)
            )
        )
        
        # Also search in customer/vendor names
        query = query.outerjoin(Customer, Receipt.customer_id == Customer.id).outerjoin(
            Vendor, Receipt.vendor_id == Vendor.id
        ).filter(
            or_(
                Receipt.receipt_number.ilike(search_term),
                Receipt.reference_number.ilike(search_term),
                Receipt.description.ilike(search_term),
                Customer.name.ilike(search_term),
                Vendor.name.ilike(search_term)
            )
        )
    
    if form.receipt_type.data:
        query = query.filter(Receipt.receipt_type == form.receipt_type.data)
    
    if form.payment_method.data:
        query = query.filter(Receipt.payment_method == form.payment_method.data)
    
    if form.date_from.data:
        query = query.filter(Receipt.receipt_date >= form.date_from.data)
    
    if form.date_to.data:
        query = query.filter(Receipt.receipt_date <= form.date_to.data)
    
    # Order by receipt date (newest first)
    query = query.order_by(desc(Receipt.receipt_date), desc(Receipt.created_at))
    
    # Paginate
    receipts = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Get statistics
    stats = {
        'total_receipts': Receipt.query.count(),
        'receipt_count': Receipt.query.filter_by(receipt_type='receipt').count(),
        'payment_count': Receipt.query.filter_by(receipt_type='payment').count(),
        'total_amount': db.session.query(func.sum(Receipt.amount)).scalar() or Decimal('0')
    }
    
    return render_template(
        'receipts/index.html',
        receipts=receipts,
        form=form,
        stats=stats,
        title='الإيصالات الإلكترونية'
    )


@receipts_bp.route('/new')
@login_required
@permission_required('receipts')
def new():
    """Display form for creating new receipt"""
    form = ReceiptForm()
    
    # Pre-fill receipt type if specified in query params
    receipt_type = request.args.get('type')
    if receipt_type in ['payment', 'receipt']:
        form.receipt_type.data = receipt_type
    
    # Pre-fill customer/vendor if specified
    customer_id = request.args.get('customer_id')
    if customer_id:
        form.customer_id.data = customer_id
        form.receipt_type.data = 'receipt'
    
    vendor_id = request.args.get('vendor_id')
    if vendor_id:
        form.vendor_id.data = vendor_id
        form.receipt_type.data = 'payment'
    
    # Pre-fill invoice if specified
    invoice_id = request.args.get('invoice_id')
    if invoice_id:
        invoice = Invoice.query.get(invoice_id)
        if invoice:
            form.invoice_id.data = invoice_id
            form.customer_id.data = str(invoice.customer_id)
            form.receipt_type.data = 'receipt'
            form.amount.data = invoice.remaining_amount
    
    # Auto-generate receipt number
    if not form.receipt_number.data:
        prefix = 'REC' if form.receipt_type.data == 'receipt' else 'PAY'
        form.receipt_number.data = generate_reference_number(prefix)
    
    return render_template(
        'receipts/form.html',
        form=form,
        title='إيصال جديد'
    )


@receipts_bp.route('/create', methods=['POST'])
@login_required
@permission_required('receipts')
def create():
    """Create new receipt"""
    form = ReceiptForm()
    
    if form.validate_on_submit():
        try:
            # Create receipt
            receipt = Receipt(
                receipt_number=form.receipt_number.data,
                reference_number=form.reference_number.data or None,
                receipt_type=form.receipt_type.data,
                receipt_date=form.receipt_date.data,
                customer_id=form.customer_id.data or None,
                vendor_id=form.vendor_id.data or None,
                invoice_id=form.invoice_id.data or None,
                amount=form.amount.data,
                currency=form.currency.data or 'EGP',
                payment_method=form.payment_method.data,
                account_id=form.account_id.data or None,
                bank_name=form.bank_name.data or None,
                bank_reference=form.bank_reference.data or None,
                check_number=form.check_number.data or None,
                check_date=form.check_date.data or None,
                description=form.description.data or None,
                notes=form.notes.data or None,
                is_confirmed=form.is_confirmed.data,
                created_by_id=current_user.id
            )
            
            # Set confirmation timestamp if confirmed
            if receipt.is_confirmed:
                receipt.confirmed_at = datetime.utcnow()
            
            db.session.add(receipt)
            db.session.commit()
            
            # Check action
            action = request.form.get('action', 'save')
            if action == 'save_and_confirm' and not receipt.is_confirmed:
                receipt.is_confirmed = True
                receipt.confirmed_at = datetime.utcnow()
                db.session.commit()
            
            if action == 'save_and_confirm':
                flash(f'تم إنشاء وتأكيد الإيصال "{receipt.receipt_number}" بنجاح', 'success')
            else:
                flash(f'تم إنشاء الإيصال "{receipt.receipt_number}" بنجاح', 'success')
            
            return redirect(url_for('receipts.detail', receipt_id=receipt.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء الإيصال: {str(e)}', 'error')
    
    return render_template(
        'receipts/form.html',
        form=form,
        title='إيصال جديد'
    )


@receipts_bp.route('/<uuid:receipt_id>')
@login_required
@permission_required('receipts')
def detail(receipt_id):
    """Display receipt details"""
    receipt = Receipt.query.options(
        joinedload(Receipt.customer),
        joinedload(Receipt.vendor),
        joinedload(Receipt.invoice),
        joinedload(Receipt.account),
        joinedload(Receipt.created_by),
        joinedload(Receipt.journal_entry)
    ).get_or_404(receipt_id)
    
    # Get related documents
    related_documents = []
    
    if receipt.invoice:
        related_documents.append({
            'type': 'invoice',
            'number': receipt.invoice.invoice_number,
            'url': url_for('invoices.detail', invoice_id=receipt.invoice.id),
            'date': receipt.invoice.invoice_date
        })
    
    if receipt.journal_entry:
        related_documents.append({
            'type': 'journal',
            'number': receipt.journal_entry.entry_number,
            'url': url_for('journal.detail', entry_id=receipt.journal_entry.id),
            'date': receipt.journal_entry.entry_date
        })
    
    return render_template(
        'receipts/detail.html',
        receipt=receipt,
        related_documents=related_documents,
        title=f'إيصال رقم {receipt.receipt_number}'
    )


@receipts_bp.route('/<uuid:receipt_id>/edit')
@login_required
@permission_required('receipts')
def edit(receipt_id):
    """Display form for editing receipt"""
    receipt = Receipt.query.get_or_404(receipt_id)
    
    if not receipt.can_be_edited():
        flash('لا يمكن تعديل هذا الإيصال', 'error')
        return redirect(url_for('receipts.detail', receipt_id=receipt.id))
    
    form = ReceiptForm(obj=receipt, receipt=receipt)
    
    return render_template(
        'receipts/form.html',
        form=form,
        receipt=receipt,
        title=f'تعديل الإيصال: {receipt.receipt_number}'
    )


@receipts_bp.route('/<uuid:receipt_id>/update', methods=['POST'])
@login_required
@permission_required('receipts')
def update(receipt_id):
    """Update receipt"""
    receipt = Receipt.query.get_or_404(receipt_id)
    
    if not receipt.can_be_edited():
        flash('لا يمكن تعديل هذا الإيصال', 'error')
        return redirect(url_for('receipts.detail', receipt_id=receipt.id))
    
    form = ReceiptForm(receipt=receipt)
    
    if form.validate_on_submit():
        try:
            # Update receipt details
            receipt.receipt_number = form.receipt_number.data
            receipt.reference_number = form.reference_number.data or None
            receipt.receipt_type = form.receipt_type.data
            receipt.receipt_date = form.receipt_date.data
            receipt.customer_id = form.customer_id.data or None
            receipt.vendor_id = form.vendor_id.data or None
            receipt.invoice_id = form.invoice_id.data or None
            receipt.amount = form.amount.data
            receipt.currency = form.currency.data or 'EGP'
            receipt.payment_method = form.payment_method.data
            receipt.account_id = form.account_id.data or None
            receipt.bank_name = form.bank_name.data or None
            receipt.bank_reference = form.bank_reference.data or None
            receipt.check_number = form.check_number.data or None
            receipt.check_date = form.check_date.data or None
            receipt.description = form.description.data or None
            receipt.notes = form.notes.data or None
            receipt.updated_at = datetime.utcnow()
            
            # Handle confirmation
            if form.is_confirmed.data and not receipt.is_confirmed:
                receipt.is_confirmed = True
                receipt.confirmed_at = datetime.utcnow()
            elif not form.is_confirmed.data and receipt.is_confirmed:
                receipt.is_confirmed = False
                receipt.confirmed_at = None
            
            db.session.commit()
            
            flash(f'تم تحديث الإيصال "{receipt.receipt_number}" بنجاح', 'success')
            return redirect(url_for('receipts.detail', receipt_id=receipt.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث الإيصال: {str(e)}', 'error')
    
    return render_template(
        'receipts/form.html',
        form=form,
        receipt=receipt,
        title=f'تعديل الإيصال: {receipt.receipt_number}'
    )


@receipts_bp.route('/<uuid:receipt_id>/delete', methods=['POST'])
@login_required
@permission_required('receipts')
def delete(receipt_id):
    """Delete receipt"""
    receipt = Receipt.query.get_or_404(receipt_id)
    
    if not receipt.can_be_deleted():
        flash('لا يمكن حذف هذا الإيصال', 'error')
        return redirect(url_for('receipts.detail', receipt_id=receipt.id))
    
    try:
        receipt_number = receipt.receipt_number
        db.session.delete(receipt)
        db.session.commit()
        
        flash(f'تم حذف الإيصال "{receipt_number}" بنجاح', 'success')
        return redirect(url_for('receipts.index'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف الإيصال: {str(e)}', 'error')
        return redirect(url_for('receipts.detail', receipt_id=receipt.id))


@receipts_bp.route('/<uuid:receipt_id>/pdf')
@login_required
@permission_required('receipts')
def pdf(receipt_id):
    """Generate PDF for receipt"""
    receipt = Receipt.query.options(
        joinedload(Receipt.customer),
        joinedload(Receipt.vendor),
        joinedload(Receipt.invoice)
    ).get_or_404(receipt_id)
    
    # This would generate PDF using a library like ReportLab or WeasyPrint
    # For now, return a placeholder response
    
    response = make_response("PDF generation not implemented yet")
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = f'inline; filename=receipt_{receipt.receipt_number}.pdf'
    
    return response


@receipts_bp.route('/quick_receipt')
@login_required
@permission_required('receipts')
def quick_receipt():
    """Display quick receipt form"""
    form = QuickReceiptForm()
    
    return render_template(
        'receipts/quick_form.html',
        form=form,
        title='إيصال سريع'
    )


@receipts_bp.route('/bulk_action', methods=['POST'])
@login_required
@permission_required('receipts')
def bulk_action():
    """Execute bulk action on receipts"""
    action = request.form.get('action')
    receipt_ids = request.form.get('receipt_ids', '').split(',')
    
    if not action or not receipt_ids:
        flash('يرجى تحديد إجراء وإيصالات', 'error')
        return redirect(url_for('receipts.index'))
    
    try:
        receipts = Receipt.query.filter(Receipt.id.in_(receipt_ids)).all()
        
        if action == 'confirm':
            for receipt in receipts:
                if not receipt.is_confirmed:
                    receipt.is_confirmed = True
                    receipt.confirmed_at = datetime.utcnow()
            db.session.commit()
            flash(f'تم تأكيد {len(receipts)} إيصال بنجاح', 'success')
            
        elif action == 'delete':
            deletable_receipts = [r for r in receipts if r.can_be_deleted()]
            for receipt in deletable_receipts:
                db.session.delete(receipt)
            db.session.commit()
            flash(f'تم حذف {len(deletable_receipts)} إيصال بنجاح', 'success')
            
            if len(deletable_receipts) < len(receipts):
                flash(f'{len(receipts) - len(deletable_receipts)} إيصال لم يتم حذفها لعدم إمكانية الحذف', 'warning')
        
        elif action == 'generate_pdf':
            # Implement PDF generation
            flash('سيتم تنفيذ إنتاج PDF قريباً', 'info')
        
        elif action == 'export':
            # Implement export functionality
            flash('سيتم تنفيذ التصدير قريباً', 'info')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تنفيذ الإجراء: {str(e)}', 'error')
    
    return redirect(url_for('receipts.index'))


# API Routes
@receipts_bp.route('/<uuid:receipt_id>/confirm', methods=['POST'])
@login_required
@permission_required('receipts')
def confirm(receipt_id):
    """Confirm receipt"""
    receipt = Receipt.query.get_or_404(receipt_id)
    
    try:
        if not receipt.is_confirmed:
            receipt.is_confirmed = True
            receipt.confirmed_at = datetime.utcnow()
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': 'تم تأكيد الإيصال بنجاح'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'الإيصال مؤكد بالفعل'
            }), 400
            
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'حدث خطأ: {str(e)}'
        }), 500


@receipts_bp.route('/export/<format>')
@login_required
@permission_required('receipts')
def export(format):
    """Export receipts in different formats"""
    try:
        if format == 'pdf':
            flash('سيتم تنفيذ تصدير PDF قريباً', 'info')
        elif format == 'excel':
            flash('سيتم تنفيذ تصدير Excel قريباً', 'info')
        elif format == 'csv':
            flash('سيتم تنفيذ تصدير CSV قريباً', 'info')
        else:
            flash('تنسيق التصدير غير مدعوم', 'error')

        return redirect(url_for('receipts.index'))

    except Exception as e:
        flash(f'حدث خطأ أثناء التصدير: {str(e)}', 'error')
        return redirect(url_for('receipts.index'))


@receipts_bp.route('/<uuid:receipt_id>/send-email', methods=['POST'])
@login_required
@permission_required('receipts')
def send_email(receipt_id):
    """Send receipt via email"""
    receipt = Receipt.query.get_or_404(receipt_id)
    
    entity = receipt.customer or receipt.vendor
    if not entity or not entity.email:
        return jsonify({
            'success': False,
            'message': 'العميل/المورد ليس لديه بريد إلكتروني'
        }), 400
    
    try:
        # This would implement actual email sending
        # For now, simulate the process
        
        receipt.email_sent_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': 'تم إرسال الإيصال بالبريد الإلكتروني بنجاح'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء الإرسال: {str(e)}'
        }), 500


# ETA eReceipt Integration Routes
@receipts_bp.route('/<uuid:receipt_id>/submit-eta', methods=['POST'])
@login_required
@permission_required('receipts')
def submit_to_eta(receipt_id):
    """إرسال الإيصال إلى مصلحة الضرائب"""
    receipt = Receipt.query.get_or_404(receipt_id)

    try:
        # التحقق من أن الإيصال لم يتم إرساله من قبل
        if receipt.eta_status in ['submitted', 'accepted']:
            return jsonify({
                'success': False,
                'message': 'تم إرسال هذا الإيصال مسبقاً إلى مصلحة الضرائب'
            }), 400

        # استخدام خدمة الإيصال الإلكتروني
        ereceipt_service = ETAeReceiptService()

        # التحقق من صحة البيانات أولاً
        is_valid, errors = ereceipt_service.validate_receipt_data(receipt)
        if not is_valid:
            return jsonify({
                'success': False,
                'message': 'بيانات الإيصال غير صحيحة',
                'errors': errors
            }), 400

        # إرسال الإيصال
        success, response_data = ereceipt_service.submit_receipt(receipt)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم إرسال الإيصال بنجاح إلى مصلحة الضرائب',
                'eta_uuid': response_data.get('uuid'),
                'eta_status': receipt.eta_status
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في إرسال الإيصال إلى مصلحة الضرائب',
                'error_details': response_data
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إرسال الإيصال: {str(e)}'
        }), 500


@receipts_bp.route('/<uuid:receipt_id>/check-eta-status', methods=['POST'])
@login_required
@permission_required('receipts')
def check_eta_status(receipt_id):
    """التحقق من حالة الإيصال في مصلحة الضرائب"""
    receipt = Receipt.query.get_or_404(receipt_id)

    try:
        if not receipt.eta_uuid:
            return jsonify({
                'success': False,
                'message': 'لم يتم إرسال هذا الإيصال إلى مصلحة الضرائب بعد'
            }), 400

        ereceipt_service = ETAeReceiptService()
        success, response_data = ereceipt_service.check_receipt_status(receipt)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم التحقق من حالة الإيصال بنجاح',
                'eta_status': receipt.eta_status,
                'response_data': response_data
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في التحقق من حالة الإيصال',
                'error_details': response_data
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في التحقق من الحالة: {str(e)}'
        }), 500


@receipts_bp.route('/<uuid:receipt_id>/cancel-eta', methods=['POST'])
@login_required
@permission_required('receipts')
def cancel_eta_receipt(receipt_id):
    """إلغاء الإيصال في مصلحة الضرائب"""
    receipt = Receipt.query.get_or_404(receipt_id)

    try:
        if not receipt.eta_uuid:
            return jsonify({
                'success': False,
                'message': 'لم يتم إرسال هذا الإيصال إلى مصلحة الضرائب'
            }), 400

        if receipt.eta_status == 'cancelled':
            return jsonify({
                'success': False,
                'message': 'تم إلغاء هذا الإيصال مسبقاً'
            }), 400

        reason = request.json.get('reason', 'إلغاء بواسطة المستخدم')

        ereceipt_service = ETAeReceiptService()
        success, response_data = ereceipt_service.cancel_receipt(receipt, reason)

        if success:
            return jsonify({
                'success': True,
                'message': 'تم إلغاء الإيصال بنجاح في مصلحة الضرائب',
                'eta_status': receipt.eta_status
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في إلغاء الإيصال',
                'error_details': response_data
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إلغاء الإيصال: {str(e)}'
        }), 500


@receipts_bp.route('/<uuid:receipt_id>/eta-pdf')
@login_required
@permission_required('receipts')
def get_eta_pdf(receipt_id):
    """الحصول على PDF الإيصال من مصلحة الضرائب"""
    receipt = Receipt.query.get_or_404(receipt_id)

    try:
        if not receipt.eta_uuid:
            flash('لم يتم إرسال هذا الإيصال إلى مصلحة الضرائب', 'error')
            return redirect(url_for('receipts.detail', receipt_id=receipt.id))

        ereceipt_service = ETAeReceiptService()
        success, pdf_content = ereceipt_service.get_receipt_pdf(receipt)

        if success and pdf_content:
            response = make_response(pdf_content)
            response.headers['Content-Type'] = 'application/pdf'
            response.headers['Content-Disposition'] = f'inline; filename=eta_receipt_{receipt.receipt_number}.pdf'
            return response
        else:
            flash('فشل في الحصول على PDF من مصلحة الضرائب', 'error')
            return redirect(url_for('receipts.detail', receipt_id=receipt.id))

    except Exception as e:
        flash(f'خطأ في الحصول على PDF: {str(e)}', 'error')
        return redirect(url_for('receipts.detail', receipt_id=receipt.id))


@receipts_bp.route('/<uuid:receipt_id>/validate-eta', methods=['POST'])
@login_required
@permission_required('receipts')
def validate_eta_data(receipt_id):
    """التحقق من صحة بيانات الإيصال للإرسال إلى مصلحة الضرائب"""
    receipt = Receipt.query.get_or_404(receipt_id)

    try:
        ereceipt_service = ETAeReceiptService()
        is_valid, errors = ereceipt_service.validate_receipt_data(receipt)

        return jsonify({
            'success': True,
            'is_valid': is_valid,
            'errors': errors,
            'message': 'البيانات صحيحة وجاهزة للإرسال' if is_valid else 'توجد أخطاء في البيانات'
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في التحقق من البيانات: {str(e)}'
        }), 500


@receipts_bp.route('/eta-settings')
@login_required
@permission_required('receipts')
def eta_settings():
    """عرض إعدادات التكامل مع مصلحة الضرائب للإيصالات"""
    try:
        ereceipt_service = ETAeReceiptService()

        # الحصول على أنواع الضرائب ووحدات القياس المدعومة
        tax_types = ereceipt_service.get_tax_types()
        unit_types = ereceipt_service.get_unit_types()

        return render_template(
            'receipts/eta_settings.html',
            tax_types=tax_types,
            unit_types=unit_types,
            title='إعدادات الإيصال الإلكتروني'
        )

    except Exception as e:
        flash(f'خطأ في تحميل الإعدادات: {str(e)}', 'error')
        return redirect(url_for('receipts.index'))
