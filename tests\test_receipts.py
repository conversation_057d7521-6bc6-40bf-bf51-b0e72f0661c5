"""
Tests for receipt functionality
"""

import pytest
from decimal import Decimal
from datetime import date, datetime, timedelta
from app import db
from app.models.receipt import Receipt
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.models.invoice import Invoice
from app.models.user import User


class TestReceiptModel:
    """Test Receipt model functionality"""
    
    def test_receipt_creation(self, app, admin_user, test_customer):
        """Test creating a new receipt"""
        with app.app_context():
            receipt = Receipt(
                receipt_number='REC-2024-001',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('1000.00'),
                payment_method='cash',
                currency='EGP',
                created_by_id=admin_user.id
            )
            db.session.add(receipt)
            db.session.commit()
            
            assert receipt.id is not None
            assert receipt.receipt_number == 'REC-2024-001'
            assert receipt.receipt_type == 'receipt'
            assert receipt.customer == test_customer
            assert receipt.amount == Decimal('1000.00')
            assert receipt.created_by == admin_user
    
    def test_payment_receipt_creation(self, app, admin_user, test_vendor):
        """Test creating a payment receipt"""
        with app.app_context():
            receipt = Receipt(
                receipt_number='PAY-2024-001',
                receipt_type='payment',
                vendor_id=test_vendor.id,
                receipt_date=date.today(),
                amount=Decimal('500.00'),
                payment_method='bank_transfer',
                bank_reference='TXN123456',
                currency='EGP',
                created_by_id=admin_user.id
            )
            db.session.add(receipt)
            db.session.commit()
            
            assert receipt.id is not None
            assert receipt.receipt_type == 'payment'
            assert receipt.vendor == test_vendor
            assert receipt.bank_reference == 'TXN123456'
    
    def test_receipt_type_display(self, app, admin_user, test_customer):
        """Test receipt type display methods"""
        with app.app_context():
            receipt_in = Receipt(
                receipt_number='REC-001',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            
            payment_out = Receipt(
                receipt_number='PAY-001',
                receipt_type='payment',
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            
            assert receipt_in.get_type_display() == 'إيصال قبض'
            assert payment_out.get_type_display() == 'إيصال دفع'
    
    def test_payment_method_display(self, app, admin_user, test_customer):
        """Test payment method display methods"""
        with app.app_context():
            receipt = Receipt(
                receipt_number='REC-002',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='bank_transfer',
                created_by_id=admin_user.id
            )
            
            assert receipt.get_payment_method_display() == 'تحويل بنكي'
    
    def test_receipt_can_be_edited(self, app, admin_user, test_customer):
        """Test receipt edit permissions"""
        with app.app_context():
            # Unconfirmed receipt can be edited
            unconfirmed_receipt = Receipt(
                receipt_number='REC-003',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                is_confirmed=False,
                created_by_id=admin_user.id
            )
            assert unconfirmed_receipt.can_be_edited() is True
            
            # Confirmed receipt cannot be edited
            confirmed_receipt = Receipt(
                receipt_number='REC-004',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                is_confirmed=True,
                confirmed_at=datetime.utcnow(),
                created_by_id=admin_user.id
            )
            assert confirmed_receipt.can_be_edited() is False
    
    def test_receipt_can_be_deleted(self, app, admin_user, test_customer):
        """Test receipt deletion permissions"""
        with app.app_context():
            # Unconfirmed receipt can be deleted
            unconfirmed_receipt = Receipt(
                receipt_number='REC-005',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                is_confirmed=False,
                created_by_id=admin_user.id
            )
            assert unconfirmed_receipt.can_be_deleted() is True
            
            # Confirmed receipt cannot be deleted
            confirmed_receipt = Receipt(
                receipt_number='REC-006',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                is_confirmed=True,
                confirmed_at=datetime.utcnow(),
                created_by_id=admin_user.id
            )
            assert confirmed_receipt.can_be_deleted() is False
    
    def test_receipt_with_invoice(self, app, admin_user, test_customer):
        """Test receipt linked to invoice"""
        with app.app_context():
            # Create invoice first
            invoice = Invoice(
                invoice_number='INV-001',
                customer_id=test_customer.id,
                invoice_date=date.today(),
                total_amount=Decimal('1000.00'),
                status='pending',
                created_by_id=admin_user.id
            )
            db.session.add(invoice)
            db.session.flush()
            
            # Create receipt for invoice
            receipt = Receipt(
                receipt_number='REC-007',
                receipt_type='receipt',
                customer_id=test_customer.id,
                invoice_id=invoice.id,
                receipt_date=date.today(),
                amount=Decimal('500.00'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            db.session.add(receipt)
            db.session.commit()
            
            assert receipt.invoice == invoice
            assert receipt.customer == test_customer
    
    def test_amount_in_words(self, app, admin_user, test_customer):
        """Test amount in words conversion"""
        with app.app_context():
            receipt = Receipt(
                receipt_number='REC-008',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('1500.50'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            
            # This is a simplified test - actual implementation would be more complex
            words = receipt.amount_in_words()
            assert isinstance(words, str)
            assert len(words) > 0


class TestReceiptViews:
    """Test receipt views and forms"""
    
    def test_receipts_index_page(self, client, admin_user, auth):
        """Test receipts index page"""
        auth.login()
        response = client.get('/receipts/')
        assert response.status_code == 200
        assert 'الإيصالات الإلكترونية' in response.get_data(as_text=True)
    
    def test_receipt_creation_form(self, client, admin_user, auth):
        """Test receipt creation form"""
        auth.login()
        
        # Get form page
        response = client.get('/receipts/new')
        assert response.status_code == 200
        assert 'إيصال جديد' in response.get_data(as_text=True)
    
    def test_receipt_creation(self, client, admin_user, auth, test_customer):
        """Test creating receipt through form"""
        auth.login()
        
        # Submit receipt form
        response = client.post('/receipts/create', data={
            'receipt_number': 'TEST-REC-001',
            'receipt_type': 'receipt',
            'customer_id': str(test_customer.id),
            'receipt_date': date.today().isoformat(),
            'amount': '1000.00',
            'payment_method': 'cash',
            'currency': 'EGP',
            'description': 'إيصال اختبار',
            'action': 'save'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        
        # Check if receipt was created
        receipt = Receipt.query.filter_by(receipt_number='TEST-REC-001').first()
        assert receipt is not None
        assert receipt.customer == test_customer
    
    def test_payment_creation(self, client, admin_user, auth, test_vendor):
        """Test creating payment receipt"""
        auth.login()
        
        # Submit payment form
        response = client.post('/receipts/create', data={
            'receipt_number': 'TEST-PAY-001',
            'receipt_type': 'payment',
            'vendor_id': str(test_vendor.id),
            'receipt_date': date.today().isoformat(),
            'amount': '500.00',
            'payment_method': 'bank_transfer',
            'bank_reference': 'TXN123',
            'currency': 'EGP',
            'description': 'دفعة اختبار',
            'action': 'save'
        }, follow_redirects=True)
        
        assert response.status_code == 200
        
        # Check if payment was created
        payment = Receipt.query.filter_by(receipt_number='TEST-PAY-001').first()
        assert payment is not None
        assert payment.vendor == test_vendor
        assert payment.receipt_type == 'payment'
    
    def test_receipt_search(self, client, admin_user, auth, test_customer):
        """Test receipt search functionality"""
        auth.login()
        
        # Create test receipt
        with client.application.app_context():
            receipt = Receipt(
                receipt_number='SEARCH-001',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            db.session.add(receipt)
            db.session.commit()
        
        response = client.get('/receipts/?search=SEARCH-001')
        assert response.status_code == 200
        assert 'SEARCH-001' in response.get_data(as_text=True)
    
    def test_receipt_detail_page(self, client, admin_user, auth, test_customer):
        """Test receipt detail page"""
        auth.login()
        
        # Create test receipt
        with client.application.app_context():
            receipt = Receipt(
                receipt_number='DETAIL-001',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            db.session.add(receipt)
            db.session.commit()
            receipt_id = receipt.id
        
        response = client.get(f'/receipts/{receipt_id}')
        assert response.status_code == 200
        assert 'DETAIL-001' in response.get_data(as_text=True)
    
    def test_receipt_confirmation(self, client, admin_user, auth, test_customer):
        """Test receipt confirmation"""
        auth.login()
        
        # Create unconfirmed receipt
        with client.application.app_context():
            receipt = Receipt(
                receipt_number='CONFIRM-001',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                is_confirmed=False,
                created_by_id=admin_user.id
            )
            db.session.add(receipt)
            db.session.commit()
            receipt_id = receipt.id
        
        # Confirm receipt via API
        response = client.post(f'/receipts/{receipt_id}/confirm', 
                              headers={'Content-Type': 'application/json'})
        assert response.status_code == 200
        
        data = response.get_json()
        assert data['success'] is True
        
        # Check if receipt was confirmed
        with client.application.app_context():
            receipt = Receipt.query.get(receipt_id)
            assert receipt.is_confirmed is True
            assert receipt.confirmed_at is not None


class TestReceiptPermissions:
    """Test receipt access permissions"""
    
    def test_admin_access(self, client, admin_user, auth):
        """Test admin user access to receipts"""
        auth.login('admin', 'admin123')
        
        response = client.get('/receipts/')
        assert response.status_code == 200
    
    def test_accountant_access(self, client, accountant_user, auth):
        """Test accountant user access to receipts"""
        auth.login('accountant', 'acc123')
        
        response = client.get('/receipts/')
        assert response.status_code == 200
    
    def test_employee_access(self, client, employee_user, auth):
        """Test employee user access to receipts"""
        auth.login('employee', 'emp123')
        
        # Employees should not have access to receipts
        response = client.get('/receipts/')
        assert response.status_code == 302  # Redirect to login or access denied


class TestReceiptBusinessLogic:
    """Test receipt business logic"""
    
    def test_receipt_validation(self, app, admin_user, test_customer, test_vendor):
        """Test receipt validation rules"""
        with app.app_context():
            # Receipt type 'receipt' should have customer
            receipt = Receipt(
                receipt_number='VAL-001',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            db.session.add(receipt)
            db.session.commit()
            
            assert receipt.customer is not None
            assert receipt.vendor is None
            
            # Receipt type 'payment' should have vendor
            payment = Receipt(
                receipt_number='VAL-002',
                receipt_type='payment',
                vendor_id=test_vendor.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            db.session.add(payment)
            db.session.commit()
            
            assert payment.vendor is not None
            assert payment.customer is None
    
    def test_check_payment_details(self, app, admin_user, test_customer):
        """Test check payment specific fields"""
        with app.app_context():
            receipt = Receipt(
                receipt_number='CHECK-001',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('1000.00'),
                payment_method='check',
                check_number='123456',
                check_date=date.today(),
                bank_name='البنك الأهلي المصري',
                created_by_id=admin_user.id
            )
            db.session.add(receipt)
            db.session.commit()
            
            assert receipt.payment_method == 'check'
            assert receipt.check_number == '123456'
            assert receipt.bank_name == 'البنك الأهلي المصري'
    
    def test_bank_transfer_details(self, app, admin_user, test_vendor):
        """Test bank transfer specific fields"""
        with app.app_context():
            receipt = Receipt(
                receipt_number='TRANSFER-001',
                receipt_type='payment',
                vendor_id=test_vendor.id,
                receipt_date=date.today(),
                amount=Decimal('2000.00'),
                payment_method='bank_transfer',
                bank_name='بنك مصر',
                bank_reference='TXN789456',
                created_by_id=admin_user.id
            )
            db.session.add(receipt)
            db.session.commit()
            
            assert receipt.payment_method == 'bank_transfer'
            assert receipt.bank_reference == 'TXN789456'
            assert receipt.bank_name == 'بنك مصر'
    
    def test_receipt_unique_number(self, app, admin_user, test_customer):
        """Test receipt number uniqueness"""
        with app.app_context():
            # Create first receipt
            receipt1 = Receipt(
                receipt_number='UNIQUE-001',
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('100.00'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            db.session.add(receipt1)
            db.session.commit()
            
            # Try to create second receipt with same number
            receipt2 = Receipt(
                receipt_number='UNIQUE-001',  # Same number
                receipt_type='receipt',
                customer_id=test_customer.id,
                receipt_date=date.today(),
                amount=Decimal('200.00'),
                payment_method='cash',
                created_by_id=admin_user.id
            )
            db.session.add(receipt2)
            
            # This should raise an integrity error
            with pytest.raises(Exception):
                db.session.commit()


if __name__ == '__main__':
    pytest.main([__file__])
