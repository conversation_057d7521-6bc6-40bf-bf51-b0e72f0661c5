"""
Forms for vendor management
"""

from flask_wtf import FlaskForm
from wtforms import (
    StringField, TextAreaField, SelectField, DecimalField, 
    IntegerField, BooleanField, EmailField, URLField
)
from wtforms.validators import DataRequired, Length, Optional, Email, URL, NumberRange, ValidationError
from app.models.vendor import Vendor
from app.models.account import Account


class VendorForm(FlaskForm):
    """Form for creating and editing vendors"""
    
    name = StringField(
        'اسم المورد',
        validators=[
            DataRequired(message='اسم المورد مطلوب'),
            Length(min=2, max=200, message='اسم المورد يجب أن يكون بين 2 و 200 حرف')
        ],
        render_kw={
            'placeholder': 'اسم المورد',
            'class': 'form-control'
        }
    )
    
    name_en = StringField(
        'الاسم بالإنجليزية',
        validators=[
            Optional(),
            Length(max=200, message='الاسم بالإنجليزية يجب ألا يزيد عن 200 حرف')
        ],
        render_kw={
            'placeholder': 'Vendor name in English',
            'class': 'form-control',
            'dir': 'ltr'
        }
    )
    
    vendor_type = SelectField(
        'نوع المورد',
        choices=[
            ('', 'اختر نوع المورد'),
            ('individual', 'فرد'),
            ('company', 'شركة')
        ],
        validators=[DataRequired(message='نوع المورد مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    tax_id = StringField(
        'الرقم الضريبي',
        validators=[
            Optional(),
            Length(max=50, message='الرقم الضريبي يجب ألا يزيد عن 50 حرف')
        ],
        render_kw={
            'placeholder': 'الرقم الضريبي أو السجل التجاري',
            'class': 'form-control'
        }
    )
    
    email = EmailField(
        'البريد الإلكتروني',
        validators=[
            Optional(),
            Email(message='البريد الإلكتروني غير صحيح')
        ],
        render_kw={
            'placeholder': '<EMAIL>',
            'class': 'form-control',
            'dir': 'ltr'
        }
    )
    
    phone = StringField(
        'رقم الهاتف',
        validators=[
            Optional(),
            Length(max=20, message='رقم الهاتف يجب ألا يزيد عن 20 رقم')
        ],
        render_kw={
            'placeholder': '01234567890',
            'class': 'form-control',
            'dir': 'ltr'
        }
    )
    
    website = URLField(
        'الموقع الإلكتروني',
        validators=[
            Optional(),
            URL(message='الموقع الإلكتروني غير صحيح')
        ],
        render_kw={
            'placeholder': 'https://www.example.com',
            'class': 'form-control',
            'dir': 'ltr'
        }
    )
    
    address = TextAreaField(
        'العنوان',
        validators=[
            Optional(),
            Length(max=500, message='العنوان يجب ألا يزيد عن 500 حرف')
        ],
        render_kw={
            'placeholder': 'العنوان التفصيلي',
            'class': 'form-control',
            'rows': 3
        }
    )
    
    city = StringField(
        'المدينة',
        validators=[
            Optional(),
            Length(max=100, message='المدينة يجب ألا تزيد عن 100 حرف')
        ],
        render_kw={
            'placeholder': 'القاهرة',
            'class': 'form-control'
        }
    )
    
    state = StringField(
        'المحافظة',
        validators=[
            Optional(),
            Length(max=100, message='المحافظة يجب ألا تزيد عن 100 حرف')
        ],
        render_kw={
            'placeholder': 'القاهرة',
            'class': 'form-control'
        }
    )
    
    postal_code = StringField(
        'الرمز البريدي',
        validators=[
            Optional(),
            Length(max=20, message='الرمز البريدي يجب ألا يزيد عن 20 حرف')
        ],
        render_kw={
            'placeholder': '12345',
            'class': 'form-control'
        }
    )
    
    country = SelectField(
        'البلد',
        choices=[
            ('', 'اختر البلد'),
            ('EG', 'مصر'),
            ('SA', 'السعودية'),
            ('AE', 'الإمارات'),
            ('KW', 'الكويت'),
            ('QA', 'قطر'),
            ('BH', 'البحرين'),
            ('OM', 'عمان'),
            ('JO', 'الأردن'),
            ('LB', 'لبنان'),
            ('SY', 'سوريا'),
            ('IQ', 'العراق'),
            ('YE', 'اليمن'),
            ('LY', 'ليبيا'),
            ('TN', 'تونس'),
            ('DZ', 'الجزائر'),
            ('MA', 'المغرب'),
            ('SD', 'السودان')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    credit_limit = DecimalField(
        'حد الائتمان',
        validators=[
            Optional(),
            NumberRange(min=0, message='حد الائتمان يجب أن يكون أكبر من أو يساوي صفر')
        ],
        places=2,
        render_kw={
            'placeholder': '0.00',
            'class': 'form-control',
            'step': '0.01',
            'min': '0'
        }
    )
    
    payment_terms = IntegerField(
        'شروط الدفع (بالأيام)',
        validators=[
            Optional(),
            NumberRange(min=0, max=365, message='شروط الدفع يجب أن تكون بين 0 و 365 يوم')
        ],
        render_kw={
            'placeholder': '30',
            'class': 'form-control',
            'min': '0',
            'max': '365'
        }
    )
    
    currency = SelectField(
        'العملة',
        choices=[
            ('EGP', 'جنيه مصري'),
            ('USD', 'دولار أمريكي'),
            ('EUR', 'يورو'),
            ('SAR', 'ريال سعودي'),
            ('AED', 'درهم إماراتي'),
            ('KWD', 'دينار كويتي'),
            ('QAR', 'ريال قطري'),
            ('BHD', 'دينار بحريني'),
            ('OMR', 'ريال عماني')
        ],
        default='EGP',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    account_id = SelectField(
        'الحساب المحاسبي',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[
            Optional(),
            Length(max=1000, message='الملاحظات يجب ألا تزيد عن 1000 حرف')
        ],
        render_kw={
            'placeholder': 'ملاحظات إضافية عن المورد...',
            'class': 'form-control',
            'rows': 4
        }
    )
    
    is_active = BooleanField(
        'مورد نشط',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    def __init__(self, vendor=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.vendor = vendor
        self.populate_account_choices()
    
    def populate_account_choices(self):
        """Populate account choices for vendors"""
        choices = [('', 'اختر الحساب (اختياري)')]
        
        # Get vendor accounts (typically under Accounts Payable)
        accounts = Account.query.filter(
            Account.is_active == True,
            Account.type == 'Liability'  # Vendor accounts are usually liabilities
        ).order_by(Account.code).all()
        
        for account in accounts:
            choices.append((
                str(account.id),
                f"{account.code} - {account.name}"
            ))
        
        self.account_id.choices = choices
    
    def validate_tax_id(self, field):
        """Validate tax ID uniqueness"""
        if field.data:
            query = Vendor.query.filter_by(tax_id=field.data)
            if self.vendor:
                query = query.filter(Vendor.id != self.vendor.id)
            
            if query.first():
                raise ValidationError('الرقم الضريبي موجود بالفعل')
    
    def validate_email(self, field):
        """Validate email uniqueness"""
        if field.data:
            query = Vendor.query.filter_by(email=field.data)
            if self.vendor:
                query = query.filter(Vendor.id != self.vendor.id)
            
            if query.first():
                raise ValidationError('البريد الإلكتروني موجود بالفعل')


class VendorSearchForm(FlaskForm):
    """Form for searching vendors"""
    
    search = StringField(
        'البحث',
        validators=[Optional()],
        render_kw={
            'placeholder': 'البحث في اسم المورد أو الرقم الضريبي...',
            'class': 'form-control'
        }
    )
    
    vendor_type = SelectField(
        'نوع المورد',
        choices=[
            ('', 'جميع الأنواع'),
            ('individual', 'فرد'),
            ('company', 'شركة')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    status = SelectField(
        'الحالة',
        choices=[
            ('', 'جميع الحالات'),
            ('active', 'نشط'),
            ('inactive', 'غير نشط')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )


class VendorImportForm(FlaskForm):
    """Form for importing vendors from file"""
    
    import_type = SelectField(
        'نوع الاستيراد',
        choices=[
            ('csv', 'ملف CSV'),
            ('excel', 'ملف Excel'),
            ('json', 'ملف JSON')
        ],
        validators=[DataRequired(message='نوع الاستيراد مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    file_content = TextAreaField(
        'محتوى الملف',
        validators=[DataRequired(message='محتوى الملف مطلوب')],
        render_kw={
            'placeholder': 'الصق محتوى الملف هنا...',
            'class': 'form-control',
            'rows': 10
        }
    )
    
    update_existing = BooleanField(
        'تحديث الموردين الموجودين',
        render_kw={'class': 'form-check-input'}
    )
    
    create_accounts = BooleanField(
        'إنشاء حسابات محاسبية تلقائياً',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class VendorBulkActionForm(FlaskForm):
    """Form for bulk actions on vendors"""
    
    action = SelectField(
        'الإجراء',
        choices=[
            ('', 'اختر الإجراء'),
            ('activate', 'تفعيل'),
            ('deactivate', 'إلغاء تفعيل'),
            ('delete', 'حذف'),
            ('export', 'تصدير'),
            ('send_statements', 'إرسال كشوف حساب')
        ],
        validators=[DataRequired(message='الإجراء مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    selected_vendors = StringField(
        'الموردين المحددين',
        validators=[DataRequired(message='يجب تحديد مورد واحد على الأقل')]
    )
    
    confirmation = BooleanField(
        'أؤكد تنفيذ هذا الإجراء',
        validators=[DataRequired(message='يجب تأكيد الإجراء')],
        render_kw={'class': 'form-check-input'}
    )
