{% extends "base.html" %}

{% block title %}التكامل مع مصلحة الضرائب المصرية{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-university me-2"></i>التكامل مع مصلحة الضرائب المصرية</h2>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="testConnection()">
                        <i class="fas fa-plug me-2"></i>اختبار الاتصال
                    </button>
                    <a href="{{ url_for('eta.settings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-cog me-2"></i>الإعدادات
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات التكامل -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_transactions }}</h4>
                            <p class="card-text">إجمالي المعاملات</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exchange-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ pending_transactions }}</h4>
                            <p class="card-text">معاملات معلقة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ successful_transactions }}</h4>
                            <p class="card-text">معاملات ناجحة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ failed_transactions }}</h4>
                            <p class="card-text">معاملات فاشلة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-times-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإعدادات الحالية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>الإعدادات الحالية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>البيئة:</strong>
                        </div>
                        <div class="col-sm-6">
                            <span class="badge bg-{{ 'success' if settings.environment == 'production' else 'warning' }}">
                                {{ 'الإنتاج' if settings.environment == 'production' else 'التجريب' }}
                            </span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>الرقم الضريبي:</strong>
                        </div>
                        <div class="col-sm-6">
                            {{ settings.company_tax_id or 'غير محدد' }}
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>كود النشاط:</strong>
                        </div>
                        <div class="col-sm-6">
                            {{ settings.company_activity_code or 'غير محدد' }}
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>الإرسال التلقائي:</strong>
                        </div>
                        <div class="col-sm-6">
                            <span class="badge bg-{{ 'success' if settings.auto_submit == 'true' else 'secondary' }}">
                                {{ 'مفعل' if settings.auto_submit == 'true' else 'معطل' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools me-2"></i>أدوات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('eta.transactions') }}" class="btn btn-outline-primary">
                            <i class="fas fa-list me-2"></i>عرض جميع المعاملات
                        </a>
                        <a href="{{ url_for('eta.error_logs') }}" class="btn btn-outline-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>سجل الأخطاء
                        </a>
                        <a href="{{ url_for('eta.tax_codes') }}" class="btn btn-outline-info">
                            <i class="fas fa-barcode me-2"></i>أكواد الضرائب
                        </a>
                        <button type="button" class="btn btn-outline-success" onclick="syncTaxCodes()">
                            <i class="fas fa-sync me-2"></i>مزامنة الأكواد
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- آخر المعاملات -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>آخر المعاملات
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_transactions %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>رقم المستند</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإرسال</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions %}
                                <tr>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if transaction.transaction_type == 'invoice' else 'info' }}">
                                            {{ 'فاتورة' if transaction.transaction_type == 'invoice' else 'إيصال' }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if transaction.invoice %}
                                            {{ transaction.invoice.invoice_number }}
                                        {% elif transaction.receipt %}
                                            {{ transaction.receipt.receipt_number }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if transaction.response_status == 'accepted' else 'warning' if transaction.response_status == 'pending' else 'danger' }}">
                                            {% if transaction.response_status == 'accepted' %}
                                                مقبول
                                            {% elif transaction.response_status == 'pending' %}
                                                معلق
                                            {% elif transaction.response_status == 'submitted' %}
                                                مرسل
                                            {% else %}
                                                فاشل
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>{{ transaction.sent_at.strftime('%Y-%m-%d %H:%M') if transaction.sent_at else '-' }}</td>
                                    <td>
                                        <a href="{{ url_for('eta.transaction_details', transaction_id=transaction.id) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if transaction.submission_uuid %}
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="checkStatus('{{ transaction.submission_uuid }}')">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد معاملات حتى الآن</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function testConnection() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الاختبار...';
    btn.disabled = true;
    
    fetch('{{ url_for("eta.test_connection") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            showAlert('danger', 'خطأ في الاتصال: ' + error.message);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

function syncTaxCodes() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المزامنة...';
    btn.disabled = true;
    
    fetch('{{ url_for("eta.sync_tax_codes") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('success', data.message);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            showAlert('danger', 'خطأ في المزامنة: ' + error.message);
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

function checkStatus(submissionUuid) {
    fetch(`{{ url_for("eta.check_submission_status", submission_uuid="") }}${submissionUuid}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('info', `حالة الإرسال: ${data.status}`);
                // إعادة تحميل الصفحة لتحديث البيانات
                setTimeout(() => location.reload(), 2000);
            } else {
                showAlert('danger', data.message);
            }
        })
        .catch(error => {
            showAlert('danger', 'خطأ في التحقق من الحالة: ' + error.message);
        });
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
