"""
Egyptian Tax Authority API integration
"""

import requests
import json
from datetime import datetime
from flask import current_app
from app.models.system_setting import SystemSetting
from app.models.tax_transaction import TaxTransaction

class EgyptianTaxAPI:
    """Egyptian Tax Authority API client"""
    
    def __init__(self):
        self.settings = SystemSetting.get_tax_api_settings()
        self.base_url = self.settings['base_url']
        self.client_id = self.settings['client_id']
        self.client_secret = self.settings['client_secret']
        self.environment = self.settings['environment']
        self.access_token = None
    
    def authenticate(self):
        """Authenticate with tax authority API"""
        if not self.client_id or not self.client_secret:
            raise Exception("Tax API credentials not configured")
        
        auth_url = f"{self.base_url}/connect/token"
        
        auth_data = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'InvoicingAPI'
        }
        
        try:
            response = requests.post(auth_url, data=auth_data, timeout=30)
            response.raise_for_status()
            
            token_data = response.json()
            self.access_token = token_data.get('access_token')
            
            if not self.access_token:
                raise Exception("Failed to get access token")
            
            return True
            
        except requests.exceptions.RequestException as e:
            current_app.logger.error(f"Tax API authentication failed: {str(e)}")
            return False
        except Exception as e:
            current_app.logger.error(f"Tax API authentication error: {str(e)}")
            return False
    
    def get_headers(self):
        """Get API request headers"""
        if not self.access_token:
            if not self.authenticate():
                raise Exception("Failed to authenticate with tax authority")
        
        return {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    
    def submit_invoice(self, invoice):
        """Submit invoice to tax authority"""
        try:
            # Prepare invoice data
            invoice_data = self.prepare_invoice_data(invoice)
            
            # Create tax transaction record
            tax_transaction = TaxTransaction.create_for_invoice(
                invoice.id, 
                'submit'
            )
            tax_transaction.request_data = invoice_data
            
            # Submit to API (mock implementation for now)
            if self.environment == 'sandbox':
                # Mock response for sandbox
                response_data = self.mock_submit_response(invoice_data)
                success = True
            else:
                # Real API call
                submit_url = f"{self.base_url}/api/v1/invoices/submit"
                
                response = requests.post(
                    submit_url,
                    headers=self.get_headers(),
                    json=invoice_data,
                    timeout=60
                )
                
                response.raise_for_status()
                response_data = response.json()
                success = response_data.get('success', False)
            
            # Update tax transaction
            if success:
                tax_invoice_number = response_data.get('taxInvoiceNumber')
                tax_transaction.mark_as_success(response_data, tax_invoice_number)
                
                # Update invoice
                invoice.tax_status = 'accepted'
                
                return {
                    'success': True,
                    'tax_invoice_number': tax_invoice_number,
                    'message': 'تم إرسال الفاتورة بنجاح'
                }
            else:
                error_message = response_data.get('message', 'فشل في إرسال الفاتورة')
                tax_transaction.mark_as_failed(error_message, response_data)
                
                return {
                    'success': False,
                    'message': error_message
                }
        
        except requests.exceptions.RequestException as e:
            error_message = f"خطأ في الاتصال: {str(e)}"
            if 'tax_transaction' in locals():
                tax_transaction.mark_as_failed(error_message)
            
            current_app.logger.error(f"Tax API submit error: {str(e)}")
            return {
                'success': False,
                'message': error_message
            }
        
        except Exception as e:
            error_message = f"خطأ غير متوقع: {str(e)}"
            if 'tax_transaction' in locals():
                tax_transaction.mark_as_failed(error_message)
            
            current_app.logger.error(f"Tax API error: {str(e)}")
            return {
                'success': False,
                'message': error_message
            }
    
    def prepare_invoice_data(self, invoice):
        """Prepare invoice data for tax authority API"""
        company_info = SystemSetting.get_company_info()
        
        # Prepare invoice lines
        lines = []
        for line in invoice.lines:
            lines.append({
                'description': line.description,
                'unitPrice': float(line.unit_price),
                'quantity': float(line.quantity),
                'discountPercent': float(line.discount_percent),
                'taxPercent': float(line.tax_percent),
                'lineTotal': float(line.line_total)
            })
        
        # Prepare main invoice data
        invoice_data = {
            'issuer': {
                'name': company_info['name'],
                'taxId': company_info['tax_id'],
                'address': company_info['address'],
                'phone': company_info['phone'],
                'email': company_info['email']
            },
            'receiver': {
                'name': invoice.customer.name,
                'taxId': invoice.customer.tax_id,
                'address': invoice.customer.address,
                'phone': invoice.customer.phone,
                'email': invoice.customer.email
            },
            'invoice': {
                'internalId': invoice.invoice_number,
                'issueDate': invoice.issue_date.isoformat(),
                'dueDate': invoice.due_date.isoformat() if invoice.due_date else None,
                'currencyCode': 'EGP',
                'subtotal': float(invoice.subtotal),
                'discountAmount': float(invoice.discount_amount),
                'taxAmount': float(invoice.tax_amount),
                'totalAmount': float(invoice.total_amount),
                'lines': lines
            }
        }
        
        return invoice_data
    
    def mock_submit_response(self, invoice_data):
        """Mock response for sandbox environment"""
        import random
        import string
        
        # Generate mock tax invoice number
        tax_invoice_number = ''.join(random.choices(string.digits, k=10))
        
        return {
            'success': True,
            'taxInvoiceNumber': tax_invoice_number,
            'submissionId': ''.join(random.choices(string.ascii_uppercase + string.digits, k=8)),
            'message': 'Invoice submitted successfully',
            'timestamp': datetime.utcnow().isoformat()
        }
    
    def query_invoice_status(self, tax_invoice_number):
        """Query invoice status from tax authority"""
        try:
            if self.environment == 'sandbox':
                # Mock response for sandbox
                return {
                    'success': True,
                    'status': 'accepted',
                    'message': 'Invoice is accepted'
                }
            else:
                # Real API call
                query_url = f"{self.base_url}/api/v1/invoices/{tax_invoice_number}/status"
                
                response = requests.get(
                    query_url,
                    headers=self.get_headers(),
                    timeout=30
                )
                
                response.raise_for_status()
                return response.json()
        
        except Exception as e:
            current_app.logger.error(f"Tax API query error: {str(e)}")
            return {
                'success': False,
                'message': f"خطأ في الاستعلام: {str(e)}"
            }
    
    def cancel_invoice(self, tax_invoice_number, reason):
        """Cancel invoice in tax authority"""
        try:
            cancel_data = {
                'taxInvoiceNumber': tax_invoice_number,
                'reason': reason
            }
            
            if self.environment == 'sandbox':
                # Mock response for sandbox
                return {
                    'success': True,
                    'message': 'Invoice cancelled successfully'
                }
            else:
                # Real API call
                cancel_url = f"{self.base_url}/api/v1/invoices/cancel"
                
                response = requests.post(
                    cancel_url,
                    headers=self.get_headers(),
                    json=cancel_data,
                    timeout=30
                )
                
                response.raise_for_status()
                return response.json()
        
        except Exception as e:
            current_app.logger.error(f"Tax API cancel error: {str(e)}")
            return {
                'success': False,
                'message': f"خطأ في الإلغاء: {str(e)}"
            }

# Convenience functions
def submit_invoice_to_tax_authority(invoice):
    """Submit invoice to Egyptian Tax Authority"""
    api = EgyptianTaxAPI()
    return api.submit_invoice(invoice)

def query_tax_invoice_status(tax_invoice_number):
    """Query invoice status from tax authority"""
    api = EgyptianTaxAPI()
    return api.query_invoice_status(tax_invoice_number)

def cancel_tax_invoice(tax_invoice_number, reason):
    """Cancel invoice in tax authority"""
    api = EgyptianTaxAPI()
    return api.cancel_invoice(tax_invoice_number, reason)
