/**
 * SystemTax - Main JavaScript Application
 */

// Global App Object
window.SystemTax = {
    // Configuration
    config: {
        apiBaseUrl: '/api',
        currency: 'ج.م',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: 'HH:mm'
    },
    
    // Utility functions
    utils: {},
    
    // Components
    components: {},
    
    // API functions
    api: {},
    
    // Initialize application
    init: function() {
        this.utils.init();
        this.components.init();
        this.bindEvents();
        console.log('SystemTax initialized');
    },
    
    // Bind global events
    bindEvents: function() {
        // Auto-hide alerts
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Confirm delete actions
        $(document).on('click', '[data-confirm]', function(e) {
            const message = $(this).data('confirm') || 'هل أنت متأكد من هذا الإجراء؟';
            if (!confirm(message)) {
                e.preventDefault();
                return false;
            }
        });
        
        // Loading states for forms
        $(document).on('submit', 'form', function() {
            const $form = $(this);
            const $submitBtn = $form.find('button[type="submit"]');
            
            $submitBtn.prop('disabled', true);
            $submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري المعالجة...');
            
            // Re-enable after 10 seconds as fallback
            setTimeout(function() {
                $submitBtn.prop('disabled', false);
                $submitBtn.html($submitBtn.data('original-text') || 'حفظ');
            }, 10000);
        });
        
        // Store original button text
        $('button[type="submit"]').each(function() {
            $(this).data('original-text', $(this).html());
        });
    }
};

// Utility Functions
SystemTax.utils = {
    init: function() {
        this.setupDatePickers();
        this.setupTooltips();
        this.setupNumberFormatting();
    },
    
    // Setup date pickers
    setupDatePickers: function() {
        $('input[type="date"]').each(function() {
            if (!$(this).val() && $(this).data('default') === 'today') {
                $(this).val(new Date().toISOString().split('T')[0]);
            }
        });
    },
    
    // Setup tooltips
    setupTooltips: function() {
        $('[data-bs-toggle="tooltip"]').tooltip();
    },
    
    // Setup number formatting
    setupNumberFormatting: function() {
        $('.currency-input').on('input', function() {
            let value = $(this).val().replace(/[^\d.]/g, '');
            if (value) {
                $(this).val(parseFloat(value).toFixed(2));
            }
        });
    },
    
    // Format currency
    formatCurrency: function(amount) {
        if (isNaN(amount)) return '0.00 ' + SystemTax.config.currency;
        return parseFloat(amount).toLocaleString('ar-EG', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }) + ' ' + SystemTax.config.currency;
    },
    
    // Format date
    formatDate: function(date) {
        if (!date) return '';
        return new Date(date).toLocaleDateString('ar-EG');
    },
    
    // Show loading
    showLoading: function(element) {
        const $el = $(element);
        $el.prop('disabled', true);
        $el.html('<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...');
    },
    
    // Hide loading
    hideLoading: function(element, originalText) {
        const $el = $(element);
        $el.prop('disabled', false);
        $el.html(originalText || $el.data('original-text') || 'حفظ');
    },
    
    // Show notification
    showNotification: function(message, type = 'info') {
        const alertClass = type === 'error' ? 'danger' : type;
        const alertHtml = `
            <div class="alert alert-${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $('.main-content').prepend(alertHtml);
        
        // Auto-hide after 5 seconds
        setTimeout(function() {
            $('.alert').first().fadeOut('slow');
        }, 5000);
    },
    
    // Validate form
    validateForm: function(form) {
        let isValid = true;
        const $form = $(form);
        
        // Clear previous errors
        $form.find('.is-invalid').removeClass('is-invalid');
        $form.find('.invalid-feedback').remove();
        
        // Check required fields
        $form.find('[required]').each(function() {
            const $field = $(this);
            if (!$field.val().trim()) {
                $field.addClass('is-invalid');
                $field.after('<div class="invalid-feedback">هذا الحقل مطلوب</div>');
                isValid = false;
            }
        });
        
        // Check email fields
        $form.find('input[type="email"]').each(function() {
            const $field = $(this);
            const email = $field.val().trim();
            if (email && !SystemTax.utils.isValidEmail(email)) {
                $field.addClass('is-invalid');
                $field.after('<div class="invalid-feedback">البريد الإلكتروني غير صحيح</div>');
                isValid = false;
            }
        });
        
        return isValid;
    },
    
    // Validate email
    isValidEmail: function(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },
    
    // Debounce function
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};

// API Functions
SystemTax.api = {
    // Generic API call
    call: function(endpoint, options = {}) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': $('meta[name=csrf-token]').attr('content')
            }
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        return fetch(SystemTax.config.apiBaseUrl + endpoint, finalOptions)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .catch(error => {
                console.error('API Error:', error);
                SystemTax.utils.showNotification('حدث خطأ في الاتصال بالخادم', 'error');
                throw error;
            });
    },
    
    // Search customers
    searchCustomers: function(term) {
        return this.call(`/customers/api/search?term=${encodeURIComponent(term)}`);
    },
    
    // Search accounts
    searchAccounts: function(term, type = '') {
        return this.call(`/accounts/api/search?term=${encodeURIComponent(term)}&type=${type}`);
    },
    
    // Get account balance
    getAccountBalance: function(accountId, asOfDate = '') {
        return this.call(`/accounts/${accountId}/balance?as_of_date=${asOfDate}`);
    }
};

// Components
SystemTax.components = {
    init: function() {
        this.initSearchComponents();
        this.initTableComponents();
        this.initFormComponents();
    },
    
    // Initialize search components
    initSearchComponents: function() {
        // Customer search autocomplete
        $('.customer-search').each(function() {
            const $input = $(this);
            const $results = $('<div class="search-results"></div>');
            $input.after($results);
            
            const debouncedSearch = SystemTax.utils.debounce(function(term) {
                if (term.length < 2) {
                    $results.hide();
                    return;
                }
                
                SystemTax.api.searchCustomers(term)
                    .then(customers => {
                        let html = '';
                        customers.forEach(customer => {
                            html += `
                                <div class="search-result-item" data-id="${customer.id}">
                                    <strong>${customer.name}</strong>
                                    ${customer.tax_id ? `<br><small>الرقم الضريبي: ${customer.tax_id}</small>` : ''}
                                </div>
                            `;
                        });
                        $results.html(html).show();
                    });
            }, 300);
            
            $input.on('input', function() {
                debouncedSearch($(this).val());
            });
            
            $results.on('click', '.search-result-item', function() {
                const customerId = $(this).data('id');
                const customerName = $(this).find('strong').text();
                $input.val(customerName);
                $input.data('customer-id', customerId);
                $results.hide();
            });
        });
    },
    
    // Initialize table components
    initTableComponents: function() {
        // Sortable tables
        $('.sortable-table th[data-sort]').click(function() {
            const $th = $(this);
            const $table = $th.closest('table');
            const column = $th.data('sort');
            const direction = $th.hasClass('sort-asc') ? 'desc' : 'asc';
            
            // Update URL with sort parameters
            const url = new URL(window.location);
            url.searchParams.set('sort', column);
            url.searchParams.set('direction', direction);
            window.location.href = url.toString();
        });
        
        // Select all checkboxes
        $('.select-all').change(function() {
            const isChecked = $(this).is(':checked');
            $(this).closest('table').find('.select-item').prop('checked', isChecked);
        });
    },
    
    // Initialize form components
    initFormComponents: function() {
        // Dynamic form rows (for invoice lines, journal lines, etc.)
        $('.add-row').click(function() {
            const $table = $(this).data('target') ? $($(this).data('target')) : $(this).closest('.card').find('tbody');
            const $template = $table.find('.row-template');
            
            if ($template.length) {
                const $newRow = $template.clone().removeClass('row-template d-none');
                $newRow.find('input, select, textarea').val('');
                $table.append($newRow);
                
                // Focus on first input
                $newRow.find('input, select, textarea').first().focus();
            }
        });
        
        // Remove row
        $(document).on('click', '.remove-row', function() {
            $(this).closest('tr').remove();
        });
        
        // Calculate totals for invoice/journal forms
        $(document).on('input', '.calculate-total', function() {
            SystemTax.components.calculateTotals();
        });
    },
    
    // Calculate totals for forms
    calculateTotals: function() {
        let subtotal = 0;
        let totalTax = 0;
        let totalDiscount = 0;
        
        $('.line-row:not(.row-template)').each(function() {
            const $row = $(this);
            const unitPrice = parseFloat($row.find('.unit-price').val()) || 0;
            const quantity = parseFloat($row.find('.quantity').val()) || 0;
            const discountPercent = parseFloat($row.find('.discount-percent').val()) || 0;
            const taxPercent = parseFloat($row.find('.tax-percent').val()) || 0;
            
            const lineSubtotal = unitPrice * quantity;
            const lineDiscount = lineSubtotal * (discountPercent / 100);
            const lineAfterDiscount = lineSubtotal - lineDiscount;
            const lineTax = lineAfterDiscount * (taxPercent / 100);
            const lineTotal = lineAfterDiscount + lineTax;
            
            $row.find('.line-total').text(SystemTax.utils.formatCurrency(lineTotal));
            
            subtotal += lineSubtotal;
            totalDiscount += lineDiscount;
            totalTax += lineTax;
        });
        
        const grandTotal = subtotal - totalDiscount + totalTax;
        
        $('.subtotal').text(SystemTax.utils.formatCurrency(subtotal));
        $('.total-discount').text(SystemTax.utils.formatCurrency(totalDiscount));
        $('.total-tax').text(SystemTax.utils.formatCurrency(totalTax));
        $('.grand-total').text(SystemTax.utils.formatCurrency(grandTotal));
    }
};

// Initialize when document is ready
$(document).ready(function() {
    SystemTax.init();
});

// Export for global access
window.SystemTax = SystemTax;
