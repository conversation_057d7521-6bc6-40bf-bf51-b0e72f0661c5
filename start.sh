#!/bin/bash
# SystemTax Quick Start Script for Linux/macOS
# This script helps you start SystemTax quickly

set -e

echo ""
echo "========================================"
echo "   SystemTax - نظام محاسبي ويب متكامل"
echo "========================================"
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python 3 is not installed${NC}"
    echo "Please install Python 3.9+ from https://python.org"
    exit 1
fi

# Check Python version
PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
echo -e "${GREEN}✅ Python version: $PYTHON_VERSION${NC}"

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo -e "${BLUE}📦 Creating virtual environment...${NC}"
    python3 -m venv venv
fi

# Activate virtual environment
echo -e "${BLUE}🔄 Activating virtual environment...${NC}"
source venv/bin/activate

# Check if requirements are installed
if [ ! -f "venv/lib/python*/site-packages/flask" ] && [ ! -f "venv/lib/python*/site-packages/Flask*" ]; then
    echo -e "${BLUE}📥 Installing requirements...${NC}"
    pip install -r requirements.txt
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚙️ Creating environment file...${NC}"
    cp .env.example .env
    echo -e "${YELLOW}⚠️  Please edit .env file with your settings${NC}"
fi

# Check if database is initialized
if ! python cli.py check &> /dev/null; then
    echo -e "${BLUE}🗄️ Initializing database...${NC}"
    python cli.py init-db
    
    echo -e "${BLUE}👤 Creating admin user...${NC}"
    python cli.py create-admin
fi

echo ""
echo -e "${GREEN}🚀 Starting SystemTax...${NC}"
echo -e "${BLUE}📖 Open http://localhost:8000 in your browser${NC}"
echo -e "${YELLOW}👤 Default login: admin / admin123${NC}"
echo -e "${YELLOW}⚠️  Don't forget to change the admin password!${NC}"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

# Start the application
python run.py
