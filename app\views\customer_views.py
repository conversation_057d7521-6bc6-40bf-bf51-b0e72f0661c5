"""
Customer management views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, abort
from flask_login import login_required, current_user
from sqlalchemy import or_, and_, func, desc
from sqlalchemy.orm import joinedload
from app import db
from app.models.customer import Customer
from app.models.account import Account
from app.forms.customer_forms import CustomerForm, CustomerSearchForm, CustomerImportForm, CustomerBulkActionForm
from app.utils.decorators import permission_required
from app.utils.helpers import get_pagination_params, generate_reference_number
from decimal import Decimal
from datetime import datetime, date, timedelta

# Create blueprint
customers_bp = Blueprint('customers', __name__, url_prefix='/customers')


@customers_bp.route('/')
@login_required
@permission_required('customers')
def index():
    """Display customers list with search and filtering"""
    form = CustomerSearchForm(request.args)
    page, per_page = get_pagination_params()
    
    # Build query
    query = Customer.query.options(
        joinedload(Customer.account)
    )
    
    # Apply search filters
    if form.search.data:
        search_term = f"%{form.search.data}%"
        query = query.filter(
            or_(
                Customer.name.ilike(search_term),
                Customer.name_en.ilike(search_term),
                Customer.tax_id.ilike(search_term),
                Customer.email.ilike(search_term),
                Customer.phone.ilike(search_term)
            )
        )
    
    if form.customer_type.data:
        query = query.filter(Customer.customer_type == form.customer_type.data)
    
    if form.status.data:
        if form.status.data == 'active':
            query = query.filter(Customer.is_active == True)
        elif form.status.data == 'inactive':
            query = query.filter(Customer.is_active == False)
    
    # Order by name
    query = query.order_by(Customer.name)
    
    # Paginate
    customers = query.paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # Get statistics
    stats = {
        'total_customers': Customer.query.count(),
        'active_customers': Customer.query.filter_by(is_active=True).count(),
        'total_receivables': Decimal('0'),  # Will be calculated from invoices
        'average_balance': Decimal('0')
    }
    
    return render_template(
        'customers/index.html',
        customers=customers,
        form=form,
        stats=stats,
        title='العملاء'
    )


@customers_bp.route('/new')
@login_required
@permission_required('customers')
def new():
    """Display form for creating new customer"""
    form = CustomerForm()
    
    return render_template(
        'customers/form.html',
        form=form,
        title='عميل جديد'
    )


@customers_bp.route('/create', methods=['POST'])
@login_required
@permission_required('customers')
def create():
    """Create new customer"""
    form = CustomerForm()
    
    if form.validate_on_submit():
        try:
            # Create customer
            customer = Customer(
                name=form.name.data,
                name_en=form.name_en.data or None,
                customer_type=form.customer_type.data,
                tax_id=form.tax_id.data or None,
                email=form.email.data or None,
                phone=form.phone.data or None,
                website=form.website.data or None,
                address=form.address.data or None,
                city=form.city.data or None,
                state=form.state.data or None,
                postal_code=form.postal_code.data or None,
                country=form.country.data or None,
                credit_limit=form.credit_limit.data or None,
                payment_terms=form.payment_terms.data or None,
                currency=form.currency.data or 'EGP',
                account_id=form.account_id.data or None,
                notes=form.notes.data or None,
                is_active=form.is_active.data,
                created_by_id=current_user.id
            )
            
            db.session.add(customer)
            db.session.commit()
            
            flash(f'تم إنشاء العميل "{customer.name}" بنجاح', 'success')
            return redirect(url_for('customers.detail', customer_id=customer.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء إنشاء العميل: {str(e)}', 'error')
    
    return render_template(
        'customers/form.html',
        form=form,
        title='عميل جديد'
    )


@customers_bp.route('/<uuid:customer_id>')
@login_required
@permission_required('customers')
def detail(customer_id):
    """Display customer details"""
    customer = Customer.query.options(
        joinedload(Customer.account),
        joinedload(Customer.created_by)
    ).get_or_404(customer_id)
    
    # Get recent invoices (when invoice model is available)
    recent_invoices = []
    
    # Get recent activity
    recent_activity = []
    
    return render_template(
        'customers/detail.html',
        customer=customer,
        recent_invoices=recent_invoices,
        recent_activity=recent_activity,
        title=f'تفاصيل العميل: {customer.name}'
    )


@customers_bp.route('/<uuid:customer_id>/edit')
@login_required
@permission_required('customers')
def edit(customer_id):
    """Display form for editing customer"""
    customer = Customer.query.get_or_404(customer_id)
    
    form = CustomerForm(obj=customer, customer=customer)
    
    return render_template(
        'customers/form.html',
        form=form,
        customer=customer,
        title=f'تعديل العميل: {customer.name}'
    )


@customers_bp.route('/<uuid:customer_id>/update', methods=['POST'])
@login_required
@permission_required('customers')
def update(customer_id):
    """Update customer"""
    customer = Customer.query.get_or_404(customer_id)
    
    form = CustomerForm(customer=customer)
    
    if form.validate_on_submit():
        try:
            # Update customer details
            customer.name = form.name.data
            customer.name_en = form.name_en.data or None
            customer.customer_type = form.customer_type.data
            customer.tax_id = form.tax_id.data or None
            customer.email = form.email.data or None
            customer.phone = form.phone.data or None
            customer.website = form.website.data or None
            customer.address = form.address.data or None
            customer.city = form.city.data or None
            customer.state = form.state.data or None
            customer.postal_code = form.postal_code.data or None
            customer.country = form.country.data or None
            customer.credit_limit = form.credit_limit.data or None
            customer.payment_terms = form.payment_terms.data or None
            customer.currency = form.currency.data or 'EGP'
            customer.account_id = form.account_id.data or None
            customer.notes = form.notes.data or None
            customer.is_active = form.is_active.data
            customer.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            flash(f'تم تحديث العميل "{customer.name}" بنجاح', 'success')
            return redirect(url_for('customers.detail', customer_id=customer.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ أثناء تحديث العميل: {str(e)}', 'error')
    
    return render_template(
        'customers/form.html',
        form=form,
        customer=customer,
        title=f'تعديل العميل: {customer.name}'
    )


@customers_bp.route('/<uuid:customer_id>/delete', methods=['POST'])
@login_required
@permission_required('customers')
def delete(customer_id):
    """Delete customer"""
    customer = Customer.query.get_or_404(customer_id)
    
    if not customer.can_be_deleted():
        flash('لا يمكن حذف هذا العميل لوجود معاملات مرتبطة به', 'error')
        return redirect(url_for('customers.detail', customer_id=customer.id))
    
    try:
        customer_name = customer.name
        db.session.delete(customer)
        db.session.commit()
        
        flash(f'تم حذف العميل "{customer_name}" بنجاح', 'success')
        return redirect(url_for('customers.index'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء حذف العميل: {str(e)}', 'error')
        return redirect(url_for('customers.detail', customer_id=customer.id))


@customers_bp.route('/<uuid:customer_id>/statement')
@login_required
@permission_required('customers')
def statement(customer_id):
    """Display customer statement"""
    customer = Customer.query.get_or_404(customer_id)
    
    # Get date range from request
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    if date_from:
        date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
    else:
        # Default to current month
        today = date.today()
        date_from = date(today.year, today.month, 1)
    
    if date_to:
        date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
    else:
        date_to = date.today()
    
    # Get transactions (will be implemented when invoice/payment models are ready)
    transactions = []
    opening_balance = Decimal('0')
    total_sales = Decimal('0')
    total_payments = Decimal('0')
    closing_balance = Decimal('0')
    
    # Aging analysis
    aging_analysis = {
        'current': Decimal('0'),
        'days_30': Decimal('0'),
        'days_60': Decimal('0'),
        'days_90': Decimal('0'),
        'over_120': Decimal('0'),
        'total': Decimal('0')
    }
    
    return render_template(
        'customers/statement.html',
        customer=customer,
        transactions=transactions,
        date_from=date_from,
        date_to=date_to,
        opening_balance=opening_balance,
        total_sales=total_sales,
        total_payments=total_payments,
        closing_balance=closing_balance,
        aging_analysis=aging_analysis,
        title=f'كشف حساب العميل: {customer.name}'
    )


@customers_bp.route('/import_data')
@login_required
@permission_required('customers')
def import_data():
    """Display import form"""
    form = CustomerImportForm()
    
    return render_template(
        'customers/import.html',
        form=form,
        title='استيراد العملاء'
    )


@customers_bp.route('/bulk_action', methods=['POST'])
@login_required
@permission_required('customers')
def bulk_action():
    """Execute bulk action on customers"""
    action = request.form.get('action')
    customer_ids = request.form.get('customer_ids', '').split(',')
    
    if not action or not customer_ids:
        flash('يرجى تحديد إجراء وعملاء', 'error')
        return redirect(url_for('customers.index'))
    
    try:
        customers = Customer.query.filter(Customer.id.in_(customer_ids)).all()
        
        if action == 'activate':
            for customer in customers:
                customer.is_active = True
            db.session.commit()
            flash(f'تم تفعيل {len(customers)} عميل بنجاح', 'success')
            
        elif action == 'deactivate':
            for customer in customers:
                customer.is_active = False
            db.session.commit()
            flash(f'تم إلغاء تفعيل {len(customers)} عميل بنجاح', 'success')
            
        elif action == 'delete':
            deletable_customers = [c for c in customers if c.can_be_deleted()]
            for customer in deletable_customers:
                db.session.delete(customer)
            db.session.commit()
            flash(f'تم حذف {len(deletable_customers)} عميل بنجاح', 'success')
            
            if len(deletable_customers) < len(customers):
                flash(f'{len(customers) - len(deletable_customers)} عميل لم يتم حذفهم لوجود معاملات مرتبطة', 'warning')
        
        elif action == 'export':
            # Implement export functionality
            flash('سيتم تنفيذ التصدير قريباً', 'info')
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء تنفيذ الإجراء: {str(e)}', 'error')
    
    return redirect(url_for('customers.index'))


# API Routes
@customers_bp.route('/<uuid:customer_id>/balance')
@login_required
@permission_required('customers')
def balance(customer_id):
    """API endpoint to get customer balance"""
    customer = Customer.query.get_or_404(customer_id)
    
    return jsonify({
        'balance': float(customer.get_balance()),
        'credit_limit': float(customer.credit_limit or 0),
        'credit_available': float((customer.credit_limit or 0) - customer.get_balance()),
        'last_updated': datetime.utcnow().isoformat()
    })


@customers_bp.route('/search')
@login_required
@permission_required('customers')
def search():
    """API endpoint for customer search"""
    query = request.args.get('q', '')
    limit = min(int(request.args.get('limit', 10)), 50)
    
    if len(query) < 2:
        return jsonify([])
    
    customers = Customer.query.filter(
        and_(
            Customer.is_active == True,
            or_(
                Customer.name.ilike(f'%{query}%'),
                Customer.name_en.ilike(f'%{query}%'),
                Customer.tax_id.ilike(f'%{query}%')
            )
        )
    ).limit(limit).all()
    
    return jsonify([
        {
            'id': str(customer.id),
            'name': customer.name,
            'name_en': customer.name_en,
            'tax_id': customer.tax_id,
            'email': customer.email,
            'phone': customer.phone,
            'balance': float(customer.get_balance())
        }
        for customer in customers
    ])
