{% extends "base.html" %}

{% block title %}قائمة الدخل - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-chart-area me-3"></i>
                قائمة الدخل
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('reports.index') }}">التقارير</a></li>
                    <li class="breadcrumb-item active">قائمة الدخل</li>
                </ol>
            </nav>
        </div>
        <div>
            <div class="btn-group">
                <button type="button" class="btn btn-info" onclick="window.print()">
                    <i class="fas fa-print me-2"></i>
                    طباعة
                </button>
                <a href="{{ url_for('reports.income_statement_pdf') }}?{{ request.query_string.decode() }}" 
                   class="btn btn-danger" target="_blank">
                    <i class="fas fa-file-pdf me-2"></i>
                    PDF
                </a>
                <a href="{{ url_for('reports.income_statement_excel') }}?{{ request.query_string.decode() }}" 
                   class="btn btn-success">
                    <i class="fas fa-file-excel me-2"></i>
                    Excel
                </a>
            </div>
            <a href="{{ url_for('reports.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للتقارير
            </a>
        </div>
    </div>
</div>

<!-- Report Parameters -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-filter me-2"></i>
            معايير التقرير
        </h5>
    </div>
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                {{ form.date_from.label(class="form-label") }}
                {{ form.date_from(class="form-control") }}
            </div>
            <div class="col-md-3">
                {{ form.date_to.label(class="form-label") }}
                {{ form.date_to(class="form-control") }}
            </div>
            <div class="col-md-3">
                {{ form.comparison_period.label(class="form-label") }}
                {{ form.comparison_period(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ form.show_details.label(class="form-label") }}
                <div class="form-check">
                    {{ form.show_details(class="form-check-input") }}
                    {{ form.show_details.label(class="form-check-label") }}
                </div>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Report Content -->
<div class="card mb-4" id="report-content">
    <div class="card-header text-center bg-light">
        <h3 class="mb-1">{{ company_name or 'اسم الشركة' }}</h3>
        <h4 class="mb-1">قائمة الدخل</h4>
        <h5 class="text-muted">
            من {{ date_from|date }} إلى {{ date_to|date }}
        </h5>
    </div>
    
    <div class="card-body">
        {% if income_statement_data %}
        <div class="table-responsive">
            <table class="table table-borderless">
                <thead>
                    <tr>
                        <th style="width: 60%;">البيان</th>
                        <th class="text-end" style="width: 20%;">الفترة الحالية</th>
                        {% if comparison_data %}
                        <th class="text-end" style="width: 20%;">فترة المقارنة</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    <!-- Revenue Section -->
                    <tr class="table-primary">
                        <td class="fw-bold fs-5">
                            <i class="fas fa-arrow-up me-2"></i>
                            الإيرادات
                        </td>
                        <td></td>
                        {% if comparison_data %}<td></td>{% endif %}
                    </tr>
                    
                    {% for revenue in income_statement_data.revenues %}
                    <tr>
                        <td class="ps-4">{{ revenue.name }}</td>
                        <td class="text-end">{{ revenue.amount|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ revenue.comparison_amount|currency }}</td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                    
                    <tr class="table-light fw-bold">
                        <td class="ps-4">إجمالي الإيرادات</td>
                        <td class="text-end">{{ income_statement_data.total_revenue|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ comparison_data.total_revenue|currency }}</td>
                        {% endif %}
                    </tr>
                    
                    <tr><td colspan="{{ 3 if comparison_data else 2 }}">&nbsp;</td></tr>
                    
                    <!-- Cost of Goods Sold -->
                    {% if income_statement_data.cost_of_goods_sold %}
                    <tr class="table-warning">
                        <td class="fw-bold fs-5">
                            <i class="fas fa-box me-2"></i>
                            تكلفة البضاعة المباعة
                        </td>
                        <td></td>
                        {% if comparison_data %}<td></td>{% endif %}
                    </tr>
                    
                    {% for cogs in income_statement_data.cost_of_goods_sold %}
                    <tr>
                        <td class="ps-4">{{ cogs.name }}</td>
                        <td class="text-end">{{ cogs.amount|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ cogs.comparison_amount|currency }}</td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                    
                    <tr class="table-light fw-bold">
                        <td class="ps-4">إجمالي تكلفة البضاعة المباعة</td>
                        <td class="text-end">{{ income_statement_data.total_cogs|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ comparison_data.total_cogs|currency }}</td>
                        {% endif %}
                    </tr>
                    
                    <tr class="table-success fw-bold">
                        <td class="ps-4">إجمالي الربح</td>
                        <td class="text-end">{{ income_statement_data.gross_profit|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ comparison_data.gross_profit|currency }}</td>
                        {% endif %}
                    </tr>
                    
                    <tr><td colspan="{{ 3 if comparison_data else 2 }}">&nbsp;</td></tr>
                    {% endif %}
                    
                    <!-- Operating Expenses -->
                    <tr class="table-danger">
                        <td class="fw-bold fs-5">
                            <i class="fas fa-arrow-down me-2"></i>
                            المصروفات التشغيلية
                        </td>
                        <td></td>
                        {% if comparison_data %}<td></td>{% endif %}
                    </tr>
                    
                    {% for expense in income_statement_data.operating_expenses %}
                    <tr>
                        <td class="ps-4">{{ expense.name }}</td>
                        <td class="text-end">{{ expense.amount|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ expense.comparison_amount|currency }}</td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                    
                    <tr class="table-light fw-bold">
                        <td class="ps-4">إجمالي المصروفات التشغيلية</td>
                        <td class="text-end">{{ income_statement_data.total_operating_expenses|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ comparison_data.total_operating_expenses|currency }}</td>
                        {% endif %}
                    </tr>
                    
                    <tr class="table-info fw-bold">
                        <td class="ps-4">الربح التشغيلي</td>
                        <td class="text-end">{{ income_statement_data.operating_profit|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ comparison_data.operating_profit|currency }}</td>
                        {% endif %}
                    </tr>
                    
                    <tr><td colspan="{{ 3 if comparison_data else 2 }}">&nbsp;</td></tr>
                    
                    <!-- Other Income/Expenses -->
                    {% if income_statement_data.other_income or income_statement_data.other_expenses %}
                    <tr class="table-secondary">
                        <td class="fw-bold fs-5">
                            <i class="fas fa-plus-minus me-2"></i>
                            الإيرادات والمصروفات الأخرى
                        </td>
                        <td></td>
                        {% if comparison_data %}<td></td>{% endif %}
                    </tr>
                    
                    {% for other in income_statement_data.other_income %}
                    <tr>
                        <td class="ps-4">{{ other.name }}</td>
                        <td class="text-end">{{ other.amount|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ other.comparison_amount|currency }}</td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                    
                    {% for other in income_statement_data.other_expenses %}
                    <tr>
                        <td class="ps-4">{{ other.name }}</td>
                        <td class="text-end">({{ other.amount|currency }})</td>
                        {% if comparison_data %}
                        <td class="text-end">({{ other.comparison_amount|currency }})</td>
                        {% endif %}
                    </tr>
                    {% endfor %}
                    
                    <tr class="table-light fw-bold">
                        <td class="ps-4">صافي الإيرادات/المصروفات الأخرى</td>
                        <td class="text-end">{{ income_statement_data.net_other|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ comparison_data.net_other|currency }}</td>
                        {% endif %}
                    </tr>
                    
                    <tr><td colspan="{{ 3 if comparison_data else 2 }}">&nbsp;</td></tr>
                    {% endif %}
                    
                    <!-- Net Income Before Tax -->
                    <tr class="table-warning fw-bold">
                        <td class="ps-4">صافي الربح قبل الضرائب</td>
                        <td class="text-end">{{ income_statement_data.net_income_before_tax|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ comparison_data.net_income_before_tax|currency }}</td>
                        {% endif %}
                    </tr>
                    
                    <!-- Tax Expenses -->
                    {% if income_statement_data.tax_expenses %}
                    <tr>
                        <td class="ps-4">مصروف الضرائب</td>
                        <td class="text-end">({{ income_statement_data.tax_expenses|currency }})</td>
                        {% if comparison_data %}
                        <td class="text-end">({{ comparison_data.tax_expenses|currency }})</td>
                        {% endif %}
                    </tr>
                    {% endif %}
                    
                    <!-- Net Income -->
                    <tr class="table-success fw-bold fs-5 border-top border-3">
                        <td class="ps-4">صافي الربح</td>
                        <td class="text-end">{{ income_statement_data.net_income|currency }}</td>
                        {% if comparison_data %}
                        <td class="text-end">{{ comparison_data.net_income|currency }}</td>
                        {% endif %}
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- Financial Ratios -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-calculator me-2"></i>
                            النسب المالية
                        </h6>
                    </div>
                    <div class="card-body">
                        <table class="table table-sm mb-0">
                            <tr>
                                <td><strong>هامش إجمالي الربح:</strong></td>
                                <td class="text-end">{{ ratios.gross_profit_margin|percentage }}%</td>
                            </tr>
                            <tr>
                                <td><strong>هامش الربح التشغيلي:</strong></td>
                                <td class="text-end">{{ ratios.operating_profit_margin|percentage }}%</td>
                            </tr>
                            <tr>
                                <td><strong>هامش صافي الربح:</strong></td>
                                <td class="text-end">{{ ratios.net_profit_margin|percentage }}%</td>
                            </tr>
                            <tr>
                                <td><strong>نسبة المصروفات للإيرادات:</strong></td>
                                <td class="text-end">{{ ratios.expense_ratio|percentage }}%</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            مقارنة الفترات
                        </h6>
                    </div>
                    <div class="card-body">
                        {% if comparison_data %}
                        <table class="table table-sm mb-0">
                            <tr>
                                <td><strong>نمو الإيرادات:</strong></td>
                                <td class="text-end">
                                    <span class="text-{{ 'success' if growth.revenue_growth >= 0 else 'danger' }}">
                                        {{ growth.revenue_growth|percentage }}%
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>نمو صافي الربح:</strong></td>
                                <td class="text-end">
                                    <span class="text-{{ 'success' if growth.profit_growth >= 0 else 'danger' }}">
                                        {{ growth.profit_growth|percentage }}%
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تغير المصروفات:</strong></td>
                                <td class="text-end">
                                    <span class="text-{{ 'danger' if growth.expense_growth >= 0 else 'success' }}">
                                        {{ growth.expense_growth|percentage }}%
                                    </span>
                                </td>
                            </tr>
                        </table>
                        {% else %}
                        <p class="text-muted mb-0">اختر فترة مقارنة لعرض النمو</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-chart-area fa-3x text-muted mb-3"></i>
            <h5>لا توجد بيانات</h5>
            <p class="text-muted">لا توجد إيرادات أو مصروفات في الفترة المحددة.</p>
            <a href="{{ url_for('journal.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة قيد جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Chart Visualization -->
{% if income_statement_data %}
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-chart-pie me-2"></i>
            التمثيل البياني
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <canvas id="revenueExpenseChart" width="400" height="200"></canvas>
            </div>
            <div class="col-md-6">
                <canvas id="profitTrendChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if income_statement_data %}
// Revenue vs Expense Chart
const revenueExpenseCtx = document.getElementById('revenueExpenseChart').getContext('2d');
new Chart(revenueExpenseCtx, {
    type: 'doughnut',
    data: {
        labels: ['الإيرادات', 'المصروفات', 'صافي الربح'],
        datasets: [{
            data: [
                {{ income_statement_data.total_revenue }},
                {{ income_statement_data.total_operating_expenses }},
                {{ income_statement_data.net_income }}
            ],
            backgroundColor: [
                '#28a745',
                '#dc3545',
                '#007bff'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'توزيع الإيرادات والمصروفات'
            }
        }
    }
});

// Profit Trend Chart (if comparison data exists)
{% if comparison_data %}
const profitTrendCtx = document.getElementById('profitTrendChart').getContext('2d');
new Chart(profitTrendCtx, {
    type: 'line',
    data: {
        labels: ['فترة المقارنة', 'الفترة الحالية'],
        datasets: [{
            label: 'صافي الربح',
            data: [
                {{ comparison_data.net_income }},
                {{ income_statement_data.net_income }}
            ],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'اتجاه الربح'
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
{% endif %}
{% endif %}
</script>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .page-header,
    .card:not(#report-content),
    .btn,
    .breadcrumb {
        display: none !important;
    }
    
    #report-content {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
    
    .card-header {
        background-color: #f8f9fa !important;
        color: black !important;
    }
}

.table td {
    padding: 0.5rem;
}

.table .ps-4 {
    padding-left: 2rem !important;
}
</style>
{% endblock %}
