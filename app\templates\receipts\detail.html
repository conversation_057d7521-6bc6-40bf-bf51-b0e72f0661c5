{% extends "base.html" %}

{% block title %}إيصال رقم {{ receipt.receipt_number }} - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-receipt me-3"></i>
                إيصال رقم {{ receipt.receipt_number }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('receipts.index') }}">الإيصالات</a></li>
                    <li class="breadcrumb-item active">{{ receipt.receipt_number }}</li>
                </ol>
            </nav>
        </div>
        <div>
            {% if receipt.can_be_edited() %}
            <a href="{{ url_for('receipts.edit', receipt_id=receipt.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
            {% endif %}
            <a href="{{ url_for('receipts.pdf', receipt_id=receipt.id) }}" class="btn btn-info" target="_blank">
                <i class="fas fa-file-pdf me-2"></i>
                PDF
            </a>
            <a href="{{ url_for('receipts.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Receipt Details -->
    <div class="col-lg-8">
        <!-- Receipt Header -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    تفاصيل الإيصال
                </h5>
                <div>
                    <span class="badge bg-{{ 'success' if receipt.receipt_type == 'receipt' else 'warning' }} fs-6">
                        <i class="fas fa-{{ 'arrow-down' if receipt.receipt_type == 'receipt' else 'arrow-up' }} me-1"></i>
                        {{ receipt.get_type_display() }}
                    </span>
                    <span class="badge bg-{{ 'success' if receipt.is_confirmed else 'warning' }} fs-6 ms-2">
                        {{ 'مؤكد' if receipt.is_confirmed else 'غير مؤكد' }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الإيصال:</strong></td>
                                <td>{{ receipt.receipt_number }}</td>
                            </tr>
                            {% if receipt.reference_number %}
                            <tr>
                                <td><strong>الرقم المرجعي:</strong></td>
                                <td>{{ receipt.reference_number }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>تاريخ الإيصال:</strong></td>
                                <td>{{ receipt.receipt_date|date }}</td>
                            </tr>
                            <tr>
                                <td><strong>نوع الإيصال:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if receipt.receipt_type == 'receipt' else 'warning' }}">
                                        {{ receipt.get_type_display() }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>العملة:</strong></td>
                                <td>{{ receipt.currency }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ receipt.created_at.strftime('%Y-%m-%d %H:%M') if receipt.created_at else '' }}</td>
                            </tr>
                            {% if receipt.updated_at %}
                            <tr>
                                <td><strong>آخر تحديث:</strong></td>
                                <td>{{ receipt.updated_at.strftime('%Y-%m-%d %H:%M') if receipt.updated_at else '' }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>أنشأ بواسطة:</strong></td>
                                <td>{{ receipt.created_by.username if receipt.created_by else 'غير محدد' }}</td>
                            </tr>
                            {% if receipt.confirmed_at %}
                            <tr>
                                <td><strong>تاريخ التأكيد:</strong></td>
                                <td>{{ receipt.confirmed_at.strftime('%Y-%m-%d %H:%M') if receipt.confirmed_at else '' }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                {% if receipt.description %}
                <div class="mt-3">
                    <strong>الوصف:</strong>
                    <p>{{ receipt.description }}</p>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Customer/Vendor Information -->
        {% if receipt.customer or receipt.vendor %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-{{ 'user' if receipt.customer else 'truck' }} me-2"></i>
                    معلومات {{ 'العميل' if receipt.customer else 'المورد' }}
                </h5>
            </div>
            <div class="card-body">
                {% set entity = receipt.customer or receipt.vendor %}
                {% set entity_type = 'customer' if receipt.customer else 'vendor' %}
                
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الاسم:</strong></td>
                                <td>
                                    <a href="{{ url_for(entity_type + 's.detail', **{entity_type + '_id': entity.id}) }}">
                                        {{ entity.name }}
                                    </a>
                                </td>
                            </tr>
                            {% if entity.tax_id %}
                            <tr>
                                <td><strong>الرقم الضريبي:</strong></td>
                                <td>{{ entity.tax_id }}</td>
                            </tr>
                            {% endif %}
                            {% if entity.email %}
                            <tr>
                                <td><strong>البريد الإلكتروني:</strong></td>
                                <td><a href="mailto:{{ entity.email }}">{{ entity.email }}</a></td>
                            </tr>
                            {% endif %}
                            {% if entity.phone %}
                            <tr>
                                <td><strong>الهاتف:</strong></td>
                                <td><a href="tel:{{ entity.phone }}">{{ entity.phone }}</a></td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        {% if entity.address %}
                        <p><strong>العنوان:</strong><br>
                        {{ entity.address }}
                        {% if entity.city %}, {{ entity.city }}{% endif %}
                        {% if entity.state %}, {{ entity.state }}{% endif %}
                        {% if entity.postal_code %} {{ entity.postal_code }}{% endif %}
                        </p>
                        {% endif %}
                        
                        <p><strong>الرصيد الحالي:</strong> 
                        <span class="fw-bold text-{{ 'success' if entity.get_balance() >= 0 else 'danger' }}">
                            {{ '{:,.2f} ج.م'.format(entity.get_balance()|float) if entity.get_balance() else '0.00 ج.م' }}
                        </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Payment Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill me-2"></i>
                    معلومات الدفع
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المبلغ:</strong></td>
                                <td>
                                    <span class="fs-4 fw-bold text-{{ 'success' if receipt.receipt_type == 'receipt' else 'warning' }}">
                                        {{ '{:,.2f} ج.م'.format(receipt.amount|float) if receipt.amount else '0.00 ج.م' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>طريقة الدفع:</strong></td>
                                <td>
                                    <span class="badge bg-info">{{ receipt.get_payment_method_display() }}</span>
                                </td>
                            </tr>
                            {% if receipt.account %}
                            <tr>
                                <td><strong>الحساب:</strong></td>
                                <td>
                                    <a href="{{ url_for('accounts.detail', account_id=receipt.account.id) }}">
                                        {{ receipt.account.code }} - {{ receipt.account.name }}
                                    </a>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        {% if receipt.payment_method == 'bank_transfer' %}
                            {% if receipt.bank_name %}
                            <p><strong>اسم البنك:</strong> {{ receipt.bank_name }}</p>
                            {% endif %}
                            {% if receipt.bank_reference %}
                            <p><strong>مرجع التحويل:</strong> {{ receipt.bank_reference }}</p>
                            {% endif %}
                        {% elif receipt.payment_method == 'check' %}
                            {% if receipt.check_number %}
                            <p><strong>رقم الشيك:</strong> {{ receipt.check_number }}</p>
                            {% endif %}
                            {% if receipt.check_date %}
                            <p><strong>تاريخ الشيك:</strong> {{ receipt.check_date|date }}</p>
                            {% endif %}
                            {% if receipt.bank_name %}
                            <p><strong>البنك المسحوب عليه:</strong> {{ receipt.bank_name }}</p>
                            {% endif %}
                        {% endif %}
                        
                        <div class="mt-3">
                            <strong>المبلغ بالحروف:</strong>
                            <p class="text-muted">{{ receipt.amount_in_words() }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Related Invoice -->
        {% if receipt.invoice %}
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    الفاتورة المرتبطة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم الفاتورة:</strong></td>
                                <td>
                                    <a href="{{ url_for('invoices.detail', invoice_id=receipt.invoice.id) }}">
                                        {{ receipt.invoice.invoice_number }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الفاتورة:</strong></td>
                                <td>{{ receipt.invoice.invoice_date|date }}</td>
                            </tr>
                            <tr>
                                <td><strong>إجمالي الفاتورة:</strong></td>
                                <td>{{ receipt.invoice.total_amount|currency }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المبلغ المدفوع:</strong></td>
                                <td>{{ receipt.invoice.paid_amount|currency }}</td>
                            </tr>
                            <tr>
                                <td><strong>المبلغ المتبقي:</strong></td>
                                <td>
                                    <span class="fw-bold text-{{ 'success' if receipt.invoice.remaining_amount <= 0 else 'warning' }}">
                                        {{ receipt.invoice.remaining_amount|currency }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>حالة الفاتورة:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if receipt.invoice.status == 'paid' else 'warning' }}">
                                        {{ receipt.invoice.get_status_display() }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Notes -->
        {% if receipt.notes %}
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sticky-note me-2"></i>
                    ملاحظات
                </h5>
            </div>
            <div class="card-body">
                <p>{{ receipt.notes|nl2br }}</p>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if receipt.can_be_edited() %}
                    <a href="{{ url_for('receipts.edit', receipt_id=receipt.id) }}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل الإيصال
                    </a>
                    {% endif %}
                    
                    <a href="{{ url_for('receipts.pdf', receipt_id=receipt.id) }}" class="btn btn-outline-info" target="_blank">
                        <i class="fas fa-file-pdf me-2"></i>
                        عرض PDF
                    </a>
                    
                    {% if receipt.customer and receipt.customer.email %}
                    <button type="button" class="btn btn-outline-primary" onclick="sendReceiptEmail()">
                        <i class="fas fa-envelope me-2"></i>
                        إرسال بريد
                    </button>
                    {% endif %}
                    
                    {% if not receipt.is_confirmed %}
                    <button type="button" class="btn btn-outline-success" onclick="confirmReceipt()">
                        <i class="fas fa-check me-2"></i>
                        تأكيد الإيصال
                    </button>
                    {% endif %}
                    
                    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                    
                    {% if receipt.can_be_deleted() %}
                    <hr>
                    <form method="POST" action="{{ url_for('receipts.delete', receipt_id=receipt.id) }}" 
                          onsubmit="return confirm('هل أنت متأكد من حذف هذا الإيصال؟')">
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash me-2"></i>
                            حذف الإيصال
                        </button>
                    </form>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Receipt Summary -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    ملخص الإيصال
                </h6>
            </div>
            <div class="card-body text-center">
                <h3 class="text-{{ 'success' if receipt.receipt_type == 'receipt' else 'warning' }}">
                    {{ receipt.amount|currency }}
                </h3>
                <p class="text-muted mb-0">{{ receipt.get_type_display() }}</p>
                
                <hr>
                
                <div class="row">
                    <div class="col-6">
                        <small class="text-muted">طريقة الدفع</small>
                        <p class="mb-0">{{ receipt.get_payment_method_display() }}</p>
                    </div>
                    <div class="col-6">
                        <small class="text-muted">العملة</small>
                        <p class="mb-0">{{ receipt.currency }}</p>
                    </div>
                </div>
                
                {% if receipt.is_confirmed %}
                <div class="mt-3">
                    <span class="badge bg-success">
                        <i class="fas fa-check me-1"></i>
                        إيصال مؤكد
                    </span>
                </div>
                {% else %}
                <div class="mt-3">
                    <span class="badge bg-warning">
                        <i class="fas fa-clock me-1"></i>
                        في انتظار التأكيد
                    </span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Journal Entry -->
        {% if receipt.journal_entry %}
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-book me-2"></i>
                    القيد المحاسبي
                </h6>
            </div>
            <div class="card-body">
                <p><strong>رقم القيد:</strong>
                <a href="{{ url_for('journal.detail', entry_id=receipt.journal_entry.id) }}">
                    {{ receipt.journal_entry.entry_number }}
                </a>
                </p>
                
                <p><strong>تاريخ القيد:</strong> {{ receipt.journal_entry.entry_date|date }}</p>
                
                <p><strong>إجمالي القيد:</strong> {{ receipt.journal_entry.total_amount|currency }}</p>
                
                <p><strong>حالة القيد:</strong>
                <span class="badge bg-{{ 'success' if receipt.journal_entry.is_posted else 'warning' }}">
                    {{ 'مرحل' if receipt.journal_entry.is_posted else 'غير مرحل' }}
                </span>
                </p>
            </div>
        </div>
        {% endif %}

        <!-- Related Documents -->
        {% if related_documents %}
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>
                    مستندات مرتبطة
                </h6>
            </div>
            <div class="card-body">
                {% for doc in related_documents %}
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-{{ 'file-invoice' if doc.type == 'invoice' else 'book' if doc.type == 'journal' else 'file' }} me-2"></i>
                    <a href="{{ doc.url }}" class="text-decoration-none">
                        {{ doc.number }}
                    </a>
                    <small class="text-muted ms-auto">{{ doc.date|date }}</small>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmReceipt() {
    if (confirm('هل تريد تأكيد هذا الإيصال؟')) {
        fetch(`/receipts/{{ receipt.id }}/confirm`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تأكيد الإيصال بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function sendReceiptEmail() {
    if (confirm('هل تريد إرسال الإيصال بالبريد الإلكتروني؟')) {
        fetch(`/receipts/{{ receipt.id }}/send-email`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم إرسال الإيصال بالبريد الإلكتروني بنجاح');
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .page-header,
    .btn,
    .card:last-child {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .badge {
        color: black !important;
        background-color: transparent !important;
        border: 1px solid black !important;
    }
}
</style>
{% endblock %}
