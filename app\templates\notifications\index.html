{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary" onclick="markAllAsRead()">
                <i class="fas fa-check-double me-2"></i>
                تحديد الكل كمقروء
            </button>
            <a href="{{ url_for('notifications.preferences') }}" class="btn btn-outline-primary">
                <i class="fas fa-cog me-2"></i>
                الإعدادات
            </a>
        </div>
    </div>

    <!-- Notification Stats -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الإشعارات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ notifications|length }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bell fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                غير مقروءة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ unread_count }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                مقروءة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ notifications|length - unread_count }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                هذا الأسبوع
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% set week_count = 0 %}
                                {% for notification in notifications %}
                                    {% if (notification.created_at.date() - today.date()).days <= 7 %}
                                        {% set week_count = week_count + 1 %}
                                    {% endif %}
                                {% endfor %}
                                {{ week_count }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">تصفية الإشعارات</h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="type" class="form-label">نوع الإشعار</label>
                    <select name="type" id="type" class="form-select">
                        <option value="all" {% if filter_type == 'all' %}selected{% endif %}>جميع الأنواع</option>
                        <option value="info" {% if filter_type == 'info' %}selected{% endif %}>معلومات</option>
                        <option value="success" {% if filter_type == 'success' %}selected{% endif %}>نجاح</option>
                        <option value="warning" {% if filter_type == 'warning' %}selected{% endif %}>تحذير</option>
                        <option value="error" {% if filter_type == 'error' %}selected{% endif %}>خطأ</option>
                        <option value="invoice_due" {% if filter_type == 'invoice_due' %}selected{% endif %}>فواتير مستحقة</option>
                        <option value="payment_received" {% if filter_type == 'payment_received' %}selected{% endif %}>مدفوعات مستلمة</option>
                        <option value="low_balance" {% if filter_type == 'low_balance' %}selected{% endif %}>رصيد منخفض</option>
                        <option value="system_update" {% if filter_type == 'system_update' %}selected{% endif %}>تحديثات النظام</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="unread" class="form-label">الحالة</label>
                    <select name="unread" id="unread" class="form-select">
                        <option value="" {% if not unread_only %}selected{% endif %}>جميع الإشعارات</option>
                        <option value="true" {% if unread_only %}selected{% endif %}>غير مقروءة فقط</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i>
                        تطبيق التصفية
                    </button>
                    <a href="{{ url_for('notifications.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        إزالة التصفية
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الإشعارات</h6>
        </div>
        <div class="card-body p-0">
            {% if notifications %}
                <div class="list-group list-group-flush">
                    {% for notification in notifications %}
                    <div class="list-group-item {% if not notification.is_read %}list-group-item-light{% endif %}" 
                         data-notification-id="{{ notification.id }}">
                        <div class="d-flex w-100 justify-content-between align-items-start">
                            <div class="d-flex align-items-start">
                                <div class="notification-icon me-3 mt-1">
                                    <i class="fas fa-{{ notification.get_icon() }} text-{{ notification.get_color() }} fa-lg"></i>
                                </div>
                                <div class="notification-content">
                                    <h6 class="mb-1 {% if not notification.is_read %}fw-bold{% endif %}">
                                        {{ notification.title }}
                                        {% if not notification.is_read %}
                                        <span class="badge bg-primary ms-2">جديد</span>
                                        {% endif %}
                                    </h6>
                                    <p class="mb-2 text-muted">{{ notification.message }}</p>
                                    <small class="text-muted">
                                        <i class="fas fa-clock me-1"></i>
                                        {{ notification.created_at|timeago }}
                                        {% if notification.read_at %}
                                        <span class="ms-3">
                                            <i class="fas fa-check me-1"></i>
                                            قُرئ في {{ notification.read_at|timeago }}
                                        </span>
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                            <div class="notification-actions">
                                {% if notification.action_url %}
                                <a href="{{ notification.action_url }}" 
                                   class="btn btn-sm btn-outline-primary me-2"
                                   onclick="markAsRead({{ notification.id }})">
                                    {{ notification.action_text or 'عرض' }}
                                </a>
                                {% endif %}
                                {% if not notification.is_read %}
                                <button type="button" 
                                        class="btn btn-sm btn-outline-success"
                                        onclick="markAsRead({{ notification.id }})"
                                        title="تحديد كمقروء">
                                    <i class="fas fa-check"></i>
                                </button>
                                {% endif %}
                                <a href="{{ url_for('notifications.detail', notification_id=notification.id) }}" 
                                   class="btn btn-sm btn-outline-info"
                                   title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-bell-slash fa-3x text-gray-300 mb-3"></i>
                    <h5 class="text-gray-600">لا توجد إشعارات</h5>
                    <p class="text-muted">
                        {% if unread_only %}
                        لا توجد إشعارات غير مقروءة
                        {% else %}
                        لم يتم العثور على إشعارات تطابق المعايير المحددة
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Mark as Read Modal -->
<div class="modal fade" id="markAllReadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد العملية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل تريد تحديد جميع الإشعارات كمقروءة؟</p>
                <p class="text-muted small">سيتم تحديد {{ unread_count }} إشعار كمقروء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="confirmMarkAllAsRead()">تأكيد</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function markAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/mark-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (notificationElement) {
                notificationElement.classList.remove('list-group-item-light');
                const title = notificationElement.querySelector('h6');
                title.classList.remove('fw-bold');
                const badge = title.querySelector('.badge');
                if (badge) badge.remove();
                
                const markBtn = notificationElement.querySelector('.btn-outline-success');
                if (markBtn) markBtn.remove();
            }
            
            // Update unread count
            updateUnreadCount();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

function markAllAsRead() {
    const modal = new bootstrap.Modal(document.getElementById('markAllReadModal'));
    modal.show();
}

function confirmMarkAllAsRead() {
    fetch('/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error marking all notifications as read:', error);
    });
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('markAllReadModal'));
    modal.hide();
}

function updateUnreadCount() {
    fetch('/notifications/unread-count')
    .then(response => response.json())
    .then(data => {
        // Update any unread count displays
        const badges = document.querySelectorAll('.notification-badge');
        badges.forEach(badge => {
            if (data.count > 0) {
                badge.textContent = data.count;
                badge.style.display = 'inline-block';
            } else {
                badge.style.display = 'none';
            }
        });
    })
    .catch(error => {
        console.error('Error updating unread count:', error);
    });
}
</script>
{% endblock %}
