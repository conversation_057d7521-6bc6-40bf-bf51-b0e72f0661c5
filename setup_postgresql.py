#!/usr/bin/env python3
"""
إعداد قاعدة بيانات PostgreSQL لنظام SystemTax
PostgreSQL Database Setup for SystemTax
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from sqlalchemy import create_engine, text
from sqlalchemy.exc import OperationalError
import getpass

def create_database():
    """إنشاء قاعدة البيانات والمستخدم"""
    print("🚀 إعداد قاعدة بيانات PostgreSQL لنظام SystemTax")
    print("=" * 60)
    
    # الحصول على معلومات الاتصال
    print("\n📋 معلومات الاتصال بـ PostgreSQL:")
    host = input("Host (افتراضي: localhost): ").strip() or "localhost"
    port = input("Port (افتراضي: 5432): ").strip() or "5432"
    admin_user = input("Admin Username (افتراضي: postgres): ").strip() or "postgres"
    admin_password = getpass.getpass("Admin Password: ")
    
    print("\n📋 معلومات قاعدة البيانات الجديدة:")
    db_name = input("Database Name (افتراضي: systemtax_production): ").strip() or "systemtax_production"
    db_user = input("Database User (افتراضي: systemtax_user): ").strip() or "systemtax_user"
    db_password = getpass.getpass("Database Password: ")
    
    try:
        # الاتصال بـ PostgreSQL كـ admin
        print(f"\n🔌 الاتصال بـ PostgreSQL على {host}:{port}...")
        conn = psycopg2.connect(
            host=host,
            port=port,
            user=admin_user,
            password=admin_password,
            database="postgres"
        )
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # التحقق من وجود المستخدم
        print(f"👤 التحقق من وجود المستخدم {db_user}...")
        cursor.execute("SELECT 1 FROM pg_roles WHERE rolname = %s", (db_user,))
        user_exists = cursor.fetchone()
        
        if not user_exists:
            print(f"➕ إنشاء المستخدم {db_user}...")
            cursor.execute(f"CREATE USER {db_user} WITH PASSWORD %s", (db_password,))
            print(f"✅ تم إنشاء المستخدم {db_user}")
        else:
            print(f"ℹ️ المستخدم {db_user} موجود مسبقاً")
        
        # التحقق من وجود قاعدة البيانات
        print(f"🗄️ التحقق من وجود قاعدة البيانات {db_name}...")
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = %s", (db_name,))
        db_exists = cursor.fetchone()
        
        if not db_exists:
            print(f"➕ إنشاء قاعدة البيانات {db_name}...")
            cursor.execute(f"CREATE DATABASE {db_name} OWNER {db_user}")
            print(f"✅ تم إنشاء قاعدة البيانات {db_name}")
        else:
            print(f"ℹ️ قاعدة البيانات {db_name} موجودة مسبقاً")
        
        # منح الصلاحيات
        print(f"🔐 منح الصلاحيات للمستخدم {db_user}...")
        cursor.execute(f"GRANT ALL PRIVILEGES ON DATABASE {db_name} TO {db_user}")
        cursor.execute(f"ALTER USER {db_user} CREATEDB")
        
        cursor.close()
        conn.close()
        
        print(f"✅ تم إعداد قاعدة البيانات بنجاح!")
        
        # إنشاء connection string
        connection_string = f"postgresql://{db_user}:{db_password}@{host}:{port}/{db_name}"
        
        print(f"\n📝 Connection String:")
        print(f"DATABASE_URL={connection_string}")
        
        return connection_string
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return None


def migrate_from_sqlite(sqlite_path, postgresql_url):
    """ترحيل البيانات من SQLite إلى PostgreSQL"""
    print(f"\n🔄 ترحيل البيانات من SQLite إلى PostgreSQL...")
    
    if not os.path.exists(sqlite_path):
        print(f"❌ ملف SQLite غير موجود: {sqlite_path}")
        return False
    
    try:
        # الاتصال بقواعد البيانات
        sqlite_engine = create_engine(f"sqlite:///{sqlite_path}")
        postgresql_engine = create_engine(postgresql_url)
        
        # الحصول على قائمة الجداول
        with sqlite_engine.connect() as sqlite_conn:
            tables_result = sqlite_conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in tables_result if not row[0].startswith('sqlite_')]
        
        print(f"📋 تم العثور على {len(tables)} جدول للترحيل")
        
        # ترحيل كل جدول
        for table in tables:
            print(f"📦 ترحيل جدول {table}...")
            
            # قراءة البيانات من SQLite
            with sqlite_engine.connect() as sqlite_conn:
                data = sqlite_conn.execute(text(f"SELECT * FROM {table}"))
                rows = data.fetchall()
                columns = data.keys()
            
            if rows:
                # إنشاء الجدول في PostgreSQL إذا لم يكن موجوداً
                with postgresql_engine.connect() as pg_conn:
                    # إنشاء placeholders للقيم
                    placeholders = ', '.join([f':{col}' for col in columns])
                    
                    # إدراج البيانات
                    for row in rows:
                        row_dict = dict(zip(columns, row))
                        try:
                            pg_conn.execute(
                                text(f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"),
                                row_dict
                            )
                        except Exception as e:
                            print(f"⚠️ خطأ في إدراج سجل في جدول {table}: {e}")
                    
                    pg_conn.commit()
                
                print(f"✅ تم ترحيل {len(rows)} سجل من جدول {table}")
            else:
                print(f"ℹ️ جدول {table} فارغ")
        
        print(f"🎉 تم ترحيل جميع البيانات بنجاح!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في ترحيل البيانات: {e}")
        return False


def setup_production_database():
    """إعداد قاعدة بيانات الإنتاج"""
    print("🏭 إعداد قاعدة بيانات الإنتاج")
    print("=" * 40)
    
    # إنشاء قاعدة البيانات
    connection_string = create_database()
    if not connection_string:
        return False
    
    # إنشاء الجداول
    print(f"\n🏗️ إنشاء جداول النظام...")
    try:
        from app import create_app, db
        from app.models.system_settings import SystemSettings
        
        # إنشاء التطبيق مع إعدادات الإنتاج
        os.environ['DATABASE_URL'] = connection_string
        os.environ['FLASK_ENV'] = 'production'
        
        app = create_app()
        with app.app_context():
            # إنشاء الجداول
            db.create_all()
            print("✅ تم إنشاء جداول النظام")
            
            # تهيئة الإعدادات الافتراضية
            print("⚙️ تهيئة الإعدادات الافتراضية...")
            SystemSettings.initialize_default_settings()
            print("✅ تم تهيئة الإعدادات الافتراضية")
        
        print(f"\n🎉 تم إعداد قاعدة بيانات الإنتاج بنجاح!")
        print(f"📝 أضف هذا السطر لملف .env:")
        print(f"DATABASE_URL={connection_string}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {e}")
        return False


def migrate_existing_data():
    """ترحيل البيانات الموجودة"""
    print("\n🔄 ترحيل البيانات الموجودة")
    print("=" * 30)
    
    sqlite_path = input("مسار ملف SQLite (افتراضي: instance/systemtax.db): ").strip()
    if not sqlite_path:
        sqlite_path = "instance/systemtax.db"
    
    postgresql_url = input("PostgreSQL URL: ").strip()
    if not postgresql_url:
        print("❌ يجب إدخال PostgreSQL URL")
        return False
    
    return migrate_from_sqlite(sqlite_path, postgresql_url)


def main():
    """الدالة الرئيسية"""
    print("🎯 مرحباً بك في معالج إعداد قاعدة بيانات SystemTax")
    print("=" * 60)
    
    while True:
        print("\nاختر العملية المطلوبة:")
        print("1. إعداد قاعدة بيانات إنتاج جديدة")
        print("2. ترحيل البيانات من SQLite إلى PostgreSQL")
        print("3. الخروج")
        
        choice = input("\nاختيارك (1-3): ").strip()
        
        if choice == "1":
            setup_production_database()
        elif choice == "2":
            migrate_existing_data()
        elif choice == "3":
            print("👋 وداعاً!")
            break
        else:
            print("❌ اختيار غير صحيح")
        
        input("\nاضغط Enter للمتابعة...")


if __name__ == "__main__":
    main()
