"""
Customer model for managing customer data
"""

import uuid
from datetime import datetime
from app import db

class Customer(db.Model):
    __tablename__ = 'customers'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    name = db.Column(db.String(150), nullable=False)
    name_en = db.Column(db.String(150))
    tax_id = db.Column(db.String(20), index=True)
    address = db.Column(db.Text)
    address_en = db.Column(db.Text)
    email = db.Column(db.String(100), index=True)
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    credit_limit = db.Column(db.Numeric(14, 2), default=0)
    payment_terms = db.Column(db.Integer, default=30)  # days
    currency = db.Column(db.String(3), default='EGP')
    postal_code = db.Column(db.String(10))
    country = db.Column(db.String(50), default='مصر')
    notes = db.Column(db.Text)
    account_id = db.Column(db.String(36), db.ForeignKey('accounts.id'))
    created_by_id = db.Column(db.String(36), db.ForeignKey('users.id'))
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    account = db.relationship('Account', backref='customers')
    created_by = db.relationship('User', backref='created_customers')
    invoices = db.relationship('Invoice', backref='customer', lazy='dynamic')
    receipts = db.relationship('Receipt', foreign_keys='Receipt.customer_id', lazy='dynamic')
    
    def __init__(self, name, name_en=None, tax_id=None, address=None,
                 address_en=None, email=None, phone=None, mobile=None,
                 credit_limit=None, payment_terms=30, currency='EGP',
                 postal_code=None, country='مصر', notes=None,
                 account_id=None, created_by_id=None, is_active=True):
        self.name = name
        self.name_en = name_en
        self.tax_id = tax_id
        self.address = address
        self.address_en = address_en
        self.email = email
        self.phone = phone
        self.mobile = mobile
        self.credit_limit = credit_limit or 0
        self.payment_terms = payment_terms
        self.currency = currency
        self.postal_code = postal_code
        self.country = country
        self.notes = notes
        self.account_id = account_id
        self.created_by_id = created_by_id
        self.is_active = is_active
    
    def get_display_name(self):
        """Get display name for customer"""
        return self.name
    
    def get_contact_info(self):
        """Get formatted contact information"""
        contact = []
        if self.phone:
            contact.append(f"هاتف: {self.phone}")
        if self.mobile:
            contact.append(f"موبايل: {self.mobile}")
        if self.email:
            contact.append(f"بريد: {self.email}")
        return " | ".join(contact)
    
    def get_total_invoices_amount(self):
        """Get total amount of all invoices"""
        from app.models.invoice import Invoice
        total = db.session.query(db.func.sum(Invoice.total_amount)).filter_by(
            customer_id=self.id
        ).scalar()
        return float(total or 0)
    
    def get_total_receipts_amount(self):
        """Get total amount of all receipts"""
        from app.models.receipt import Receipt
        total = db.session.query(db.func.sum(Receipt.amount_received)).filter_by(
            customer_id=self.id
        ).scalar()
        return float(total or 0)
    
    def get_outstanding_balance(self):
        """Get outstanding balance (invoices - receipts)"""
        return self.get_total_invoices_amount() - self.get_total_receipts_amount()
    
    def get_recent_invoices(self, limit=5):
        """Get recent invoices for this customer"""
        return self.invoices.order_by(
            self.invoices.property.mapper.class_.issue_date.desc()
        ).limit(limit).all()
    
    def get_recent_receipts(self, limit=5):
        """Get recent receipts for this customer"""
        return self.receipts.order_by(
            self.receipts.property.mapper.class_.receipt_date.desc()
        ).limit(limit).all()
    
    @classmethod
    def search(cls, term):
        """Search customers by name, tax_id, email, or phone"""
        return cls.query.filter(
            db.or_(
                cls.name.ilike(f'%{term}%'),
                cls.name_en.ilike(f'%{term}%') if term else False,
                cls.tax_id.ilike(f'%{term}%') if term else False,
                cls.email.ilike(f'%{term}%') if term else False,
                cls.phone.ilike(f'%{term}%') if term else False,
                cls.mobile.ilike(f'%{term}%') if term else False
            ),
            cls.is_active == True
        ).order_by(cls.name).all()
    
    @classmethod
    def get_active(cls):
        """Get all active customers"""
        return cls.query.filter_by(is_active=True).order_by(cls.name).all()
    
    def to_dict(self):
        """Convert customer to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'name_en': self.name_en,
            'tax_id': self.tax_id,
            'address': self.address,
            'address_en': self.address_en,
            'email': self.email,
            'phone': self.phone,
            'mobile': self.mobile,
            'is_active': self.is_active,
            'total_invoices': self.get_total_invoices_amount(),
            'total_receipts': self.get_total_receipts_amount(),
            'outstanding_balance': self.get_outstanding_balance(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<Customer {self.name}>'
