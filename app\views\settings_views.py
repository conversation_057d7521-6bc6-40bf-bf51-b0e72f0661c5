"""
واجهات إدارة إعدادات النظام
System Settings Management Views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.system_settings import SystemSettings, get_setting, set_setting, get_eta_settings, get_company_settings
from app.utils.decorators import permission_required
from app.services.eta_service import ETAService
import os

# Create blueprint
settings_bp = Blueprint('settings', __name__, url_prefix='/settings')


@settings_bp.route('/')
@login_required
@permission_required('admin')
def index():
    """عرض جميع الإعدادات"""
    categories = {
        'eta': 'إعدادات مصلحة الضرائب',
        'company': 'إعدادات الشركة',
        'system': 'إعدادات النظام',
        'security': 'إعدادات الأمان',
        'notifications': 'إعدادات الإشعارات'
    }
    
    settings_by_category = {}
    for category, title in categories.items():
        settings_by_category[category] = {
            'title': title,
            'settings': SystemSettings.query.filter_by(category=category).all()
        }
    
    return render_template(
        'settings/index.html',
        settings_by_category=settings_by_category,
        title='إعدادات النظام'
    )


@settings_bp.route('/eta')
@login_required
@permission_required('admin')
def eta_settings():
    """إعدادات مصلحة الضرائب"""
    eta_settings = SystemSettings.query.filter_by(category='eta').all()
    company_settings = SystemSettings.query.filter_by(category='company').all()
    
    return render_template(
        'settings/eta_settings.html',
        eta_settings=eta_settings,
        company_settings=company_settings,
        title='إعدادات مصلحة الضرائب'
    )


@settings_bp.route('/eta/save', methods=['POST'])
@login_required
@permission_required('admin')
def save_eta_settings():
    """حفظ إعدادات مصلحة الضرائب"""
    try:
        # ETA Settings
        eta_settings = [
            ('ETA_BASE_URL', request.form.get('eta_base_url', ''), 'string'),
            ('ETA_CLIENT_ID', request.form.get('eta_client_id', ''), 'string'),
            ('ETA_CLIENT_SECRET', request.form.get('eta_client_secret', ''), 'string'),
            ('ETA_ENVIRONMENT', request.form.get('eta_environment', 'sandbox'), 'string'),
            ('ETA_TIMEOUT', request.form.get('eta_timeout', '30'), 'integer'),
            ('ETA_AUTO_SUBMIT', request.form.get('eta_auto_submit') == 'on', 'boolean'),
            ('ETA_DEVICE_SERIAL', request.form.get('eta_device_serial', 'DEVICE001'), 'string'),
        ]
        
        # Company Settings
        company_settings = [
            ('COMPANY_NAME', request.form.get('company_name', ''), 'string'),
            ('COMPANY_TAX_ID', request.form.get('company_tax_id', ''), 'string'),
            ('COMPANY_ACTIVITY_CODE', request.form.get('company_activity_code', ''), 'string'),
            ('COMPANY_BRANCH_ID', request.form.get('company_branch_id', '0'), 'string'),
            ('COMPANY_ADDRESS', request.form.get('company_address', ''), 'string'),
            ('COMPANY_PHONE', request.form.get('company_phone', ''), 'string'),
            ('COMPANY_EMAIL', request.form.get('company_email', ''), 'string'),
        ]
        
        # Save ETA settings
        for key, value, data_type in eta_settings:
            set_setting(
                key=key,
                value=value,
                data_type=data_type,
                category='eta',
                updated_by=current_user.username
            )
        
        # Save Company settings
        for key, value, data_type in company_settings:
            set_setting(
                key=key,
                value=value,
                data_type=data_type,
                category='company',
                updated_by=current_user.username
            )
        
        flash('تم حفظ إعدادات مصلحة الضرائب بنجاح', 'success')
        
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ الإعدادات: {str(e)}', 'error')
    
    return redirect(url_for('settings.eta_settings'))


@settings_bp.route('/eta/test', methods=['POST'])
@login_required
@permission_required('admin')
def test_eta_connection():
    """اختبار الاتصال مع مصلحة الضرائب"""
    try:
        eta_service = ETAService()
        
        # Test authentication
        success = eta_service.authenticate()
        
        if success:
            # Test getting document types
            doc_types = eta_service.get_document_types()
            
            return jsonify({
                'success': True,
                'message': 'تم الاتصال بمصلحة الضرائب بنجاح',
                'data': {
                    'authenticated': True,
                    'document_types_count': len(doc_types) if doc_types else 0,
                    'environment': get_setting('ETA_ENVIRONMENT', 'sandbox')
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في المصادقة مع مصلحة الضرائب',
                'data': {
                    'authenticated': False,
                    'environment': get_setting('ETA_ENVIRONMENT', 'sandbox')
                }
            })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في الاتصال: {str(e)}',
            'data': {
                'authenticated': False,
                'error': str(e)
            }
        })


@settings_bp.route('/system')
@login_required
@permission_required('admin')
def system_settings():
    """إعدادات النظام العامة"""
    system_settings = SystemSettings.query.filter_by(category='system').all()
    security_settings = SystemSettings.query.filter_by(category='security').all()
    notification_settings = SystemSettings.query.filter_by(category='notifications').all()
    
    return render_template(
        'settings/system_settings.html',
        system_settings=system_settings,
        security_settings=security_settings,
        notification_settings=notification_settings,
        title='إعدادات النظام'
    )


@settings_bp.route('/system/save', methods=['POST'])
@login_required
@permission_required('admin')
def save_system_settings():
    """حفظ إعدادات النظام"""
    try:
        # System Settings
        system_settings = [
            ('SYSTEM_CURRENCY', request.form.get('system_currency', 'EGP'), 'string'),
            ('SYSTEM_LANGUAGE', request.form.get('system_language', 'ar'), 'string'),
            ('SYSTEM_TIMEZONE', request.form.get('system_timezone', 'Africa/Cairo'), 'string'),
            ('BACKUP_ENABLED', request.form.get('backup_enabled') == 'on', 'boolean'),
            ('BACKUP_RETENTION_DAYS', request.form.get('backup_retention_days', '30'), 'integer'),
        ]
        
        # Security Settings
        security_settings = [
            ('SESSION_TIMEOUT', request.form.get('session_timeout', '480'), 'integer'),
            ('PASSWORD_MIN_LENGTH', request.form.get('password_min_length', '8'), 'integer'),
            ('LOGIN_ATTEMPTS_LIMIT', request.form.get('login_attempts_limit', '5'), 'integer'),
        ]
        
        # Notification Settings
        notification_settings = [
            ('EMAIL_NOTIFICATIONS', request.form.get('email_notifications') == 'on', 'boolean'),
            ('SMS_NOTIFICATIONS', request.form.get('sms_notifications') == 'on', 'boolean'),
        ]
        
        # Save all settings
        all_settings = [
            (system_settings, 'system'),
            (security_settings, 'security'),
            (notification_settings, 'notifications')
        ]
        
        for settings_list, category in all_settings:
            for key, value, data_type in settings_list:
                set_setting(
                    key=key,
                    value=value,
                    data_type=data_type,
                    category=category,
                    updated_by=current_user.username
                )
        
        flash('تم حفظ إعدادات النظام بنجاح', 'success')
        
    except Exception as e:
        flash(f'حدث خطأ أثناء حفظ الإعدادات: {str(e)}', 'error')
    
    return redirect(url_for('settings.system_settings'))


@settings_bp.route('/backup/create', methods=['POST'])
@login_required
@permission_required('admin')
def create_backup():
    """إنشاء نسخة احتياطية"""
    try:
        from app.utils.backup import create_database_backup
        
        backup_file = create_database_backup()
        
        if backup_file:
            return jsonify({
                'success': True,
                'message': 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_file': backup_file
            })
        else:
            return jsonify({
                'success': False,
                'message': 'فشل في إنشاء النسخة الاحتياطية'
            })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في إنشاء النسخة الاحتياطية: {str(e)}'
        })


@settings_bp.route('/initialize', methods=['POST'])
@login_required
@permission_required('admin')
def initialize_settings():
    """تهيئة الإعدادات الافتراضية"""
    try:
        SystemSettings.initialize_default_settings()
        
        return jsonify({
            'success': True,
            'message': 'تم تهيئة الإعدادات الافتراضية بنجاح'
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في تهيئة الإعدادات: {str(e)}'
        })


@settings_bp.route('/export')
@login_required
@permission_required('admin')
def export_settings():
    """تصدير الإعدادات"""
    try:
        settings = SystemSettings.query.all()
        settings_data = [setting.to_dict() for setting in settings]
        
        return jsonify({
            'success': True,
            'data': settings_data,
            'count': len(settings_data)
        })
    
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'خطأ في تصدير الإعدادات: {str(e)}'
        })


@settings_bp.route('/reset/<category>', methods=['POST'])
@login_required
@permission_required('admin')
def reset_category_settings(category):
    """إعادة تعيين إعدادات فئة معينة"""
    try:
        # Delete existing settings in category (except system settings)
        SystemSettings.query.filter_by(category=category, is_system=False).delete()
        
        # Re-initialize default settings
        SystemSettings.initialize_default_settings()
        
        return jsonify({
            'success': True,
            'message': f'تم إعادة تعيين إعدادات {category} بنجاح'
        })
    
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'خطأ في إعادة التعيين: {str(e)}'
        })
