#!/usr/bin/env python3
"""
تشغيل نظام SystemTax في بيئة الإنتاج
Production Runner for SystemTax
"""

import os
import sys
from app import create_app, db
from app.models.system_settings import SystemSettings

def setup_environment():
    """إعداد متغيرات البيئة"""
    # تعيين بيئة الإنتاج
    os.environ['FLASK_ENV'] = 'production'
    
    # التحقق من متغيرات البيئة المطلوبة
    required_vars = [
        'DATABASE_URL',
        'SECRET_KEY',
        'ETA_CLIENT_ID',
        'ETA_CLIENT_SECRET',
        'COMPANY_TAX_ID'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.environ.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ متغيرات البيئة المطلوبة مفقودة:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\nيرجى تعيين هذه المتغيرات في ملف .env أو متغيرات النظام")
        return False
    
    return True

def check_database():
    """التحقق من قاعدة البيانات"""
    try:
        app = create_app('production')
        with app.app_context():
            # اختبار الاتصال
            db.engine.execute('SELECT 1')
            print("✅ الاتصال بقاعدة البيانات ناجح")
            
            # التحقق من الجداول
            tables = db.engine.table_names()
            if not tables:
                print("⚠️ قاعدة البيانات فارغة - سيتم إنشاء الجداول")
                db.create_all()
                SystemSettings.initialize_default_settings()
                print("✅ تم إنشاء الجداول وتهيئة الإعدادات")
            else:
                print(f"✅ تم العثور على {len(tables)} جدول في قاعدة البيانات")
            
            return True
            
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {e}")
        return False

def check_eta_settings():
    """التحقق من إعدادات ETA"""
    try:
        app = create_app('production')
        with app.app_context():
            from app.models.system_settings import get_setting
            
            eta_client_id = get_setting('ETA_CLIENT_ID')
            eta_client_secret = get_setting('ETA_CLIENT_SECRET')
            company_tax_id = get_setting('COMPANY_TAX_ID')
            
            if not eta_client_id or not eta_client_secret:
                print("⚠️ إعدادات ETA غير مكتملة")
                print("   يرجى تكوين إعدادات مصلحة الضرائب من واجهة الإدارة")
                return False
            
            if not company_tax_id:
                print("⚠️ الرقم الضريبي للشركة غير محدد")
                print("   يرجى تكوين معلومات الشركة من واجهة الإدارة")
                return False
            
            print("✅ إعدادات ETA مكتملة")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في التحقق من إعدادات ETA: {e}")
        return False

def run_production_server():
    """تشغيل خادم الإنتاج"""
    print("🚀 تشغيل نظام SystemTax في بيئة الإنتاج")
    print("=" * 50)
    
    # إعداد البيئة
    if not setup_environment():
        return False
    
    # التحقق من قاعدة البيانات
    print("\n🗄️ التحقق من قاعدة البيانات...")
    if not check_database():
        return False
    
    # التحقق من إعدادات ETA
    print("\n⚙️ التحقق من إعدادات ETA...")
    check_eta_settings()  # تحذير فقط، لا يوقف التشغيل
    
    # إنشاء التطبيق
    print("\n🏗️ إنشاء التطبيق...")
    app = create_app('production')
    
    # إعدادات الخادم
    host = os.environ.get('HOST', '0.0.0.0')
    port = int(os.environ.get('PORT', 8000))
    debug = False
    
    print(f"\n🌐 بدء الخادم على {host}:{port}")
    print(f"📱 رابط النظام: http://{host}:{port}")
    print("🔒 بيئة الإنتاج - Debug معطل")
    print("\nاضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        return False
    
    return True

def run_development_server():
    """تشغيل خادم التطوير"""
    print("🧪 تشغيل نظام SystemTax في بيئة التطوير")
    print("=" * 50)
    
    os.environ['FLASK_ENV'] = 'development'
    
    app = create_app('development')
    
    host = '127.0.0.1'
    port = 8000
    debug = True
    
    print(f"\n🌐 بدء خادم التطوير على {host}:{port}")
    print(f"📱 رابط النظام: http://{host}:{port}")
    print("🔧 بيئة التطوير - Debug مفعل")
    print("\nاضغط Ctrl+C لإيقاف الخادم")
    print("=" * 50)
    
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف الخادم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل الخادم: {e}")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    else:
        print("🎯 اختر وضع التشغيل:")
        print("1. الإنتاج (Production)")
        print("2. التطوير (Development)")
        choice = input("\nاختيارك (1-2): ").strip()
        
        if choice == "1":
            mode = "production"
        elif choice == "2":
            mode = "development"
        else:
            print("❌ اختيار غير صحيح")
            return
    
    if mode == "production":
        run_production_server()
    elif mode == "development":
        run_development_server()
    else:
        print("❌ وضع غير صحيح. استخدم: production أو development")
        print("مثال: python run_production.py production")

if __name__ == "__main__":
    main()
