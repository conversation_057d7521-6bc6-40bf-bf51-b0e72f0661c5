"""
System Settings model for application configuration
"""

import uuid
from datetime import datetime
from app import db

class SystemSetting(db.Model):
    __tablename__ = 'system_settings'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    key = db.Column(db.String(100), unique=True, nullable=False, index=True)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __init__(self, key, value, description=None):
        self.key = key
        self.value = value
        self.description = description
    
    @classmethod
    def get_value(cls, key, default=None):
        """Get setting value by key"""
        setting = cls.query.filter_by(key=key).first()
        return setting.value if setting else default
    
    @classmethod
    def set_value(cls, key, value, description=None):
        """Set setting value by key"""
        setting = cls.query.filter_by(key=key).first()
        if setting:
            setting.value = value
            setting.updated_at = datetime.utcnow()
            if description:
                setting.description = description
        else:
            setting = cls(key=key, value=value, description=description)
            db.session.add(setting)
        
        db.session.commit()
        return setting
    
    @classmethod
    def get_company_info(cls):
        """Get company information settings"""
        return {
            'name': cls.get_value('company_name', 'شركتك'),
            'name_en': cls.get_value('company_name_en', 'Your Company'),
            'tax_id': cls.get_value('company_tax_id', '*********'),
            'address': cls.get_value('company_address', 'عنوان الشركة'),
            'phone': cls.get_value('company_phone', '0*********0'),
            'email': cls.get_value('company_email', '<EMAIL>'),
        }
    
    @classmethod
    def get_invoice_settings(cls):
        """Get invoice-related settings"""
        return {
            'prefix': cls.get_value('invoice_prefix', 'INV'),
            'default_tax_rate': float(cls.get_value('default_tax_rate', 14)),
            'currency_code': cls.get_value('currency_code', 'EGP'),
            'currency_symbol': cls.get_value('currency_symbol', 'ج.م'),
        }
    
    @classmethod
    def get_receipt_settings(cls):
        """Get receipt-related settings"""
        return {
            'prefix': cls.get_value('receipt_prefix', 'REC'),
            'currency_code': cls.get_value('currency_code', 'EGP'),
            'currency_symbol': cls.get_value('currency_symbol', 'ج.م'),
        }
    
    @classmethod
    def get_tax_api_settings(cls):
        """Get tax authority API settings"""
        return {
            'base_url': cls.get_value('tax_api_base_url', 'https://api.eta.gov.eg'),
            'client_id': cls.get_value('tax_api_client_id', ''),
            'client_secret': cls.get_value('tax_api_client_secret', ''),
            'environment': cls.get_value('tax_api_environment', 'sandbox'),
        }
    
    @classmethod
    def initialize_default_settings(cls):
        """Initialize default system settings"""
        default_settings = [
            ('company_name', 'شركتك', 'اسم الشركة'),
            ('company_name_en', 'Your Company', 'Company Name in English'),
            ('company_tax_id', '*********', 'الرقم الضريبي للشركة'),
            ('company_address', 'عنوان الشركة', 'عنوان الشركة'),
            ('company_phone', '0*********0', 'هاتف الشركة'),
            ('company_email', '<EMAIL>', 'بريد الشركة الإلكتروني'),
            ('default_tax_rate', '14', 'معدل الضريبة الافتراضي'),
            ('invoice_prefix', 'INV', 'بادئة رقم الفاتورة'),
            ('receipt_prefix', 'REC', 'بادئة رقم الإيصال'),
            ('currency_code', 'EGP', 'رمز العملة'),
            ('currency_symbol', 'ج.م', 'رمز العملة'),
            ('tax_api_base_url', 'https://api.eta.gov.eg', 'رابط API مصلحة الضرائب'),
            ('tax_api_environment', 'sandbox', 'بيئة API الضرائب'),
        ]
        
        for key, value, description in default_settings:
            existing = cls.query.filter_by(key=key).first()
            if not existing:
                setting = cls(key=key, value=value, description=description)
                db.session.add(setting)
        
        db.session.commit()
    
    def to_dict(self):
        """Convert setting to dictionary"""
        return {
            'id': self.id,
            'key': self.key,
            'value': self.value,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f'<SystemSetting {self.key}: {self.value}>'
