"""
Helper functions for account management
"""

from typing import List, Dict, Optional, Tuple
from decimal import Decimal
from datetime import date, datetime
from sqlalchemy import func, and_, or_
from app import db
from app.models.account import Account
from app.models.journal_line import JournalLine
from app.models.journal_entry import JournalEntry


def create_default_accounts() -> List[Account]:
    """
    Create default chart of accounts for a new company
    Returns list of created accounts
    """
    default_accounts = [
        # Assets (1000-1999)
        ('1000', 'الأصول', 'Assets', 'Asset', None),
        ('1100', 'الأصول المتداولة', 'Current Assets', 'Asset', '1000'),
        ('1110', 'النقدية والبنوك', 'Cash and Banks', 'Asset', '1100'),
        ('1111', 'الصندوق', 'Cash on Hand', 'Asset', '1110'),
        ('1112', 'البنك الأهلي المصري', 'National Bank of Egypt', 'Asset', '1110'),
        ('1120', 'العملاء', 'Accounts Receivable', 'Asset', '1100'),
        ('1121', 'عملاء محليين', 'Local Customers', 'Asset', '1120'),
        ('1122', 'عملاء أجانب', 'Foreign Customers', 'Asset', '1120'),
        ('1130', 'المخزون', 'Inventory', 'Asset', '1100'),
        ('1131', 'مخزون بضاعة', 'Merchandise Inventory', 'Asset', '1130'),
        ('1140', 'مصروفات مدفوعة مقدماً', 'Prepaid Expenses', 'Asset', '1100'),
        
        ('1200', 'الأصول الثابتة', 'Fixed Assets', 'Asset', '1000'),
        ('1210', 'الأراضي والمباني', 'Land and Buildings', 'Asset', '1200'),
        ('1211', 'الأراضي', 'Land', 'Asset', '1210'),
        ('1212', 'المباني', 'Buildings', 'Asset', '1210'),
        ('1220', 'المعدات والآلات', 'Equipment and Machinery', 'Asset', '1200'),
        ('1221', 'معدات المكتب', 'Office Equipment', 'Asset', '1220'),
        ('1222', 'أجهزة الكمبيوتر', 'Computer Equipment', 'Asset', '1220'),
        ('1230', 'مجمع الإهلاك', 'Accumulated Depreciation', 'Asset', '1200'),
        
        # Liabilities (2000-2999)
        ('2000', 'الخصوم', 'Liabilities', 'Liability', None),
        ('2100', 'الخصوم المتداولة', 'Current Liabilities', 'Liability', '2000'),
        ('2110', 'الموردين', 'Accounts Payable', 'Liability', '2100'),
        ('2111', 'موردين محليين', 'Local Suppliers', 'Liability', '2110'),
        ('2112', 'موردين أجانب', 'Foreign Suppliers', 'Liability', '2110'),
        ('2120', 'الضرائب المستحقة', 'Taxes Payable', 'Liability', '2100'),
        ('2121', 'ضريبة القيمة المضافة', 'VAT Payable', 'Liability', '2120'),
        ('2122', 'ضريبة الدخل', 'Income Tax Payable', 'Liability', '2120'),
        ('2130', 'المرتبات المستحقة', 'Salaries Payable', 'Liability', '2100'),
        ('2140', 'قروض قصيرة الأجل', 'Short-term Loans', 'Liability', '2100'),
        
        ('2200', 'الخصوم طويلة الأجل', 'Long-term Liabilities', 'Liability', '2000'),
        ('2210', 'قروض طويلة الأجل', 'Long-term Loans', 'Liability', '2200'),
        
        # Equity (3000-3999)
        ('3000', 'حقوق الملكية', 'Equity', 'Equity', None),
        ('3100', 'رأس المال', 'Capital', 'Equity', '3000'),
        ('3110', 'رأس المال المدفوع', 'Paid-in Capital', 'Equity', '3100'),
        ('3200', 'الأرباح المحتجزة', 'Retained Earnings', 'Equity', '3000'),
        ('3210', 'أرباح العام الحالي', 'Current Year Earnings', 'Equity', '3200'),
        ('3220', 'أرباح السنوات السابقة', 'Prior Years Earnings', 'Equity', '3200'),
        
        # Income (4000-4999)
        ('4000', 'الإيرادات', 'Revenue', 'Income', None),
        ('4100', 'إيرادات المبيعات', 'Sales Revenue', 'Income', '4000'),
        ('4110', 'مبيعات محلية', 'Local Sales', 'Income', '4100'),
        ('4120', 'مبيعات تصدير', 'Export Sales', 'Income', '4100'),
        ('4200', 'إيرادات أخرى', 'Other Revenue', 'Income', '4000'),
        ('4210', 'إيرادات فوائد', 'Interest Revenue', 'Income', '4200'),
        ('4220', 'إيرادات استثمارات', 'Investment Revenue', 'Income', '4200'),
        
        # Expenses (5000-5999)
        ('5000', 'المصروفات', 'Expenses', 'Expense', None),
        ('5100', 'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 'Expense', '5000'),
        ('5110', 'مشتريات', 'Purchases', 'Expense', '5100'),
        ('5120', 'مصروفات شحن', 'Freight Expenses', 'Expense', '5100'),
        
        ('5200', 'المصروفات التشغيلية', 'Operating Expenses', 'Expense', '5000'),
        ('5210', 'مصروفات إدارية', 'Administrative Expenses', 'Expense', '5200'),
        ('5211', 'المرتبات والأجور', 'Salaries and Wages', 'Expense', '5210'),
        ('5212', 'إيجار المكتب', 'Office Rent', 'Expense', '5210'),
        ('5213', 'مصروفات كهرباء', 'Electricity Expenses', 'Expense', '5210'),
        ('5214', 'مصروفات هاتف وإنترنت', 'Phone and Internet', 'Expense', '5210'),
        ('5215', 'مصروفات قرطاسية', 'Stationery Expenses', 'Expense', '5210'),
        
        ('5220', 'مصروفات تسويقية', 'Marketing Expenses', 'Expense', '5200'),
        ('5221', 'إعلانات', 'Advertising', 'Expense', '5220'),
        ('5222', 'عمولات مبيعات', 'Sales Commissions', 'Expense', '5220'),
        
        ('5300', 'المصروفات المالية', 'Financial Expenses', 'Expense', '5000'),
        ('5310', 'فوائد القروض', 'Interest Expenses', 'Expense', '5300'),
        ('5320', 'رسوم بنكية', 'Bank Charges', 'Expense', '5300'),
    ]
    
    created_accounts = {}
    accounts_list = []
    
    try:
        for code, name, name_en, account_type, parent_code in default_accounts:
            # Find parent account if specified
            parent_id = None
            if parent_code and parent_code in created_accounts:
                parent_id = created_accounts[parent_code].id
            
            # Check if account already exists
            existing = Account.query.filter_by(code=code).first()
            if existing:
                created_accounts[code] = existing
                continue
            
            # Create new account
            account = Account(
                code=code,
                name=name,
                name_en=name_en,
                type=account_type,
                parent_id=parent_id,
                is_active=True
            )
            
            db.session.add(account)
            db.session.flush()  # Get the ID
            
            created_accounts[code] = account
            accounts_list.append(account)
        
        db.session.commit()
        return accounts_list
        
    except Exception as e:
        db.session.rollback()
        raise e


def get_account_hierarchy() -> Dict:
    """
    Get complete account hierarchy as nested dictionary
    """
    accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
    
    # Build hierarchy
    hierarchy = {}
    account_map = {account.id: account for account in accounts}
    
    for account in accounts:
        if account.parent_id is None:
            # Root account
            hierarchy[account.id] = {
                'account': account,
                'children': {}
            }
        else:
            # Find parent in hierarchy
            parent = account_map.get(account.parent_id)
            if parent:
                # Add to parent's children
                parent_node = find_account_in_hierarchy(hierarchy, parent.id)
                if parent_node:
                    parent_node['children'][account.id] = {
                        'account': account,
                        'children': {}
                    }
    
    return hierarchy


def find_account_in_hierarchy(hierarchy: Dict, account_id: str) -> Optional[Dict]:
    """
    Find account node in hierarchy dictionary
    """
    for node_id, node in hierarchy.items():
        if node_id == account_id:
            return node
        
        # Search in children recursively
        found = find_account_in_hierarchy(node['children'], account_id)
        if found:
            return found
    
    return None


def calculate_trial_balance(as_of_date: Optional[date] = None) -> Dict:
    """
    Calculate trial balance as of specified date
    """
    if as_of_date is None:
        as_of_date = date.today()
    
    # Get all leaf accounts (accounts with no children)
    leaf_accounts = Account.query.filter(
        Account.is_active == True,
        ~Account.children.any()
    ).order_by(Account.code).all()
    
    trial_balance = {
        'accounts': [],
        'total_debits': Decimal('0'),
        'total_credits': Decimal('0'),
        'is_balanced': True,
        'as_of_date': as_of_date
    }
    
    for account in leaf_accounts:
        balance = account.get_balance(as_of_date)
        
        if balance != 0:
            # Determine debit/credit based on account type and balance
            if account.type in ['Asset', 'Expense']:
                # Normal debit balance accounts
                debit_balance = balance if balance > 0 else Decimal('0')
                credit_balance = abs(balance) if balance < 0 else Decimal('0')
            else:
                # Normal credit balance accounts (Liability, Equity, Income)
                credit_balance = balance if balance > 0 else Decimal('0')
                debit_balance = abs(balance) if balance < 0 else Decimal('0')
            
            if debit_balance > 0 or credit_balance > 0:
                trial_balance['accounts'].append({
                    'account': account,
                    'debit_balance': debit_balance,
                    'credit_balance': credit_balance
                })
                
                trial_balance['total_debits'] += debit_balance
                trial_balance['total_credits'] += credit_balance
    
    # Check if balanced
    trial_balance['is_balanced'] = (
        trial_balance['total_debits'] == trial_balance['total_credits']
    )
    
    return trial_balance


def get_account_balances_by_type(as_of_date: Optional[date] = None) -> Dict:
    """
    Get account balances grouped by account type
    """
    if as_of_date is None:
        as_of_date = date.today()
    
    balances = {
        'Asset': Decimal('0'),
        'Liability': Decimal('0'),
        'Equity': Decimal('0'),
        'Income': Decimal('0'),
        'Expense': Decimal('0')
    }
    
    # Get all leaf accounts
    leaf_accounts = Account.query.filter(
        Account.is_active == True,
        ~Account.children.any()
    ).all()
    
    for account in leaf_accounts:
        balance = account.get_balance(as_of_date)
        balances[account.type] += balance
    
    return balances


def validate_account_code(code: str, exclude_account_id: Optional[str] = None) -> Tuple[bool, str]:
    """
    Validate account code format and uniqueness
    Returns (is_valid, error_message)
    """
    # Check format
    if not code:
        return False, "رمز الحساب مطلوب"
    
    if not code.isdigit():
        return False, "رمز الحساب يجب أن يحتوي على أرقام فقط"
    
    if len(code) < 1 or len(code) > 20:
        return False, "رمز الحساب يجب أن يكون بين 1 و 20 رقم"
    
    # Check uniqueness
    query = Account.query.filter_by(code=code)
    if exclude_account_id:
        query = query.filter(Account.id != exclude_account_id)
    
    if query.first():
        return False, "رمز الحساب موجود بالفعل"
    
    return True, ""


def suggest_account_code(account_type: str, parent_code: Optional[str] = None) -> str:
    """
    Suggest next available account code based on type and parent
    """
    # Base codes for each type
    type_prefixes = {
        'Asset': '1',
        'Liability': '2',
        'Equity': '3',
        'Income': '4',
        'Expense': '5'
    }
    
    if parent_code:
        # Find next available code under parent
        prefix = parent_code
        existing_codes = Account.query.filter(
            Account.code.like(f"{prefix}%")
        ).with_entities(Account.code).all()
        
        existing_numbers = []
        for (code,) in existing_codes:
            if code.startswith(prefix) and len(code) > len(prefix):
                try:
                    suffix = code[len(prefix):]
                    if suffix.isdigit():
                        existing_numbers.append(int(suffix))
                except ValueError:
                    continue
        
        # Find next available number
        next_number = 1
        while next_number in existing_numbers:
            next_number += 1
        
        return f"{prefix}{next_number:02d}"
    
    else:
        # Find next available main code
        prefix = type_prefixes.get(account_type, '9')
        base_code = f"{prefix}000"
        
        existing_codes = Account.query.filter(
            Account.code.like(f"{prefix}%")
        ).with_entities(Account.code).all()
        
        existing_numbers = []
        for (code,) in existing_codes:
            if code.startswith(prefix) and len(code) == 4:
                try:
                    suffix = code[1:]
                    if suffix.isdigit():
                        existing_numbers.append(int(suffix))
                except ValueError:
                    continue
        
        # Find next available number
        next_number = 100
        while next_number in existing_numbers:
            next_number += 100
        
        return f"{prefix}{next_number:03d}"


def export_accounts_to_csv() -> str:
    """
    Export all accounts to CSV format
    Returns CSV string
    """
    import csv
    import io
    
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow([
        'رمز الحساب', 'اسم الحساب', 'الاسم بالإنجليزية',
        'نوع الحساب', 'رمز الحساب الأب', 'الوصف', 'نشط'
    ])
    
    # Write accounts
    accounts = Account.query.order_by(Account.code).all()
    for account in accounts:
        parent_code = account.parent.code if account.parent else ''
        writer.writerow([
            account.code,
            account.name,
            account.name_en or '',
            account.type,
            parent_code,
            account.description or '',
            'نعم' if account.is_active else 'لا'
        ])
    
    return output.getvalue()


def import_accounts_from_csv(csv_content: str, update_existing: bool = False) -> Tuple[int, int, List[str]]:
    """
    Import accounts from CSV content
    Returns (created_count, updated_count, errors)
    """
    import csv
    import io
    
    created_count = 0
    updated_count = 0
    errors = []
    
    try:
        reader = csv.DictReader(io.StringIO(csv_content))
        
        for row_num, row in enumerate(reader, start=2):
            try:
                code = row.get('رمز الحساب', '').strip()
                name = row.get('اسم الحساب', '').strip()
                name_en = row.get('الاسم بالإنجليزية', '').strip()
                account_type = row.get('نوع الحساب', '').strip()
                parent_code = row.get('رمز الحساب الأب', '').strip()
                description = row.get('الوصف', '').strip()
                is_active = row.get('نشط', '').strip().lower() in ['نعم', 'yes', 'true', '1']
                
                if not code or not name or not account_type:
                    errors.append(f"الصف {row_num}: البيانات الأساسية مطلوبة")
                    continue
                
                # Find parent if specified
                parent_id = None
                if parent_code:
                    parent = Account.query.filter_by(code=parent_code).first()
                    if not parent:
                        errors.append(f"الصف {row_num}: الحساب الأب غير موجود")
                        continue
                    parent_id = parent.id
                
                # Check if account exists
                existing = Account.query.filter_by(code=code).first()
                
                if existing:
                    if update_existing:
                        existing.name = name
                        existing.name_en = name_en or None
                        existing.type = account_type
                        existing.parent_id = parent_id
                        existing.description = description or None
                        existing.is_active = is_active
                        updated_count += 1
                    else:
                        errors.append(f"الصف {row_num}: الحساب موجود بالفعل")
                else:
                    account = Account(
                        code=code,
                        name=name,
                        name_en=name_en or None,
                        type=account_type,
                        parent_id=parent_id,
                        description=description or None,
                        is_active=is_active
                    )
                    db.session.add(account)
                    created_count += 1
                    
            except Exception as e:
                errors.append(f"الصف {row_num}: {str(e)}")
        
        if not errors:
            db.session.commit()
        else:
            db.session.rollback()
            
    except Exception as e:
        errors.append(f"خطأ في معالجة الملف: {str(e)}")
        db.session.rollback()
    
    return created_count, updated_count, errors
