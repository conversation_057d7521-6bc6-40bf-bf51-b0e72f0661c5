"""
Tests for customers and vendors functionality
"""

import pytest
from decimal import Decimal
from datetime import date, datetime
from app import db
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.models.account import Account
from app.models.user import User


class TestCustomerModel:
    """Test Customer model functionality"""
    
    def test_customer_creation(self, app, admin_user):
        """Test creating a new customer"""
        with app.app_context():
            customer = Customer(
                name='شركة الاختبار',
                customer_type='company',
                tax_id='*********',
                email='<EMAIL>',
                phone='0*********0',
                created_by_id=admin_user.id
            )
            db.session.add(customer)
            db.session.commit()
            
            assert customer.id is not None
            assert customer.name == 'شركة الاختبار'
            assert customer.customer_type == 'company'
            assert customer.tax_id == '*********'
            assert customer.is_active is True
            assert customer.created_by == admin_user
    
    def test_customer_type_display(self, app, admin_user):
        """Test customer type display method"""
        with app.app_context():
            individual = Customer(
                name='أحمد محمد',
                customer_type='individual',
                created_by_id=admin_user.id
            )
            company = Customer(
                name='شركة الاختبار',
                customer_type='company',
                created_by_id=admin_user.id
            )
            
            assert individual.get_type_display() == 'فرد'
            assert company.get_type_display() == 'شركة'
    
    def test_customer_balance_calculation(self, app, admin_user):
        """Test customer balance calculation"""
        with app.app_context():
            customer = Customer(
                name='عميل الاختبار',
                customer_type='individual',
                created_by_id=admin_user.id
            )
            db.session.add(customer)
            db.session.commit()
            
            # Initially balance should be zero
            assert customer.get_balance() == Decimal('0')
    
    def test_customer_can_be_deleted(self, app, admin_user):
        """Test customer deletion validation"""
        with app.app_context():
            customer = Customer(
                name='عميل للحذف',
                customer_type='individual',
                created_by_id=admin_user.id
            )
            db.session.add(customer)
            db.session.commit()
            
            # Customer without transactions can be deleted
            assert customer.can_be_deleted() is True
    
    def test_customer_with_account(self, app, admin_user, test_accounts):
        """Test customer with linked account"""
        with app.app_context():
            customer_account = test_accounts['customer_receivable']
            
            customer = Customer(
                name='عميل مع حساب',
                customer_type='company',
                account_id=customer_account.id,
                created_by_id=admin_user.id
            )
            db.session.add(customer)
            db.session.commit()
            
            assert customer.account == customer_account
            assert customer.account.name == customer_account.name


class TestVendorModel:
    """Test Vendor model functionality"""
    
    def test_vendor_creation(self, app, admin_user):
        """Test creating a new vendor"""
        with app.app_context():
            vendor = Vendor(
                name='مورد الاختبار',
                vendor_type='company',
                tax_id='*********',
                email='<EMAIL>',
                phone='01*********',
                created_by_id=admin_user.id
            )
            db.session.add(vendor)
            db.session.commit()
            
            assert vendor.id is not None
            assert vendor.name == 'مورد الاختبار'
            assert vendor.vendor_type == 'company'
            assert vendor.tax_id == '*********'
            assert vendor.is_active is True
            assert vendor.created_by == admin_user
    
    def test_vendor_type_display(self, app, admin_user):
        """Test vendor type display method"""
        with app.app_context():
            individual = Vendor(
                name='محمد أحمد',
                vendor_type='individual',
                created_by_id=admin_user.id
            )
            company = Vendor(
                name='شركة التوريد',
                vendor_type='company',
                created_by_id=admin_user.id
            )
            
            assert individual.get_type_display() == 'فرد'
            assert company.get_type_display() == 'شركة'
    
    def test_vendor_balance_calculation(self, app, admin_user):
        """Test vendor balance calculation"""
        with app.app_context():
            vendor = Vendor(
                name='مورد الاختبار',
                vendor_type='company',
                created_by_id=admin_user.id
            )
            db.session.add(vendor)
            db.session.commit()
            
            # Initially balance should be zero
            assert vendor.get_balance() == Decimal('0')
    
    def test_vendor_can_be_deleted(self, app, admin_user):
        """Test vendor deletion validation"""
        with app.app_context():
            vendor = Vendor(
                name='مورد للحذف',
                vendor_type='individual',
                created_by_id=admin_user.id
            )
            db.session.add(vendor)
            db.session.commit()
            
            # Vendor without transactions can be deleted
            assert vendor.can_be_deleted() is True


class TestCustomerViews:
    """Test customer views and forms"""
    
    def test_customers_index_page(self, client, admin_user, auth):
        """Test customers index page"""
        auth.login()
        response = client.get('/customers/')
        assert response.status_code == 200
        assert 'العملاء' in response.get_data(as_text=True)
    
    def test_customer_creation_form(self, client, admin_user, auth):
        """Test customer creation form"""
        auth.login()
        
        # Get form page
        response = client.get('/customers/new')
        assert response.status_code == 200
        assert 'عميل جديد' in response.get_data(as_text=True)
    
    def test_customer_creation(self, client, admin_user, auth):
        """Test creating customer through form"""
        auth.login()
        
        # Submit customer form
        response = client.post('/customers/create', data={
            'name': 'عميل اختبار',
            'customer_type': 'individual',
            'email': '<EMAIL>',
            'phone': '0*********0',
            'is_active': True
        }, follow_redirects=True)
        
        assert response.status_code == 200
        
        # Check if customer was created
        customer = Customer.query.filter_by(name='عميل اختبار').first()
        assert customer is not None
        assert customer.email == '<EMAIL>'
    
    def test_customer_search(self, client, admin_user, auth):
        """Test customer search functionality"""
        auth.login()
        
        # Create test customer
        with client.application.app_context():
            customer = Customer(
                name='عميل للبحث',
                customer_type='individual',
                created_by_id=admin_user.id
            )
            db.session.add(customer)
            db.session.commit()
        
        response = client.get('/customers/?search=عميل للبحث')
        assert response.status_code == 200
        assert 'عميل للبحث' in response.get_data(as_text=True)
    
    def test_customer_detail_page(self, client, admin_user, auth):
        """Test customer detail page"""
        auth.login()
        
        # Create test customer
        with client.application.app_context():
            customer = Customer(
                name='عميل التفاصيل',
                customer_type='company',
                tax_id='*********',
                created_by_id=admin_user.id
            )
            db.session.add(customer)
            db.session.commit()
            customer_id = customer.id
        
        response = client.get(f'/customers/{customer_id}')
        assert response.status_code == 200
        assert 'عميل التفاصيل' in response.get_data(as_text=True)
    
    def test_customer_edit_form(self, client, admin_user, auth):
        """Test customer edit form"""
        auth.login()
        
        # Create test customer
        with client.application.app_context():
            customer = Customer(
                name='عميل للتعديل',
                customer_type='individual',
                created_by_id=admin_user.id
            )
            db.session.add(customer)
            db.session.commit()
            customer_id = customer.id
        
        response = client.get(f'/customers/{customer_id}/edit')
        assert response.status_code == 200
        assert 'تعديل العميل' in response.get_data(as_text=True)


class TestVendorViews:
    """Test vendor views and forms"""
    
    def test_vendors_index_page(self, client, admin_user, auth):
        """Test vendors index page"""
        auth.login()
        response = client.get('/vendors/')
        assert response.status_code == 200
        assert 'الموردين' in response.get_data(as_text=True)
    
    def test_vendor_creation_form(self, client, admin_user, auth):
        """Test vendor creation form"""
        auth.login()
        
        # Get form page
        response = client.get('/vendors/new')
        assert response.status_code == 200
        assert 'مورد جديد' in response.get_data(as_text=True)
    
    def test_vendor_creation(self, client, admin_user, auth):
        """Test creating vendor through form"""
        auth.login()
        
        # Submit vendor form
        response = client.post('/vendors/create', data={
            'name': 'مورد اختبار',
            'vendor_type': 'company',
            'email': '<EMAIL>',
            'phone': '01*********',
            'is_active': True
        }, follow_redirects=True)
        
        assert response.status_code == 200
        
        # Check if vendor was created
        vendor = Vendor.query.filter_by(name='مورد اختبار').first()
        assert vendor is not None
        assert vendor.email == '<EMAIL>'
    
    def test_vendor_search(self, client, admin_user, auth):
        """Test vendor search functionality"""
        auth.login()
        
        # Create test vendor
        with client.application.app_context():
            vendor = Vendor(
                name='مورد للبحث',
                vendor_type='company',
                created_by_id=admin_user.id
            )
            db.session.add(vendor)
            db.session.commit()
        
        response = client.get('/vendors/?search=مورد للبحث')
        assert response.status_code == 200
        assert 'مورد للبحث' in response.get_data(as_text=True)


class TestCustomerVendorPermissions:
    """Test customer and vendor access permissions"""
    
    def test_admin_access_customers(self, client, admin_user, auth):
        """Test admin user access to customers"""
        auth.login('admin', 'admin123')
        
        response = client.get('/customers/')
        assert response.status_code == 200
    
    def test_admin_access_vendors(self, client, admin_user, auth):
        """Test admin user access to vendors"""
        auth.login('admin', 'admin123')
        
        response = client.get('/vendors/')
        assert response.status_code == 200
    
    def test_accountant_access_customers(self, client, accountant_user, auth):
        """Test accountant user access to customers"""
        auth.login('accountant', 'acc123')
        
        response = client.get('/customers/')
        assert response.status_code == 200
    
    def test_employee_access_customers(self, client, employee_user, auth):
        """Test employee user access to customers"""
        auth.login('employee', 'emp123')
        
        # Employees should not have access to customers
        response = client.get('/customers/')
        assert response.status_code == 302  # Redirect to login or access denied


class TestCustomerVendorBusinessLogic:
    """Test customer and vendor business logic"""
    
    def test_customer_credit_limit(self, app, admin_user):
        """Test customer credit limit functionality"""
        with app.app_context():
            customer = Customer(
                name='عميل الائتمان',
                customer_type='company',
                credit_limit=Decimal('10000.00'),
                created_by_id=admin_user.id
            )
            db.session.add(customer)
            db.session.commit()
            
            assert customer.credit_limit == Decimal('10000.00')
            
            # Test credit availability
            current_balance = customer.get_balance()
            credit_available = customer.credit_limit - current_balance
            assert credit_available == Decimal('10000.00')
    
    def test_vendor_payment_terms(self, app, admin_user):
        """Test vendor payment terms"""
        with app.app_context():
            vendor = Vendor(
                name='مورد الدفع',
                vendor_type='company',
                payment_terms=30,
                created_by_id=admin_user.id
            )
            db.session.add(vendor)
            db.session.commit()
            
            assert vendor.payment_terms == 30
    
    def test_customer_unique_constraints(self, app, admin_user):
        """Test customer unique constraints"""
        with app.app_context():
            # Create first customer
            customer1 = Customer(
                name='عميل أول',
                customer_type='company',
                tax_id='*********',
                email='<EMAIL>',
                created_by_id=admin_user.id
            )
            db.session.add(customer1)
            db.session.commit()
            
            # Try to create second customer with same tax_id
            customer2 = Customer(
                name='عميل ثاني',
                customer_type='company',
                tax_id='*********',  # Same tax_id
                email='<EMAIL>',
                created_by_id=admin_user.id
            )
            db.session.add(customer2)
            
            # This should raise an integrity error
            with pytest.raises(Exception):
                db.session.commit()
    
    def test_vendor_unique_constraints(self, app, admin_user):
        """Test vendor unique constraints"""
        with app.app_context():
            # Create first vendor
            vendor1 = Vendor(
                name='مورد أول',
                vendor_type='company',
                tax_id='*********',
                email='<EMAIL>',
                created_by_id=admin_user.id
            )
            db.session.add(vendor1)
            db.session.commit()
            
            # Try to create second vendor with same email
            vendor2 = Vendor(
                name='مورد ثاني',
                vendor_type='company',
                tax_id='*********',
                email='<EMAIL>',  # Same email
                created_by_id=admin_user.id
            )
            db.session.add(vendor2)
            
            # This should raise an integrity error
            with pytest.raises(Exception):
                db.session.commit()


if __name__ == '__main__':
    pytest.main([__file__])
