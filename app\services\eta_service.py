"""
خدمة التكامل مع مصلحة الضرائب المصرية
Egyptian Tax Authority (ETA) Integration Service
"""

import requests
import json
import hashlib
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from app import db
from app.models.eta_integration import ETATaxTransaction, ETASettings, ETAErrorLog
from app.models.system_settings import get_setting
from app.services.eta_ereceipt_service import ETAeReceiptService
from app.models.invoice import Invoice
from app.models.receipt import Receipt

class ETAService:
    """خدمة التكامل مع مصلحة الضرائب المصرية"""
    
    def __init__(self):
        # استخدام نظام الإعدادات الجديد مع fallback للنظام القديم
        self.base_url = get_setting('ETA_BASE_URL', 'https://api.invoicing.eta.gov.eg/api/v1')
        self.client_id = get_setting('ETA_CLIENT_ID', '')
        self.client_secret = get_setting('ETA_CLIENT_SECRET', '')
        self.environment = get_setting('ETA_ENVIRONMENT', 'sandbox')
        self.timeout = int(get_setting('ETA_TIMEOUT', '30'))
        self.access_token = None
        self.token_expires_at = None
    
    def authenticate(self) -> bool:
        """المصادقة مع مصلحة الضرائب والحصول على Access Token"""
        try:
            url = f"{self.base_url}/connect/token"
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
            
            data = {
                'grant_type': 'client_credentials',
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'scope': 'InvoicingAPI'
            }
            
            response = requests.post(url, headers=headers, data=data, timeout=self.timeout)
            
            if response.status_code == 200:
                token_data = response.json()
                self.access_token = token_data.get('access_token')
                expires_in = token_data.get('expires_in', 3600)
                self.token_expires_at = datetime.utcnow() + timedelta(seconds=expires_in)
                return True
            else:
                print(f"Authentication failed: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            print(f"Authentication error: {str(e)}")
            return False
    
    def is_token_valid(self) -> bool:
        """التحقق من صحة الـ Token"""
        if not self.access_token or not self.token_expires_at:
            return False
        return datetime.utcnow() < self.token_expires_at
    
    def get_headers(self) -> Dict[str, str]:
        """الحصول على Headers للطلبات"""
        if not self.is_token_valid():
            if not self.authenticate():
                raise Exception("Failed to authenticate with ETA")
        
        return {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    
    def get_document_types(self) -> List[Dict]:
        """الحصول على أنواع المستندات المتاحة"""
        try:
            url = f"{self.base_url}/documenttypes"
            headers = self.get_headers()
            
            response = requests.get(url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"Failed to get document types: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"Error getting document types: {str(e)}")
            return []
    
    def submit_invoice(self, invoice: Invoice) -> Tuple[bool, Dict]:
        """إرسال فاتورة إلى مصلحة الضرائب"""
        try:
            # إنشاء معاملة ضريبية
            tax_transaction = ETATaxTransaction('invoice', invoice_id=invoice.id)
            db.session.add(tax_transaction)
            db.session.commit()
            
            # تحضير بيانات الفاتورة
            invoice_data = self._prepare_invoice_data(invoice)
            
            # إرسال الفاتورة
            url = f"{self.base_url}/documentsubmissions"
            headers = self.get_headers()
            
            payload = {
                "documents": [invoice_data]
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=self.timeout)
            
            if response.status_code == 202:  # Accepted
                response_data = response.json()
                
                # تحديث معاملة الضرائب
                tax_transaction.submission_uuid = response_data.get('submissionId')
                tax_transaction.response_status = 'submitted'
                tax_transaction.response_data = response_data
                
                # تحديث الفاتورة
                invoice.eta_submission_uuid = response_data.get('submissionId')
                invoice.eta_status = 'submitted'
                invoice.eta_submitted_at = datetime.utcnow()
                
                db.session.commit()
                
                return True, response_data
            else:
                # تسجيل الخطأ
                error_data = response.json() if response.content else {}
                self._log_error(tax_transaction.id, 'submission_failed', 
                              f"HTTP {response.status_code}", error_data)
                
                tax_transaction.response_status = 'failed'
                tax_transaction.response_message = f"HTTP {response.status_code}"
                tax_transaction.response_data = error_data
                db.session.commit()
                
                return False, error_data
                
        except Exception as e:
            # تسجيل الخطأ
            if 'tax_transaction' in locals():
                self._log_error(tax_transaction.id, 'exception', str(e))
                tax_transaction.response_status = 'error'
                tax_transaction.response_message = str(e)
                db.session.commit()
            
            return False, {'error': str(e)}
    
    def submit_receipt(self, receipt: Receipt) -> Tuple[bool, Dict]:
        """إرسال إيصال إلى مصلحة الضرائب"""
        try:
            # إنشاء معاملة ضريبية
            tax_transaction = ETATaxTransaction('receipt', receipt_id=receipt.id)
            db.session.add(tax_transaction)
            db.session.commit()
            
            # تحضير بيانات الإيصال
            receipt_data = self._prepare_receipt_data(receipt)
            
            # إرسال الإيصال
            url = f"{self.base_url}/receipts/submit"
            headers = self.get_headers()
            
            response = requests.post(url, headers=headers, json=receipt_data, timeout=self.timeout)
            
            if response.status_code == 200:
                response_data = response.json()
                
                # تحديث معاملة الضرائب
                tax_transaction.eta_uuid = response_data.get('uuid')
                tax_transaction.eta_internal_id = response_data.get('internalId')
                tax_transaction.response_status = 'accepted'
                tax_transaction.response_data = response_data
                
                # تحديث الإيصال
                receipt.eta_uuid = response_data.get('uuid')
                receipt.eta_internal_id = response_data.get('internalId')
                receipt.eta_status = 'accepted'
                receipt.eta_submitted_at = datetime.utcnow()
                
                db.session.commit()
                
                return True, response_data
            else:
                # تسجيل الخطأ
                error_data = response.json() if response.content else {}
                self._log_error(tax_transaction.id, 'submission_failed', 
                              f"HTTP {response.status_code}", error_data)
                
                tax_transaction.response_status = 'failed'
                tax_transaction.response_message = f"HTTP {response.status_code}"
                tax_transaction.response_data = error_data
                db.session.commit()
                
                return False, error_data
                
        except Exception as e:
            # تسجيل الخطأ
            if 'tax_transaction' in locals():
                self._log_error(tax_transaction.id, 'exception', str(e))
                tax_transaction.response_status = 'error'
                tax_transaction.response_message = str(e)
                db.session.commit()
            
            return False, {'error': str(e)}
    
    def _prepare_invoice_data(self, invoice: Invoice) -> Dict:
        """تحضير بيانات الفاتورة للإرسال"""
        # هذه دالة مبسطة - يجب تطويرها حسب متطلبات مصلحة الضرائب
        return {
            "issuer": {
                "address": {
                    "branchID": ETASettings.get_setting('COMPANY_BRANCH_ID', '0'),
                    "country": "EG",
                    "governate": "Cairo",
                    "regionCity": "Cairo",
                    "street": "Main Street",
                    "buildingNumber": "123"
                },
                "type": "B",
                "id": ETASettings.get_setting('COMPANY_TAX_ID', ''),
                "name": "SystemTax Company"
            },
            "receiver": {
                "address": {
                    "country": "EG",
                    "governate": "Cairo",
                    "regionCity": "Cairo",
                    "street": invoice.customer.address if invoice.customer else "Unknown",
                    "buildingNumber": "1"
                },
                "type": "B",
                "id": invoice.customer.tax_id if invoice.customer and invoice.customer.tax_id else "*********",
                "name": invoice.customer.name if invoice.customer else "Cash Customer"
            },
            "documentType": "I",
            "documentTypeVersion": "1.0",
            "dateTimeIssued": invoice.issue_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "taxpayerActivityCode": ETASettings.get_setting('COMPANY_ACTIVITY_CODE', '1000'),
            "internalID": invoice.invoice_number,
            "invoiceLines": [
                {
                    "description": line.description,
                    "itemType": "EGS",
                    "itemCode": line.item_code or "EG-*********-123456",
                    "unitType": line.unit_type or "EA",
                    "quantity": float(line.quantity),
                    "unitValue": {
                        "currencySold": "EGP",
                        "amountEGP": float(line.unit_price)
                    },
                    "salesTotal": float(line.sales_total or (line.unit_price * line.quantity)),
                    "total": float(line.line_total),
                    "valueDifference": 0,
                    "totalTaxableFees": 0,
                    "netTotal": float(line.net_amount or line.line_total),
                    "itemsDiscount": float(line.discount_amount or 0),
                    "taxableItems": [
                        {
                            "taxType": "T1",
                            "amount": float(line.tax_amount or 0),
                            "subType": "V009",
                            "rate": float(line.tax_rate or 14.0)
                        }
                    ]
                } for line in invoice.lines
            ],
            "totalDiscountAmount": float(invoice.discount_amount or 0),
            "totalSalesAmount": float(invoice.net_amount or invoice.total_amount),
            "netAmount": float(invoice.net_amount or invoice.total_amount),
            "taxTotals": [
                {
                    "taxType": "T1",
                    "amount": float(invoice.tax_amount or 0)
                }
            ],
            "totalAmount": float(invoice.total_amount),
            "extraDiscountAmount": 0,
            "totalItemsDiscountAmount": float(invoice.discount_amount or 0)
        }
    
    def _prepare_receipt_data(self, receipt: Receipt) -> Dict:
        """تحضير بيانات الإيصال للإرسال"""
        return {
            "header": {
                "dateTimeIssued": receipt.receipt_date.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "receiptNumber": receipt.receipt_number,
                "uuid": receipt.id,
                "previousUUID": "",
                "referenceOldUUID": "",
                "currency": "EGP",
                "exchangeRate": 1,
                "sOrderNameCode": "",
                "orderdeliveryMode": "",
                "grossWeight": 0,
                "netWeight": 0
            },
            "documentType": {
                "receiptType": "s",
                "typeVersion": "1.0"
            },
            "seller": {
                "rin": ETASettings.get_setting('COMPANY_TAX_ID', ''),
                "companyTradeName": "SystemTax Company",
                "branchCode": ETASettings.get_setting('COMPANY_BRANCH_ID', '0'),
                "branchAddress": {
                    "country": "EG",
                    "governate": "C",
                    "regionCity": "Cairo",
                    "street": "Main Street",
                    "buildingNumber": "123"
                },
                "deviceSerialNumber": "1",
                "syndicateNumber": "",
                "activityCode": ETASettings.get_setting('COMPANY_ACTIVITY_CODE', '1000')
            },
            "buyer": {
                "type": "P",
                "id": "",
                "name": receipt.customer.name if receipt.customer else "Cash Customer",
                "mobileNumber": receipt.customer.phone if receipt.customer else "",
                "paymentNumber": ""
            },
            "itemData": [
                {
                    "internalCode": "ITEM001",
                    "description": f"Payment for receipt {receipt.receipt_number}",
                    "itemType": "EGS",
                    "itemCode": "EG-*********-123456",
                    "unitType": "EA",
                    "quantity": 1,
                    "unitPrice": float(receipt.amount_received),
                    "totalSale": float(receipt.amount_received),
                    "total": float(receipt.amount_received),
                    "commercialDiscountData": [],
                    "itemDiscountData": [],
                    "taxableItems": [
                        {
                            "taxType": "T1",
                            "amount": 0,
                            "subType": "V009",
                            "rate": 0
                        }
                    ]
                }
            ],
            "totalSale": float(receipt.amount_received),
            "totalCommercialDiscount": 0,
            "totalItemsDiscount": 0,
            "extraReceiptDiscount": 0,
            "netAmount": float(receipt.amount_received),
            "feesAmount": 0,
            "totalAmount": float(receipt.amount_received),
            "taxTotals": [
                {
                    "taxType": "T1",
                    "amount": 0
                }
            ],
            "paymentMethod": "C",
            "adjustment": 0,
            "contractingPartyRin": "",
            "contractingPartyName": "",
            "source": "POS",
            "metadata": {
                "receiptVersion": "1.0"
            }
        }
    
    def _log_error(self, transaction_id: str, error_type: str, error_message: str, error_details: Dict = None):
        """تسجيل الأخطاء"""
        error_log = ETAErrorLog(
            transaction_id=transaction_id,
            error_type=error_type,
            error_message=error_message,
            error_details=error_details
        )
        db.session.add(error_log)
        db.session.commit()
    
    def check_submission_status(self, submission_uuid: str) -> Tuple[bool, Dict]:
        """التحقق من حالة الإرسال"""
        try:
            url = f"{self.base_url}/documentsubmissions/{submission_uuid}"
            headers = self.get_headers()
            
            response = requests.get(url, headers=headers, timeout=self.timeout)
            
            if response.status_code == 200:
                return True, response.json()
            else:
                return False, {'error': f"HTTP {response.status_code}"}
                
        except Exception as e:
            return False, {'error': str(e)}

    def check_receipt_status(self, receipt: Receipt) -> Tuple[bool, Dict]:
        """التحقق من حالة الإيصال في مصلحة الضرائب"""
        try:
            ereceipt_service = ETAeReceiptService()
            return ereceipt_service.check_receipt_status(receipt)
        except Exception as e:
            return False, {"error": str(e)}

    def cancel_receipt(self, receipt: Receipt, reason: str = "") -> Tuple[bool, Dict]:
        """إلغاء الإيصال في مصلحة الضرائب"""
        try:
            ereceipt_service = ETAeReceiptService()
            return ereceipt_service.cancel_receipt(receipt, reason)
        except Exception as e:
            return False, {"error": str(e)}

    def get_receipt_pdf(self, receipt: Receipt) -> Tuple[bool, bytes]:
        """الحصول على PDF الإيصال من مصلحة الضرائب"""
        try:
            ereceipt_service = ETAeReceiptService()
            return ereceipt_service.get_receipt_pdf(receipt)
        except Exception as e:
            return False, b""

    def validate_receipt_data(self, receipt: Receipt) -> Tuple[bool, List[str]]:
        """التحقق من صحة بيانات الإيصال"""
        try:
            ereceipt_service = ETAeReceiptService()
            return ereceipt_service.validate_receipt_data(receipt)
        except Exception as e:
            return False, [str(e)]

    def ensure_authenticated(self) -> bool:
        """التأكد من المصادقة"""
        if not self.is_token_valid():
            return self.authenticate()
        return True
