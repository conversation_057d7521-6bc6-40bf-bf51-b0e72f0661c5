"""
SystemTax Application Package
نظام محاسبي ويب متكامل
"""

from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_login import <PERSON>ginManager
from flask_caching import Cache
from flask_wtf.csrf import CSRFProtect

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
login_manager = LoginManager()
cache = Cache()
csrf = CSRFProtect()

def create_app(config_name='default'):
    """Application factory function"""
    app = Flask(__name__)
    
    # Load configuration
    try:
        # Try to use new production config first
        from config_production import get_config
        config_class = get_config(config_name)
        app.config.from_object(config_class)
    except ImportError:
        # Fallback to old config
        from app.config import config
        app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)
    migrate.init_app(app, db)
    cache.init_app(app)
    csrf.init_app(app)
    
    # Configure login manager
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        from app.models.user import User
        return User.query.get(user_id)
    
    # Import models to ensure they are registered with SQLAlchemy
    from app.models import user, account, customer, vendor, journal, invoice, receipt, tax_transaction
    
    # Register blueprints
    register_blueprints(app)
    
    # Register error handlers
    register_error_handlers(app)
    
    # Register context processors
    register_context_processors(app)

    # Register template filters
    register_template_filters(app)

    # Initialize system settings
    with app.app_context():
        try:
            from app.models.system_settings import SystemSettings
            SystemSettings.initialize_default_settings()
        except Exception as e:
            print(f"Warning: Could not initialize system settings: {e}")

    return app

def register_blueprints(app):
    """Register application blueprints"""
    from app.views.auth import auth_bp
    from app.views.dashboard_views import dashboard_bp
    from app.views.accounts import accounts_bp
    from app.views.journal_views import journal_bp
    from app.views.customer_views import customers_bp
    from app.views.invoice_views import invoices_bp
    from app.views.receipt_views import receipts_bp
    from app.views.report_views import reports_bp
    from app.views.settings_views import settings_bp
    from app.views.eta_views import eta_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/')
    app.register_blueprint(accounts_bp, url_prefix='/accounts')
    app.register_blueprint(journal_bp, url_prefix='/journal')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(invoices_bp, url_prefix='/invoices')
    app.register_blueprint(receipts_bp, url_prefix='/receipts')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    app.register_blueprint(eta_bp, url_prefix='/eta')

def register_error_handlers(app):
    """Register error handlers"""
    from flask import render_template
    
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403

def register_context_processors(app):
    """Register context processors"""
    @app.context_processor
    def inject_app_config():
        return {
            'APP_NAME': app.config['APP_NAME'],
            'COMPANY_NAME': app.config['COMPANY_NAME'],
            'APP_VERSION': app.config['APP_VERSION']
        }

def register_template_filters(app):
    """Register custom template filters"""
    from app.utils.helpers import format_currency, format_date, format_datetime

    @app.template_filter('currency')
    def currency_filter(amount, currency_symbol='ج.م'):
        return format_currency(amount, currency_symbol)

    @app.template_filter('date')
    def date_filter(date_obj, format_str='%Y-%m-%d'):
        return format_date(date_obj, format_str)

    @app.template_filter('datetime')
    def datetime_filter(datetime_obj, format_str='%Y-%m-%d %H:%M'):
        return format_datetime(datetime_obj, format_str)

    @app.template_filter('time')
    def time_filter(datetime_obj, format_str='%H:%M'):
        if datetime_obj is None:
            return ''
        if isinstance(datetime_obj, str):
            return datetime_obj
        try:
            return datetime_obj.strftime(format_str)
        except:
            return str(datetime_obj)

    @app.template_filter('timeago')
    def timeago_filter(datetime_obj):
        """Convert datetime to Arabic relative time"""
        if datetime_obj is None:
            return ''

        from datetime import datetime, timedelta

        if isinstance(datetime_obj, str):
            return datetime_obj

        try:
            now = datetime.now()
            if datetime_obj.tzinfo is None:
                # Make both timezone-naive for comparison
                diff = now - datetime_obj
            else:
                # Handle timezone-aware datetime
                diff = now.replace(tzinfo=datetime_obj.tzinfo) - datetime_obj

            if diff.days > 0:
                if diff.days == 1:
                    return 'أمس'
                elif diff.days < 7:
                    return f'منذ {diff.days} أيام'
                elif diff.days < 30:
                    weeks = diff.days // 7
                    return f'منذ {weeks} أسبوع' if weeks == 1 else f'منذ {weeks} أسابيع'
                elif diff.days < 365:
                    months = diff.days // 30
                    return f'منذ {months} شهر' if months == 1 else f'منذ {months} أشهر'
                else:
                    years = diff.days // 365
                    return f'منذ {years} سنة' if years == 1 else f'منذ {years} سنوات'

            elif diff.seconds > 3600:
                hours = diff.seconds // 3600
                return f'منذ {hours} ساعة' if hours == 1 else f'منذ {hours} ساعات'
            elif diff.seconds > 60:
                minutes = diff.seconds // 60
                return f'منذ {minutes} دقيقة' if minutes == 1 else f'منذ {minutes} دقائق'
            else:
                return 'الآن'

        except Exception as e:
            return str(datetime_obj)
