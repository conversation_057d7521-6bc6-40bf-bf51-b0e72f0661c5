#!/usr/bin/env python3
"""
إصلاح جدول الحسابات
Fix accounts table structure
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية"""
    db_path = 'instance/systemtax.db'
    if os.path.exists(db_path):
        backup_path = f'instance/systemtax_backup_accounts_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return backup_path
    return None

def check_accounts_table():
    """فحص هيكل جدول الحسابات"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص إذا كان الجدول موجود
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='accounts'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("ℹ️ جدول accounts غير موجود")
            conn.close()
            return False, []
        
        # الحصول على هيكل الجدول
        cursor.execute("PRAGMA table_info(accounts)")
        columns = cursor.fetchall()
        
        print("📋 هيكل جدول الحسابات الحالي:")
        column_names = []
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
            column_names.append(col[1])
        
        conn.close()
        return True, column_names
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجدول: {e}")
        return False, []

def fix_accounts_table():
    """إصلاح جدول الحسابات"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(accounts)")
        existing_columns = [col[1] for col in cursor.fetchall()]
        
        # الأعمدة المطلوبة
        required_columns = {
            'name_en': 'VARCHAR(200)',
            'type': 'VARCHAR(50) NOT NULL',
            'balance': 'DECIMAL(15,2) DEFAULT 0',
            'description': 'TEXT'
        }
        
        # إضافة الأعمدة المفقودة
        added_columns = []
        for column_name, column_def in required_columns.items():
            if column_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE accounts ADD COLUMN {column_name} {column_def}")
                    added_columns.append(column_name)
                    print(f"✅ تم إضافة العمود: {column_name}")
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة العمود {column_name}: {e}")
        
        # تحديث العمود account_type إلى type إذا كان موجود
        if 'account_type' in existing_columns and 'type' not in existing_columns:
            try:
                cursor.execute("UPDATE accounts SET type = account_type WHERE type IS NULL")
                print("✅ تم تحديث بيانات العمود type")
            except Exception as e:
                print(f"⚠️ خطأ في تحديث العمود type: {e}")
        
        if not added_columns:
            print("ℹ️ جميع الأعمدة موجودة مسبقاً")
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إصلاح جدول accounts")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الجدول: {e}")
        return False

def create_default_accounts():
    """إنشاء الحسابات الافتراضية"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # الحسابات الافتراضية
        default_accounts = [
            # الأصول
            ('1000', 'الأصول', 'Assets', 'Asset', None, 1),
            ('1100', 'الأصول المتداولة', 'Current Assets', 'Asset', 1, 1),
            ('1110', 'النقدية', 'Cash', 'Asset', 2, 1),
            ('1120', 'البنوك', 'Banks', 'Asset', 2, 1),
            ('1130', 'العملاء', 'Accounts Receivable', 'Asset', 2, 1),
            ('1140', 'المخزون', 'Inventory', 'Asset', 2, 1),
            
            # الخصوم
            ('2000', 'الخصوم', 'Liabilities', 'Liability', None, 1),
            ('2100', 'الخصوم المتداولة', 'Current Liabilities', 'Liability', 6, 1),
            ('2110', 'الموردين', 'Accounts Payable', 'Liability', 7, 1),
            ('2120', 'الضرائب المستحقة', 'Taxes Payable', 'Liability', 7, 1),
            
            # حقوق الملكية
            ('3000', 'حقوق الملكية', 'Equity', 'Equity', None, 1),
            ('3100', 'رأس المال', 'Capital', 'Equity', 10, 1),
            ('3200', 'الأرباح المحتجزة', 'Retained Earnings', 'Equity', 10, 1),
            
            # الإيرادات
            ('4000', 'الإيرادات', 'Revenue', 'Income', None, 1),
            ('4100', 'إيرادات المبيعات', 'Sales Revenue', 'Income', 13, 1),
            ('4200', 'إيرادات أخرى', 'Other Income', 'Income', 13, 1),
            
            # المصروفات
            ('5000', 'المصروفات', 'Expenses', 'Expense', None, 1),
            ('5100', 'تكلفة البضاعة المباعة', 'Cost of Goods Sold', 'Expense', 16, 1),
            ('5200', 'مصروفات التشغيل', 'Operating Expenses', 'Expense', 16, 1),
            ('5300', 'مصروفات أخرى', 'Other Expenses', 'Expense', 16, 1)
        ]
        
        # التحقق من وجود حسابات
        cursor.execute("SELECT COUNT(*) FROM accounts")
        account_count = cursor.fetchone()[0]
        
        if account_count == 0:
            print("📊 إنشاء الحسابات الافتراضية...")
            
            for code, name, name_en, acc_type, parent_id, is_active in default_accounts:
                try:
                    cursor.execute("""
                        INSERT INTO accounts (code, name, name_en, type, parent_id, is_active)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (code, name, name_en, acc_type, parent_id, is_active))
                except Exception as e:
                    print(f"⚠️ خطأ في إدراج الحساب {code}: {e}")
            
            print(f"✅ تم إنشاء {len(default_accounts)} حساب افتراضي")
        else:
            print(f"ℹ️ يوجد {account_count} حساب في النظام")
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الحسابات: {e}")
        return False

def recreate_accounts_table():
    """إعادة إنشاء جدول الحسابات بالهيكل الصحيح"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # حفظ البيانات الموجودة
        cursor.execute("SELECT * FROM accounts")
        existing_data = cursor.fetchall()
        
        # حذف الجدول القديم
        cursor.execute("DROP TABLE IF EXISTS accounts")
        
        # إنشاء الجدول الجديد
        cursor.execute("""
            CREATE TABLE accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code VARCHAR(20) UNIQUE NOT NULL,
                name VARCHAR(200) NOT NULL,
                name_en VARCHAR(200),
                type VARCHAR(50) NOT NULL,
                parent_id INTEGER,
                balance DECIMAL(15,2) DEFAULT 0,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_id) REFERENCES accounts (id)
            )
        """)
        
        # إنشاء الفهرس
        cursor.execute("CREATE INDEX idx_accounts_code ON accounts(code)")
        cursor.execute("CREATE INDEX idx_accounts_type ON accounts(type)")
        
        print("✅ تم إعادة إنشاء جدول accounts بالهيكل الصحيح")
        
        # استعادة البيانات إذا كانت موجودة
        if existing_data:
            print("🔄 استعادة البيانات الموجودة...")
            for row in existing_data:
                try:
                    # تحديد الأعمدة حسب البيانات الموجودة
                    if len(row) >= 6:  # الحد الأدنى من الأعمدة
                        cursor.execute("""
                            INSERT INTO accounts (id, code, name, type, parent_id, is_active, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        """, (row[0], row[1], row[2], row[3] if len(row) > 3 else 'Asset', 
                              row[4] if len(row) > 4 else None, row[5] if len(row) > 5 else 1,
                              row[6] if len(row) > 6 else datetime.now(), row[7] if len(row) > 7 else datetime.now()))
                except Exception as e:
                    print(f"⚠️ خطأ في استعادة السجل: {e}")
        
        conn.commit()
        conn.close()
        
        print("✅ تم استعادة البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعادة إنشاء الجدول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح جدول الحسابات")
    print("=" * 40)
    
    # إنشاء نسخة احتياطية
    backup_path = backup_database()
    
    # فحص هيكل الجدول
    table_exists, columns = check_accounts_table()
    
    if not table_exists:
        print("\n🏗️ إنشاء جدول accounts جديد...")
        if not recreate_accounts_table():
            print("❌ فشل في إنشاء الجدول")
            return False
    else:
        # فحص إذا كان العمود name_en موجود
        if 'name_en' not in columns or 'type' not in columns:
            print("\n🔧 إصلاح هيكل الجدول...")
            if not fix_accounts_table():
                print("❌ فشل في إصلاح الجدول")
                print("💡 جرب إعادة إنشاء الجدول:")
                choice = input("هل تريد إعادة إنشاء الجدول؟ (y/n): ")
                if choice.lower() == 'y':
                    if not recreate_accounts_table():
                        return False
                else:
                    return False
        else:
            print("✅ هيكل الجدول صحيح")
    
    # إنشاء الحسابات الافتراضية
    print("\n📊 إنشاء الحسابات الافتراضية...")
    if not create_default_accounts():
        print("❌ فشل في إنشاء الحسابات")
        return False
    
    print("\n🎉 تم إصلاح جدول الحسابات بنجاح!")
    print("✅ يمكنك الآن تشغيل النظام: python app.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ فشل في الإصلاح!")
            exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إلغاء العملية")
        exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        exit(1)
