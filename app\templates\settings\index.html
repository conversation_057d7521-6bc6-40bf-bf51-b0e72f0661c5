{% extends "base.html" %}

{% block title %}الإعدادات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">إعدادات النظام</h1>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- إعدادات الشركة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>
                        إعدادات الشركة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#companyInfo" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">معلومات الشركة</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">اسم الشركة، العنوان، الرقم الضريبي</p>
                        </a>
                        <a href="#logoSettings" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">الشعار والهوية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">شعار الشركة وألوان النظام</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإعدادات المحاسبية -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        الإعدادات المحاسبية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#fiscalYear" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">السنة المالية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">تحديد بداية ونهاية السنة المالية</p>
                        </a>
                        <a href="#currency" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">العملة الأساسية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">تحديد العملة الافتراضية للنظام</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإعدادات الضريبية -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-percentage me-2"></i>
                        الإعدادات الضريبية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#taxRates" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">معدلات الضرائب</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">ضريبة القيمة المضافة وضريبة الدخل</p>
                        </a>
                        <a href="#einvoice" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">إعدادات الفاتورة الإلكترونية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">ربط مع منظومة الفاتورة الإلكترونية</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات المستخدمين -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#users" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">المستخدمين</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">إضافة وإدارة مستخدمي النظام</p>
                        </a>
                        <a href="#permissions" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">الصلاحيات</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">تحديد صلاحيات المستخدمين</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- النسخ الاحتياطي -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        النسخ الاحتياطي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">إنشاء نسخة احتياطية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">حفظ نسخة من البيانات</p>
                        </a>
                        <a href="#restore" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">استعادة النسخة الاحتياطية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">استعادة البيانات من نسخة احتياطية</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات النظام -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#generalSettings" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">إعدادات عامة</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">اللغة، المنطقة الزمنية، التنسيق</p>
                        </a>
                        <a href="#activityLog" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">سجل النشاطات</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">عرض سجل العمليات والتغييرات</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>إصدار النظام</h6>
                                <p class="text-muted">1.0.0</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>تاريخ آخر تحديث</h6>
                                <p class="text-muted">2025-07-17</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>عدد المستخدمين</h6>
                                <p class="text-muted">1</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>حالة النظام</h6>
                                <p class="text-success">يعمل بشكل طبيعي</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Company Info Modal -->
<div class="modal fade" id="companyInfo" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معلومات الشركة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" class="form-control" value="شركة SystemTax">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اسم الشركة بالإنجليزية</label>
                            <input type="text" class="form-control" value="SystemTax Company">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الرقم الضريبي</label>
                            <input type="text" class="form-control" value="*********">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم السجل التجاري</label>
                            <input type="text" class="form-control" value="*********">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">العنوان</label>
                            <textarea class="form-control" rows="3">القاهرة، مصر</textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">الهاتف</label>
                            <input type="text" class="form-control" value="+20 2 1234567">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" value="<EMAIL>">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ البيانات بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Logo Settings Modal -->
<div class="modal fade" id="logoSettings" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الشعار والهوية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">شعار الشركة</label>
                        <input type="file" class="form-control" accept="image/*">
                        <small class="text-muted">يفضل أن يكون الشعار بصيغة PNG أو JPG</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اللون الأساسي</label>
                        <input type="color" class="form-control form-control-color" value="#0d6efd">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">اللون الثانوي</label>
                        <input type="color" class="form-control form-control-color" value="#6c757d">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ الإعدادات بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Fiscal Year Modal -->
<div class="modal fade" id="fiscalYear" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">السنة المالية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">بداية السنة المالية</label>
                        <input type="date" class="form-control" value="2025-01-01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">نهاية السنة المالية</label>
                        <input type="date" class="form-control" value="2025-12-31">
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        تغيير السنة المالية سيؤثر على جميع التقارير والحسابات
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ السنة المالية بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Currency Modal -->
<div class="modal fade" id="currency" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">العملة الأساسية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">العملة الأساسية</label>
                        <select class="form-select">
                            <option value="EGP" selected>جنيه مصري (EGP)</option>
                            <option value="USD">دولار أمريكي (USD)</option>
                            <option value="EUR">يورو (EUR)</option>
                            <option value="SAR">ريال سعودي (SAR)</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">رمز العملة</label>
                        <input type="text" class="form-control" value="ج.م" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">عدد الخانات العشرية</label>
                        <select class="form-select">
                            <option value="2" selected>2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ إعدادات العملة بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Tax Rates Modal -->
<div class="modal fade" id="taxRates" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معدلات الضرائب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">ضريبة القيمة المضافة (%)</label>
                        <input type="number" class="form-control" value="14" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ضريبة الدخل (%)</label>
                        <input type="number" class="form-control" value="22.5" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ضريبة الخدمات (%)</label>
                        <input type="number" class="form-control" value="10" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">ضريبة الدمغة (ج.م)</label>
                        <input type="number" class="form-control" value="1" step="0.01">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ معدلات الضرائب بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- E-Invoice Modal -->
<div class="modal fade" id="einvoice" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إعدادات الفاتورة الإلكترونية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">رقم التسجيل في منظومة الفاتورة</label>
                            <input type="text" class="form-control" placeholder="ETA Registration ID">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">كود النشاط</label>
                            <input type="text" class="form-control" placeholder="Activity Code">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label">رابط API منظومة الفاتورة</label>
                            <input type="url" class="form-control" value="https://api.invoicing.eta.gov.eg">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Client ID</label>
                            <input type="text" class="form-control" placeholder="Client ID">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Client Secret</label>
                            <input type="password" class="form-control" placeholder="Client Secret">
                        </div>
                        <div class="col-12">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="autoSubmit">
                                <label class="form-check-label" for="autoSubmit">
                                    إرسال الفواتير تلقائياً لمنظومة الفاتورة الإلكترونية
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="alert('تم اختبار الاتصال بنجاح!')">اختبار الاتصال</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ إعدادات الفاتورة الإلكترونية بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Users Management Modal -->
<div class="modal fade" id="users" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إدارة المستخدمين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between mb-3">
                    <h6>قائمة المستخدمين</h6>
                    <button type="button" class="btn btn-primary btn-sm" onclick="alert('سيتم فتح نموذج إضافة مستخدم جديد')">
                        <i class="fas fa-plus me-2"></i>إضافة مستخدم
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>admin</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-danger">مدير النظام</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td>الآن</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="alert('تعديل المستخدم')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="alert('حذف المستخدم')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Permissions Modal -->
<div class="modal fade" id="permissions" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إدارة الصلاحيات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>الأدوار</h6>
                        <div class="list-group">
                            <a href="#" class="list-group-item list-group-item-action active">مدير النظام</a>
                            <a href="#" class="list-group-item list-group-item-action">محاسب</a>
                            <a href="#" class="list-group-item list-group-item-action">مستخدم عادي</a>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h6>صلاحيات مدير النظام</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked disabled>
                                    <label class="form-check-label">إدارة المستخدمين</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked disabled>
                                    <label class="form-check-label">إدارة العملاء</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked disabled>
                                    <label class="form-check-label">إدارة الموردين</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked disabled>
                                    <label class="form-check-label">إدارة الفواتير</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked disabled>
                                    <label class="form-check-label">إدارة الإيصالات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked disabled>
                                    <label class="form-check-label">عرض التقارير</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked disabled>
                                    <label class="form-check-label">إدارة الإعدادات</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" checked disabled>
                                    <label class="form-check-label">النسخ الاحتياطي</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ الصلاحيات بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Backup Modal -->
<div class="modal fade" id="backup" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء نسخة احتياطية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم إنشاء نسخة احتياطية من جميع البيانات والإعدادات
                </div>
                <form>
                    <div class="mb-3">
                        <label class="form-label">اسم النسخة الاحتياطية</label>
                        <input type="text" class="form-control" value="backup_{{ moment().format('YYYY_MM_DD_HH_mm') }}">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">وصف النسخة</label>
                        <textarea class="form-control" rows="3" placeholder="وصف اختياري للنسخة الاحتياطية"></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" checked>
                        <label class="form-check-label">تضمين الملفات المرفقة</label>
                    </div>
                </form>
                <div class="mt-3">
                    <div class="progress" style="display: none;" id="backupProgress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="startBackup()">
                    <i class="fas fa-download me-2"></i>إنشاء النسخة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Restore Modal -->
<div class="modal fade" id="restore" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استعادة النسخة الاحتياطية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> استعادة النسخة الاحتياطية ستحل محل جميع البيانات الحالية
                </div>
                <form>
                    <div class="mb-3">
                        <label class="form-label">اختر ملف النسخة الاحتياطية</label>
                        <input type="file" class="form-control" accept=".sql,.zip,.bak">
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" required>
                        <label class="form-check-label">أؤكد أنني أريد استبدال جميع البيانات الحالية</label>
                    </div>
                </form>
                <div class="mt-3">
                    <div class="progress" style="display: none;" id="restoreProgress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="startRestore()">
                    <i class="fas fa-upload me-2"></i>استعادة النسخة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- General Settings Modal -->
<div class="modal fade" id="generalSettings" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الإعدادات العامة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">اللغة</label>
                            <select class="form-select">
                                <option value="ar" selected>العربية</option>
                                <option value="en">English</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">المنطقة الزمنية</label>
                            <select class="form-select">
                                <option value="Africa/Cairo" selected>القاهرة (GMT+2)</option>
                                <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                <option value="UTC">UTC (GMT+0)</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تنسيق التاريخ</label>
                            <select class="form-select">
                                <option value="dd/mm/yyyy" selected>يوم/شهر/سنة</option>
                                <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                                <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">تنسيق الوقت</label>
                            <select class="form-select">
                                <option value="24" selected>24 ساعة</option>
                                <option value="12">12 ساعة</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" checked>
                                <label class="form-check-label">تفعيل الإشعارات</label>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" checked>
                                <label class="form-check-label">حفظ سجل العمليات</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ الإعدادات العامة بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Activity Log Modal -->
<div class="modal fade" id="activityLog" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">سجل النشاطات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between mb-3">
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" style="width: auto;">
                            <option>جميع الأنشطة</option>
                            <option>تسجيل الدخول</option>
                            <option>إنشاء فاتورة</option>
                            <option>تعديل عميل</option>
                        </select>
                        <input type="date" class="form-control form-control-sm" style="width: auto;">
                    </div>
                    <button class="btn btn-outline-danger btn-sm" onclick="alert('تم مسح السجل')">
                        <i class="fas fa-trash me-2"></i>مسح السجل
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>الوقت</th>
                                <th>المستخدم</th>
                                <th>النشاط</th>
                                <th>التفاصيل</th>
                                <th>عنوان IP</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2025-07-17 14:30:25</td>
                                <td>admin</td>
                                <td><span class="badge bg-success">تسجيل دخول</span></td>
                                <td>تسجيل دخول ناجح</td>
                                <td>127.0.0.1</td>
                            </tr>
                            <tr>
                                <td>2025-07-17 14:25:10</td>
                                <td>admin</td>
                                <td><span class="badge bg-primary">إنشاء فاتورة</span></td>
                                <td>إنشاء فاتورة رقم INV-001</td>
                                <td>127.0.0.1</td>
                            </tr>
                            <tr>
                                <td>2025-07-17 14:20:05</td>
                                <td>admin</td>
                                <td><span class="badge bg-info">تعديل عميل</span></td>
                                <td>تعديل بيانات العميل: شركة ABC</td>
                                <td>127.0.0.1</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم تصدير السجل')">
                    <i class="fas fa-download me-2"></i>تصدير السجل
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function startBackup() {
    const progress = document.getElementById('backupProgress');
    progress.style.display = 'block';
    const progressBar = progress.querySelector('.progress-bar');

    let width = 0;
    const interval = setInterval(() => {
        width += 10;
        progressBar.style.width = width + '%';
        progressBar.textContent = width + '%';

        if (width >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                alert('تم إنشاء النسخة الاحتياطية بنجاح!');
                progress.style.display = 'none';
                progressBar.style.width = '0%';
            }, 500);
        }
    }, 200);
}

function startRestore() {
    if (!confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم حذف جميع البيانات الحالية!')) {
        return;
    }

    const progress = document.getElementById('restoreProgress');
    progress.style.display = 'block';
    const progressBar = progress.querySelector('.progress-bar');

    let width = 0;
    const interval = setInterval(() => {
        width += 15;
        progressBar.style.width = width + '%';
        progressBar.textContent = width + '%';

        if (width >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                alert('تم استعادة النسخة الاحتياطية بنجاح!');
                progress.style.display = 'none';
                progressBar.style.width = '0%';
            }, 500);
        }
    }, 300);
}
</script>
{% endblock %}
