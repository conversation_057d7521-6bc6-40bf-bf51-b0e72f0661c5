{% extends "base.html" %}

{% block title %}الإعدادات{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">إعدادات النظام</h1>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- إعدادات الشركة -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-building me-2"></i>
                        إعدادات الشركة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#companyInfo" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">معلومات الشركة</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">اسم الشركة، العنوان، الرقم الضريبي</p>
                        </a>
                        <a href="#logoSettings" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">الشعار والهوية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">شعار الشركة وألوان النظام</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإعدادات المحاسبية -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        الإعدادات المحاسبية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#fiscalYear" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">السنة المالية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">تحديد بداية ونهاية السنة المالية</p>
                        </a>
                        <a href="#currency" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">العملة الأساسية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">تحديد العملة الافتراضية للنظام</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- الإعدادات الضريبية -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-percentage me-2"></i>
                        الإعدادات الضريبية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#taxRates" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">معدلات الضرائب</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">ضريبة القيمة المضافة وضريبة الدخل</p>
                        </a>
                        <a href="{{ url_for('eta.settings') }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">إعدادات التكامل مع مصلحة الضرائب</h6>
                                <i class="fas fa-external-link-alt"></i>
                            </div>
                            <p class="mb-1">إدارة شاملة للتكامل مع مصلحة الضرائب المصرية</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات المستخدمين -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        إدارة المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#users" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">المستخدمين</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">إضافة وإدارة مستخدمي النظام</p>
                        </a>
                        <a href="#permissions" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">الصلاحيات</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">تحديد صلاحيات المستخدمين</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- النسخ الاحتياطي -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-database me-2"></i>
                        النسخ الاحتياطي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#backup" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">إنشاء نسخة احتياطية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">حفظ نسخة من البيانات</p>
                        </a>
                        <a href="#restore" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">استعادة النسخة الاحتياطية</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">استعادة البيانات من نسخة احتياطية</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات النظام -->
        <div class="col-md-6 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>
                        إعدادات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="#generalSettings" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">إعدادات عامة</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">اللغة، المنطقة الزمنية، التنسيق</p>
                        </a>
                        <a href="#activityLog" class="list-group-item list-group-item-action" data-bs-toggle="modal">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">سجل النشاطات</h6>
                                <i class="fas fa-chevron-left"></i>
                            </div>
                            <p class="mb-1">عرض سجل العمليات والتغييرات</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات النظام -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>إصدار النظام</h6>
                                <p class="text-muted">1.0.0</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>تاريخ آخر تحديث</h6>
                                <p class="text-muted">2025-07-17</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>عدد المستخدمين</h6>
                                <p class="text-muted">1</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h6>حالة النظام</h6>
                                <p class="text-success">يعمل بشكل طبيعي</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Company Info Modal -->
<div class="modal fade" id="companyInfo" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معلومات الشركة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="company-name">اسم الشركة</label>
                            <input type="text" class="form-control" id="company-name" value="شركة SystemTax">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="company-name-en">اسم الشركة بالإنجليزية</label>
                            <input type="text" class="form-control" id="company-name-en" value="SystemTax Company">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="tax-number">الرقم الضريبي</label>
                            <input type="text" class="form-control" id="tax-number" value="*********">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="commercial-register">رقم السجل التجاري</label>
                            <input type="text" class="form-control" id="commercial-register" value="*********">
                        </div>
                        <div class="col-12 mb-3">
                            <label class="form-label" for="address">العنوان</label>
                            <textarea class="form-control" id="address" rows="3">القاهرة، مصر</textarea>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="phone">الهاتف</label>
                            <input type="text" class="form-control" id="phone" value="+20 2 1234567">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="email">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" value="<EMAIL>">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ البيانات بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Logo Settings Modal -->
<div class="modal fade" id="logoSettings" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الشعار والهوية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label" for="company-logo">شعار الشركة</label>
                        <input type="file" class="form-control" id="company-logo" accept="image/*">
                        <small class="text-muted">يفضل أن يكون الشعار بصيغة PNG أو JPG</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="primary-color">اللون الأساسي</label>
                        <input type="color" class="form-control form-control-color" id="primary-color" value="#0d6efd">
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="secondary-color">اللون الثانوي</label>
                        <input type="color" class="form-control form-control-color" id="secondary-color" value="#6c757d">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ الإعدادات بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Fiscal Year Modal -->
<div class="modal fade" id="fiscalYear" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">السنة المالية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label" for="fiscal-year-start">بداية السنة المالية</label>
                        <input type="date" class="form-control" id="fiscal-year-start" value="2025-01-01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="fiscal-year-end">نهاية السنة المالية</label>
                        <input type="date" class="form-control" id="fiscal-year-end" value="2025-12-31">
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        تغيير السنة المالية سيؤثر على جميع التقارير والحسابات
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ السنة المالية بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Currency Modal -->
<div class="modal fade" id="currency" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">العملة الأساسية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label" for="base-currency">العملة الأساسية</label>
                        <select class="form-select" id="base-currency">
                            <option value="EGP" selected>جنيه مصري (EGP)</option>
                            <option value="USD">دولار أمريكي (USD)</option>
                            <option value="EUR">يورو (EUR)</option>
                            <option value="SAR">ريال سعودي (SAR)</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="currency-symbol">رمز العملة</label>
                        <input type="text" class="form-control" id="currency-symbol" value="ج.م" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="decimal-places">عدد الخانات العشرية</label>
                        <select class="form-select" id="decimal-places">
                            <option value="2" selected>2</option>
                            <option value="3">3</option>
                            <option value="4">4</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ إعدادات العملة بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Tax Rates Modal -->
<div class="modal fade" id="taxRates" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معدلات الضرائب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label" for="vat-rate">ضريبة القيمة المضافة (%)</label>
                        <input type="number" class="form-control" id="vat-rate" value="14" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="income-tax-rate">ضريبة الدخل (%)</label>
                        <input type="number" class="form-control" id="income-tax-rate" value="22.5" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="service-tax-rate">ضريبة الخدمات (%)</label>
                        <input type="number" class="form-control" id="service-tax-rate" value="10" step="0.01">
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="stamp-tax">ضريبة الدمغة (ج.م)</label>
                        <input type="number" class="form-control" id="stamp-tax" value="1" step="0.01">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ معدلات الضرائب بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>



<!-- Users Management Modal -->
<div class="modal fade" id="users" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إدارة المستخدمين</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between mb-3">
                    <h6>قائمة المستخدمين</h6>
                    <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addUser">
                        <i class="fas fa-plus me-2"></i>إضافة مستخدم
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>الحالة</th>
                                <th>آخر دخول</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>admin</td>
                                <td><EMAIL></td>
                                <td><span class="badge bg-danger">مدير النظام</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td>الآن</td>
                                <td>
                                    <button class="btn btn-sm btn-outline-primary" onclick="editUser('admin')" title="تعديل المستخدم">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('admin')" title="حذف المستخدم">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUser" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة مستخدم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="newUsername" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="newUsername" required minlength="3" maxlength="50">
                            <div class="form-text">يجب أن يكون اسم المستخدم فريداً</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="newEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="newEmail" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="newPassword" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="newPassword" required minlength="6">
                            <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirmPassword" class="form-label">تأكيد كلمة المرور</label>
                            <input type="password" class="form-control" id="confirmPassword" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="newRole" class="form-label">الدور</label>
                            <select class="form-select" id="newRole" required>
                                <option value="">اختر الدور</option>
                                <option value="admin">مدير النظام</option>
                                <option value="accountant">محاسب</option>
                                <option value="user">مستخدم عادي</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="newFullName" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="newFullName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="newPhone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="newPhone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="newDepartment" class="form-label">القسم</label>
                            <select class="form-select" id="newDepartment">
                                <option value="">اختر القسم</option>
                                <option value="accounting">المحاسبة</option>
                                <option value="sales">المبيعات</option>
                                <option value="purchasing">المشتريات</option>
                                <option value="management">الإدارة</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="newUserActive" checked>
                                <label class="form-check-label" for="newUserActive">
                                    المستخدم نشط
                                </label>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="sendWelcomeEmail">
                                <label class="form-check-label" for="sendWelcomeEmail">
                                    إرسال بريد ترحيبي للمستخدم
                                </label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addNewUser()">
                    <i class="fas fa-save me-2"></i>حفظ المستخدم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Permissions Modal -->
<div class="modal fade" id="permissions" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إدارة الصلاحيات والأدوار</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <!-- Roles List -->
                    <div class="col-md-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">الأدوار</h6>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="addNewRole()">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="list-group" id="rolesList">
                            <button type="button" class="list-group-item list-group-item-action active" data-role="admin" onclick="selectRole('admin', this)">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>مدير النظام</span>
                                    <span class="badge bg-danger">Admin</span>
                                </div>
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" data-role="accountant" onclick="selectRole('accountant', this)">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>محاسب</span>
                                    <span class="badge bg-warning">Accountant</span>
                                </div>
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" data-role="user" onclick="selectRole('user', this)">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>مستخدم عادي</span>
                                    <span class="badge bg-info">User</span>
                                </div>
                            </button>
                            <button type="button" class="list-group-item list-group-item-action" data-role="viewer" onclick="selectRole('viewer', this)">
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>مستعرض فقط</span>
                                    <span class="badge bg-secondary">Viewer</span>
                                </div>
                            </button>
                        </div>
                    </div>

                    <!-- Permissions Panel -->
                    <div class="col-md-9">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0" id="currentRoleTitle">صلاحيات مدير النظام</h6>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-success" onclick="selectAllPermissions()">تحديد الكل</button>
                                <button type="button" class="btn btn-outline-danger" onclick="clearAllPermissions()">إلغاء الكل</button>
                            </div>
                        </div>

                        <div class="row" id="permissionsContainer">
                            <!-- Dashboard Permissions -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="dashboard_view" data-permission="dashboard_view">
                                            <label class="form-check-label" for="dashboard_view">عرض لوحة التحكم</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="analytics_view" data-permission="analytics_view">
                                            <label class="form-check-label" for="analytics_view">عرض التحليلات</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Users Management -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-users me-2"></i>إدارة المستخدمين
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="users_view" data-permission="users_view">
                                            <label class="form-check-label" for="users_view">عرض المستخدمين</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="users_create" data-permission="users_create">
                                            <label class="form-check-label" for="users_create">إضافة مستخدمين</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="users_edit" data-permission="users_edit">
                                            <label class="form-check-label" for="users_edit">تعديل المستخدمين</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="users_delete" data-permission="users_delete">
                                            <label class="form-check-label" for="users_delete">حذف المستخدمين</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customers Management -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-user-tie me-2"></i>إدارة العملاء
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="customers_view" data-permission="customers_view">
                                            <label class="form-check-label" for="customers_view">عرض العملاء</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="customers_create" data-permission="customers_create">
                                            <label class="form-check-label" for="customers_create">إضافة عملاء</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="customers_edit" data-permission="customers_edit">
                                            <label class="form-check-label" for="customers_edit">تعديل العملاء</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="customers_delete" data-permission="customers_delete">
                                            <label class="form-check-label" for="customers_delete">حذف العملاء</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Vendors Management -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-truck me-2"></i>إدارة الموردين
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="vendors_view" data-permission="vendors_view">
                                            <label class="form-check-label" for="vendors_view">عرض الموردين</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="vendors_create" data-permission="vendors_create">
                                            <label class="form-check-label" for="vendors_create">إضافة موردين</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="vendors_edit" data-permission="vendors_edit">
                                            <label class="form-check-label" for="vendors_edit">تعديل الموردين</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="vendors_delete" data-permission="vendors_delete">
                                            <label class="form-check-label" for="vendors_delete">حذف الموردين</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Invoices Management -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-file-invoice me-2"></i>إدارة الفواتير
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="invoices_view" data-permission="invoices_view">
                                            <label class="form-check-label" for="invoices_view">عرض الفواتير</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="invoices_create" data-permission="invoices_create">
                                            <label class="form-check-label" for="invoices_create">إنشاء فواتير</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="invoices_edit" data-permission="invoices_edit">
                                            <label class="form-check-label" for="invoices_edit">تعديل الفواتير</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="invoices_delete" data-permission="invoices_delete">
                                            <label class="form-check-label" for="invoices_delete">حذف الفواتير</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Receipts Management -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-receipt me-2"></i>إدارة الإيصالات
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="receipts_view" data-permission="receipts_view">
                                            <label class="form-check-label" for="receipts_view">عرض الإيصالات</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="receipts_create" data-permission="receipts_create">
                                            <label class="form-check-label" for="receipts_create">إنشاء إيصالات</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="receipts_edit" data-permission="receipts_edit">
                                            <label class="form-check-label" for="receipts_edit">تعديل الإيصالات</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="receipts_delete" data-permission="receipts_delete">
                                            <label class="form-check-label" for="receipts_delete">حذف الإيصالات</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Reports -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-chart-bar me-2"></i>التقارير
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="reports_view" data-permission="reports_view">
                                            <label class="form-check-label" for="reports_view">عرض التقارير</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="reports_export" data-permission="reports_export">
                                            <label class="form-check-label" for="reports_export">تصدير التقارير</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="reports_financial" data-permission="reports_financial">
                                            <label class="form-check-label" for="reports_financial">التقارير المالية</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="reports_tax" data-permission="reports_tax">
                                            <label class="form-check-label" for="reports_tax">التقارير الضريبية</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Settings -->
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header py-2">
                                        <h6 class="mb-0">
                                            <i class="fas fa-cog me-2"></i>الإعدادات
                                        </h6>
                                    </div>
                                    <div class="card-body py-2">
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="settings_view" data-permission="settings_view">
                                            <label class="form-check-label" for="settings_view">عرض الإعدادات</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="settings_edit" data-permission="settings_edit">
                                            <label class="form-check-label" for="settings_edit">تعديل الإعدادات</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="backup_create" data-permission="backup_create">
                                            <label class="form-check-label" for="backup_create">إنشاء نسخ احتياطية</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input permission-checkbox" type="checkbox" id="backup_restore" data-permission="backup_restore">
                                            <label class="form-check-label" for="backup_restore">استعادة النسخ الاحتياطية</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" onclick="savePermissions()">
                    <i class="fas fa-save me-2"></i>حفظ الصلاحيات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Backup Modal -->
<div class="modal fade" id="backup" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إنشاء نسخة احتياطية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    سيتم إنشاء نسخة احتياطية من جميع البيانات والإعدادات
                </div>
                <form>
                    <div class="mb-3">
                        <label class="form-label" for="backupName">اسم النسخة الاحتياطية</label>
                        <input type="text" class="form-control" value="backup_2025_07_17_14_30" id="backupName">
                    </div>
                    <div class="mb-3">
                        <label class="form-label" for="backup-description">وصف النسخة</label>
                        <textarea class="form-control" id="backup-description" rows="3" placeholder="وصف اختياري للنسخة الاحتياطية"></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="include-attachments" checked>
                        <label class="form-check-label" for="include-attachments">تضمين الملفات المرفقة</label>
                    </div>
                </form>
                <div class="mt-3">
                    <progress class="progress" style="display: none;" id="backupProgress" value="0" max="100"></progress>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="startBackup()">
                    <i class="fas fa-download me-2"></i>إنشاء النسخة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Restore Modal -->
<div class="modal fade" id="restore" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">استعادة النسخة الاحتياطية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> استعادة النسخة الاحتياطية ستحل محل جميع البيانات الحالية
                </div>
                <form>
                    <div class="mb-3">
                        <label class="form-label" for="backup-file">اختر ملف النسخة الاحتياطية</label>
                        <input type="file" class="form-control" id="backup-file" accept=".sql,.zip,.bak">
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="confirm-restore" required>
                        <label class="form-check-label" for="confirm-restore">أؤكد أنني أريد استبدال جميع البيانات الحالية</label>
                    </div>
                </form>
                <div class="mt-3">
                    <progress class="progress" style="display: none;" id="restoreProgress" value="0" max="100"></progress>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" onclick="startRestore()">
                    <i class="fas fa-upload me-2"></i>استعادة النسخة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- General Settings Modal -->
<div class="modal fade" id="generalSettings" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الإعدادات العامة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="language">اللغة</label>
                            <select class="form-select" id="language">
                                <option value="ar" selected>العربية</option>
                                <option value="en">English</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="timezone">المنطقة الزمنية</label>
                            <select class="form-select" id="timezone">
                                <option value="Africa/Cairo" selected>القاهرة (GMT+2)</option>
                                <option value="Asia/Riyadh">الرياض (GMT+3)</option>
                                <option value="UTC">UTC (GMT+0)</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="date-format">تنسيق التاريخ</label>
                            <select class="form-select" id="date-format">
                                <option value="dd/mm/yyyy" selected>يوم/شهر/سنة</option>
                                <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                                <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label" for="time-format">تنسيق الوقت</label>
                            <select class="form-select" id="time-format">
                                <option value="24" selected>24 ساعة</option>
                                <option value="12">12 ساعة</option>
                            </select>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enable-notifications" checked>
                                <label class="form-check-label" for="enable-notifications">تفعيل الإشعارات</label>
                            </div>
                        </div>
                        <div class="col-12 mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enable-logging" checked>
                                <label class="form-check-label" for="enable-logging">حفظ سجل العمليات</label>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم حفظ الإعدادات العامة بنجاح!')">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Activity Log Modal -->
<div class="modal fade" id="activityLog" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">سجل النشاطات</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex justify-content-between mb-3">
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" style="width: auto;">
                            <option>جميع الأنشطة</option>
                            <option>تسجيل الدخول</option>
                            <option>إنشاء فاتورة</option>
                            <option>تعديل عميل</option>
                        </select>
                        <input type="date" class="form-control form-control-sm" style="width: auto;">
                    </div>
                    <button class="btn btn-outline-danger btn-sm" onclick="alert('تم مسح السجل')">
                        <i class="fas fa-trash me-2"></i>مسح السجل
                    </button>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-sm">
                        <thead>
                            <tr>
                                <th>الوقت</th>
                                <th>المستخدم</th>
                                <th>النشاط</th>
                                <th>التفاصيل</th>
                                <th>عنوان IP</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2025-07-17 14:30:25</td>
                                <td>admin</td>
                                <td><span class="badge bg-success">تسجيل دخول</span></td>
                                <td>تسجيل دخول ناجح</td>
                                <td>127.0.0.1</td>
                            </tr>
                            <tr>
                                <td>2025-07-17 14:25:10</td>
                                <td>admin</td>
                                <td><span class="badge bg-primary">إنشاء فاتورة</span></td>
                                <td>إنشاء فاتورة رقم INV-001</td>
                                <td>127.0.0.1</td>
                            </tr>
                            <tr>
                                <td>2025-07-17 14:20:05</td>
                                <td>admin</td>
                                <td><span class="badge bg-info">تعديل عميل</span></td>
                                <td>تعديل بيانات العميل: شركة ABC</td>
                                <td>127.0.0.1</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="alert('تم تصدير السجل')">
                    <i class="fas fa-download me-2"></i>تصدير السجل
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Generate backup name with current date and time
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');

    const backupName = `backup_${year}_${month}_${day}_${hour}_${minute}`;
    const backupNameInput = document.getElementById('backupName');
    if (backupNameInput) {
        backupNameInput.value = backupName;
    }
});

function startBackup() {
    const progress = document.getElementById('backupProgress');
    progress.style.display = 'block';
    const progressBar = progress.querySelector('.progress-bar');

    let width = 0;
    const interval = setInterval(() => {
        width += 10;
        progressBar.style.width = width + '%';
        progressBar.textContent = width + '%';

        if (width >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                alert('تم إنشاء النسخة الاحتياطية بنجاح!');
                progress.style.display = 'none';
                progressBar.style.width = '0%';
            }, 500);
        }
    }, 200);
}

function startRestore() {
    if (!confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم حذف جميع البيانات الحالية!')) {
        return;
    }

    const progress = document.getElementById('restoreProgress');
    progress.style.display = 'block';
    const progressBar = progress.querySelector('.progress-bar');

    let width = 0;
    const interval = setInterval(() => {
        width += 15;
        progressBar.style.width = width + '%';
        progressBar.textContent = width + '%';

        if (width >= 100) {
            clearInterval(interval);
            setTimeout(() => {
                alert('تم استعادة النسخة الاحتياطية بنجاح!');
                progress.style.display = 'none';
                progressBar.style.width = '0%';
            }, 500);
        }
    }, 300);
}

function addNewUser() {
    // Get form values
    const username = document.getElementById('newUsername').value;
    const email = document.getElementById('newEmail').value;
    const password = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const role = document.getElementById('newRole').value;
    const fullName = document.getElementById('newFullName').value;

    const isActive = document.getElementById('newUserActive').checked;
    const sendEmail = document.getElementById('sendWelcomeEmail').checked;

    // Validate form
    if (!username || !email || !password || !role || !fullName) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    if (password !== confirmPassword) {
        alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
        return;
    }

    if (password.length < 6) {
        alert('يجب أن تكون كلمة المرور 6 أحرف على الأقل');
        return;
    }

    // Simulate adding user
    const roleNames = {
        'admin': 'مدير النظام',
        'accountant': 'محاسب',
        'user': 'مستخدم عادي'
    };

    // Add new row to users table
    const usersTable = document.querySelector('#users .table tbody');
    const newRow = document.createElement('tr');

    // Determine badge color for role
    let roleBadgeColor = 'info';
    if (role === 'admin') {
        roleBadgeColor = 'danger';
    } else if (role === 'accountant') {
        roleBadgeColor = 'warning';
    }

    newRow.innerHTML = `
        <td>${username}</td>
        <td>${email}</td>
        <td><span class="badge bg-${roleBadgeColor}">${roleNames[role]}</span></td>
        <td><span class="badge bg-${isActive ? 'success' : 'secondary'}">${isActive ? 'نشط' : 'غير نشط'}</span></td>
        <td>لم يسجل دخول بعد</td>
        <td>
            <button class="btn btn-sm btn-outline-primary" onclick="alert('تعديل المستخدم: ${username}')">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${username}')">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;

    usersTable.appendChild(newRow);

    // Clear form
    document.getElementById('addUserForm').reset();
    document.getElementById('newUserActive').checked = true;

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addUser'));
    modal.hide();

    // Show success message
    alert(`تم إضافة المستخدم "${fullName}" بنجاح!${sendEmail ? '\nتم إرسال بريد ترحيبي.' : ''}`);
}

function deleteUser(username) {
    if (username === 'admin') {
        alert('لا يمكن حذف مدير النظام الرئيسي');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف المستخدم "${username}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        // Find and remove the row
        const rows = document.querySelectorAll('#users .table tbody tr');
        rows.forEach(row => {
            if (row.cells[0].textContent === username) {
                row.remove();
            }
        });

        alert(`تم حذف المستخدم "${username}" بنجاح`);
    }
}

function editUser(username) {
    if (username === 'admin') {
        // Show admin edit form
        const newEmail = prompt('البريد الإلكتروني الجديد:', '<EMAIL>');
        if (newEmail && newEmail !== '<EMAIL>') {
            // Update the email in the table
            const rows = document.querySelectorAll('#users .table tbody tr');
            rows.forEach(row => {
                if (row.cells[0].textContent === 'admin') {
                    row.cells[1].textContent = newEmail;
                }
            });
            alert('تم تحديث البريد الإلكتروني بنجاح');
        }
    } else {
        alert(`سيتم فتح نموذج تعديل المستخدم: ${username}`);
    }
}

// Permissions Management
const rolePermissions = {
    admin: [
        'dashboard_view', 'analytics_view', 'users_view', 'users_create', 'users_edit', 'users_delete',
        'customers_view', 'customers_create', 'customers_edit', 'customers_delete',
        'vendors_view', 'vendors_create', 'vendors_edit', 'vendors_delete',
        'invoices_view', 'invoices_create', 'invoices_edit', 'invoices_delete',
        'receipts_view', 'receipts_create', 'receipts_edit', 'receipts_delete',
        'reports_view', 'reports_export', 'reports_financial', 'reports_tax',
        'settings_view', 'settings_edit', 'backup_create', 'backup_restore'
    ],
    accountant: [
        'dashboard_view', 'analytics_view', 'customers_view', 'customers_create', 'customers_edit',
        'vendors_view', 'vendors_create', 'vendors_edit',
        'invoices_view', 'invoices_create', 'invoices_edit',
        'receipts_view', 'receipts_create', 'receipts_edit',
        'reports_view', 'reports_export', 'reports_financial', 'reports_tax'
    ],
    user: [
        'dashboard_view', 'customers_view', 'vendors_view',
        'invoices_view', 'invoices_create', 'receipts_view', 'receipts_create',
        'reports_view'
    ],
    viewer: [
        'dashboard_view', 'customers_view', 'vendors_view',
        'invoices_view', 'receipts_view', 'reports_view'
    ]
};

let currentRole = 'admin';

function selectRole(role, element) {
    // Update active role
    currentRole = role;

    // Update UI
    document.querySelectorAll('#rolesList .list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    element.classList.add('active');

    // Update title
    const roleTitles = {
        admin: 'مدير النظام',
        accountant: 'محاسب',
        user: 'مستخدم عادي',
        viewer: 'مستعرض فقط'
    };
    document.getElementById('currentRoleTitle').textContent = `صلاحيات ${roleTitles[role]}`;

    // Update permissions
    loadRolePermissions(role);
}

function loadRolePermissions(role) {
    // Clear all checkboxes
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.checked = false;
        checkbox.disabled = false;
    });

    // Set permissions for the role
    const permissions = rolePermissions[role] || [];
    permissions.forEach(permission => {
        const checkbox = document.querySelector(`[data-permission="${permission}"]`);
        if (checkbox) {
            checkbox.checked = true;
        }
    });

    // Disable admin permissions for non-admin roles
    if (role !== 'admin') {
        const adminOnlyPermissions = ['users_view', 'users_create', 'users_edit', 'users_delete', 'settings_edit', 'backup_create', 'backup_restore'];
        adminOnlyPermissions.forEach(permission => {
            const checkbox = document.querySelector(`[data-permission="${permission}"]`);
            if (checkbox && !permissions.includes(permission)) {
                checkbox.disabled = true;
            }
        });
    }
}

function selectAllPermissions() {
    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        if (!checkbox.disabled) {
            checkbox.checked = true;
        }
    });
}

function clearAllPermissions() {
    if (currentRole === 'admin') {
        if (!confirm('هل أنت متأكد من إلغاء جميع صلاحيات مدير النظام؟ هذا قد يؤثر على وصولك للنظام.')) {
            return;
        }
    }

    document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        if (!checkbox.disabled) {
            checkbox.checked = false;
        }
    });
}

function savePermissions() {
    const selectedPermissions = [];
    document.querySelectorAll('.permission-checkbox:checked').forEach(checkbox => {
        selectedPermissions.push(checkbox.dataset.permission);
    });

    // Update role permissions
    rolePermissions[currentRole] = selectedPermissions;

    // Show success message
    const roleTitles = {
        admin: 'مدير النظام',
        accountant: 'محاسب',
        user: 'مستخدم عادي',
        viewer: 'مستعرض فقط'
    };

    alert(`تم حفظ صلاحيات ${roleTitles[currentRole]} بنجاح!\nعدد الصلاحيات: ${selectedPermissions.length}`);

    console.log(`Permissions for ${currentRole}:`, selectedPermissions);
}

function addNewRole() {
    const roleName = prompt('اسم الدور الجديد:');
    if (roleName && roleName.trim()) {
        const roleKey = roleName.toLowerCase().replace(/\s+/g, '_');

        // Add to permissions object
        rolePermissions[roleKey] = ['dashboard_view'];

        // Add to UI
        const rolesList = document.getElementById('rolesList');
        const newRoleElement = document.createElement('a');
        newRoleElement.href = '#';
        newRoleElement.className = 'list-group-item list-group-item-action';
        newRoleElement.dataset.role = roleKey;
        newRoleElement.onclick = () => selectRole(roleKey, newRoleElement);
        newRoleElement.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <span>${roleName}</span>
                <span class="badge bg-primary">Custom</span>
            </div>
        `;

        rolesList.appendChild(newRoleElement);

        alert(`تم إضافة الدور "${roleName}" بنجاح!`);
    }
}

// Initialize permissions on page load
document.addEventListener('DOMContentLoaded', function() {
    // Load default admin permissions
    loadRolePermissions('admin');
});
</script>
{% endblock %}
