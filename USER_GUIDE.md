# 📖 دليل المستخدم - نظام SystemTax
# User Guide - SystemTax System

## 🎯 **مرحباً بك في SystemTax!**

**SystemTax** هو نظام محاسبي ويب متكامل مع دعم كامل للفاتورة والإيصال الإلكتروني المصري.

---

## 🚀 **البدء السريع:**

### **1. تشغيل النظام:**
```bash
# للتطوير والاختبار
python app.py

# للإنتاج
python run_production.py production
```

### **2. الوصول للنظام:**
- **الرابط**: http://localhost:8000
- **المستخدم الافتراضي**: admin
- **كلمة المرور الافتراضية**: admin123

### **3. أول خطوات:**
1. **تسجيل الدخول** بالبيانات الافتراضية
2. **تغيير كلمة المرور** من الإعدادات
3. **تكوين إعدادات الشركة** ومصلحة الضرائب
4. **إنشاء أول إيصال** واختبار النظام

---

## ⚙️ **إعداد التكامل مع مصلحة الضرائب:**

### **🏛️ الخطوات المطلوبة:**

#### **1. الحصول على بيانات ETA:**
- **Client ID** من مصلحة الضرائب
- **Client Secret** من مصلحة الضرائب
- **الرقم الضريبي** للشركة
- **كود النشاط** التجاري

#### **2. تكوين الإعدادات:**
1. **اذهب إلى**: الإعدادات → إعدادات مصلحة الضرائب
2. **أدخل البيانات**:
   ```
   ✅ رابط API: اختر البيئة المناسبة
   ✅ Client ID: معرف العميل
   ✅ Client Secret: كلمة سر العميل
   ✅ الرقم الضريبي: رقم الشركة
   ✅ كود النشاط: كود النشاط التجاري
   ```
3. **اختبر الاتصال** بالضغط على "اختبار الاتصال"
4. **احفظ الإعدادات**

#### **3. البيئات المتاحة:**
- **بيئة الاختبار (Sandbox)**: للتدريب والاختبار
- **بيئة الإنتاج (Production)**: للاستخدام الفعلي

---

## 🧾 **إدارة الإيصالات الإلكترونية:**

### **📝 إنشاء إيصال جديد:**
1. **اذهب إلى**: الإيصالات → إيصال جديد
2. **أدخل البيانات الأساسية**:
   - رقم الإيصال (تلقائي)
   - تاريخ الإصدار
   - العميل (اختياري)
3. **أضف البنود**:
   - وصف الصنف
   - الكمية
   - السعر
   - نوع الضريبة
4. **احفظ الإيصال**

### **📤 إرسال للضرائب:**
1. **افتح تفاصيل الإيصال**
2. **اضغط "إرسال لمصلحة الضرائب"**
3. **انتظر التأكيد**
4. **سيتم إنشاء QR Code تلقائياً**

### **📱 QR Code:**
- **ينشأ تلقائياً** عند إرسال الإيصال
- **جاهز للطباعة** بجودة عالية
- **يحتوي على جميع البيانات** المطلوبة
- **متوافق مع قارئات QR** المعيارية

---

## 📊 **التقارير والإحصائيات:**

### **📈 التقارير المتاحة:**
- **ميزان المراجعة**: عرض وتصدير CSV/PDF
- **تقرير المبيعات**: تحليل المبيعات الشهرية
- **تقرير الضرائب**: ملخص الضرائب المدفوعة
- **تقرير العملاء**: تحليل أداء العملاء

### **📋 لوحة التحكم:**
- **إحصائيات سريعة** للمبيعات
- **رسوم بيانية** تفاعلية
- **تنبيهات** للمهام المطلوبة
- **ملخص الأداء** الشهري

---

## 👥 **إدارة المستخدمين:**

### **🔐 الصلاحيات:**
- **مدير النظام**: جميع الصلاحيات
- **محاسب**: الإيصالات والتقارير
- **مستخدم**: عرض فقط

### **➕ إضافة مستخدم جديد:**
1. **اذهب إلى**: المستخدمين → مستخدم جديد
2. **أدخل البيانات**: الاسم، البريد، كلمة المرور
3. **حدد الصلاحيات** المناسبة
4. **احفظ المستخدم**

---

## 🔧 **الصيانة والنسخ الاحتياطي:**

### **💾 النسخ الاحتياطي:**
- **تلقائي**: يومياً في الساعة 2 صباحاً
- **يدوي**: من الإعدادات → إنشاء نسخة احتياطية
- **الاحتفاظ**: 30 يوم افتراضياً

### **🔄 التحديثات:**
- **تحقق دورياً** من التحديثات
- **اقرأ ملاحظات الإصدار** قبل التحديث
- **انشئ نسخة احتياطية** قبل التحديث

---

## ❓ **حل المشاكل الشائعة:**

### **🔌 مشاكل الاتصال بـ ETA:**
**المشكلة**: فشل في الاتصال
**الحل**:
1. تحقق من بيانات Client ID/Secret
2. تأكد من الرقم الضريبي صحيح
3. جرب بيئة الاختبار أولاً
4. تحقق من الاتصال بالإنترنت

### **📱 مشاكل QR Code:**
**المشكلة**: QR Code لا يظهر
**الحل**:
1. تأكد من إرسال الإيصال لـ ETA أولاً
2. اضغط "إعادة إنشاء QR Code"
3. تحقق من مجلد uploads/qr_codes

### **🗄️ مشاكل قاعدة البيانات:**
**المشكلة**: خطأ في قاعدة البيانات
**الحل**:
1. تحقق من وجود مجلد instance
2. شغل migrate_receipts_eta.py
3. استعد من النسخة الاحتياطية

---

## 📞 **الدعم الفني:**

### **📧 طرق التواصل:**
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +20 123 456 7890
- **الدعم المباشر**: متاح 24/7

### **📚 الموارد:**
- **التوثيق الكامل**: في مجلد docs/
- **أمثلة عملية**: في مجلد examples/
- **فيديوهات تعليمية**: على قناة YouTube

---

## 🎯 **نصائح للاستخدام الأمثل:**

### **⚡ الأداء:**
- **استخدم PostgreSQL** للشركات الكبيرة
- **نظف الملفات القديمة** دورياً
- **راقب استخدام القرص** الصلب

### **🔐 الأمان:**
- **غير كلمات المرور** الافتراضية
- **استخدم HTTPS** في الإنتاج
- **حدث النظام** بانتظام
- **راجع الصلاحيات** دورياً

### **📈 الكفاءة:**
- **استخدم القوالب** للإيصالات المتكررة
- **فعل الإرسال التلقائي** لـ ETA
- **راجع التقارير** أسبوعياً
- **درب الموظفين** على النظام

---

## 🎉 **مبروك!**

**أنت الآن جاهز لاستخدام SystemTax بكفاءة عالية!**

**النظام يوفر لك:**
- ✅ **إدارة محاسبية** شاملة
- ✅ **تكامل كامل** مع مصلحة الضرائب
- ✅ **QR Code** جاهز للطباعة
- ✅ **تقارير متقدمة** وإحصائيات
- ✅ **أمان عالي** وموثوقية

**🚀 ابدأ رحلتك مع SystemTax الآن!**

---

**📝 ملاحظة**: هذا الدليل يغطي الاستخدام الأساسي. للمزيد من التفاصيل، راجع التوثيق الكامل أو تواصل مع الدعم الفني.
