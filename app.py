"""
SystemTax - نظام محاسبي ويب متكامل
Integrated Web Accounting System with E-Invoice & E-Receipt
"""

import os
from flask import Flask, render_template
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_app(config_name=None):
    """Application factory pattern"""
    app = Flask(__name__,
                template_folder='app/templates',
                static_folder='app/static')

    # Load configuration
    from app.config import config
    config_name = config_name or os.getenv('FLASK_ENV', 'development')
    app.config.from_object(config.get(config_name, config['development']))

    # Initialize extensions
    from app import db, migrate, login_manager, cache, csrf

    db.init_app(app)
    migrate.init_app(app, db)
    cache.init_app(app)
    csrf.init_app(app)

    # Configure login manager
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        from app.models.user import User
        from app import db
        return db.session.get(User, user_id)

    # Register blueprints
    from app.views import (auth_bp, dashboard_bp, accounts_bp, journal_bp,
                          customers_bp, vendors_bp, invoices_bp, receipts_bp,
                          reports_bp, settings_bp)
    from app.views.eta_views import eta_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/')
    app.register_blueprint(accounts_bp, url_prefix='/accounts')
    app.register_blueprint(journal_bp, url_prefix='/journal')
    app.register_blueprint(customers_bp, url_prefix='/customers')
    app.register_blueprint(vendors_bp, url_prefix='/vendors')
    app.register_blueprint(invoices_bp, url_prefix='/invoices')
    app.register_blueprint(receipts_bp, url_prefix='/receipts')
    app.register_blueprint(reports_bp, url_prefix='/reports')
    app.register_blueprint(settings_bp, url_prefix='/settings')
    app.register_blueprint(eta_bp, url_prefix='/eta')

    # Error handlers
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500

    @app.errorhandler(403)
    def forbidden_error(error):
        return render_template('errors/403.html'), 403

    # Context processors
    @app.context_processor
    def inject_app_config():
        from datetime import datetime
        return {
            'APP_NAME': app.config['APP_NAME'],
            'COMPANY_NAME': app.config['COMPANY_NAME'],
            'APP_VERSION': app.config['APP_VERSION'],
            'moment': lambda: datetime.now()
        }

    # Template filters
    @app.template_filter('currency')
    def currency_filter(amount):
        from app.utils.helpers import format_currency
        return format_currency(amount)

    @app.template_filter('date')
    def date_filter(date_obj):
        from app.utils.helpers import format_date
        return format_date(date_obj)

    @app.template_filter('datetime')
    def datetime_filter(datetime_obj):
        from app.utils.helpers import format_datetime
        return format_datetime(datetime_obj)

    return app

# Create app instance
app = create_app()

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=8000, debug=True)
