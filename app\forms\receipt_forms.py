"""
Forms for receipt management
"""

from flask_wtf import FlaskForm
from wtforms import (
    StringField, TextAreaField, SelectField, DecimalField, 
    DateField, BooleanField, HiddenField
)
from wtforms.validators import DataRequired, Length, Optional, NumberRange, ValidationError
from app.models.receipt import Receipt
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.models.invoice import Invoice
from app.models.account import Account
from datetime import date, datetime


class ReceiptForm(FlaskForm):
    """Form for creating and editing receipts"""
    
    receipt_number = StringField(
        'رقم الإيصال',
        validators=[
            DataRequired(message='رقم الإيصال مطلوب'),
            Length(min=1, max=50, message='رقم الإيصال يجب أن يكون بين 1 و 50 حرف')
        ],
        render_kw={
            'placeholder': 'REC-2024-001',
            'class': 'form-control'
        }
    )
    
    reference_number = StringField(
        'الرقم المرجعي',
        validators=[
            Optional(),
            Length(max=50, message='الرقم المرجعي يجب ألا يزيد عن 50 حرف')
        ],
        render_kw={
            'placeholder': 'رقم مرجعي اختياري',
            'class': 'form-control'
        }
    )
    
    receipt_type = SelectField(
        'نوع الإيصال',
        choices=[
            ('', 'اختر نوع الإيصال'),
            ('receipt', 'إيصال قبض'),
            ('payment', 'إيصال دفع')
        ],
        validators=[DataRequired(message='نوع الإيصال مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    receipt_date = DateField(
        'تاريخ الإيصال',
        validators=[DataRequired(message='تاريخ الإيصال مطلوب')],
        default=date.today,
        render_kw={'class': 'form-control'}
    )
    
    customer_id = SelectField(
        'العميل',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    vendor_id = SelectField(
        'المورد',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    invoice_id = SelectField(
        'الفاتورة المرتبطة',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    amount = DecimalField(
        'المبلغ',
        validators=[
            DataRequired(message='المبلغ مطلوب'),
            NumberRange(min=0.01, message='المبلغ يجب أن يكون أكبر من صفر')
        ],
        places=2,
        render_kw={
            'class': 'form-control',
            'step': '0.01',
            'min': '0.01'
        }
    )
    
    currency = SelectField(
        'العملة',
        choices=[
            ('EGP', 'جنيه مصري'),
            ('USD', 'دولار أمريكي'),
            ('EUR', 'يورو'),
            ('SAR', 'ريال سعودي'),
            ('AED', 'درهم إماراتي')
        ],
        default='EGP',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    payment_method = SelectField(
        'طريقة الدفع',
        choices=[
            ('', 'اختر طريقة الدفع'),
            ('cash', 'نقدي'),
            ('check', 'شيك'),
            ('bank_transfer', 'تحويل بنكي'),
            ('credit_card', 'بطاقة ائتمان'),
            ('debit_card', 'بطاقة خصم'),
            ('online', 'دفع إلكتروني')
        ],
        validators=[DataRequired(message='طريقة الدفع مطلوبة')],
        render_kw={'class': 'form-select'}
    )
    
    account_id = SelectField(
        'الحساب',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    # Bank/Check specific fields
    bank_name = StringField(
        'اسم البنك',
        validators=[
            Optional(),
            Length(max=100, message='اسم البنك يجب ألا يزيد عن 100 حرف')
        ],
        render_kw={
            'placeholder': 'اسم البنك',
            'class': 'form-control'
        }
    )
    
    bank_reference = StringField(
        'مرجع البنك',
        validators=[
            Optional(),
            Length(max=100, message='مرجع البنك يجب ألا يزيد عن 100 حرف')
        ],
        render_kw={
            'placeholder': 'رقم التحويل أو المرجع',
            'class': 'form-control'
        }
    )
    
    check_number = StringField(
        'رقم الشيك',
        validators=[
            Optional(),
            Length(max=50, message='رقم الشيك يجب ألا يزيد عن 50 حرف')
        ],
        render_kw={
            'placeholder': 'رقم الشيك',
            'class': 'form-control'
        }
    )
    
    check_date = DateField(
        'تاريخ الشيك',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    description = TextAreaField(
        'الوصف',
        validators=[
            Optional(),
            Length(max=500, message='الوصف يجب ألا يزيد عن 500 حرف')
        ],
        render_kw={
            'placeholder': 'وصف الإيصال...',
            'class': 'form-control',
            'rows': 3
        }
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[
            Optional(),
            Length(max=1000, message='الملاحظات يجب ألا تزيد عن 1000 حرف')
        ],
        render_kw={
            'placeholder': 'ملاحظات إضافية...',
            'class': 'form-control',
            'rows': 3
        }
    )
    
    is_confirmed = BooleanField(
        'إيصال مؤكد',
        default=False,
        render_kw={'class': 'form-check-input'}
    )
    
    def __init__(self, receipt=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.receipt = receipt
        self.populate_choices()
    
    def populate_choices(self):
        """Populate all select field choices"""
        self.populate_customer_choices()
        self.populate_vendor_choices()
        self.populate_account_choices()
        self.populate_invoice_choices()
    
    def populate_customer_choices(self):
        """Populate customer choices"""
        choices = [('', 'اختر العميل')]
        
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        
        for customer in customers:
            display_name = customer.name
            if customer.tax_id:
                display_name += f" ({customer.tax_id})"
            
            choices.append((str(customer.id), display_name))
        
        self.customer_id.choices = choices
    
    def populate_vendor_choices(self):
        """Populate vendor choices"""
        choices = [('', 'اختر المورد')]
        
        vendors = Vendor.query.filter_by(is_active=True).order_by(Vendor.name).all()
        
        for vendor in vendors:
            display_name = vendor.name
            if vendor.tax_id:
                display_name += f" ({vendor.tax_id})"
            
            choices.append((str(vendor.id), display_name))
        
        self.vendor_id.choices = choices
    
    def populate_account_choices(self):
        """Populate account choices"""
        choices = [('', 'اختر الحساب (اختياري)')]
        
        # Get cash and bank accounts
        accounts = Account.query.filter(
            Account.is_active == True,
            Account.type.in_(['Asset'])  # Cash, Bank accounts are usually assets
        ).order_by(Account.code).all()
        
        for account in accounts:
            choices.append((
                str(account.id),
                f"{account.code} - {account.name}"
            ))
        
        self.account_id.choices = choices
    
    def populate_invoice_choices(self):
        """Populate invoice choices (will be updated dynamically)"""
        choices = [('', 'اختر الفاتورة (اختياري)')]
        self.invoice_id.choices = choices
    
    def validate_receipt_number(self, field):
        """Validate receipt number uniqueness"""
        if field.data:
            query = Receipt.query.filter_by(receipt_number=field.data)
            if self.receipt:
                query = query.filter(Receipt.id != self.receipt.id)
            
            if query.first():
                raise ValidationError('رقم الإيصال موجود بالفعل')
    
    def validate(self, extra_validators=None):
        """Custom validation"""
        if not super().validate(extra_validators):
            return False
        
        # Validate that either customer or vendor is selected based on receipt type
        if self.receipt_type.data == 'receipt' and not self.customer_id.data:
            self.customer_id.errors.append('العميل مطلوب لإيصال القبض')
            return False
        
        if self.receipt_type.data == 'payment' and not self.vendor_id.data:
            self.vendor_id.errors.append('المورد مطلوب لإيصال الدفع')
            return False
        
        # Validate bank/check specific fields
        if self.payment_method.data == 'check':
            if not self.check_number.data:
                self.check_number.errors.append('رقم الشيك مطلوب')
                return False
        
        if self.payment_method.data == 'bank_transfer':
            if not self.bank_reference.data:
                self.bank_reference.errors.append('مرجع التحويل مطلوب')
                return False
        
        return True


class ReceiptSearchForm(FlaskForm):
    """Form for searching receipts"""
    
    search = StringField(
        'البحث',
        validators=[Optional()],
        render_kw={
            'placeholder': 'البحث في رقم الإيصال أو العميل...',
            'class': 'form-control'
        }
    )
    
    receipt_type = SelectField(
        'نوع الإيصال',
        choices=[
            ('', 'جميع الأنواع'),
            ('receipt', 'إيصال قبض'),
            ('payment', 'إيصال دفع')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    payment_method = SelectField(
        'طريقة الدفع',
        choices=[
            ('', 'جميع الطرق'),
            ('cash', 'نقدي'),
            ('check', 'شيك'),
            ('bank_transfer', 'تحويل بنكي'),
            ('credit_card', 'بطاقة ائتمان'),
            ('debit_card', 'بطاقة خصم'),
            ('online', 'دفع إلكتروني')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    date_from = DateField(
        'من تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )


class QuickReceiptForm(FlaskForm):
    """Form for quick receipt creation"""
    
    receipt_type = SelectField(
        'نوع الإيصال',
        choices=[
            ('receipt', 'إيصال قبض'),
            ('payment', 'إيصال دفع')
        ],
        validators=[DataRequired(message='نوع الإيصال مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    customer_id = SelectField(
        'العميل',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    vendor_id = SelectField(
        'المورد',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    amount = DecimalField(
        'المبلغ',
        validators=[
            DataRequired(message='المبلغ مطلوب'),
            NumberRange(min=0.01, message='المبلغ يجب أن يكون أكبر من صفر')
        ],
        places=2,
        render_kw={
            'class': 'form-control',
            'step': '0.01',
            'min': '0.01'
        }
    )
    
    payment_method = SelectField(
        'طريقة الدفع',
        choices=[
            ('cash', 'نقدي'),
            ('check', 'شيك'),
            ('bank_transfer', 'تحويل بنكي'),
            ('credit_card', 'بطاقة ائتمان')
        ],
        default='cash',
        validators=[DataRequired(message='طريقة الدفع مطلوبة')],
        render_kw={'class': 'form-select'}
    )
    
    description = StringField(
        'الوصف',
        validators=[
            DataRequired(message='الوصف مطلوب'),
            Length(min=1, max=500, message='الوصف يجب أن يكون بين 1 و 500 حرف')
        ],
        render_kw={
            'placeholder': 'وصف الإيصال',
            'class': 'form-control'
        }
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.populate_choices()
    
    def populate_choices(self):
        """Populate customer and vendor choices"""
        # Customer choices
        customer_choices = [('', 'اختر العميل')]
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        for customer in customers:
            customer_choices.append((str(customer.id), customer.name))
        self.customer_id.choices = customer_choices
        
        # Vendor choices
        vendor_choices = [('', 'اختر المورد')]
        vendors = Vendor.query.filter_by(is_active=True).order_by(Vendor.name).all()
        for vendor in vendors:
            vendor_choices.append((str(vendor.id), vendor.name))
        self.vendor_id.choices = vendor_choices


class ReceiptBulkActionForm(FlaskForm):
    """Form for bulk actions on receipts"""
    
    action = SelectField(
        'الإجراء',
        choices=[
            ('', 'اختر الإجراء'),
            ('confirm', 'تأكيد'),
            ('generate_pdf', 'إنتاج PDF'),
            ('send_email', 'إرسال بريد'),
            ('export', 'تصدير'),
            ('delete', 'حذف')
        ],
        validators=[DataRequired(message='الإجراء مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    selected_receipts = StringField(
        'الإيصالات المحددة',
        validators=[DataRequired(message='يجب تحديد إيصال واحد على الأقل')]
    )
    
    confirmation = BooleanField(
        'أؤكد تنفيذ هذا الإجراء',
        validators=[DataRequired(message='يجب تأكيد الإجراء')],
        render_kw={'class': 'form-check-input'}
    )
