version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:15-alpine
    container_name: systemtax_db
    environment:
      POSTGRES_DB: systemtax
      POSTGRES_USER: systemtax_user
      POSTGRES_PASSWORD: systemtax_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - systemtax_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U systemtax_user -d systemtax"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: systemtax_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - systemtax_network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Flask Web Application
  web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: systemtax_web
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=******************************************************/systemtax
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your-secret-key-change-in-production-please
      - APP_NAME=SystemTax
      - COMPANY_NAME=شركتك
      - PYTHONPATH=/app
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./backups:/app/backups
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - systemtax_network
    restart: unless-stopped
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        python cli.py check &&
        echo 'Starting SystemTax...' &&
        python app.py
      "
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/auth/login"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  postgres_data:
  redis_data:

networks:
  systemtax_network:
    driver: bridge
