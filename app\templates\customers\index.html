{% extends "base.html" %}

{% block title %}العملاء - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-users me-3"></i>
                العملاء
            </h1>
            <p class="mb-0 mt-2">إدارة بيانات العملاء والحسابات المدينة</p>
        </div>
        <div>
            <a href="{{ url_for('customers.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                عميل جديد
            </a>
            <a href="{{ url_for('customers.import_data') }}" class="btn btn-outline-light">
                <i class="fas fa-upload me-2"></i>
                استيراد
            </a>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                {{ form.search.label(class="form-label") }}
                {{ form.search(class="form-control", placeholder="البحث في اسم العميل أو الرقم الضريبي...") }}
            </div>
            <div class="col-md-2">
                {{ form.customer_type.label(class="form-label") }}
                {{ form.customer_type(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ form.status.label(class="form-label") }}
                {{ form.status(class="form-select") }}
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('customers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>
                        مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Customers Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            قائمة العملاء
        </h5>
        <span class="badge bg-primary">{{ customers.total if customers else 0 }} عميل</span>
    </div>
    <div class="card-body">
        {% if customers and customers.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all" class="form-check-input">
                        </th>
                        <th>اسم العميل</th>
                        <th>الرقم الضريبي</th>
                        <th>النوع</th>
                        <th>الهاتف</th>
                        <th>الرصيد</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for customer in customers.items %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input customer-checkbox" 
                                   value="{{ customer.id }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-3">
                                    <div class="avatar-title bg-primary rounded-circle">
                                        {{ customer.name[0] if customer.name else 'ع' }}
                                    </div>
                                </div>
                                <div>
                                    <a href="{{ url_for('customers.detail', customer_id=customer.id) }}" 
                                       class="text-decoration-none fw-bold">
                                        {{ customer.name }}
                                    </a>
                                    {% if customer.name_en %}
                                        <br><small class="text-muted">{{ customer.name_en }}</small>
                                    {% endif %}
                                    {% if customer.email %}
                                        <br><small class="text-muted">{{ customer.email }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if customer.tax_id %}
                                <span class="badge bg-info">{{ customer.tax_id }}</span>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if customer.customer_type == 'individual' else 'primary' }}">
                                {{ customer.get_type_display() }}
                            </span>
                        </td>
                        <td>
                            {% if customer.phone %}
                                <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                    {{ customer.phone }}
                                </a>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="fw-bold text-{{ 'success' if customer.get_balance() >= 0 else 'danger' }}">
                                {{ customer.get_balance()|currency }}
                            </span>
                        </td>
                        <td>
                            {% if customer.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('customers.detail', customer_id=customer.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('customers.edit', customer_id=customer.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('customers.statement', customer_id=customer.id) }}" 
                                   class="btn btn-outline-info" title="كشف حساب">
                                    <i class="fas fa-file-alt"></i>
                                </a>
                                {% if customer.can_be_deleted() %}
                                <form method="POST" action="{{ url_for('customers.delete', customer_id=customer.id) }}" 
                                      class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا العميل؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Bulk Actions -->
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <select class="form-select me-2" id="bulk-action" style="width: auto;">
                        <option value="">اختر إجراء</option>
                        <option value="activate">تفعيل</option>
                        <option value="deactivate">إلغاء تفعيل</option>
                        <option value="export">تصدير</option>
                        <option value="delete">حذف</option>
                    </select>
                    <button type="button" class="btn btn-outline-secondary" onclick="executeBulkAction()">
                        تنفيذ
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <small class="text-muted">
                    <span id="selected-count">0</span> عميل محدد
                </small>
            </div>
        </div>

        <!-- Pagination -->
        {% if customers.pages > 1 %}
        <nav aria-label="تصفح الصفحات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if customers.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('customers.index', page=customers.prev_num, **request.args) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in customers.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != customers.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('customers.index', page=page_num, **request.args) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if customers.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('customers.index', page=customers.next_num, **request.args) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5>لا يوجد عملاء</h5>
            <p class="text-muted">لم يتم العثور على أي عملاء تطابق معايير البحث.</p>
            <a href="{{ url_for('customers.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة عميل جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">إجمالي العملاء</h5>
                <h3 class="text-primary">{{ stats.total_customers if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">عملاء نشطين</h5>
                <h3 class="text-success">{{ stats.active_customers if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">إجمالي المديونية</h5>
                <h3 class="text-info">{{ stats.total_receivables|currency if stats else '0.00 ج.م' }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">متوسط الرصيد</h5>
                <h3 class="text-warning">{{ stats.average_balance|currency if stats else '0.00 ج.م' }}</h3>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Select all checkbox functionality
    $('#select-all').change(function() {
        $('.customer-checkbox').prop('checked', this.checked);
        updateSelectedCount();
    });
    
    $('.customer-checkbox').change(function() {
        updateSelectedCount();
        
        // Update select all checkbox
        const totalCheckboxes = $('.customer-checkbox').length;
        const checkedCheckboxes = $('.customer-checkbox:checked').length;
        
        $('#select-all').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
        $('#select-all').prop('checked', checkedCheckboxes === totalCheckboxes);
    });
    
    // Auto-submit search form on type change
    $('#customer_type, #status').change(function() {
        $(this).closest('form').submit();
    });
});

function updateSelectedCount() {
    const count = $('.customer-checkbox:checked').length;
    $('#selected-count').text(count);
}

function executeBulkAction() {
    const action = $('#bulk-action').val();
    const selectedCustomers = $('.customer-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (!action) {
        alert('يرجى اختيار إجراء');
        return;
    }
    
    if (selectedCustomers.length === 0) {
        alert('يرجى تحديد عميل واحد على الأقل');
        return;
    }
    
    if (confirm(`هل تريد تنفيذ هذا الإجراء على ${selectedCustomers.length} عميل؟`)) {
        // Create form and submit
        const form = $('<form>', {
            method: 'POST',
            action: '{{ url_for("customers.bulk_action") }}'
        });
        
        form.append($('<input>', {
            type: 'hidden',
            name: 'csrf_token',
            value: '{{ csrf_token() }}'
        }));
        
        form.append($('<input>', {
            type: 'hidden',
            name: 'action',
            value: action
        }));
        
        form.append($('<input>', {
            type: 'hidden',
            name: 'customer_ids',
            value: selectedCustomers.join(',')
        }));
        
        $('body').append(form);
        form.submit();
    }
}
</script>
{% endblock %}
