"""
Forms package for SystemTax application
"""

from .auth import LoginForm, RegistrationForm, ChangePasswordForm, UserEditForm
from .account import AccountForm, AccountSearchForm
from .customer_forms import (
    CustomerForm,
    CustomerSearchForm,
    CustomerImportForm,
    CustomerBulkActionForm
)
from .vendor_forms import (
    VendorForm,
    VendorSearchForm,
    VendorImportForm,
    VendorBulkActionForm
)
from .journal_forms import (
    JournalEntryForm,
    JournalLineForm,
    JournalSearchForm,
    LedgerForm,
    JournalImportForm,
    JournalBulkActionForm,
    QuickJournalForm
)
from .invoice_forms import (
    InvoiceForm,
    InvoiceLineForm,
    InvoiceSearchForm,
    QuickInvoiceForm,
    InvoiceBulkActionForm,
    InvoiceSettingsForm
)
from .receipt_forms import (
    ReceiptForm,
    ReceiptSearchForm,
    QuickReceiptForm,
    ReceiptBulkActionForm
)
from .report_forms import (
    BaseReportForm,
    TrialBalanceForm,
    IncomeStatementForm,
    BalanceSheetForm,
    CashFlowForm,
    TaxReportForm,
    SalesReportForm,
    PurchasesReportForm,
    AgingReportForm,
    JournalReportForm,
    CustomReportForm,
    ReportScheduleForm
)
from .dashboard_forms import (
    DashboardFilterForm,
    KPIConfigForm,
    ChartConfigForm,
    AlertConfigForm,
    DashboardLayoutForm,
    ExportDashboardForm
)
from .settings import (CompanySettingsForm, InvoiceSettingsForm, ReceiptSettingsForm,
                      TaxApiSettingsForm, SystemSettingForm)

__all__ = [
    'LoginForm', 'RegistrationForm', 'ChangePasswordForm', 'UserEditForm',
    'AccountForm', 'AccountSearchForm',
    'CustomerForm', 'CustomerSearchForm', 'CustomerImportForm', 'CustomerBulkActionForm',
    'VendorForm', 'VendorSearchForm', 'VendorImportForm', 'VendorBulkActionForm',
    'JournalEntryForm', 'JournalLineForm', 'JournalSearchForm', 'LedgerForm',
    'JournalImportForm', 'JournalBulkActionForm', 'QuickJournalForm',
    'InvoiceForm', 'InvoiceLineForm', 'InvoiceSearchForm', 'QuickInvoiceForm',
    'InvoiceBulkActionForm', 'InvoiceSettingsForm', 'QuickInvoiceForm', 'InvoiceLineForm',
    'ReceiptForm', 'ReceiptSearchForm', 'QuickReceiptForm', 'ReceiptBulkActionForm',
    # Report Forms
    'BaseReportForm', 'TrialBalanceForm', 'IncomeStatementForm', 'BalanceSheetForm',
    'CashFlowForm', 'TaxReportForm', 'SalesReportForm', 'PurchasesReportForm',
    'AgingReportForm', 'JournalReportForm', 'CustomReportForm', 'ReportScheduleForm',
    # Dashboard Forms
    'DashboardFilterForm', 'KPIConfigForm', 'ChartConfigForm', 'AlertConfigForm',
    'DashboardLayoutForm', 'ExportDashboardForm', 'ReceiptAllocationForm',
    'CompanySettingsForm', 'InvoiceSettingsForm', 'ReceiptSettingsForm',
    'TaxApiSettingsForm', 'SystemSettingForm'
]
