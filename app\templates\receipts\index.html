{% extends "base.html" %}

{% block title %}الإيصالات - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-receipt me-3"></i>
                الإيصالات الإلكترونية
            </h1>
            <p class="mb-0 mt-2">إدارة إيصالات الدفع والقبض الإلكترونية</p>
        </div>
        <div>
            <div class="btn-group">
                <a href="{{ url_for('receipts.new') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>
                    إيصال جديد
                </a>
                <button type="button" class="btn btn-primary dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown">
                    <span class="visually-hidden">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('receipts.new') }}?type=payment">إيصال دفع</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('receipts.new') }}?type=receipt">إيصال قبض</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{{ url_for('receipts.quick_receipt') }}">إيصال سريع</a></li>
                </ul>
            </div>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-light dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-download me-2"></i>
                    تصدير
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('receipts.export', format='pdf') }}">PDF</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('receipts.export', format='excel') }}">Excel</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('receipts.export', format='csv') }}">CSV</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-3">
                {{ form.search.label(class="form-label") }}
                {{ form.search(class="form-control", placeholder="البحث في رقم الإيصال أو العميل...") }}
            </div>
            <div class="col-md-2">
                {{ form.receipt_type.label(class="form-label") }}
                {{ form.receipt_type(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ form.payment_method.label(class="form-label") }}
                {{ form.payment_method(class="form-select") }}
            </div>
            <div class="col-md-2">
                {{ form.date_from.label(class="form-label") }}
                {{ form.date_from(class="form-control") }}
            </div>
            <div class="col-md-2">
                {{ form.date_to.label(class="form-label") }}
                {{ form.date_to(class="form-control") }}
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Receipts Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            قائمة الإيصالات
        </h5>
        <span class="badge bg-primary">{{ receipts.total if receipts else 0 }} إيصال</span>
    </div>
    <div class="card-body">
        {% if receipts and receipts.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="select-all" class="form-check-input">
                        </th>
                        <th>رقم الإيصال</th>
                        <th>النوع</th>
                        <th>العميل/المورد</th>
                        <th>التاريخ</th>
                        <th>المبلغ</th>
                        <th>طريقة الدفع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for receipt in receipts.items %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input receipt-checkbox" 
                                   value="{{ receipt.id }}">
                        </td>
                        <td>
                            <a href="{{ url_for('receipts.detail', receipt_id=receipt.id) }}" 
                               class="text-decoration-none fw-bold">
                                {{ receipt.receipt_number }}
                            </a>
                            {% if receipt.reference_number %}
                                <br><small class="text-muted">مرجع: {{ receipt.reference_number }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if receipt.receipt_type == 'receipt' else 'warning' }}">
                                <i class="fas fa-{{ 'arrow-down' if receipt.receipt_type == 'receipt' else 'arrow-up' }} me-1"></i>
                                {{ receipt.get_type_display() }}
                            </span>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm me-2">
                                    <div class="avatar-title bg-{{ 'primary' if receipt.customer else 'warning' }} rounded-circle">
                                        {% if receipt.customer %}
                                            {{ receipt.customer.name[0] }}
                                        {% elif receipt.vendor %}
                                            {{ receipt.vendor.name[0] }}
                                        {% else %}
                                            ؟
                                        {% endif %}
                                    </div>
                                </div>
                                <div>
                                    {% if receipt.customer %}
                                        <a href="{{ url_for('customers.detail', customer_id=receipt.customer.id) }}" 
                                           class="text-decoration-none">
                                            {{ receipt.customer.name }}
                                        </a>
                                        {% if receipt.customer.tax_id %}
                                            <br><small class="text-muted">{{ receipt.customer.tax_id }}</small>
                                        {% endif %}
                                    {% elif receipt.vendor %}
                                        <a href="{{ url_for('vendors.detail', vendor_id=receipt.vendor.id) }}" 
                                           class="text-decoration-none">
                                            {{ receipt.vendor.name }}
                                        </a>
                                        {% if receipt.vendor.tax_id %}
                                            <br><small class="text-muted">{{ receipt.vendor.tax_id }}</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {{ receipt.receipt_date|date }}
                            <br><small class="text-muted">{{ receipt.created_at|time }}</small>
                        </td>
                        <td>
                            <span class="fw-bold text-{{ 'success' if receipt.receipt_type == 'receipt' else 'warning' }}">
                                {{ receipt.amount|currency }}
                            </span>
                            {% if receipt.currency != 'EGP' %}
                                <br><small class="text-muted">{{ receipt.currency }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {{ receipt.get_payment_method_display() }}
                            </span>
                            {% if receipt.check_number %}
                                <br><small class="text-muted">شيك: {{ receipt.check_number }}</small>
                            {% elif receipt.bank_reference %}
                                <br><small class="text-muted">مرجع: {{ receipt.bank_reference }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if receipt.is_confirmed else 'warning' }}">
                                {{ 'مؤكد' if receipt.is_confirmed else 'غير مؤكد' }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('receipts.detail', receipt_id=receipt.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if receipt.can_be_edited() %}
                                <a href="{{ url_for('receipts.edit', receipt_id=receipt.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                                <a href="{{ url_for('receipts.pdf', receipt_id=receipt.id) }}" 
                                   class="btn btn-outline-info" title="PDF" target="_blank">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                {% if not receipt.is_confirmed %}
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="confirmReceipt('{{ receipt.id }}')" title="تأكيد">
                                    <i class="fas fa-check"></i>
                                </button>
                                {% endif %}
                                {% if receipt.can_be_deleted() %}
                                <form method="POST" action="{{ url_for('receipts.delete', receipt_id=receipt.id) }}" 
                                      class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الإيصال؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Bulk Actions -->
        <div class="row mt-3">
            <div class="col-md-6">
                <div class="d-flex align-items-center">
                    <select class="form-select me-2" id="bulk-action" style="width: auto;">
                        <option value="">اختر إجراء</option>
                        <option value="confirm">تأكيد</option>
                        <option value="generate_pdf">إنتاج PDF</option>
                        <option value="send_email">إرسال بريد</option>
                        <option value="export">تصدير</option>
                        <option value="delete">حذف</option>
                    </select>
                    <button type="button" class="btn btn-outline-secondary" onclick="executeBulkAction()">
                        تنفيذ
                    </button>
                </div>
            </div>
            <div class="col-md-6 text-end">
                <small class="text-muted">
                    <span id="selected-count">0</span> إيصال محدد
                </small>
            </div>
        </div>

        <!-- Pagination -->
        {% if receipts.pages > 1 %}
        <nav aria-label="تصفح الصفحات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if receipts.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('receipts.index', page=receipts.prev_num, **request.args) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in receipts.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != receipts.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('receipts.index', page=page_num, **request.args) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if receipts.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('receipts.index', page=receipts.next_num, **request.args) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
            <h5>لا توجد إيصالات</h5>
            <p class="text-muted">لم يتم العثور على أي إيصالات تطابق معايير البحث.</p>
            <a href="{{ url_for('receipts.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء إيصال جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">إجمالي الإيصالات</h5>
                <h3 class="text-primary">{{ stats.total_receipts if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">إيصالات قبض</h5>
                <h3 class="text-success">{{ stats.receipt_count if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">إيصالات دفع</h5>
                <h3 class="text-warning">{{ stats.payment_count if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">إجمالي المبلغ</h5>
                <h3 class="text-info">{{ stats.total_amount|currency if stats else '0.00 ج.م' }}</h3>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Select all checkbox functionality
    $('#select-all').change(function() {
        $('.receipt-checkbox').prop('checked', this.checked);
        updateSelectedCount();
    });
    
    $('.receipt-checkbox').change(function() {
        updateSelectedCount();
        
        // Update select all checkbox
        const totalCheckboxes = $('.receipt-checkbox').length;
        const checkedCheckboxes = $('.receipt-checkbox:checked').length;
        
        $('#select-all').prop('indeterminate', checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes);
        $('#select-all').prop('checked', checkedCheckboxes === totalCheckboxes);
    });
    
    // Auto-submit search form on type change
    $('#receipt_type, #payment_method').change(function() {
        $(this).closest('form').submit();
    });
});

function updateSelectedCount() {
    const count = $('.receipt-checkbox:checked').length;
    $('#selected-count').text(count);
}

function confirmReceipt(receiptId) {
    if (confirm('هل تريد تأكيد هذا الإيصال؟')) {
        fetch(`/receipts/${receiptId}/confirm`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('تم تأكيد الإيصال بنجاح');
                location.reload();
            } else {
                alert('حدث خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function executeBulkAction() {
    const action = $('#bulk-action').val();
    const selectedReceipts = $('.receipt-checkbox:checked').map(function() {
        return this.value;
    }).get();
    
    if (!action) {
        alert('يرجى اختيار إجراء');
        return;
    }
    
    if (selectedReceipts.length === 0) {
        alert('يرجى تحديد إيصال واحد على الأقل');
        return;
    }
    
    if (confirm(`هل تريد تنفيذ هذا الإجراء على ${selectedReceipts.length} إيصال؟`)) {
        // Create form and submit
        const form = $('<form>', {
            method: 'POST',
            action: '{{ url_for("receipts.bulk_action") }}'
        });
        
        form.append($('<input>', {
            type: 'hidden',
            name: 'csrf_token',
            value: '{{ csrf_token() }}'
        }));
        
        form.append($('<input>', {
            type: 'hidden',
            name: 'action',
            value: action
        }));
        
        form.append($('<input>', {
            type: 'hidden',
            name: 'receipt_ids',
            value: selectedReceipts.join(',')
        }));
        
        $('body').append(form);
        form.submit();
    }
}
</script>
{% endblock %}
