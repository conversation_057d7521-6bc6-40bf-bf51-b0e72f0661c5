{% extends "base.html" %}

{% block title %}{{ title }} - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-{{ 'plus' if not account else 'edit' }} me-3"></i>
                {{ title }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('accounts.index') }}">دليل الحسابات</a></li>
                    {% if account %}
                    <li class="breadcrumb-item"><a href="{{ url_for('accounts.detail', account_id=account.id) }}">{{ account.name }}</a></li>
                    {% endif %}
                    <li class="breadcrumb-item active">{{ title }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('accounts.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <!-- Main Form -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الحساب
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="accountForm">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- Account Code -->
                        <div class="col-md-6 mb-3">
                            {{ form.code.label(class="form-label required") }}
                            {{ form.code(class="form-control" + (" is-invalid" if form.code.errors else ""), 
                                        placeholder="مثال: 1100") }}
                            {% if form.code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.code.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">رمز الحساب يجب أن يكون فريداً</div>
                        </div>
                        
                        <!-- Account Type -->
                        <div class="col-md-6 mb-3">
                            {{ form.type.label(class="form-label required") }}
                            {{ form.type(class="form-select" + (" is-invalid" if form.type.errors else "")) }}
                            {% if form.type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- Account Name (Arabic) -->
                        <div class="col-md-6 mb-3">
                            {{ form.name.label(class="form-label required") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else ""), 
                                        placeholder="اسم الحساب بالعربية") }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Account Name (English) -->
                        <div class="col-md-6 mb-3">
                            {{ form.name_en.label(class="form-label") }}
                            {{ form.name_en(class="form-control" + (" is-invalid" if form.name_en.errors else ""), 
                                           placeholder="Account name in English") }}
                            {% if form.name_en.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name_en.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Parent Account -->
                    <div class="mb-3">
                        {{ form.parent_id.label(class="form-label") }}
                        <div class="input-group">
                            {{ form.parent_id(class="form-select" + (" is-invalid" if form.parent_id.errors else "")) }}
                            <button type="button" class="btn btn-outline-secondary" onclick="clearParent()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        {% if form.parent_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.parent_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">اختر الحساب الأب إذا كان هذا حساب فرعي</div>
                    </div>
                    
                    <!-- Status -->
                    <div class="mb-4">
                        <div class="form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>
                                حفظ
                            </button>
                            <a href="{{ url_for('accounts.index') }}" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        
                        {% if account and account.can_be_deleted() %}
                        <div>
                            <form method="POST" action="{{ url_for('accounts.delete', account_id=account.id) }}" 
                                  class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الحساب؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                <button type="submit" class="btn btn-outline-danger">
                                    <i class="fas fa-trash me-2"></i>
                                    حذف الحساب
                                </button>
                            </form>
                        </div>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Help Card -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    مساعدة
                </h6>
            </div>
            <div class="card-body">
                <h6>أنواع الحسابات:</h6>
                <ul class="list-unstyled">
                    <li><span class="badge bg-success me-2">الأصول</span> ما تملكه الشركة</li>
                    <li><span class="badge bg-danger me-2">الخصوم</span> ما على الشركة من التزامات</li>
                    <li><span class="badge bg-primary me-2">رأس المال</span> حقوق الملكية</li>
                    <li><span class="badge bg-info me-2">الإيرادات</span> دخل الشركة</li>
                    <li><span class="badge bg-warning me-2">المصروفات</span> نفقات الشركة</li>
                </ul>
                
                <hr>
                
                <h6>نصائح:</h6>
                <ul class="small">
                    <li>استخدم أرقام متسلسلة للحسابات</li>
                    <li>ابدأ الأصول بـ 1، الخصوم بـ 2، رأس المال بـ 3</li>
                    <li>الإيرادات تبدأ بـ 4، المصروفات بـ 5</li>
                    <li>اجعل الحسابات الفرعية أكثر تفصيلاً</li>
                </ul>
            </div>
        </div>
        
        <!-- Account Code Generator -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-magic me-2"></i>
                    مولد رمز الحساب
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">نوع الحساب:</label>
                    <select class="form-select" id="codeGenerator">
                        <option value="">اختر النوع</option>
                        <option value="1">أصول (1xxx)</option>
                        <option value="2">خصوم (2xxx)</option>
                        <option value="3">رأس المال (3xxx)</option>
                        <option value="4">إيرادات (4xxx)</option>
                        <option value="5">مصروفات (5xxx)</option>
                    </select>
                </div>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="generateCode()">
                    <i class="fas fa-magic me-2"></i>
                    اقتراح رمز
                </button>
            </div>
        </div>
        
        {% if account %}
        <!-- Account Info -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info me-2"></i>
                    معلومات إضافية
                </h6>
            </div>
            <div class="card-body">
                <p><strong>تاريخ الإنشاء:</strong><br>{{ account.created_at|datetime }}</p>
                {% if account.updated_at %}
                <p><strong>آخر تحديث:</strong><br>{{ account.updated_at|datetime }}</p>
                {% endif %}
                <p><strong>عدد الحسابات الفرعية:</strong><br>{{ account.children.count() }}</p>
                {% if account.is_leaf_account() %}
                <p><strong>الرصيد الحالي:</strong><br>{{ account.get_balance()|currency }}</p>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function clearParent() {
    document.getElementById('parent_id').value = '';
}

function generateCode() {
    const type = document.getElementById('codeGenerator').value;
    if (!type) {
        alert('يرجى اختيار نوع الحساب أولاً');
        return;
    }
    
    // Simple code generation logic
    const baseCode = type + '000';
    document.getElementById('code').value = baseCode;
    
    // Update account type
    const typeMap = {
        '1': 'Asset',
        '2': 'Liability', 
        '3': 'Equity',
        '4': 'Income',
        '5': 'Expense'
    };
    
    document.getElementById('type').value = typeMap[type];
}

// Form validation
document.getElementById('accountForm').addEventListener('submit', function(e) {
    const code = document.getElementById('code').value;
    const name = document.getElementById('name').value;
    const type = document.getElementById('type').value;
    
    if (!code || !name || !type) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    // Validate code format
    if (!/^\d+$/.test(code)) {
        e.preventDefault();
        alert('رمز الحساب يجب أن يحتوي على أرقام فقط');
        return false;
    }
});

// Auto-suggest account type based on code
document.getElementById('code').addEventListener('input', function() {
    const code = this.value;
    if (code.length > 0) {
        const firstDigit = code.charAt(0);
        const typeMap = {
            '1': 'Asset',
            '2': 'Liability',
            '3': 'Equity', 
            '4': 'Income',
            '5': 'Expense'
        };
        
        if (typeMap[firstDigit]) {
            document.getElementById('type').value = typeMap[firstDigit];
        }
    }
});
</script>
{% endblock %}
