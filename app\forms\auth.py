"""
Authentication forms
"""

from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, SelectField, BooleanField, SubmitField
from wtforms.validators import DataRequired, Email, Length, EqualTo, ValidationError
from app.models.user import User

class LoginForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('اسم المستخدم', validators=[DataRequired(), Length(min=3, max=50)])
    password = PasswordField('كلمة المرور', validators=[DataRequired()])
    remember_me = BooleanField('تذكرني')
    submit = SubmitField('تسجيل الدخول')

class RegistrationForm(FlaskForm):
    username = String<PERSON>ield('اسم المستخدم', validators=[
        DataRequired(), 
        Length(min=3, max=50)
    ])
    email = StringField('البريد الإلكتروني', validators=[
        DataRequired(), 
        Email()
    ])
    password = Pass<PERSON>Field('كلمة المرور', validators=[
        DataRequired(), 
        Length(min=6)
    ])
    password2 = PasswordField('تأكيد كلمة المرور', validators=[
        DataRequired(), 
        EqualTo('password', message='كلمات المرور غير متطابقة')
    ])
    role = SelectField('الدور', choices=[
        ('employee', 'موظف'),
        ('accountant', 'محاسب'),
        ('admin', 'مدير')
    ], default='employee')
    submit = SubmitField('إنشاء حساب')
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user:
            raise ValidationError('اسم المستخدم مستخدم بالفعل. يرجى اختيار اسم آخر.')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user:
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل. يرجى اختيار بريد آخر.')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('كلمة المرور الحالية', validators=[DataRequired()])
    new_password = PasswordField('كلمة المرور الجديدة', validators=[
        DataRequired(), 
        Length(min=6)
    ])
    new_password2 = PasswordField('تأكيد كلمة المرور الجديدة', validators=[
        DataRequired(), 
        EqualTo('new_password', message='كلمات المرور غير متطابقة')
    ])
    submit = SubmitField('تغيير كلمة المرور')

class UserEditForm(FlaskForm):
    username = StringField('اسم المستخدم', validators=[
        DataRequired(), 
        Length(min=3, max=50)
    ])
    email = StringField('البريد الإلكتروني', validators=[
        DataRequired(), 
        Email()
    ])
    role = SelectField('الدور', choices=[
        ('employee', 'موظف'),
        ('accountant', 'محاسب'),
        ('admin', 'مدير')
    ])
    is_active = BooleanField('نشط')
    submit = SubmitField('حفظ التغييرات')
    
    def __init__(self, original_user, *args, **kwargs):
        super(UserEditForm, self).__init__(*args, **kwargs)
        self.original_user = original_user
    
    def validate_username(self, username):
        if username.data != self.original_user.username:
            user = User.query.filter_by(username=username.data).first()
            if user:
                raise ValidationError('اسم المستخدم مستخدم بالفعل. يرجى اختيار اسم آخر.')
    
    def validate_email(self, email):
        if email.data != self.original_user.email:
            user = User.query.filter_by(email=email.data).first()
            if user:
                raise ValidationError('البريد الإلكتروني مستخدم بالفعل. يرجى اختيار بريد آخر.')
