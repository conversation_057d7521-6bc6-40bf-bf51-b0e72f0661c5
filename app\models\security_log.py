"""
Security logging model
"""

from app import db
from datetime import datetime
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Index
from sqlalchemy.orm import relationship


class SecurityLog(db.Model):
    """Security event logging model"""
    
    __tablename__ = 'security_logs'
    
    id = Column(Integer, primary_key=True)
    
    # Event information
    event_type = Column(String(50), nullable=False, comment='نوع الحدث الأمني')
    message = Column(Text, nullable=False, comment='رسالة الحدث')
    details = Column(Text, nullable=True, comment='تفاصيل إضافية (JSON)')
    
    # Context information
    ip_address = Column(String(45), nullable=True, comment='عنوان IP')
    user_agent = Column(Text, nullable=True, comment='معلومات المتصفح')
    request_path = Column(String(500), nullable=True, comment='مسار الطلب')
    request_method = Column(String(10), nullable=True, comment='طريقة الطلب')
    
    # User information
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='المستخدم')
    username = Column(String(80), nullable=True, comment='اسم المستخدم')
    
    # Severity and status
    severity = Column(String(20), default='info', comment='مستوى الخطورة')
    status = Column(String(20), default='logged', comment='حالة المعالجة')
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    resolved_at = Column(DateTime, nullable=True, comment='تاريخ الحل')
    
    # Relationships
    user = relationship('User', backref='security_logs')
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_security_logs_event_type', 'event_type'),
        Index('idx_security_logs_created_at', 'created_at'),
        Index('idx_security_logs_user_id', 'user_id'),
        Index('idx_security_logs_ip_address', 'ip_address'),
        Index('idx_security_logs_severity', 'severity'),
    )
    
    def __repr__(self):
        return f'<SecurityLog {self.event_type}: {self.message[:50]}>'
    
    @property
    def is_critical(self):
        """Check if this is a critical security event"""
        critical_events = [
            'account_lockout',
            'privilege_escalation_attempt',
            'data_breach_attempt',
            'unauthorized_access',
            'system_compromise'
        ]
        return self.event_type in critical_events or self.severity == 'critical'
    
    @property
    def is_resolved(self):
        """Check if this security event has been resolved"""
        return self.resolved_at is not None
    
    def mark_resolved(self):
        """Mark this security event as resolved"""
        self.resolved_at = datetime.utcnow()
        self.status = 'resolved'
        db.session.commit()
    
    def get_details_dict(self):
        """Get details as dictionary"""
        if self.details:
            try:
                import json
                return json.loads(self.details)
            except (json.JSONDecodeError, TypeError):
                return {}
        return {}
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'event_type': self.event_type,
            'message': self.message,
            'details': self.get_details_dict(),
            'ip_address': self.ip_address,
            'user_agent': self.user_agent,
            'request_path': self.request_path,
            'request_method': self.request_method,
            'user_id': self.user_id,
            'username': self.username,
            'severity': self.severity,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'resolved_at': self.resolved_at.isoformat() if self.resolved_at else None,
            'is_critical': self.is_critical,
            'is_resolved': self.is_resolved
        }
    
    @classmethod
    def log_event(cls, event_type, message, details=None, ip_address=None, 
                  user_id=None, username=None, severity='info', 
                  user_agent=None, request_path=None, request_method=None):
        """Create a new security log entry"""
        
        import json
        
        log_entry = cls(
            event_type=event_type,
            message=message,
            details=json.dumps(details) if details else None,
            ip_address=ip_address,
            user_agent=user_agent,
            request_path=request_path,
            request_method=request_method,
            user_id=user_id,
            username=username,
            severity=severity
        )
        
        db.session.add(log_entry)
        
        try:
            db.session.commit()
            return log_entry
        except Exception as e:
            db.session.rollback()
            # Log to application logger as fallback
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to create security log: {e}")
            return None
    
    @classmethod
    def get_recent_events(cls, hours=24, event_types=None, severity=None, limit=100):
        """Get recent security events"""
        
        from datetime import datetime, timedelta
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        query = cls.query.filter(cls.created_at >= cutoff_time)
        
        if event_types:
            if isinstance(event_types, str):
                event_types = [event_types]
            query = query.filter(cls.event_type.in_(event_types))
        
        if severity:
            query = query.filter(cls.severity == severity)
        
        return query.order_by(cls.created_at.desc()).limit(limit).all()
    
    @classmethod
    def get_event_counts(cls, hours=24):
        """Get event counts by type"""
        
        from datetime import datetime, timedelta
        from sqlalchemy import func
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        results = db.session.query(
            cls.event_type,
            func.count(cls.id).label('count')
        ).filter(
            cls.created_at >= cutoff_time
        ).group_by(cls.event_type).all()
        
        return {result.event_type: result.count for result in results}
    
    @classmethod
    def get_critical_events(cls, hours=24):
        """Get critical security events"""
        
        critical_events = [
            'account_lockout',
            'privilege_escalation_attempt',
            'data_breach_attempt',
            'unauthorized_access',
            'system_compromise',
            'suspicious_activity'
        ]
        
        return cls.get_recent_events(
            hours=hours,
            event_types=critical_events,
            limit=50
        )
    
    @classmethod
    def get_user_activity(cls, user_id, hours=24):
        """Get security events for a specific user"""
        
        from datetime import datetime, timedelta
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        return cls.query.filter(
            cls.user_id == user_id,
            cls.created_at >= cutoff_time
        ).order_by(cls.created_at.desc()).all()
    
    @classmethod
    def get_ip_activity(cls, ip_address, hours=24):
        """Get security events for a specific IP address"""
        
        from datetime import datetime, timedelta
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        return cls.query.filter(
            cls.ip_address == ip_address,
            cls.created_at >= cutoff_time
        ).order_by(cls.created_at.desc()).all()
    
    @classmethod
    def cleanup_old_logs(cls, days=90):
        """Clean up old security logs"""
        
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        # Keep critical events longer
        critical_cutoff = datetime.utcnow() - timedelta(days=days * 2)
        
        # Delete non-critical old logs
        deleted_count = cls.query.filter(
            cls.created_at < cutoff_date,
            ~cls.event_type.in_([
                'account_lockout',
                'privilege_escalation_attempt',
                'data_breach_attempt',
                'unauthorized_access',
                'system_compromise'
            ])
        ).delete()
        
        # Delete very old critical logs
        deleted_critical = cls.query.filter(
            cls.created_at < critical_cutoff
        ).delete()
        
        db.session.commit()
        
        return deleted_count + deleted_critical


class SecurityAlert(db.Model):
    """Security alert model for tracking security incidents"""
    
    __tablename__ = 'security_alerts'
    
    id = Column(Integer, primary_key=True)
    
    # Alert information
    alert_type = Column(String(50), nullable=False, comment='نوع التنبيه')
    title = Column(String(200), nullable=False, comment='عنوان التنبيه')
    description = Column(Text, nullable=False, comment='وصف التنبيه')
    severity = Column(String(20), default='medium', comment='مستوى الخطورة')
    
    # Status tracking
    status = Column(String(20), default='open', comment='حالة التنبيه')
    assigned_to_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='مُكلف بالمعالجة')
    
    # Related data
    related_log_ids = Column(Text, nullable=True, comment='معرفات السجلات المرتبطة')
    affected_users = Column(Text, nullable=True, comment='المستخدمون المتأثرون')
    affected_systems = Column(Text, nullable=True, comment='الأنظمة المتأثرة')
    
    # Resolution
    resolution_notes = Column(Text, nullable=True, comment='ملاحظات الحل')
    resolution_actions = Column(Text, nullable=True, comment='إجراءات الحل')
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, comment='تاريخ الإنشاء')
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment='تاريخ التحديث')
    resolved_at = Column(DateTime, nullable=True, comment='تاريخ الحل')
    
    # Relationships
    assigned_to = relationship('User', backref='assigned_security_alerts')
    
    def __repr__(self):
        return f'<SecurityAlert {self.alert_type}: {self.title}>'
    
    @property
    def is_open(self):
        """Check if alert is open"""
        return self.status == 'open'
    
    @property
    def is_resolved(self):
        """Check if alert is resolved"""
        return self.status == 'resolved'
    
    @property
    def is_critical(self):
        """Check if alert is critical"""
        return self.severity == 'critical'
    
    def resolve(self, resolution_notes=None, resolution_actions=None):
        """Resolve the alert"""
        self.status = 'resolved'
        self.resolved_at = datetime.utcnow()
        
        if resolution_notes:
            self.resolution_notes = resolution_notes
        
        if resolution_actions:
            self.resolution_actions = resolution_actions
        
        db.session.commit()
    
    def assign_to(self, user_id):
        """Assign alert to a user"""
        self.assigned_to_id = user_id
        self.status = 'assigned'
        db.session.commit()
    
    def to_dict(self):
        """Convert to dictionary"""
        return {
            'id': self.id,
            'alert_type': self.alert_type,
            'title': self.title,
            'description': self.description,
            'severity': self.severity,
            'status': self.status,
            'assigned_to_id': self.assigned_to_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'resolved_at': self.resolved_at.isoformat() if self.resolved_at else None,
            'is_open': self.is_open,
            'is_resolved': self.is_resolved,
            'is_critical': self.is_critical
        }
