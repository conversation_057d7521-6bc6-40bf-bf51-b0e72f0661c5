#!/usr/bin/env python3
"""
إصلاح نهائي وشامل لقاعدة البيانات
Final and comprehensive database fix
"""

import sqlite3
import os
from werkzeug.security import generate_password_hash

def create_complete_database():
    """إنشاء قاعدة بيانات كاملة ومتوافقة مع جميع النماذج"""
    
    db_path = 'instance/systemtax.db'
    
    # إنشاء مجلد instance
    os.makedirs('instance', exist_ok=True)
    
    # حذف قاعدة البيانات القديمة
    if os.path.exists(db_path):
        try:
            os.remove(db_path)
            print('🗑️ تم حذف قاعدة البيانات القديمة')
        except:
            print('⚠️ لا يمكن حذف قاعدة البيانات القديمة')
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print('🏗️ إنشاء قاعدة بيانات SystemTax الكاملة...')
    print('=' * 50)
    
    # 1. جدول المستخدمين
    print('👥 إنشاء جدول users...')
    cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(80) UNIQUE NOT NULL,
            email VARCHAR(120) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            role VARCHAR(20) DEFAULT 'user',
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # 2. جدول الحسابات
    print('🏛️ إنشاء جدول accounts...')
    cursor.execute('''
        CREATE TABLE accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code VARCHAR(20) UNIQUE NOT NULL,
            name VARCHAR(200) NOT NULL,
            name_en VARCHAR(200),
            type VARCHAR(50) NOT NULL,
            parent_id INTEGER,
            balance DECIMAL(15,2) DEFAULT 0,
            description TEXT,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES accounts (id)
        )
    ''')
    
    # 3. جدول العملاء
    print('👤 إنشاء جدول customers...')
    cursor.execute('''
        CREATE TABLE customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(150) NOT NULL,
            name_en VARCHAR(150),
            tax_id VARCHAR(20),
            address TEXT,
            address_en TEXT,
            email VARCHAR(100),
            phone VARCHAR(20),
            mobile VARCHAR(20),
            credit_limit DECIMAL(14,2) DEFAULT 0,
            payment_terms INTEGER DEFAULT 30,
            currency VARCHAR(3) DEFAULT 'EGP',
            postal_code VARCHAR(10),
            country VARCHAR(50) DEFAULT 'مصر',
            notes TEXT,
            account_id INTEGER,
            created_by_id INTEGER,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (account_id) REFERENCES accounts (id),
            FOREIGN KEY (created_by_id) REFERENCES users (id)
        )
    ''')
    
    # 4. جدول الموردين
    print('🏪 إنشاء جدول vendors...')
    cursor.execute('''
        CREATE TABLE vendors (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(150) NOT NULL,
            name_en VARCHAR(150),
            tax_id VARCHAR(20),
            address TEXT,
            address_en TEXT,
            email VARCHAR(100),
            phone VARCHAR(20),
            mobile VARCHAR(20),
            credit_limit DECIMAL(14,2) DEFAULT 0,
            payment_terms INTEGER DEFAULT 30,
            currency VARCHAR(3) DEFAULT 'EGP',
            postal_code VARCHAR(10),
            country VARCHAR(50) DEFAULT 'مصر',
            notes TEXT,
            account_id INTEGER,
            created_by_id INTEGER,
            is_active BOOLEAN DEFAULT 1,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (account_id) REFERENCES accounts (id),
            FOREIGN KEY (created_by_id) REFERENCES users (id)
        )
    ''')
    
    # 5. جدول القيود المحاسبية
    print('📊 إنشاء جدول journal_entries...')
    cursor.execute('''
        CREATE TABLE journal_entries (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            entry_number VARCHAR(50) UNIQUE NOT NULL,
            date DATE NOT NULL,
            description TEXT,
            reference VARCHAR(100),
            total_debit DECIMAL(15,2) DEFAULT 0,
            total_credit DECIMAL(15,2) DEFAULT 0,
            is_posted BOOLEAN DEFAULT 0,
            created_by INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (created_by) REFERENCES users (id)
        )
    ''')
    
    # 6. جدول تفاصيل القيود
    print('📝 إنشاء جدول journal_entry_lines...')
    cursor.execute('''
        CREATE TABLE journal_entry_lines (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            journal_entry_id INTEGER NOT NULL,
            account_id INTEGER NOT NULL,
            description TEXT,
            debit DECIMAL(15,2) DEFAULT 0,
            credit DECIMAL(15,2) DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id),
            FOREIGN KEY (account_id) REFERENCES accounts (id)
        )
    ''')
    
    return cursor, conn

def create_receipts_table(cursor):
    """إنشاء جدول الإيصالات الكامل"""
    print('🧾 إنشاء جدول receipts...')
    cursor.execute('''
        CREATE TABLE receipts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            customer_id INTEGER,
            vendor_id INTEGER,
            invoice_id INTEGER,
            account_id INTEGER,
            journal_entry_id INTEGER,
            receipt_number VARCHAR(50) UNIQUE NOT NULL,
            receipt_date DATE NOT NULL,
            receipt_type VARCHAR(20) DEFAULT 'sales',
            amount DECIMAL(15,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'EGP',
            payment_method VARCHAR(20) DEFAULT 'cash',
            reference_number VARCHAR(100),
            description TEXT,
            notes TEXT,
            created_by_id INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            
            -- حقول ETA
            eta_uuid VARCHAR(100),
            eta_internal_id VARCHAR(100),
            eta_submission_uuid VARCHAR(100),
            eta_long_id VARCHAR(100),
            eta_hash_key VARCHAR(255),
            eta_status VARCHAR(50),
            eta_submitted_at DATETIME,
            eta_qr_code TEXT,
            eta_qr_image_path VARCHAR(255),
            previous_uuid VARCHAR(100),
            reference_old_uuid VARCHAR(100),
            document_type_name VARCHAR(50) DEFAULT 's',
            document_type_version VARCHAR(10) DEFAULT '1.2',
            datetime_issued DATETIME,
            exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
            branch_code VARCHAR(20),
            device_serial_number VARCHAR(50),
            activity_code VARCHAR(20),
            buyer_type VARCHAR(20),
            buyer_id VARCHAR(50),
            buyer_name VARCHAR(150),
            buyer_mobile VARCHAR(20),
            payment_number VARCHAR(50),
            total_sales DECIMAL(15,2) DEFAULT 0,
            total_commercial_discount DECIMAL(15,2) DEFAULT 0,
            total_items_discount DECIMAL(15,2) DEFAULT 0,
            net_amount DECIMAL(15,2) DEFAULT 0,
            fees_amount DECIMAL(15,2) DEFAULT 0,
            total_amount DECIMAL(15,2) DEFAULT 0,
            adjustment DECIMAL(15,2) DEFAULT 0,
            sales_order_name_code VARCHAR(50),
            order_delivery_mode VARCHAR(20),
            gross_weight DECIMAL(10,3) DEFAULT 0,
            net_weight DECIMAL(10,3) DEFAULT 0,
            
            FOREIGN KEY (customer_id) REFERENCES customers (id),
            FOREIGN KEY (vendor_id) REFERENCES vendors (id),
            FOREIGN KEY (account_id) REFERENCES accounts (id),
            FOREIGN KEY (journal_entry_id) REFERENCES journal_entries (id),
            FOREIGN KEY (created_by_id) REFERENCES users (id)
        )
    ''')

if __name__ == "__main__":
    try:
        cursor, conn = create_complete_database()
        create_receipts_table(cursor)
        
        # 7. جدول الفواتير
        print('📄 إنشاء جدول invoices...')
        cursor.execute('''
            CREATE TABLE invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                customer_id INTEGER,
                invoice_number VARCHAR(50) UNIQUE NOT NULL,
                issue_date DATE NOT NULL,
                due_date DATE,
                subtotal DECIMAL(15,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) DEFAULT 0,
                status VARCHAR(20) DEFAULT 'draft',
                tax_status VARCHAR(20) DEFAULT 'pending',
                notes TEXT,
                created_by_id INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                eta_uuid VARCHAR(100),
                eta_internal_id VARCHAR(100),
                eta_submission_uuid VARCHAR(100),
                eta_long_id VARCHAR(100),
                eta_hash_key VARCHAR(255),
                eta_status VARCHAR(50),
                eta_submitted_at DATETIME,
                document_type_name VARCHAR(50) DEFAULT 'i',
                document_type_version VARCHAR(10) DEFAULT '1.2',
                tax_totals TEXT,
                net_amount DECIMAL(15,2) DEFAULT 0,
                FOREIGN KEY (customer_id) REFERENCES customers (id),
                FOREIGN KEY (created_by_id) REFERENCES users (id)
            )
        ''')

        # 8. جدول بنود الفواتير
        print('📋 إنشاء جدول invoice_items...')
        cursor.execute('''
            CREATE TABLE invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                description VARCHAR(255) NOT NULL,
                quantity DECIMAL(10,3) DEFAULT 1,
                unit_price DECIMAL(15,2) NOT NULL,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                discount_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) NOT NULL,
                item_code VARCHAR(50),
                unit_type VARCHAR(20) DEFAULT 'piece',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id)
            )
        ''')

        # 9. جدول بنود الإيصالات
        print('📝 إنشاء جدول receipt_items...')
        cursor.execute('''
            CREATE TABLE receipt_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                receipt_id INTEGER NOT NULL,
                description VARCHAR(255) NOT NULL,
                quantity DECIMAL(10,3) DEFAULT 1,
                unit_price DECIMAL(15,2) NOT NULL,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(15,2) DEFAULT 0,
                total_amount DECIMAL(15,2) NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (receipt_id) REFERENCES receipts (id)
            )
        ''')

        # 10. جدول إعدادات النظام
        print('⚙️ إنشاء جدول system_settings...')
        cursor.execute('''
            CREATE TABLE system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key VARCHAR(100) UNIQUE NOT NULL,
                value TEXT,
                data_type VARCHAR(20) DEFAULT 'string',
                category VARCHAR(50) DEFAULT 'general',
                description TEXT,
                is_encrypted BOOLEAN DEFAULT 0,
                is_system BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100)
            )
        ''')

        # 11. جداول إضافية
        print('📊 إنشاء الجداول الإضافية...')

        # جدول المعاملات الضريبية
        cursor.execute('''
            CREATE TABLE tax_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                transaction_type VARCHAR(20) NOT NULL,
                reference_id INTEGER,
                reference_type VARCHAR(20),
                tax_amount DECIMAL(15,2) NOT NULL,
                tax_rate DECIMAL(5,2) NOT NULL,
                base_amount DECIMAL(15,2) NOT NULL,
                description TEXT,
                transaction_date DATE NOT NULL,
                eta_status VARCHAR(50),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول سجلات المراجعة
        cursor.execute('''
            CREATE TABLE audit_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                action VARCHAR(50) NOT NULL,
                table_name VARCHAR(50),
                record_id INTEGER,
                old_values TEXT,
                new_values TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # جدول الإشعارات
        cursor.execute('''
            CREATE TABLE notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                title VARCHAR(200) NOT NULL,
                message TEXT NOT NULL,
                type VARCHAR(20) DEFAULT 'info',
                is_read BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        print('\n📋 إنشاء الفهارس...')
        # إنشاء الفهارس مباشرة
        indexes = [
            "CREATE INDEX idx_accounts_code ON accounts(code)",
            "CREATE INDEX idx_accounts_type ON accounts(type)",
            "CREATE INDEX idx_customers_name ON customers(name)",
            "CREATE INDEX idx_customers_tax_id ON customers(tax_id)",
            "CREATE INDEX idx_invoices_customer_id ON invoices(customer_id)",
            "CREATE INDEX idx_receipts_customer_id ON receipts(customer_id)",
            "CREATE INDEX idx_journal_entries_date ON journal_entries(date)",
            "CREATE INDEX idx_journal_entry_lines_journal_id ON journal_entry_lines(journal_entry_id)",
            "CREATE INDEX idx_journal_entry_lines_account_id ON journal_entry_lines(account_id)"
        ]

        for index in indexes:
            cursor.execute(index)

        print('\n📊 إدراج البيانات الافتراضية...')
        # إدراج البيانات مباشرة

        # المستخدم الافتراضي
        password_hash = generate_password_hash('admin123')
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, role, is_active)
            VALUES (?, ?, ?, ?, ?)
        ''', ('admin', '<EMAIL>', password_hash, 'admin', 1))

        # الحسابات الافتراضية
        accounts = [
            ('1000', 'الأصول', 'Assets', 'Asset', None),
            ('4000', 'الإيرادات', 'Revenue', 'Income', None),
            ('5000', 'المصروفات', 'Expenses', 'Expense', None)
        ]

        for code, name, name_en, acc_type, parent_id in accounts:
            cursor.execute('''
                INSERT INTO accounts (code, name, name_en, type, parent_id, is_active)
                VALUES (?, ?, ?, ?, ?, 1)
            ''', (code, name, name_en, acc_type, parent_id))

        # الإعدادات الافتراضية
        settings = [
            ('COMPANY_NAME', 'شركتك', 'string', 'company', 'اسم الشركة'),
            ('APP_NAME', 'SystemTax', 'string', 'system', 'اسم التطبيق')
        ]

        for key, value, data_type, category, description in settings:
            cursor.execute('''
                INSERT INTO system_settings (key, value, data_type, category, description, is_system)
                VALUES (?, ?, ?, ?, ?, 1)
            ''', (key, value, data_type, category, description))

        # عميل تجريبي
        cursor.execute('''
            INSERT INTO customers (name, name_en, tax_id, address, email, phone, is_active, created_by_id)
            VALUES (?, ?, ?, ?, ?, ?, 1, 1)
        ''', ('شركة العميل التجريبي', 'Test Customer Company', '*********', 'القاهرة، مصر', '<EMAIL>', '0*********0'))

        # قيد تجريبي
        cursor.execute('''
            INSERT INTO journal_entries (entry_number, date, description, total_debit, total_credit, is_posted, created_by)
            VALUES ('JE-2025-0001', '2025-07-15', 'قيد تجريبي', 1000.00, 1000.00, 1, 1)
        ''')

        cursor.execute('''
            INSERT INTO journal_entry_lines (journal_entry_id, account_id, description, debit, credit)
            VALUES
            (1, 1, 'مدين - حساب الأصول', 1000.00, 0.00),
            (1, 2, 'دائن - حساب الإيرادات', 0.00, 1000.00)
        ''')

        # إيصال تجريبي
        cursor.execute('''
            INSERT INTO receipts (receipt_number, customer_id, receipt_date, amount, total_amount, receipt_type, created_by_id)
            VALUES ('REC-2025-0001', 1, '2025-07-15', 1140.00, 1140.00, 'sales', 1)
        ''')

        # فاتورة تجريبية
        cursor.execute('''
            INSERT INTO invoices (invoice_number, customer_id, issue_date, subtotal, tax_amount, total_amount, status, created_by_id)
            VALUES ('INV-2025-0001', 1, '2025-07-15', 1000.00, 140.00, 1140.00, 'completed', 1)
        ''')

        conn.commit()
        conn.close()
        print('\n🎉 تم إنشاء قاعدة البيانات الكاملة بنجاح!')

    except Exception as e:
        print(f'❌ خطأ: {e}')
        import traceback
        traceback.print_exc()

# تم دمج الدوال في الكود الرئيسي أعلاه
