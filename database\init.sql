-- SystemTax Database Initialization Script
-- نظام محاسبي ويب متكامل

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(128) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin','accountant','employee')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- جدول الحسابات
CREATE TABLE IF NOT EXISTS accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    name_en VARCHAR(100),
    type VA<PERSON><PERSON>R(20) NOT NULL CHECK (type IN ('Asset','Liability','Equity','Income','Expense')),
    parent_id UUID REFERENCES accounts(id),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW()
);

-- جدول العملاء
CREATE TABLE IF NOT EXISTS customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(150) NOT NULL,
    name_en VARCHAR(150),
    tax_id VARCHAR(20),
    address TEXT,
    address_en TEXT,
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- جدول الموردين
CREATE TABLE IF NOT EXISTS vendors (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(150) NOT NULL,
    name_en VARCHAR(150),
    tax_id VARCHAR(20),
    address TEXT,
    address_en TEXT,
    email VARCHAR(100),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- قيود اليومية
CREATE TABLE IF NOT EXISTS journal_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    entry_date DATE NOT NULL,
    reference_number VARCHAR(50),
    description TEXT,
    description_en TEXT,
    total_amount NUMERIC(14,2) NOT NULL DEFAULT 0,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- تفاصيل القيود
CREATE TABLE IF NOT EXISTS journal_lines (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    journal_id UUID REFERENCES journal_entries(id) ON DELETE CASCADE,
    account_id UUID REFERENCES accounts(id),
    description TEXT,
    amount NUMERIC(14,2) NOT NULL,
    dc CHAR(1) NOT NULL CHECK (dc IN ('D','C')),
    created_at TIMESTAMP DEFAULT NOW()
);

-- الفواتير
CREATE TABLE IF NOT EXISTS invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id),
    invoice_number VARCHAR(30) UNIQUE NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE,
    subtotal NUMERIC(14,2) NOT NULL DEFAULT 0,
    tax_amount NUMERIC(14,2) NOT NULL DEFAULT 0,
    discount_amount NUMERIC(14,2) NOT NULL DEFAULT 0,
    total_amount NUMERIC(14,2) NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'draft' CHECK (status IN ('draft','sent','paid','cancelled')),
    tax_status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (tax_status IN ('pending','sent','accepted','rejected')),
    notes TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- بنود الفاتورة
CREATE TABLE IF NOT EXISTS invoice_lines (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID REFERENCES invoices(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    description_en TEXT,
    unit_price NUMERIC(14,2) NOT NULL,
    quantity NUMERIC(10,3) NOT NULL DEFAULT 1,
    discount_percent NUMERIC(5,2) DEFAULT 0,
    tax_percent NUMERIC(5,2) DEFAULT 0,
    line_total NUMERIC(14,2) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- الإيصالات
CREATE TABLE IF NOT EXISTS receipts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id),
    receipt_number VARCHAR(30) UNIQUE NOT NULL,
    receipt_date DATE NOT NULL,
    amount_received NUMERIC(14,2) NOT NULL,
    payment_method VARCHAR(20) DEFAULT 'cash' CHECK (payment_method IN ('cash','bank','check','card')),
    reference_number VARCHAR(50),
    notes TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- ربط الإيصالات بالفواتير
CREATE TABLE IF NOT EXISTS receipt_invoice_allocations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    receipt_id UUID REFERENCES receipts(id) ON DELETE CASCADE,
    invoice_id UUID REFERENCES invoices(id),
    allocated_amount NUMERIC(14,2) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- جدول سجل التبادل مع مصلحة الضرائب
CREATE TABLE IF NOT EXISTS tax_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    invoice_id UUID REFERENCES invoices(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL DEFAULT 'submit',
    request_data JSONB,
    response_data JSONB,
    tax_invoice_number VARCHAR(50),
    status VARCHAR(20) DEFAULT 'pending',
    error_message TEXT,
    sent_at TIMESTAMP NOT NULL DEFAULT NOW(),
    response_at TIMESTAMP
);

-- إعدادات النظام
CREATE TABLE IF NOT EXISTS system_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_accounts_parent ON accounts(parent_id);
CREATE INDEX IF NOT EXISTS idx_accounts_type ON accounts(type);
CREATE INDEX IF NOT EXISTS idx_journal_entries_date ON journal_entries(entry_date);
CREATE INDEX IF NOT EXISTS idx_journal_lines_account ON journal_lines(account_id);
CREATE INDEX IF NOT EXISTS idx_journal_lines_journal ON journal_lines(journal_id);
CREATE INDEX IF NOT EXISTS idx_invoices_date ON invoices(issue_date);
CREATE INDEX IF NOT EXISTS idx_invoices_customer ON invoices(customer_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON invoices(status);
CREATE INDEX IF NOT EXISTS idx_receipts_date ON receipts(receipt_date);
CREATE INDEX IF NOT EXISTS idx_receipts_customer ON receipts(customer_id);
CREATE INDEX IF NOT EXISTS idx_tax_transactions_invoice ON tax_transactions(invoice_id);

-- إدراج البيانات الأساسية
INSERT INTO system_settings (key, value, description) VALUES
('company_name', 'شركتك', 'اسم الشركة'),
('company_name_en', 'Your Company', 'Company Name in English'),
('company_tax_id', '*********', 'الرقم الضريبي للشركة'),
('company_address', 'عنوان الشركة', 'عنوان الشركة'),
('company_phone', '0*********0', 'هاتف الشركة'),
('company_email', '<EMAIL>', 'بريد الشركة الإلكتروني'),
('default_tax_rate', '14', 'معدل الضريبة الافتراضي'),
('invoice_prefix', 'INV', 'بادئة رقم الفاتورة'),
('receipt_prefix', 'REC', 'بادئة رقم الإيصال'),
('currency_code', 'EGP', 'رمز العملة'),
('currency_symbol', 'ج.م', 'رمز العملة')
ON CONFLICT (key) DO NOTHING;

-- إنشاء حسابات افتراضية
INSERT INTO accounts (code, name, name_en, type) VALUES
('1000', 'الأصول', 'Assets', 'Asset'),
('1100', 'الأصول المتداولة', 'Current Assets', 'Asset'),
('1110', 'النقدية', 'Cash', 'Asset'),
('1120', 'البنوك', 'Banks', 'Asset'),
('1130', 'العملاء', 'Accounts Receivable', 'Asset'),
('2000', 'الخصوم', 'Liabilities', 'Liability'),
('2100', 'الخصوم المتداولة', 'Current Liabilities', 'Liability'),
('2110', 'الموردين', 'Accounts Payable', 'Liability'),
('2120', 'الضرائب المستحقة', 'Taxes Payable', 'Liability'),
('3000', 'رأس المال', 'Equity', 'Equity'),
('3100', 'رأس المال المدفوع', 'Paid Capital', 'Equity'),
('4000', 'الإيرادات', 'Revenue', 'Income'),
('4100', 'إيرادات المبيعات', 'Sales Revenue', 'Income'),
('5000', 'المصروفات', 'Expenses', 'Expense'),
('5100', 'مصروفات التشغيل', 'Operating Expenses', 'Expense')
ON CONFLICT (code) DO NOTHING;
