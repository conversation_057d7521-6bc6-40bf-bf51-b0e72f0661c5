{% extends "base.html" %}

{% block title %}لوحة التحكم - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="mb-0">
        <i class="fas fa-tachometer-alt me-3"></i>
        لوحة التحكم
    </h1>
    <p class="mb-0 mt-2">مرحباً {{ current_user.get_display_name() }}، إليك نظرة عامة على النظام</p>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0">{{ stats.total_customers }}</h3>
                    <p class="mb-0">إجمالي العملاء</p>
                </div>
                <i class="fas fa-users fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0">{{ stats.formatted_sales }}</h3>
                    <p class="mb-0">مبيعات هذا الشهر</p>
                    {% if stats.sales_growth > 0 %}
                        <small><i class="fas fa-arrow-up"></i> +{{ stats.sales_growth }}%</small>
                    {% elif stats.sales_growth < 0 %}
                        <small><i class="fas fa-arrow-down"></i> {{ stats.sales_growth }}%</small>
                    {% endif %}
                </div>
                <i class="fas fa-chart-line fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card info">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0">{{ stats.this_month_invoices }}</h3>
                    <p class="mb-0">فواتير هذا الشهر</p>
                    {% if stats.invoice_growth > 0 %}
                        <small><i class="fas fa-arrow-up"></i> +{{ stats.invoice_growth }}%</small>
                    {% elif stats.invoice_growth < 0 %}
                        <small><i class="fas fa-arrow-down"></i> {{ stats.invoice_growth }}%</small>
                    {% endif %}
                </div>
                <i class="fas fa-file-invoice fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3">
        <div class="stats-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h3 class="mb-0">{{ stats.formatted_outstanding }}</h3>
                    <p class="mb-0">مبالغ مستحقة</p>
                </div>
                <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if current_user.can_access('invoices') %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('invoices.quick') }}" class="btn btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center py-3">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                            <span>فاتورة سريعة</span>
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if current_user.can_access('receipts') %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('receipts.new') }}" class="btn btn-outline-success w-100 h-100 d-flex flex-column justify-content-center align-items-center py-3">
                            <i class="fas fa-receipt fa-2x mb-2"></i>
                            <span>إيصال جديد</span>
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if current_user.can_access('customers') %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('customers.new') }}" class="btn btn-outline-info w-100 h-100 d-flex flex-column justify-content-center align-items-center py-3">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span>عميل جديد</span>
                        </a>
                    </div>
                    {% endif %}
                    
                    {% if current_user.can_access('journal') %}
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="{{ url_for('journal.simple') }}" class="btn btn-outline-warning w-100 h-100 d-flex flex-column justify-content-center align-items-center py-3">
                            <i class="fas fa-book fa-2x mb-2"></i>
                            <span>قيد بسيط</span>
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row">
    <!-- Recent Invoices -->
    {% if current_user.can_access('invoices') and recent_invoices %}
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice me-2"></i>
                    آخر الفواتير
                </h5>
                <a href="{{ url_for('invoices.index') }}" class="btn btn-sm btn-outline-primary">عرض الكل</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in recent_invoices %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('invoices.detail', invoice_id=invoice.id) }}" class="text-decoration-none">
                                        {{ invoice.invoice_number }}
                                    </a>
                                </td>
                                <td>{{ invoice.customer_name }}</td>
                                <td>{{ invoice.total_amount|currency }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'primary' if invoice.status == 'sent' else 'secondary' }}">
                                        {{ invoice.status_display }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Recent Receipts -->
    {% if current_user.can_access('receipts') and recent_receipts %}
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    آخر الإيصالات
                </h5>
                <a href="{{ url_for('receipts.index') }}" class="btn btn-sm btn-outline-success">عرض الكل</a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم الإيصال</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>طريقة الدفع</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for receipt in recent_receipts %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('receipts.detail', receipt_id=receipt.id) }}" class="text-decoration-none">
                                        {{ receipt.receipt_number }}
                                    </a>
                                </td>
                                <td>{{ receipt.customer_name }}</td>
                                <td>{{ receipt.amount_received|currency }}</td>
                                <td>{{ receipt.payment_method_display }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Charts Row -->
{% if current_user.can_access('reports') %}
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-area me-2"></i>
                    المبيعات الشهرية
                </h5>
            </div>
            <div class="card-body">
                <canvas id="salesChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    أفضل العملاء
                </h5>
            </div>
            <div class="card-body">
                {% for customer in top_customers[:5] %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>{{ customer.customer_name }}</span>
                    <span class="badge bg-primary">{{ customer.formatted_amount }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Sales Chart
    {% if monthly_sales %}
    const salesCtx = document.getElementById('salesChart').getContext('2d');
    const salesChart = new Chart(salesCtx, {
        type: 'line',
        data: {
            labels: {{ monthly_sales|map(attribute='month_name')|list|tojson }},
            datasets: [{
                label: 'المبيعات',
                data: {{ monthly_sales|map(attribute='sales')|list|tojson }},
                borderColor: 'rgb(37, 99, 235)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return value.toLocaleString() + ' ج.م';
                        }
                    }
                }
            }
        }
    });
    {% endif %}
    
    // Auto-refresh stats every 5 minutes
    setInterval(function() {
        fetch('{{ url_for("dashboard.api_stats") }}')
            .then(response => response.json())
            .then(data => {
                // Update stats cards with new data
                console.log('Stats updated:', data);
            })
            .catch(error => console.error('Error updating stats:', error));
    }, 300000); // 5 minutes
</script>
{% endblock %}
