"""
Reports views
"""

from flask import Blueprint, render_template, request, jsonify, make_response
from flask_login import login_required
from datetime import datetime, date, timedelta
from sqlalchemy import func, and_, or_
from app import db
from app.models.invoice import Invoice
from app.models.receipt import Receipt
from app.models.customer import Customer
from app.models.account import Account
from app.models.journal import JournalEntry, JournalLine
from app.utils.decorators import accountant_required
import csv
from io import StringIO

reports_bp = Blueprint('reports', __name__)

@reports_bp.route('/')
@accountant_required
def index():
    """Reports dashboard"""
    return render_template('reports/index.html')

@reports_bp.route('/sales')
@accountant_required
def sales():
    """Sales report"""
    # Get date range from request
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    customer_id = request.args.get('customer_id')
    
    # Default to current month if no dates provided
    if not date_from or not date_to:
        today = date.today()
        date_from = date(today.year, today.month, 1)
        date_to = today
    else:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        except:
            today = date.today()
            date_from = date(today.year, today.month, 1)
            date_to = today
    
    # Build query
    query = Invoice.query.filter(
        and_(Invoice.issue_date >= date_from,
             Invoice.issue_date <= date_to)
    )
    
    if customer_id:
        query = query.filter(Invoice.customer_id == customer_id)
    
    invoices = query.order_by(Invoice.issue_date.desc()).all()
    
    # Calculate totals
    total_invoices = len(invoices)
    total_amount = sum(float(inv.total_amount) for inv in invoices)
    total_tax = sum(float(inv.tax_amount) for inv in invoices)
    
    # Get customers for filter
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
    
    return render_template('reports/sales.html',
                         invoices=invoices,
                         date_from=date_from,
                         date_to=date_to,
                         customer_id=customer_id,
                         customers=customers,
                         total_invoices=total_invoices,
                         total_amount=total_amount,
                         total_tax=total_tax)

@reports_bp.route('/receipts')
@accountant_required
def receipts():
    """Receipts report"""
    # Get date range from request
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    customer_id = request.args.get('customer_id')
    payment_method = request.args.get('payment_method')
    
    # Default to current month if no dates provided
    if not date_from or not date_to:
        today = date.today()
        date_from = date(today.year, today.month, 1)
        date_to = today
    else:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        except:
            today = date.today()
            date_from = date(today.year, today.month, 1)
            date_to = today
    
    # Build query
    query = Receipt.query.filter(
        and_(Receipt.receipt_date >= date_from,
             Receipt.receipt_date <= date_to)
    )
    
    if customer_id:
        query = query.filter(Receipt.customer_id == customer_id)
    
    if payment_method:
        query = query.filter(Receipt.payment_method == payment_method)
    
    receipts = query.order_by(Receipt.receipt_date.desc()).all()
    
    # Calculate totals
    total_receipts = len(receipts)
    total_amount = sum(float(rec.amount_received) for rec in receipts)
    
    # Group by payment method
    payment_methods = {}
    for receipt in receipts:
        method = receipt.payment_method
        if method not in payment_methods:
            payment_methods[method] = {'count': 0, 'amount': 0}
        payment_methods[method]['count'] += 1
        payment_methods[method]['amount'] += float(receipt.amount_received)
    
    # Get customers for filter
    customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
    
    return render_template('reports/receipts.html',
                         receipts=receipts,
                         date_from=date_from,
                         date_to=date_to,
                         customer_id=customer_id,
                         payment_method=payment_method,
                         customers=customers,
                         total_receipts=total_receipts,
                         total_amount=total_amount,
                         payment_methods=payment_methods)

@reports_bp.route('/tax')
@accountant_required
def tax():
    """Tax report"""
    # Get date range from request
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    # Default to current month if no dates provided
    if not date_from or not date_to:
        today = date.today()
        date_from = date(today.year, today.month, 1)
        date_to = today
    else:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        except:
            today = date.today()
            date_from = date(today.year, today.month, 1)
            date_to = today
    
    # Get invoices in date range
    invoices = Invoice.query.filter(
        and_(Invoice.issue_date >= date_from,
             Invoice.issue_date <= date_to)
    ).order_by(Invoice.issue_date.desc()).all()
    
    # Calculate tax summary
    total_sales = sum(float(inv.subtotal) for inv in invoices)
    total_tax = sum(float(inv.tax_amount) for inv in invoices)
    total_with_tax = sum(float(inv.total_amount) for inv in invoices)
    
    # Group by tax status
    tax_status_summary = {}
    for invoice in invoices:
        status = invoice.tax_status
        if status not in tax_status_summary:
            tax_status_summary[status] = {'count': 0, 'amount': 0, 'tax': 0}
        tax_status_summary[status]['count'] += 1
        tax_status_summary[status]['amount'] += float(invoice.total_amount)
        tax_status_summary[status]['tax'] += float(invoice.tax_amount)
    
    return render_template('reports/tax.html',
                         invoices=invoices,
                         date_from=date_from,
                         date_to=date_to,
                         total_sales=total_sales,
                         total_tax=total_tax,
                         total_with_tax=total_with_tax,
                         tax_status_summary=tax_status_summary)

@reports_bp.route('/profit-loss')
@accountant_required
def profit_loss():
    """Profit & Loss report"""
    # Get date range from request
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    
    # Default to current year if no dates provided
    if not date_from or not date_to:
        today = date.today()
        date_from = date(today.year, 1, 1)
        date_to = today
    else:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        except:
            today = date.today()
            date_from = date(today.year, 1, 1)
            date_to = today
    
    # Get income accounts
    income_accounts = Account.query.filter_by(type='Income', is_active=True).all()
    income_total = 0
    income_data = []
    
    for account in income_accounts:
        balance = account.get_balance(date_to)
        if balance != 0:
            income_data.append({
                'account': account,
                'balance': balance
            })
            income_total += balance
    
    # Get expense accounts
    expense_accounts = Account.query.filter_by(type='Expense', is_active=True).all()
    expense_total = 0
    expense_data = []
    
    for account in expense_accounts:
        balance = account.get_balance(date_to)
        if balance != 0:
            expense_data.append({
                'account': account,
                'balance': balance
            })
            expense_total += balance
    
    # Calculate net profit/loss
    net_profit = income_total - expense_total
    
    return render_template('reports/profit_loss.html',
                         date_from=date_from,
                         date_to=date_to,
                         income_data=income_data,
                         expense_data=expense_data,
                         income_total=income_total,
                         expense_total=expense_total,
                         net_profit=net_profit)

@reports_bp.route('/balance-sheet')
@accountant_required
def balance_sheet():
    """Balance Sheet report"""
    as_of_date = request.args.get('as_of_date')
    
    if not as_of_date:
        as_of_date = date.today()
    else:
        try:
            as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
        except:
            as_of_date = date.today()
    
    # Get assets
    asset_accounts = Account.query.filter_by(type='Asset', is_active=True).all()
    assets_total = 0
    assets_data = []
    
    for account in asset_accounts:
        balance = account.get_balance(as_of_date)
        if balance != 0:
            assets_data.append({
                'account': account,
                'balance': balance
            })
            assets_total += balance
    
    # Get liabilities
    liability_accounts = Account.query.filter_by(type='Liability', is_active=True).all()
    liabilities_total = 0
    liabilities_data = []
    
    for account in liability_accounts:
        balance = account.get_balance(as_of_date)
        if balance != 0:
            liabilities_data.append({
                'account': account,
                'balance': balance
            })
            liabilities_total += balance
    
    # Get equity
    equity_accounts = Account.query.filter_by(type='Equity', is_active=True).all()
    equity_total = 0
    equity_data = []
    
    for account in equity_accounts:
        balance = account.get_balance(as_of_date)
        if balance != 0:
            equity_data.append({
                'account': account,
                'balance': balance
            })
            equity_total += balance
    
    # Calculate total liabilities and equity
    total_liabilities_equity = liabilities_total + equity_total
    
    return render_template('reports/balance_sheet.html',
                         as_of_date=as_of_date,
                         assets_data=assets_data,
                         liabilities_data=liabilities_data,
                         equity_data=equity_data,
                         assets_total=assets_total,
                         liabilities_total=liabilities_total,
                         equity_total=equity_total,
                         total_liabilities_equity=total_liabilities_equity)

@reports_bp.route('/export/sales')
@accountant_required
def export_sales():
    """Export sales report to CSV"""
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')
    customer_id = request.args.get('customer_id')
    
    # Parse dates
    if date_from and date_to:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        except:
            today = date.today()
            date_from = date(today.year, today.month, 1)
            date_to = today
    else:
        today = date.today()
        date_from = date(today.year, today.month, 1)
        date_to = today
    
    # Get invoices
    query = Invoice.query.filter(
        and_(Invoice.issue_date >= date_from,
             Invoice.issue_date <= date_to)
    )
    
    if customer_id:
        query = query.filter(Invoice.customer_id == customer_id)
    
    invoices = query.order_by(Invoice.issue_date.desc()).all()
    
    # Create CSV
    output = StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow([
        'رقم الفاتورة', 'تاريخ الإصدار', 'العميل', 'المبلغ الفرعي', 
        'الضريبة', 'الخصم', 'المجموع', 'الحالة'
    ])
    
    # Write data
    for invoice in invoices:
        writer.writerow([
            invoice.invoice_number,
            invoice.issue_date.strftime('%Y-%m-%d'),
            invoice.customer.name,
            float(invoice.subtotal),
            float(invoice.tax_amount),
            float(invoice.discount_amount),
            float(invoice.total_amount),
            invoice.get_status_display()
        ])
    
    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv; charset=utf-8'
    response.headers['Content-Disposition'] = f'attachment; filename=sales_report_{date_from}_{date_to}.csv'
    
    return response
