"""
Forms for invoice management
"""

from flask_wtf import FlaskForm
from wtforms import (
    StringField, TextAreaField, SelectField, DecimalField, 
    IntegerField, BooleanField, DateField, HiddenField, FieldList, FormField
)
from wtforms.validators import DataRequired, Length, Optional, NumberRange, ValidationError
from app.models.invoice import Invoice
from app.models.customer import Customer
from app.models.account import Account
from datetime import date, datetime, timedelta


class InvoiceLineForm(FlaskForm):
    """Form for invoice line items"""
    
    description = StringField(
        'الوصف',
        validators=[
            DataRequired(message='وصف البند مطلوب'),
            Length(min=1, max=500, message='الوصف يجب أن يكون بين 1 و 500 حرف')
        ],
        render_kw={
            'placeholder': 'وصف الصنف أو الخدمة',
            'class': 'form-control'
        }
    )
    
    quantity = DecimalField(
        'الكمية',
        validators=[
            DataRequired(message='الكمية مطلوبة'),
            NumberRange(min=0.01, message='الكمية يجب أن تكون أكبر من صفر')
        ],
        places=3,
        default=1,
        render_kw={
            'class': 'form-control',
            'step': '0.001',
            'min': '0.001'
        }
    )
    
    unit_price = DecimalField(
        'السعر',
        validators=[
            DataRequired(message='السعر مطلوب'),
            NumberRange(min=0, message='السعر لا يمكن أن يكون سالب')
        ],
        places=2,
        default=0,
        render_kw={
            'class': 'form-control',
            'step': '0.01',
            'min': '0'
        }
    )
    
    discount_rate = DecimalField(
        'نسبة الخصم %',
        validators=[
            Optional(),
            NumberRange(min=0, max=100, message='نسبة الخصم يجب أن تكون بين 0 و 100')
        ],
        places=2,
        default=0,
        render_kw={
            'class': 'form-control',
            'step': '0.01',
            'min': '0',
            'max': '100'
        }
    )
    
    tax_rate = DecimalField(
        'نسبة الضريبة %',
        validators=[
            Optional(),
            NumberRange(min=0, max=100, message='نسبة الضريبة يجب أن تكون بين 0 و 100')
        ],
        places=2,
        default=14,
        render_kw={
            'class': 'form-control',
            'step': '0.01',
            'min': '0',
            'max': '100'
        }
    )


class InvoiceForm(FlaskForm):
    """Form for creating and editing invoices"""
    
    invoice_number = StringField(
        'رقم الفاتورة',
        validators=[
            DataRequired(message='رقم الفاتورة مطلوب'),
            Length(min=1, max=50, message='رقم الفاتورة يجب أن يكون بين 1 و 50 حرف')
        ],
        render_kw={
            'placeholder': 'INV-2024-001',
            'class': 'form-control'
        }
    )
    
    reference_number = StringField(
        'الرقم المرجعي',
        validators=[
            Optional(),
            Length(max=50, message='الرقم المرجعي يجب ألا يزيد عن 50 حرف')
        ],
        render_kw={
            'placeholder': 'رقم مرجعي اختياري',
            'class': 'form-control'
        }
    )
    
    customer_id = SelectField(
        'العميل',
        choices=[],
        validators=[DataRequired(message='العميل مطلوب')],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    invoice_date = DateField(
        'تاريخ الفاتورة',
        validators=[DataRequired(message='تاريخ الفاتورة مطلوب')],
        default=date.today,
        render_kw={'class': 'form-control'}
    )
    
    due_date = DateField(
        'تاريخ الاستحقاق',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    currency = SelectField(
        'العملة',
        choices=[
            ('EGP', 'جنيه مصري'),
            ('USD', 'دولار أمريكي'),
            ('EUR', 'يورو'),
            ('SAR', 'ريال سعودي'),
            ('AED', 'درهم إماراتي')
        ],
        default='EGP',
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    description = TextAreaField(
        'وصف الفاتورة',
        validators=[
            Optional(),
            Length(max=1000, message='الوصف يجب ألا يزيد عن 1000 حرف')
        ],
        render_kw={
            'placeholder': 'وصف عام للفاتورة (اختياري)',
            'class': 'form-control',
            'rows': 3
        }
    )
    
    notes = TextAreaField(
        'ملاحظات',
        validators=[
            Optional(),
            Length(max=1000, message='الملاحظات يجب ألا تزيد عن 1000 حرف')
        ],
        render_kw={
            'placeholder': 'ملاحظات إضافية...',
            'class': 'form-control',
            'rows': 3
        }
    )
    
    # Hidden fields for calculations
    subtotal_amount = HiddenField()
    discount_amount = HiddenField()
    tax_amount = HiddenField()
    total_amount = HiddenField()
    
    def __init__(self, invoice=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.invoice = invoice
        self.populate_customer_choices()
        
        # Set due date based on customer payment terms
        if self.customer_id.data and not self.due_date.data:
            customer = Customer.query.get(self.customer_id.data)
            if customer and customer.payment_terms:
                self.due_date.data = self.invoice_date.data + timedelta(days=customer.payment_terms)
    
    def populate_customer_choices(self):
        """Populate customer choices"""
        choices = [('', 'اختر العميل')]
        
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        
        for customer in customers:
            display_name = customer.name
            if customer.tax_id:
                display_name += f" ({customer.tax_id})"
            
            choices.append((str(customer.id), display_name))
        
        self.customer_id.choices = choices
    
    def validate_invoice_number(self, field):
        """Validate invoice number uniqueness"""
        if field.data:
            query = Invoice.query.filter_by(invoice_number=field.data)
            if self.invoice:
                query = query.filter(Invoice.id != self.invoice.id)
            
            if query.first():
                raise ValidationError('رقم الفاتورة موجود بالفعل')
    
    def validate_due_date(self, field):
        """Validate due date is not before invoice date"""
        if field.data and self.invoice_date.data:
            if field.data < self.invoice_date.data:
                raise ValidationError('تاريخ الاستحقاق لا يمكن أن يكون قبل تاريخ الفاتورة')


class InvoiceSearchForm(FlaskForm):
    """Form for searching invoices"""
    
    search = StringField(
        'البحث',
        validators=[Optional()],
        render_kw={
            'placeholder': 'البحث في رقم الفاتورة أو العميل...',
            'class': 'form-control'
        }
    )
    
    status = SelectField(
        'الحالة',
        choices=[
            ('', 'جميع الحالات'),
            ('draft', 'مسودة'),
            ('pending', 'معلقة'),
            ('paid', 'مدفوعة'),
            ('overdue', 'متأخرة'),
            ('cancelled', 'ملغية')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    customer_id = SelectField(
        'العميل',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    date_from = DateField(
        'من تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    date_to = DateField(
        'إلى تاريخ',
        validators=[Optional()],
        render_kw={'class': 'form-control'}
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.populate_customer_choices()
    
    def populate_customer_choices(self):
        """Populate customer choices"""
        choices = [('', 'جميع العملاء')]
        
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        
        for customer in customers:
            choices.append((str(customer.id), customer.name))
        
        self.customer_id.choices = choices


class QuickInvoiceForm(FlaskForm):
    """Form for quick invoice creation"""
    
    customer_id = SelectField(
        'العميل',
        choices=[],
        validators=[DataRequired(message='العميل مطلوب')],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    description = StringField(
        'الوصف',
        validators=[
            DataRequired(message='الوصف مطلوب'),
            Length(min=1, max=500, message='الوصف يجب أن يكون بين 1 و 500 حرف')
        ],
        render_kw={
            'placeholder': 'وصف الخدمة أو المنتج',
            'class': 'form-control'
        }
    )
    
    amount = DecimalField(
        'المبلغ',
        validators=[
            DataRequired(message='المبلغ مطلوب'),
            NumberRange(min=0.01, message='المبلغ يجب أن يكون أكبر من صفر')
        ],
        places=2,
        render_kw={
            'class': 'form-control',
            'step': '0.01',
            'min': '0.01'
        }
    )
    
    tax_rate = DecimalField(
        'نسبة الضريبة %',
        validators=[
            Optional(),
            NumberRange(min=0, max=100, message='نسبة الضريبة يجب أن تكون بين 0 و 100')
        ],
        places=2,
        default=14,
        render_kw={
            'class': 'form-control',
            'step': '0.01',
            'min': '0',
            'max': '100'
        }
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.populate_customer_choices()
    
    def populate_customer_choices(self):
        """Populate customer choices"""
        choices = [('', 'اختر العميل')]
        
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        
        for customer in customers:
            choices.append((str(customer.id), customer.name))
        
        self.customer_id.choices = choices


class InvoiceBulkActionForm(FlaskForm):
    """Form for bulk actions on invoices"""
    
    action = SelectField(
        'الإجراء',
        choices=[
            ('', 'اختر الإجراء'),
            ('submit_eta', 'إرسال للضرائب'),
            ('generate_pdf', 'إنتاج PDF'),
            ('send_email', 'إرسال بريد'),
            ('mark_paid', 'تحديد كمدفوع'),
            ('mark_cancelled', 'تحديد كملغي'),
            ('export', 'تصدير'),
            ('delete', 'حذف')
        ],
        validators=[DataRequired(message='الإجراء مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    selected_invoices = StringField(
        'الفواتير المحددة',
        validators=[DataRequired(message='يجب تحديد فاتورة واحدة على الأقل')]
    )
    
    confirmation = BooleanField(
        'أؤكد تنفيذ هذا الإجراء',
        validators=[DataRequired(message='يجب تأكيد الإجراء')],
        render_kw={'class': 'form-check-input'}
    )


class InvoiceSettingsForm(FlaskForm):
    """Form for invoice settings"""
    
    default_tax_rate = DecimalField(
        'نسبة الضريبة الافتراضية %',
        validators=[
            DataRequired(message='نسبة الضريبة الافتراضية مطلوبة'),
            NumberRange(min=0, max=100, message='نسبة الضريبة يجب أن تكون بين 0 و 100')
        ],
        places=2,
        default=14,
        render_kw={
            'class': 'form-control',
            'step': '0.01',
            'min': '0',
            'max': '100'
        }
    )
    
    invoice_number_prefix = StringField(
        'بادئة رقم الفاتورة',
        validators=[
            Optional(),
            Length(max=10, message='البادئة يجب ألا تزيد عن 10 أحرف')
        ],
        default='INV',
        render_kw={
            'placeholder': 'INV',
            'class': 'form-control'
        }
    )
    
    auto_generate_numbers = BooleanField(
        'إنتاج أرقام الفواتير تلقائياً',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    default_payment_terms = IntegerField(
        'شروط الدفع الافتراضية (بالأيام)',
        validators=[
            Optional(),
            NumberRange(min=0, max=365, message='شروط الدفع يجب أن تكون بين 0 و 365 يوم')
        ],
        default=30,
        render_kw={
            'class': 'form-control',
            'min': '0',
            'max': '365'
        }
    )
    
    default_currency = SelectField(
        'العملة الافتراضية',
        choices=[
            ('EGP', 'جنيه مصري'),
            ('USD', 'دولار أمريكي'),
            ('EUR', 'يورو'),
            ('SAR', 'ريال سعودي'),
            ('AED', 'درهم إماراتي')
        ],
        default='EGP',
        validators=[DataRequired(message='العملة الافتراضية مطلوبة')],
        render_kw={'class': 'form-select'}
    )
    
    email_template = TextAreaField(
        'قالب البريد الإلكتروني',
        validators=[
            Optional(),
            Length(max=2000, message='قالب البريد يجب ألا يزيد عن 2000 حرف')
        ],
        render_kw={
            'placeholder': 'قالب البريد الإلكتروني لإرسال الفواتير...',
            'class': 'form-control',
            'rows': 5
        }
    )
    
    auto_submit_eta = BooleanField(
        'إرسال تلقائي لمنظومة الضرائب',
        default=False,
        render_kw={'class': 'form-check-input'}
    )
