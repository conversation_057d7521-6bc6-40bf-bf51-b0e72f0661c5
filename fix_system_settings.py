#!/usr/bin/env python3
"""
إصلاح جدول system_settings
Fix system_settings table structure
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية"""
    db_path = 'instance/systemtax.db'
    if os.path.exists(db_path):
        backup_path = f'instance/systemtax_backup_settings_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return backup_path
    return None

def check_table_structure():
    """فحص هيكل الجدول الحالي"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص إذا كان الجدول موجود
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='system_settings'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("ℹ️ جدول system_settings غير موجود")
            conn.close()
            return False, []
        
        # الحصول على هيكل الجدول
        cursor.execute("PRAGMA table_info(system_settings)")
        columns = cursor.fetchall()
        
        print("📋 هيكل الجدول الحالي:")
        column_names = []
        for col in columns:
            print(f"   - {col[1]} ({col[2]})")
            column_names.append(col[1])
        
        conn.close()
        return True, column_names
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجدول: {e}")
        return False, []

def fix_system_settings_table():
    """إصلاح جدول system_settings"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # فحص الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(system_settings)")
        existing_columns = [col[1] for col in cursor.fetchall()]
        
        # الأعمدة المطلوبة
        required_columns = {
            'data_type': 'VARCHAR(20) DEFAULT "string"',
            'category': 'VARCHAR(50) DEFAULT "general"',
            'is_encrypted': 'BOOLEAN DEFAULT 0',
            'is_system': 'BOOLEAN DEFAULT 0',
            'updated_by': 'VARCHAR(100)'
        }
        
        # إضافة الأعمدة المفقودة
        added_columns = []
        for column_name, column_def in required_columns.items():
            if column_name not in existing_columns:
                try:
                    cursor.execute(f"ALTER TABLE system_settings ADD COLUMN {column_name} {column_def}")
                    added_columns.append(column_name)
                    print(f"✅ تم إضافة العمود: {column_name}")
                except Exception as e:
                    print(f"⚠️ خطأ في إضافة العمود {column_name}: {e}")
        
        if not added_columns:
            print("ℹ️ جميع الأعمدة موجودة مسبقاً")
        
        # إنشاء فهارس إذا لم تكن موجودة
        try:
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(key)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category)")
            print("✅ تم إنشاء الفهارس")
        except Exception as e:
            print(f"⚠️ خطأ في إنشاء الفهارس: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إصلاح جدول system_settings")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الجدول: {e}")
        return False

def create_system_settings_table():
    """إنشاء جدول system_settings من الصفر"""
    db_path = 'instance/systemtax.db'
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # حذف الجدول القديم إذا كان موجود
        cursor.execute("DROP TABLE IF EXISTS system_settings")
        
        # إنشاء الجدول الجديد
        cursor.execute("""
            CREATE TABLE system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key VARCHAR(100) UNIQUE NOT NULL,
                value TEXT,
                data_type VARCHAR(20) DEFAULT 'string',
                category VARCHAR(50) DEFAULT 'general',
                description TEXT,
                is_encrypted BOOLEAN DEFAULT 0,
                is_system BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100)
            )
        """)
        
        # إنشاء الفهارس
        cursor.execute("CREATE INDEX idx_system_settings_key ON system_settings(key)")
        cursor.execute("CREATE INDEX idx_system_settings_category ON system_settings(category)")
        
        conn.commit()
        conn.close()
        
        print("✅ تم إنشاء جدول system_settings جديد")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجدول: {e}")
        return False

def initialize_default_settings():
    """تهيئة الإعدادات الافتراضية"""
    try:
        from app import create_app, db
        from app.models.system_settings import SystemSettings
        
        app = create_app()
        with app.app_context():
            SystemSettings.initialize_default_settings()
            print("✅ تم تهيئة الإعدادات الافتراضية")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في تهيئة الإعدادات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🔧 إصلاح جدول system_settings")
    print("=" * 40)
    
    # إنشاء نسخة احتياطية
    backup_path = backup_database()
    
    # فحص هيكل الجدول
    table_exists, columns = check_table_structure()
    
    if not table_exists:
        print("\n🏗️ إنشاء جدول system_settings جديد...")
        if not create_system_settings_table():
            print("❌ فشل في إنشاء الجدول")
            return False
    else:
        # فحص إذا كان العمود data_type موجود
        if 'data_type' not in columns:
            print("\n🔧 إصلاح هيكل الجدول...")
            if not fix_system_settings_table():
                print("❌ فشل في إصلاح الجدول")
                print("💡 جرب إعادة إنشاء الجدول:")
                choice = input("هل تريد إعادة إنشاء الجدول؟ (y/n): ")
                if choice.lower() == 'y':
                    if not create_system_settings_table():
                        return False
                else:
                    return False
        else:
            print("✅ هيكل الجدول صحيح")
    
    # تهيئة الإعدادات الافتراضية
    print("\n⚙️ تهيئة الإعدادات الافتراضية...")
    if not initialize_default_settings():
        print("❌ فشل في تهيئة الإعدادات")
        return False
    
    print("\n🎉 تم إصلاح جدول system_settings بنجاح!")
    print("✅ يمكنك الآن تشغيل النظام: python app.py")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            print("\n❌ فشل في الإصلاح!")
            exit(1)
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إلغاء العملية")
        exit(1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        exit(1)
