"""
Notification management views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.notification import (
    Notification, NotificationTemplate, NotificationPreference, 
    NotificationType, NotificationChannel
)
from app.services.notification_service import NotificationService
from app.utils.decorators import permission_required
from app.utils.helpers import get_pagination_params
from datetime import datetime

# Create blueprint
notifications_bp = Blueprint('notifications', __name__, url_prefix='/notifications')


@notifications_bp.route('/')
@login_required
def index():
    """Display user notifications"""
    page, per_page = get_pagination_params()
    
    # Get filter parameters
    filter_type = request.args.get('type', 'all')
    unread_only = request.args.get('unread') == 'true'
    
    # Build query
    notifications = NotificationService.get_user_notifications(
        current_user.id,
        unread_only=unread_only,
        limit=per_page
    )
    
    # Filter by type if specified
    if filter_type != 'all':
        try:
            notification_type = NotificationType(filter_type)
            notifications = [n for n in notifications if n.type == notification_type]
        except ValueError:
            pass
    
    # Get unread count
    unread_count = NotificationService.get_unread_count(current_user.id)
    
    return render_template(
        'notifications/index.html',
        notifications=notifications,
        unread_count=unread_count,
        filter_type=filter_type,
        unread_only=unread_only,
        title='الإشعارات'
    )


@notifications_bp.route('/<int:notification_id>')
@login_required
def detail(notification_id):
    """Display notification details"""
    notification = Notification.query.filter_by(id=notification_id).first_or_404()
    
    # Check if user can view this notification
    if not (notification.user_id == current_user.id or 
            notification.is_global or 
            (notification.role and current_user.role == notification.role)):
        flash('ليس لديك صلاحية لعرض هذا الإشعار', 'error')
        return redirect(url_for('notifications.index'))
    
    # Mark as read
    if not notification.is_read:
        notification.mark_as_read()
    
    return render_template(
        'notifications/detail.html',
        notification=notification,
        title=notification.title
    )


@notifications_bp.route('/<int:notification_id>/mark-read', methods=['POST'])
@login_required
def mark_read(notification_id):
    """Mark notification as read"""
    success = NotificationService.mark_as_read(notification_id, current_user.id)
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': success})
    
    if success:
        flash('تم تحديد الإشعار كمقروء', 'success')
    else:
        flash('فشل في تحديث الإشعار', 'error')
    
    return redirect(url_for('notifications.index'))


@notifications_bp.route('/mark-all-read', methods=['POST'])
@login_required
def mark_all_read():
    """Mark all notifications as read"""
    count = NotificationService.mark_all_as_read(current_user.id)
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': True, 'count': count})
    
    flash(f'تم تحديد {count} إشعار كمقروء', 'success')
    return redirect(url_for('notifications.index'))


@notifications_bp.route('/unread-count')
@login_required
def unread_count():
    """Get unread notifications count"""
    count = NotificationService.get_unread_count(current_user.id)
    return jsonify({'count': count})


@notifications_bp.route('/refresh')
@login_required
def refresh():
    """Refresh notifications list"""
    notifications = NotificationService.get_user_notifications(
        current_user.id,
        limit=10
    )
    
    html = render_template(
        'notifications/partials/notification_list.html',
        notifications=notifications
    )
    
    return jsonify({'success': True, 'html': html})


@notifications_bp.route('/preferences')
@login_required
def preferences():
    """Display notification preferences"""
    # Get user preferences for all notification types
    preferences = {}
    for notification_type in NotificationType:
        pref = NotificationPreference.query.filter_by(
            user_id=current_user.id,
            notification_type=notification_type
        ).first()
        
        if not pref:
            # Create default preference
            pref = NotificationPreference(
                user_id=current_user.id,
                notification_type=notification_type
            )
            db.session.add(pref)
        
        preferences[notification_type.value] = pref
    
    db.session.commit()
    
    return render_template(
        'notifications/preferences.html',
        preferences=preferences,
        notification_types=NotificationType,
        title='إعدادات الإشعارات'
    )


@notifications_bp.route('/preferences', methods=['POST'])
@login_required
def update_preferences():
    """Update notification preferences"""
    try:
        for notification_type in NotificationType:
            pref = NotificationPreference.query.filter_by(
                user_id=current_user.id,
                notification_type=notification_type
            ).first()
            
            if not pref:
                pref = NotificationPreference(
                    user_id=current_user.id,
                    notification_type=notification_type
                )
                db.session.add(pref)
            
            # Update preferences from form
            type_key = notification_type.value
            pref.in_app_enabled = request.form.get(f'{type_key}_in_app') == 'on'
            pref.email_enabled = request.form.get(f'{type_key}_email') == 'on'
            pref.sms_enabled = request.form.get(f'{type_key}_sms') == 'on'
            pref.push_enabled = request.form.get(f'{type_key}_push') == 'on'
        
        # Update quiet hours
        quiet_start = request.form.get('quiet_hours_start')
        quiet_end = request.form.get('quiet_hours_end')
        
        if quiet_start and quiet_end:
            for pref in NotificationPreference.query.filter_by(user_id=current_user.id).all():
                pref.quiet_hours_start = quiet_start
                pref.quiet_hours_end = quiet_end
        
        db.session.commit()
        flash('تم حفظ إعدادات الإشعارات بنجاح', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash('حدث خطأ أثناء حفظ الإعدادات', 'error')
    
    return redirect(url_for('notifications.preferences'))


# Admin routes for notification management

@notifications_bp.route('/admin')
@login_required
@permission_required('admin')
def admin_index():
    """Admin notification management"""
    page, per_page = get_pagination_params()
    
    # Get all notifications with pagination
    notifications = Notification.query.order_by(
        Notification.created_at.desc()
    ).paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )
    
    # Get statistics
    stats = {
        'total': Notification.query.count(),
        'unread': Notification.query.filter_by(is_read=False).count(),
        'sent': Notification.query.filter_by(is_sent=True).count(),
        'scheduled': Notification.query.filter(
            Notification.scheduled_at > datetime.utcnow()
        ).count()
    }
    
    return render_template(
        'notifications/admin/index.html',
        notifications=notifications,
        stats=stats,
        title='إدارة الإشعارات'
    )


@notifications_bp.route('/admin/new')
@login_required
@permission_required('admin')
def admin_new():
    """Create new notification"""
    from app.models.user import User
    
    users = User.query.filter_by(is_active=True).all()
    templates = NotificationTemplate.query.filter_by(is_active=True).all()
    
    return render_template(
        'notifications/admin/new.html',
        users=users,
        templates=templates,
        notification_types=NotificationType,
        title='إنشاء إشعار جديد'
    )


@notifications_bp.route('/admin/new', methods=['POST'])
@login_required
@permission_required('admin')
def admin_create():
    """Create new notification"""
    try:
        # Get form data
        title = request.form.get('title')
        message = request.form.get('message')
        notification_type = NotificationType(request.form.get('type'))
        
        # Target settings
        target_type = request.form.get('target_type')
        user_id = request.form.get('user_id') if target_type == 'user' else None
        role = request.form.get('role') if target_type == 'role' else None
        is_global = target_type == 'global'
        
        # Delivery settings
        channels = ','.join(request.form.getlist('channels'))
        action_url = request.form.get('action_url')
        action_text = request.form.get('action_text')
        
        # Scheduling
        scheduled_at = None
        if request.form.get('scheduled_at'):
            scheduled_at = datetime.fromisoformat(request.form.get('scheduled_at'))
        
        expires_at = None
        if request.form.get('expires_at'):
            expires_at = datetime.fromisoformat(request.form.get('expires_at'))
        
        # Create notification
        notification = NotificationService.create_notification(
            title=title,
            message=message,
            notification_type=notification_type,
            user_id=user_id,
            role=role,
            is_global=is_global,
            channels=channels,
            action_url=action_url,
            action_text=action_text,
            scheduled_at=scheduled_at,
            expires_at=expires_at
        )
        
        flash('تم إنشاء الإشعار بنجاح', 'success')
        return redirect(url_for('notifications.admin_index'))
        
    except Exception as e:
        flash(f'حدث خطأ أثناء إنشاء الإشعار: {str(e)}', 'error')
        return redirect(url_for('notifications.admin_new'))


@notifications_bp.route('/admin/templates')
@login_required
@permission_required('admin')
def admin_templates():
    """Manage notification templates"""
    templates = NotificationTemplate.query.order_by(
        NotificationTemplate.name
    ).all()
    
    return render_template(
        'notifications/admin/templates.html',
        templates=templates,
        title='قوالب الإشعارات'
    )


@notifications_bp.route('/admin/templates/new')
@login_required
@permission_required('admin')
def admin_new_template():
    """Create new notification template"""
    return render_template(
        'notifications/admin/new_template.html',
        notification_types=NotificationType,
        title='إنشاء قالب إشعار جديد'
    )


@notifications_bp.route('/admin/templates/new', methods=['POST'])
@login_required
@permission_required('admin')
def admin_create_template():
    """Create new notification template"""
    try:
        template = NotificationTemplate(
            name=request.form.get('name'),
            description=request.form.get('description'),
            type=NotificationType(request.form.get('type')),
            title_template=request.form.get('title_template'),
            message_template=request.form.get('message_template'),
            default_channels=','.join(request.form.getlist('default_channels')),
            target_role=request.form.get('target_role'),
            is_global=request.form.get('is_global') == 'on',
            trigger_event=request.form.get('trigger_event'),
            delay_minutes=int(request.form.get('delay_minutes') or 0),
            expires_after_hours=int(request.form.get('expires_after_hours') or 0) or None,
            created_by_id=current_user.id
        )
        
        db.session.add(template)
        db.session.commit()
        
        flash('تم إنشاء قالب الإشعار بنجاح', 'success')
        return redirect(url_for('notifications.admin_templates'))
        
    except Exception as e:
        db.session.rollback()
        flash(f'حدث خطأ أثناء إنشاء القالب: {str(e)}', 'error')
        return redirect(url_for('notifications.admin_new_template'))


@notifications_bp.route('/admin/send-scheduled')
@login_required
@permission_required('admin')
def admin_send_scheduled():
    """Send scheduled notifications"""
    count = NotificationService.send_scheduled_notifications()
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': True, 'count': count})
    
    flash(f'تم إرسال {count} إشعار مجدول', 'success')
    return redirect(url_for('notifications.admin_index'))


@notifications_bp.route('/admin/cleanup')
@login_required
@permission_required('admin')
def admin_cleanup():
    """Clean up old notifications"""
    days = int(request.args.get('days', 30))
    count = NotificationService.cleanup_old_notifications(days)
    
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return jsonify({'success': True, 'count': count})
    
    flash(f'تم حذف {count} إشعار قديم', 'success')
    return redirect(url_for('notifications.admin_index'))
