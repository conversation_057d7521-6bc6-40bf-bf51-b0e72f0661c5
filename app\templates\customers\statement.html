{% extends "base.html" %}

{% block title %}كشف حساب العميل: {{ customer.name }} - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-file-alt me-3"></i>
                كشف حساب العميل
            </h1>
            <p class="mb-0 mt-2">{{ customer.name }}</p>
        </div>
        <div>
            <a href="{{ url_for('customers.detail', customer_id=customer.id) }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للعميل
            </a>
            <button type="button" class="btn btn-outline-light" onclick="window.print()">
                <i class="fas fa-print me-2"></i>
                طباعة
            </button>
            <button type="button" class="btn btn-outline-light" onclick="exportToPDF()">
                <i class="fas fa-file-pdf me-2"></i>
                تصدير PDF
            </button>
        </div>
    </div>
</div>

<!-- Date Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="date_from" class="form-label">من تاريخ:</label>
                <input type="date" class="form-control" id="date_from" name="date_from" 
                       value="{{ date_from.isoformat() if date_from else '' }}">
            </div>
            <div class="col-md-3">
                <label for="date_to" class="form-label">إلى تاريخ:</label>
                <input type="date" class="form-control" id="date_to" name="date_to" 
                       value="{{ date_to.isoformat() if date_to else '' }}">
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>
                    عرض
                </button>
            </div>
            <div class="col-md-2">
                <a href="{{ url_for('customers.statement', customer_id=customer.id) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-refresh me-2"></i>
                    إعادة تعيين
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Customer Summary -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-user me-2"></i>
            معلومات العميل
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>اسم العميل:</strong></td>
                        <td>{{ customer.name }}</td>
                    </tr>
                    {% if customer.tax_id %}
                    <tr>
                        <td><strong>الرقم الضريبي:</strong></td>
                        <td>{{ customer.tax_id }}</td>
                    </tr>
                    {% endif %}
                    {% if customer.phone %}
                    <tr>
                        <td><strong>الهاتف:</strong></td>
                        <td>{{ customer.phone }}</td>
                    </tr>
                    {% endif %}
                    {% if customer.email %}
                    <tr>
                        <td><strong>البريد الإلكتروني:</strong></td>
                        <td>{{ customer.email }}</td>
                    </tr>
                    {% endif %}
                </table>
            </div>
            <div class="col-md-6">
                {% if customer.address %}
                <p><strong>العنوان:</strong><br>
                {{ customer.address }}
                {% if customer.city %}, {{ customer.city }}{% endif %}
                {% if customer.state %}, {{ customer.state }}{% endif %}
                {% if customer.postal_code %} {{ customer.postal_code }}{% endif %}
                </p>
                {% endif %}
                
                <p><strong>فترة الكشف:</strong><br>
                {% if date_from and date_to %}
                    من {{ date_from|date }} إلى {{ date_to|date }}
                {% else %}
                    جميع الفترات
                {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Balance Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h5 class="card-title text-primary">الرصيد الافتتاحي</h5>
                <h3 class="text-primary">{{ opening_balance|currency }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h5 class="card-title text-success">إجمالي المبيعات</h5>
                <h3 class="text-success">{{ total_sales|currency }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h5 class="card-title text-danger">إجمالي المدفوعات</h5>
                <h3 class="text-danger">{{ total_payments|currency }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h5 class="card-title text-info">الرصيد الختامي</h5>
                <h3 class="text-info">{{ closing_balance|currency }}</h3>
            </div>
        </div>
    </div>
</div>

<!-- Statement Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            كشف الحساب التفصيلي
        </h5>
    </div>
    <div class="card-body">
        {% if transactions %}
        <div class="table-responsive">
            <table class="table table-bordered" id="statementTable">
                <thead class="table-light">
                    <tr>
                        <th>التاريخ</th>
                        <th>البيان</th>
                        <th>رقم المرجع</th>
                        <th class="text-center">مدين</th>
                        <th class="text-center">دائن</th>
                        <th class="text-center">الرصيد</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Opening Balance Row -->
                    {% if opening_balance != 0 %}
                    <tr class="table-info">
                        <td>{{ date_from|date if date_from else 'بداية الفترة' }}</td>
                        <td><strong>الرصيد الافتتاحي</strong></td>
                        <td class="text-center">-</td>
                        <td class="text-center">
                            {% if opening_balance > 0 %}
                                <strong class="text-success">{{ opening_balance|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if opening_balance < 0 %}
                                <strong class="text-danger">{{ opening_balance|abs|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <strong class="text-primary">{{ opening_balance|currency }}</strong>
                        </td>
                    </tr>
                    {% endif %}
                    
                    <!-- Transaction Rows -->
                    {% for transaction in transactions %}
                    <tr>
                        <td>{{ transaction.date|date }}</td>
                        <td>{{ transaction.description }}</td>
                        <td class="text-center">
                            {% if transaction.reference_url %}
                                <a href="{{ transaction.reference_url }}" class="text-decoration-none">
                                    {{ transaction.reference_number }}
                                </a>
                            {% else %}
                                {{ transaction.reference_number }}
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if transaction.debit_amount > 0 %}
                                <strong class="text-success">{{ transaction.debit_amount|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if transaction.credit_amount > 0 %}
                                <strong class="text-danger">{{ transaction.credit_amount|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <strong class="text-{{ 'success' if transaction.running_balance >= 0 else 'danger' }}">
                                {{ transaction.running_balance|currency }}
                            </strong>
                        </td>
                    </tr>
                    {% endfor %}
                    
                    <!-- Closing Balance Row -->
                    <tr class="table-dark">
                        <th colspan="3" class="text-center">الرصيد الختامي</th>
                        <th class="text-center">
                            <strong class="text-success">{{ total_sales|currency }}</strong>
                        </th>
                        <th class="text-center">
                            <strong class="text-danger">{{ total_payments|currency }}</strong>
                        </th>
                        <th class="text-center">
                            <strong class="text-primary">{{ closing_balance|currency }}</strong>
                        </th>
                    </tr>
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
            <h5>لا توجد حركات</h5>
            <p class="text-muted">لا توجد حركات لهذا العميل في الفترة المحددة.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Aging Analysis -->
{% if aging_analysis %}
<div class="card mt-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-clock me-2"></i>
            تحليل أعمار الديون
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-2">
                <div class="text-center">
                    <h5 class="text-success">{{ aging_analysis.current|currency }}</h5>
                    <small>جاري (0-30 يوم)</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="text-center">
                    <h5 class="text-warning">{{ aging_analysis.days_30|currency }}</h5>
                    <small>31-60 يوم</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="text-center">
                    <h5 class="text-danger">{{ aging_analysis.days_60|currency }}</h5>
                    <small>61-90 يوم</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="text-center">
                    <h5 class="text-danger">{{ aging_analysis.days_90|currency }}</h5>
                    <small>91-120 يوم</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="text-center">
                    <h5 class="text-danger">{{ aging_analysis.over_120|currency }}</h5>
                    <small>أكثر من 120 يوم</small>
                </div>
            </div>
            <div class="col-md-2">
                <div class="text-center">
                    <h5 class="text-primary">{{ aging_analysis.total|currency }}</h5>
                    <small>الإجمالي</small>
                </div>
            </div>
        </div>
        
        <!-- Aging Chart -->
        <div class="mt-4">
            <canvas id="agingChart" height="100"></canvas>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .page-header .btn,
    .card:first-child,
    .card:last-child {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 11px;
    }
    
    .badge {
        color: black !important;
        background-color: transparent !important;
        border: 1px solid black !important;
    }
}

.table th {
    background-color: #f8f9fa !important;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6 !important;
}

.running-balance {
    font-family: 'Courier New', monospace;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if aging_analysis %}
// Aging Analysis Chart
const agingCtx = document.getElementById('agingChart').getContext('2d');
new Chart(agingCtx, {
    type: 'bar',
    data: {
        labels: ['جاري (0-30)', '31-60 يوم', '61-90 يوم', '91-120 يوم', 'أكثر من 120'],
        datasets: [{
            label: 'المبلغ',
            data: [
                {{ aging_analysis.current }},
                {{ aging_analysis.days_30 }},
                {{ aging_analysis.days_60 }},
                {{ aging_analysis.days_90 }},
                {{ aging_analysis.over_120 }}
            ],
            backgroundColor: [
                '#28a745',
                '#ffc107',
                '#fd7e14',
                '#dc3545',
                '#6f42c1'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ج.م';
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
{% endif %}

// Set default date range to current month
document.addEventListener('DOMContentLoaded', function() {
    if (!document.getElementById('date_from').value) {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        document.getElementById('date_from').value = firstDay.toISOString().split('T')[0];
        document.getElementById('date_to').value = today.toISOString().split('T')[0];
    }
});

// Export to PDF function
function exportToPDF() {
    window.print();
}
</script>
{% endblock %}
