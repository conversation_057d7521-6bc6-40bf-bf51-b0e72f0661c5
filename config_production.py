"""
إعدادات الإنتاج لنظام SystemTax
Production Configuration for SystemTax
"""

import os
from datetime import timedelta

class ProductionConfig:
    """إعدادات الإنتاج"""
    
    # Database Configuration - PostgreSQL
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://systemtax_user:systemtax_password@localhost:5432/systemtax_production'
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 20,
        'pool_recycle': 3600,
        'pool_pre_ping': True,
        'max_overflow': 30,
        'pool_timeout': 30
    }
    
    # Security
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-super-secret-production-key-change-this'
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # File Upload
    MAX_CONTENT_LENGTH = 50 * 1024 * 1024  # 50MB
    UPLOAD_FOLDER = os.path.join(os.getcwd(), 'uploads')
    
    # ETA Integration Settings (Production)
    ETA_BASE_URL = os.environ.get('ETA_BASE_URL') or 'https://api.invoicing.eta.gov.eg/api/v1'
    ETA_CLIENT_ID = os.environ.get('ETA_CLIENT_ID') or ''
    ETA_CLIENT_SECRET = os.environ.get('ETA_CLIENT_SECRET') or ''
    ETA_ENVIRONMENT = 'production'
    ETA_TIMEOUT = 60
    ETA_AUTO_SUBMIT = os.environ.get('ETA_AUTO_SUBMIT', 'false').lower() == 'true'
    
    # Company Information
    COMPANY_NAME = os.environ.get('COMPANY_NAME') or 'شركة النظام الضريبي'
    COMPANY_TAX_ID = os.environ.get('COMPANY_TAX_ID') or ''
    COMPANY_ACTIVITY_CODE = os.environ.get('COMPANY_ACTIVITY_CODE') or ''
    COMPANY_BRANCH_ID = os.environ.get('COMPANY_BRANCH_ID') or '0'
    COMPANY_ADDRESS = os.environ.get('COMPANY_ADDRESS') or ''
    COMPANY_PHONE = os.environ.get('COMPANY_PHONE') or ''
    COMPANY_EMAIL = os.environ.get('COMPANY_EMAIL') or ''
    
    # Logging
    LOG_LEVEL = 'INFO'
    LOG_FILE = 'logs/systemtax_production.log'
    
    # Cache Configuration (Redis)
    CACHE_TYPE = 'redis'
    CACHE_REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # Email Configuration
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() == 'true'
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME') or ''
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD') or ''
    MAIL_DEFAULT_SENDER = os.environ.get('MAIL_DEFAULT_SENDER') or ''
    
    # Backup Configuration
    BACKUP_ENABLED = True
    BACKUP_SCHEDULE = '0 2 * * *'  # Daily at 2 AM
    BACKUP_RETENTION_DAYS = 30
    BACKUP_PATH = os.environ.get('BACKUP_PATH') or '/backups/systemtax'
    
    # Performance
    SQLALCHEMY_RECORD_QUERIES = False
    SEND_FILE_MAX_AGE_DEFAULT = 31536000  # 1 year
    
    # Security Headers
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"
    }


class DevelopmentConfig:
    """إعدادات التطوير"""
    
    # Database Configuration - SQLite for development
    SQLALCHEMY_DATABASE_URI = 'sqlite:///instance/systemtax_dev.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = True
    
    # Security (less strict for development)
    SECRET_KEY = 'dev-secret-key-not-for-production'
    WTF_CSRF_ENABLED = True
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    
    # File Upload
    MAX_CONTENT_LENGTH = 10 * 1024 * 1024  # 10MB
    UPLOAD_FOLDER = os.path.join(os.getcwd(), 'uploads')
    
    # ETA Integration Settings (Sandbox)
    ETA_BASE_URL = 'https://api.preprod.invoicing.eta.gov.eg/api/v1'
    ETA_CLIENT_ID = 'sandbox_client_id'
    ETA_CLIENT_SECRET = 'sandbox_client_secret'
    ETA_ENVIRONMENT = 'sandbox'
    ETA_TIMEOUT = 30
    ETA_AUTO_SUBMIT = False
    
    # Company Information (Test Data)
    COMPANY_NAME = 'شركة اختبار النظام الضريبي'
    COMPANY_TAX_ID = '*********'
    COMPANY_ACTIVITY_CODE = '1000'
    COMPANY_BRANCH_ID = '0'
    COMPANY_ADDRESS = 'عنوان تجريبي'
    COMPANY_PHONE = '0*********0'
    COMPANY_EMAIL = '<EMAIL>'
    
    # Logging
    LOG_LEVEL = 'DEBUG'
    LOG_FILE = 'logs/systemtax_dev.log'
    
    # Cache Configuration (Simple)
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 60
    
    # Debug
    DEBUG = True
    TESTING = False


# Configuration mapping
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}


def get_config(config_name=None):
    """الحصول على إعدادات التطبيق"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    return config.get(config_name, config['default'])
