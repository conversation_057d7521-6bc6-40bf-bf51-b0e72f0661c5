{% extends "base.html" %}

{% block title %}لوحة التحليلات - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-chart-line me-3"></i>
                لوحة التحليلات المالية
            </h1>
            <p class="mb-0 mt-2">تحليلات متقدمة للأداء المالي والمؤشرات الرئيسية</p>
        </div>
        <div>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt me-2"></i>
                    تحديث
                </button>
                <button type="button" class="btn btn-outline-info dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-calendar me-2"></i>
                    الفترة: {{ period_display or 'هذا الشهر' }}
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="?period=today">اليوم</a></li>
                    <li><a class="dropdown-item" href="?period=week">هذا الأسبوع</a></li>
                    <li><a class="dropdown-item" href="?period=month">هذا الشهر</a></li>
                    <li><a class="dropdown-item" href="?period=quarter">هذا الربع</a></li>
                    <li><a class="dropdown-item" href="?period=year">هذا العام</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#customPeriodModal">فترة مخصصة</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Key Performance Indicators -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي الإيرادات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ kpis.total_revenue|currency if kpis else '0.00 ج.م' }}
                        </div>
                        <div class="mt-2">
                            <span class="text-{{ 'success' if (kpis.revenue_growth or 0) >= 0 else 'danger' }} small">
                                <i class="fas fa-arrow-{{ 'up' if (kpis.revenue_growth or 0) >= 0 else 'down' }}"></i>
                                {{ "%.1f"|format(kpis.revenue_growth or 0) }}%
                            </span>
                            <span class="text-muted small">من الفترة السابقة</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            صافي الربح
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ kpis.net_profit|currency if kpis else '0.00 ج.م' }}
                        </div>
                        <div class="mt-2">
                            <span class="text-{{ 'success' if (kpis.profit_growth or 0) >= 0 else 'danger' }} small">
                                <i class="fas fa-arrow-{{ 'up' if (kpis.profit_growth or 0) >= 0 else 'down' }}"></i>
                                {{ "%.1f"|format(kpis.profit_growth or 0) }}%
                            </span>
                            <span class="text-muted small">من الفترة السابقة</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            إجمالي المصروفات
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ kpis.total_expenses|currency if kpis else '0.00 ج.م' }}
                        </div>
                        <div class="mt-2">
                            <span class="text-{{ 'danger' if (kpis.expense_growth or 0) >= 0 else 'success' }} small">
                                <i class="fas fa-arrow-{{ 'up' if (kpis.expense_growth or 0) >= 0 else 'down' }}"></i>
                                {{ "%.1f"|format(kpis.expense_growth or 0) }}%
                            </span>
                            <span class="text-muted small">من الفترة السابقة</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            الرصيد النقدي
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ kpis.cash_balance|currency if kpis else '0.00 ج.م' }}
                        </div>
                        <div class="mt-2">
                            <span class="text-{{ 'success' if (kpis.cash_flow or 0) >= 0 else 'danger' }} small">
                                <i class="fas fa-arrow-{{ 'up' if (kpis.cash_flow or 0) >= 0 else 'down' }}"></i>
                                {{ (kpis.cash_flow or 0)|currency }}
                            </span>
                            <span class="text-muted small">التدفق النقدي</span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <!-- Revenue vs Expenses Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">الإيرادات مقابل المصروفات</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow">
                        <a class="dropdown-item" href="#" onclick="exportChart('revenueExpenseChart')">تصدير كصورة</a>
                        <a class="dropdown-item" href="#" onclick="printChart('revenueExpenseChart')">طباعة</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="revenueExpenseChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Profit Margin Pie Chart -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">توزيع الأرباح</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="profitDistributionChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    <span class="mr-2">
                        <i class="fas fa-circle text-primary"></i> صافي الربح
                    </span>
                    <span class="mr-2">
                        <i class="fas fa-circle text-success"></i> الضرائب
                    </span>
                    <span class="mr-2">
                        <i class="fas fa-circle text-info"></i> المصروفات
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Financial Metrics and Recent Activity -->
<div class="row">
    <!-- Financial Metrics -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">المؤشرات المالية</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-success">
                            <div class="card-body py-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    هامش الربح
                                </div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">
                                    {{ "%.1f"|format(metrics.profit_margin or 0) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-info">
                            <div class="card-body py-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    معدل دوران الأصول
                                </div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">
                                    {{ "%.2f"|format(metrics.asset_turnover or 0) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-warning">
                            <div class="card-body py-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    نسبة السيولة
                                </div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">
                                    {{ "%.2f"|format(metrics.liquidity_ratio or 0) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-left-danger">
                            <div class="card-body py-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    نسبة المديونية
                                </div>
                                <div class="h6 mb-0 font-weight-bold text-gray-800">
                                    {{ "%.1f"|format(metrics.debt_ratio or 0) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">النشاط الأخير</h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    {% if recent_activities %}
                        {% for activity in recent_activities %}
                        <div class="timeline-item">
                            <div class="timeline-marker bg-{{ activity.type_color or 'primary' }}">
                                <i class="fas fa-{{ activity.icon or 'circle' }}"></i>
                            </div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">{{ activity.title }}</h6>
                                <p class="timeline-description">{{ activity.description }}</p>
                                <small class="text-muted">{{ activity.created_at|timeago if activity.created_at else 'منذ قليل' }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-clock fa-2x mb-3"></i>
                            <p>لا توجد أنشطة حديثة</p>
                        </div>
                    {% endif %}
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('dashboard.activity_log') }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع الأنشطة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions and Alerts -->
<div class="row">
    <!-- Quick Actions -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('invoices.new') }}" class="btn btn-primary btn-block">
                            <i class="fas fa-file-invoice mb-2"></i><br>
                            فاتورة جديدة
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('receipts.new') }}" class="btn btn-success btn-block">
                            <i class="fas fa-receipt mb-2"></i><br>
                            إيصال جديد
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('journal.new') }}" class="btn btn-info btn-block">
                            <i class="fas fa-book mb-2"></i><br>
                            قيد يومي
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('reports.trial_balance') }}" class="btn btn-warning btn-block">
                            <i class="fas fa-balance-scale mb-2"></i><br>
                            ميزان المراجعة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Notifications -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">الإشعارات</h6>
                <div class="notification-actions">
                    <button type="button" class="btn btn-sm btn-outline-light" onclick="markAllAsRead()" title="تحديد الكل كمقروء">
                        <i class="fas fa-check-double"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-light" onclick="refreshNotifications()" title="تحديث">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="notifications-container" style="max-height: 400px; overflow-y: auto;">
                    {% if notifications %}
                        {% for notification in notifications %}
                        <div class="notification-item p-3 border-bottom {% if not notification.is_read %}bg-light{% endif %}"
                             data-notification-id="{{ notification.id }}">
                            <div class="d-flex align-items-start">
                                <div class="notification-icon me-3">
                                    <i class="fas fa-{{ notification.get_icon() }} text-{{ notification.get_color() }}"></i>
                                </div>
                                <div class="notification-content flex-grow-1">
                                    <h6 class="notification-title mb-1 {% if not notification.is_read %}fw-bold{% endif %}">
                                        {{ notification.title }}
                                    </h6>
                                    <p class="notification-message mb-1 text-muted small">
                                        {{ notification.message }}
                                    </p>
                                    <div class="notification-meta d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ notification.created_at.strftime('%Y-%m-%d %H:%M') if notification.created_at else 'غير محدد' }}
                                        </small>
                                        {% if notification.action_url %}
                                        <a href="{{ notification.action_url }}"
                                           class="btn btn-sm btn-outline-primary"
                                           onclick="markAsRead({{ notification.id }})">
                                            {{ notification.action_text or 'عرض' }}
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="notification-actions">
                                    {% if not notification.is_read %}
                                    <button type="button"
                                            class="btn btn-sm btn-link text-muted p-0"
                                            onclick="markAsRead({{ notification.id }})"
                                            title="تحديد كمقروء">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                    <div class="text-center text-muted p-4">
                        <i class="fas fa-bell-slash fa-3x mb-3"></i>
                        <p>لا توجد إشعارات جديدة</p>
                    </div>
                    {% endif %}
                </div>

                {% if notifications and notifications|length > 5 %}
                <div class="card-footer text-center">
                    <a href="{{ url_for('notifications.index') }}" class="btn btn-sm btn-outline-primary">
                        عرض جميع الإشعارات
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Custom Period Modal -->
<div class="modal fade" id="customPeriodModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">اختيار فترة مخصصة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="GET">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" name="date_from" class="form-control" value="{{ date_from }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" name="date_to" class="form-control" value="{{ date_to }}">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">تطبيق</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue vs Expenses Chart
const revenueExpenseCtx = document.getElementById('revenueExpenseChart').getContext('2d');
const revenueExpenseChart = new Chart(revenueExpenseCtx, {
    type: 'line',
    data: {
        labels: {{ chart_data.revenue_expense.labels|safe if chart_data else '[]' }},
        datasets: [{
            label: 'الإيرادات',
            data: {{ chart_data.revenue_expense.revenue|safe if chart_data else '[]' }},
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            tension: 0.3
        }, {
            label: 'المصروفات',
            data: {{ chart_data.revenue_expense.expenses|safe if chart_data else '[]' }},
            borderColor: '#e74a3b',
            backgroundColor: 'rgba(231, 74, 59, 0.1)',
            tension: 0.3
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'الإيرادات مقابل المصروفات'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString('ar-EG') + ' ج.م';
                    }
                }
            }
        }
    }
});

// Profit Distribution Chart
const profitDistributionCtx = document.getElementById('profitDistributionChart').getContext('2d');
const profitDistributionChart = new Chart(profitDistributionCtx, {
    type: 'doughnut',
    data: {
        labels: ['صافي الربح', 'الضرائب', 'المصروفات التشغيلية'],
        datasets: [{
            data: {{ chart_data.profit_distribution|safe if chart_data else '[0,0,0]' }},
            backgroundColor: ['#1cc88a', '#36b9cc', '#f6c23e'],
            hoverBackgroundColor: ['#17a673', '#2c9faf', '#dda20a'],
            hoverBorderColor: "rgba(234, 236, 244, 1)",
        }],
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
            }
        }
    }
});

// Auto-refresh dashboard every 5 minutes
setInterval(function() {
    refreshDashboard();
}, 300000);

function refreshDashboard() {
    // Show loading indicator
    const refreshBtn = document.querySelector('[onclick="refreshDashboard()"]');
    const originalText = refreshBtn.innerHTML;
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
    refreshBtn.disabled = true;

    // Fetch updated data
    fetch('/dashboard/refresh', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update KPIs
            updateKPIs(data.kpis);

            // Update charts
            updateCharts(data.chart_data);

            // Update recent activities
            updateRecentActivities(data.recent_activities);

            // Show success message
            showNotification('تم تحديث البيانات بنجاح', 'success');
        } else {
            showNotification('حدث خطأ أثناء التحديث', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('حدث خطأ في الاتصال', 'error');
    })
    .finally(() => {
        // Restore button
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
    });
}

function updateKPIs(kpis) {
    // Update KPI values
    // Implementation would update the displayed values
}

function updateCharts(chartData) {
    // Update chart data
    revenueExpenseChart.data.datasets[0].data = chartData.revenue_expense.revenue;
    revenueExpenseChart.data.datasets[1].data = chartData.revenue_expense.expenses;
    revenueExpenseChart.update();

    profitDistributionChart.data.datasets[0].data = chartData.profit_distribution;
    profitDistributionChart.update();
}

function updateRecentActivities(activities) {
    // Update recent activities list
    // Implementation would update the timeline
}

function showNotification(message, type) {
    // Show toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '9999';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

function exportChart(chartId) {
    const chart = Chart.getChart(chartId);
    const url = chart.toBase64Image();
    const link = document.createElement('a');
    link.download = `${chartId}.png`;
    link.href = url;
    link.click();
}

function printChart(chartId) {
    const chart = Chart.getChart(chartId);
    const url = chart.toBase64Image();
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head><title>طباعة الرسم البياني</title></head>
            <body style="text-align: center;">
                <img src="${url}" style="max-width: 100%;">
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// Enhanced Notifications Functions
function markAsRead(notificationId) {
    fetch(`/notifications/${notificationId}/mark-read`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const notificationElement = document.querySelector(`[data-notification-id="${notificationId}"]`);
            if (notificationElement) {
                notificationElement.classList.remove('bg-light');
                notificationElement.querySelector('.notification-title').classList.remove('fw-bold');
                const actionBtn = notificationElement.querySelector('.notification-actions button');
                if (actionBtn) {
                    actionBtn.remove();
                }
            }
            updateNotificationBadge();
        }
    })
    .catch(error => {
        console.error('Error marking notification as read:', error);
    });
}

function markAllAsRead() {
    fetch('/notifications/mark-all-read', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const unreadNotifications = document.querySelectorAll('.notification-item.bg-light');
            unreadNotifications.forEach(notification => {
                notification.classList.remove('bg-light');
                notification.querySelector('.notification-title').classList.remove('fw-bold');
                const actionBtn = notification.querySelector('.notification-actions button');
                if (actionBtn) {
                    actionBtn.remove();
                }
            });
            updateNotificationBadge();
            showNotification('تم تحديد جميع الإشعارات كمقروءة', 'success');
        }
    })
    .catch(error => {
        console.error('Error marking all notifications as read:', error);
    });
}

function refreshNotifications() {
    const container = document.getElementById('notifications-container');
    const originalContent = container.innerHTML;

    // Show loading
    container.innerHTML = '<div class="text-center p-4"><div class="spinner-border text-primary" role="status"></div></div>';

    fetch('/notifications/refresh', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            container.innerHTML = data.html;
            updateNotificationBadge();
        } else {
            container.innerHTML = originalContent;
            showNotification('فشل في تحديث الإشعارات', 'error');
        }
    })
    .catch(error => {
        console.error('Error refreshing notifications:', error);
        container.innerHTML = originalContent;
        showNotification('حدث خطأ في تحديث الإشعارات', 'error');
    });
}

function updateNotificationBadge() {
    fetch('/notifications/unread-count', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        const badge = document.querySelector('.notification-badge');
        if (badge) {
            if (data.count > 0) {
                badge.textContent = data.count;
                badge.style.display = 'inline-block';
            } else {
                badge.style.display = 'none';
            }
        }
    })
    .catch(error => {
        console.error('Error updating notification badge:', error);
    });
}

// Auto-refresh notifications every 30 seconds
setInterval(function() {
    updateNotificationBadge();
}, 30000);
</script>
{% endblock %}

{% block extra_css %}
<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.timeline-content {
    background: #f8f9fc;
    padding: 10px 15px;
    border-radius: 5px;
    border-left: 3px solid #e3e6f0;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: bold;
}

.timeline-description {
    margin-bottom: 5px;
    font-size: 13px;
    color: #5a5c69;
}

.chart-area {
    position: relative;
    height: 320px;
}

.chart-pie {
    position: relative;
    height: 245px;
}

.btn-block {
    display: block;
    width: 100%;
    text-align: center;
    padding: 15px;
    border-radius: 10px;
    text-decoration: none;
    color: white;
    transition: all 0.3s;
}

.btn-block:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    color: white;
    text-decoration: none;
}

.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700;
}

.text-gray-800 {
    color: #5a5c69;
}

.text-gray-300 {
    color: #dddfeb;
}
</style>
{% endblock %}
