/**
 * Enhanced UI JavaScript for SystemTax
 * Provides advanced interactive features and smooth user experience
 */

class SystemTaxUI {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupAnimations();
        this.setupFormValidation();
        this.setupTooltips();
        this.setupAutoSave();
    }

    setupEventListeners() {
        // Enhanced form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Enhanced button clicks
        document.addEventListener('click', this.handleButtonClick.bind(this));
        
        // Enhanced input changes
        document.addEventListener('input', this.handleInputChange.bind(this));
        
        // Enhanced table interactions
        document.addEventListener('click', this.handleTableClick.bind(this));
        
        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
        
        // Window resize
        window.addEventListener('resize', this.handleResize.bind(this));
    }

    initializeComponents() {
        this.initializeDataTables();
        this.initializeDatePickers();
        this.initializeSelect2();
        this.initializeCharts();
        this.initializeModals();
        this.initializeToasts();
    }

    setupAnimations() {
        // Fade in elements on page load
        const fadeElements = document.querySelectorAll('.fade-in');
        fadeElements.forEach((element, index) => {
            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 100);
        });

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.animate-on-scroll').forEach(el => {
            observer.observe(el);
        });
    }

    setupFormValidation() {
        const forms = document.querySelectorAll('form[data-validate="true"]');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm(form)) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }

    validateForm(form) {
        let isValid = true;
        const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                this.showFieldError(input, 'هذا الحقل مطلوب');
                isValid = false;
            } else {
                this.clearFieldError(input);
            }
        });

        // Custom validation rules
        const emailInputs = form.querySelectorAll('input[type="email"]');
        emailInputs.forEach(input => {
            if (input.value && !this.isValidEmail(input.value)) {
                this.showFieldError(input, 'البريد الإلكتروني غير صحيح');
                isValid = false;
            }
        });

        const numberInputs = form.querySelectorAll('input[type="number"]');
        numberInputs.forEach(input => {
            if (input.value && isNaN(input.value)) {
                this.showFieldError(input, 'يجب أن يكون رقماً صحيحاً');
                isValid = false;
            }
        });

        return isValid;
    }

    showFieldError(field, message) {
        field.classList.add('is-invalid');
        
        let errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            field.parentNode.appendChild(errorDiv);
        }
        errorDiv.textContent = message;
    }

    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    setupTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    setupAutoSave() {
        const autoSaveForms = document.querySelectorAll('form[data-autosave="true"]');
        autoSaveForms.forEach(form => {
            let saveTimeout;
            
            form.addEventListener('input', () => {
                clearTimeout(saveTimeout);
                saveTimeout = setTimeout(() => {
                    this.autoSaveForm(form);
                }, 2000); // Auto-save after 2 seconds of inactivity
            });
        });
    }

    autoSaveForm(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        
        // Save to localStorage
        const formId = form.id || 'autosave_form';
        localStorage.setItem(`autosave_${formId}`, JSON.stringify(data));
        
        // Show auto-save indicator
        this.showAutoSaveIndicator();
    }

    showAutoSaveIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'auto-save-indicator';
        indicator.innerHTML = '<i class="fas fa-check-circle"></i> تم الحفظ التلقائي';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s;
        `;
        
        document.body.appendChild(indicator);
        
        setTimeout(() => indicator.style.opacity = '1', 100);
        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => indicator.remove(), 300);
        }, 2000);
    }

    handleFormSubmit(e) {
        const form = e.target;
        if (form.dataset.ajax === 'true') {
            e.preventDefault();
            this.submitFormAjax(form);
        }
    }

    async submitFormAjax(form) {
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="loading-spinner"></span> جاري الحفظ...';
        
        try {
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: form.method || 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showToast('تم الحفظ بنجاح', 'success');
                if (result.redirect) {
                    window.location.href = result.redirect;
                }
            } else {
                this.showToast(result.message || 'حدث خطأ أثناء الحفظ', 'error');
            }
        } catch (error) {
            this.showToast('حدث خطأ في الاتصال', 'error');
        } finally {
            // Restore button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    }

    handleButtonClick(e) {
        const button = e.target.closest('button');
        if (!button) return;

        // Handle confirmation buttons
        if (button.dataset.confirm) {
            if (!confirm(button.dataset.confirm)) {
                e.preventDefault();
                return;
            }
        }

        // Handle loading buttons
        if (button.dataset.loading) {
            this.showButtonLoading(button);
        }

        // Handle copy buttons
        if (button.dataset.copy) {
            this.copyToClipboard(button.dataset.copy);
            this.showToast('تم النسخ إلى الحافظة', 'success');
        }
    }

    showButtonLoading(button) {
        const originalText = button.innerHTML;
        button.disabled = true;
        button.innerHTML = '<span class="loading-spinner"></span> جاري التحميل...';
        
        // Auto-restore after 5 seconds if not manually restored
        setTimeout(() => {
            if (button.disabled) {
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }, 5000);
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
        } catch (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
        }
    }

    handleInputChange(e) {
        const input = e.target;
        
        // Auto-format numbers
        if (input.dataset.format === 'currency') {
            this.formatCurrency(input);
        }
        
        // Auto-calculate totals
        if (input.dataset.calculate) {
            this.calculateTotals(input.closest('form'));
        }
        
        // Live search
        if (input.dataset.search) {
            this.performLiveSearch(input);
        }
    }

    formatCurrency(input) {
        let value = input.value.replace(/[^\d.]/g, '');
        if (value) {
            const number = parseFloat(value);
            if (!isNaN(number)) {
                input.value = number.toLocaleString('ar-EG', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            }
        }
    }

    calculateTotals(form) {
        const amountInputs = form.querySelectorAll('input[data-amount]');
        let total = 0;
        
        amountInputs.forEach(input => {
            const value = parseFloat(input.value.replace(/[^\d.]/g, '')) || 0;
            total += value;
        });
        
        const totalInput = form.querySelector('input[data-total]');
        if (totalInput) {
            totalInput.value = total.toLocaleString('ar-EG', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
    }

    performLiveSearch(input) {
        const searchTerm = input.value.trim();
        const targetTable = document.querySelector(input.dataset.search);
        
        if (!targetTable) return;
        
        const rows = targetTable.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const matches = text.includes(searchTerm.toLowerCase());
            row.style.display = matches ? '' : 'none';
        });
    }

    handleTableClick(e) {
        const cell = e.target.closest('td');
        if (!cell) return;
        
        // Handle sortable columns
        const header = e.target.closest('th');
        if (header && header.dataset.sort) {
            this.sortTable(header);
        }
        
        // Handle row selection
        const row = e.target.closest('tr');
        if (row && row.dataset.selectable) {
            this.toggleRowSelection(row);
        }
    }

    sortTable(header) {
        const table = header.closest('table');
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(header.parentNode.children).indexOf(header);
        const isAscending = header.classList.contains('sort-asc');
        
        // Clear all sort classes
        header.parentNode.querySelectorAll('th').forEach(th => {
            th.classList.remove('sort-asc', 'sort-desc');
        });
        
        // Add appropriate sort class
        header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
        
        // Sort rows
        rows.sort((a, b) => {
            const aText = a.children[columnIndex].textContent.trim();
            const bText = b.children[columnIndex].textContent.trim();
            
            const aNum = parseFloat(aText.replace(/[^\d.-]/g, ''));
            const bNum = parseFloat(bText.replace(/[^\d.-]/g, ''));
            
            let comparison = 0;
            if (!isNaN(aNum) && !isNaN(bNum)) {
                comparison = aNum - bNum;
            } else {
                comparison = aText.localeCompare(bText, 'ar');
            }
            
            return isAscending ? -comparison : comparison;
        });
        
        // Reorder rows in DOM
        rows.forEach(row => tbody.appendChild(row));
    }

    toggleRowSelection(row) {
        row.classList.toggle('selected');
        const checkbox = row.querySelector('input[type="checkbox"]');
        if (checkbox) {
            checkbox.checked = row.classList.contains('selected');
        }
    }

    handleKeyboardShortcuts(e) {
        // Ctrl+S for save
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const saveBtn = document.querySelector('button[type="submit"]');
            if (saveBtn) saveBtn.click();
        }
        
        // Ctrl+N for new
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            const newBtn = document.querySelector('a[href*="/new"]');
            if (newBtn) newBtn.click();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                if (modal) modal.hide();
            }
        }
    }

    handleResize() {
        // Responsive table handling
        const tables = document.querySelectorAll('.table-responsive');
        tables.forEach(table => {
            if (window.innerWidth < 768) {
                table.classList.add('table-mobile');
            } else {
                table.classList.remove('table-mobile');
            }
        });
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        const container = document.querySelector('.toast-container') || this.createToastContainer();
        container.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // Remove toast after it's hidden
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }

    createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '9999';
        document.body.appendChild(container);
        return container;
    }

    // Initialize specific components
    initializeDataTables() {
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(table => {
            // Add DataTables functionality if library is loaded
            if (typeof DataTable !== 'undefined') {
                new DataTable(table, {
                    language: {
                        url: '/static/js/datatables-arabic.json'
                    },
                    responsive: true,
                    pageLength: 25
                });
            }
        });
    }

    initializeDatePickers() {
        const dateInputs = document.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            // Add date picker enhancements
            input.addEventListener('focus', () => {
                input.showPicker?.();
            });
        });
    }

    initializeSelect2() {
        const selects = document.querySelectorAll('.select2');
        selects.forEach(select => {
            // Add Select2 functionality if library is loaded
            if (typeof $ !== 'undefined' && $.fn.select2) {
                $(select).select2({
                    theme: 'bootstrap-5',
                    language: 'ar'
                });
            }
        });
    }

    initializeCharts() {
        // Chart initialization is handled in specific pages
    }

    initializeModals() {
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            modal.addEventListener('show.bs.modal', () => {
                const form = modal.querySelector('form');
                if (form) {
                    form.reset();
                    form.classList.remove('was-validated');
                }
            });
        });
    }

    initializeToasts() {
        // Auto-show toasts that are present on page load
        const toasts = document.querySelectorAll('.toast');
        toasts.forEach(toast => {
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
        });
    }
}

// Initialize the enhanced UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.systemTaxUI = new SystemTaxUI();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SystemTaxUI;
}
