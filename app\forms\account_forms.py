"""
Forms for account management
"""

from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, TextAreaField, BooleanField, HiddenField
from wtforms.validators import DataRequired, Length, Optional, ValidationError
from wtforms.widgets import TextArea
from app.models.account import Account


class AccountForm(FlaskForm):
    """Form for creating and editing accounts"""
    
    code = StringField(
        'رمز الحساب',
        validators=[
            DataRequired(message='رمز الحساب مطلوب'),
            Length(min=1, max=20, message='رمز الحساب يجب أن يكون بين 1 و 20 حرف')
        ],
        render_kw={
            'placeholder': 'مثال: 1100',
            'class': 'form-control',
            'dir': 'ltr'
        }
    )
    
    name = StringField(
        'اسم الحساب',
        validators=[
            DataRequired(message='اسم الحساب مطلوب'),
            Length(min=2, max=200, message='اسم الحساب يجب أن يكون بين 2 و 200 حرف')
        ],
        render_kw={
            'placeholder': 'اسم الحساب بالعربية',
            'class': 'form-control'
        }
    )
    
    name_en = StringField(
        'الاسم بالإنجليزية',
        validators=[
            Optional(),
            Length(max=200, message='الاسم بالإنجليزية يجب ألا يزيد عن 200 حرف')
        ],
        render_kw={
            'placeholder': 'Account name in English',
            'class': 'form-control',
            'dir': 'ltr'
        }
    )
    
    type = SelectField(
        'نوع الحساب',
        choices=[
            ('', 'اختر نوع الحساب'),
            ('Asset', 'أصول'),
            ('Liability', 'خصوم'),
            ('Equity', 'رأس المال'),
            ('Income', 'إيرادات'),
            ('Expense', 'مصروفات')
        ],
        validators=[DataRequired(message='نوع الحساب مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    parent_id = SelectField(
        'الحساب الأب',
        choices=[],
        validators=[Optional()],
        coerce=lambda x: x if x else None,
        render_kw={'class': 'form-select'}
    )
    
    description = TextAreaField(
        'الوصف',
        validators=[
            Optional(),
            Length(max=500, message='الوصف يجب ألا يزيد عن 500 حرف')
        ],
        render_kw={
            'placeholder': 'وصف اختياري للحساب',
            'class': 'form-control',
            'rows': 3
        }
    )
    
    is_active = BooleanField(
        'حساب نشط',
        default=True,
        render_kw={'class': 'form-check-input'}
    )
    
    def __init__(self, account=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.account = account
        
        # Populate parent account choices
        self.populate_parent_choices()
    
    def populate_parent_choices(self):
        """Populate parent account choices"""
        choices = [('', 'لا يوجد (حساب رئيسي)')]
        
        # Get all accounts except the current one (if editing)
        query = Account.query.filter_by(is_active=True)
        if self.account:
            query = query.filter(Account.id != self.account.id)
        
        accounts = query.order_by(Account.code).all()
        
        for account in accounts:
            # Don't allow circular references
            if self.account and self.account.is_ancestor_of(account):
                continue
                
            choices.append((
                str(account.id),
                f"{account.code} - {account.name}"
            ))
        
        self.parent_id.choices = choices
    
    def validate_code(self, field):
        """Validate account code uniqueness"""
        query = Account.query.filter_by(code=field.data)
        if self.account:
            query = query.filter(Account.id != self.account.id)
        
        if query.first():
            raise ValidationError('رمز الحساب موجود بالفعل')
    
    def validate_parent_id(self, field):
        """Validate parent account selection"""
        if field.data:
            parent = Account.query.get(field.data)
            if not parent:
                raise ValidationError('الحساب الأب المحدد غير موجود')
            
            # Check if parent has the same type (for consistency)
            if self.type.data and parent.type != self.type.data:
                raise ValidationError(
                    f'نوع الحساب الأب ({parent.get_type_display()}) '
                    f'يجب أن يكون نفس نوع الحساب الفرعي ({dict(self.type.choices)[self.type.data]})'
                )
            
            # Prevent circular references
            if self.account and self.account.is_ancestor_of(parent):
                raise ValidationError('لا يمكن جعل حساب فرعي كحساب أب')


class AccountSearchForm(FlaskForm):
    """Form for searching accounts"""
    
    search = StringField(
        'البحث',
        validators=[Optional()],
        render_kw={
            'placeholder': 'البحث في رمز أو اسم الحساب...',
            'class': 'form-control'
        }
    )
    
    type = SelectField(
        'نوع الحساب',
        choices=[
            ('', 'جميع الأنواع'),
            ('Asset', 'أصول'),
            ('Liability', 'خصوم'),
            ('Equity', 'رأس المال'),
            ('Income', 'إيرادات'),
            ('Expense', 'مصروفات')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    status = SelectField(
        'الحالة',
        choices=[
            ('', 'جميع الحالات'),
            ('active', 'نشط'),
            ('inactive', 'غير نشط')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    parent_only = BooleanField(
        'الحسابات الرئيسية فقط',
        render_kw={'class': 'form-check-input'}
    )


class AccountImportForm(FlaskForm):
    """Form for importing accounts from file"""
    
    import_type = SelectField(
        'نوع الاستيراد',
        choices=[
            ('csv', 'ملف CSV'),
            ('excel', 'ملف Excel'),
            ('json', 'ملف JSON')
        ],
        validators=[DataRequired(message='نوع الاستيراد مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    file_content = TextAreaField(
        'محتوى الملف',
        validators=[DataRequired(message='محتوى الملف مطلوب')],
        render_kw={
            'placeholder': 'الصق محتوى الملف هنا...',
            'class': 'form-control',
            'rows': 10
        }
    )
    
    update_existing = BooleanField(
        'تحديث الحسابات الموجودة',
        render_kw={'class': 'form-check-input'}
    )
    
    create_parents = BooleanField(
        'إنشاء الحسابات الأب تلقائياً',
        default=True,
        render_kw={'class': 'form-check-input'}
    )


class AccountMergeForm(FlaskForm):
    """Form for merging accounts"""
    
    source_account_id = SelectField(
        'الحساب المصدر',
        choices=[],
        validators=[DataRequired(message='الحساب المصدر مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    target_account_id = SelectField(
        'الحساب الهدف',
        choices=[],
        validators=[DataRequired(message='الحساب الهدف مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    merge_type = SelectField(
        'نوع الدمج',
        choices=[
            ('move_transactions', 'نقل الحركات فقط'),
            ('merge_and_delete', 'دمج وحذف الحساب المصدر'),
            ('merge_and_deactivate', 'دمج وإلغاء تفعيل الحساب المصدر')
        ],
        validators=[DataRequired(message='نوع الدمج مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    confirmation = BooleanField(
        'أؤكد أنني أفهم أن هذا الإجراء لا يمكن التراجع عنه',
        validators=[DataRequired(message='يجب تأكيد الإجراء')],
        render_kw={'class': 'form-check-input'}
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.populate_account_choices()
    
    def populate_account_choices(self):
        """Populate account choices for merging"""
        choices = [('', 'اختر الحساب')]
        
        accounts = Account.query.filter_by(is_active=True).order_by(Account.code).all()
        
        for account in accounts:
            choices.append((
                str(account.id),
                f"{account.code} - {account.name}"
            ))
        
        self.source_account_id.choices = choices
        self.target_account_id.choices = choices
    
    def validate_target_account_id(self, field):
        """Validate that target account is different from source"""
        if field.data == self.source_account_id.data:
            raise ValidationError('الحساب الهدف يجب أن يكون مختلف عن الحساب المصدر')


class AccountBulkActionForm(FlaskForm):
    """Form for bulk actions on accounts"""
    
    action = SelectField(
        'الإجراء',
        choices=[
            ('', 'اختر الإجراء'),
            ('activate', 'تفعيل'),
            ('deactivate', 'إلغاء تفعيل'),
            ('delete', 'حذف'),
            ('export', 'تصدير'),
            ('change_type', 'تغيير النوع')
        ],
        validators=[DataRequired(message='الإجراء مطلوب')],
        render_kw={'class': 'form-select'}
    )
    
    new_type = SelectField(
        'النوع الجديد',
        choices=[
            ('', 'اختر النوع'),
            ('Asset', 'أصول'),
            ('Liability', 'خصوم'),
            ('Equity', 'رأس المال'),
            ('Income', 'إيرادات'),
            ('Expense', 'مصروفات')
        ],
        validators=[Optional()],
        render_kw={'class': 'form-select'}
    )
    
    selected_accounts = HiddenField(
        'الحسابات المحددة',
        validators=[DataRequired(message='يجب تحديد حساب واحد على الأقل')]
    )
    
    confirmation = BooleanField(
        'أؤكد تنفيذ هذا الإجراء',
        validators=[DataRequired(message='يجب تأكيد الإجراء')],
        render_kw={'class': 'form-check-input'}
    )
    
    def validate_new_type(self, field):
        """Validate new type when changing account type"""
        if self.action.data == 'change_type' and not field.data:
            raise ValidationError('النوع الجديد مطلوب عند تغيير نوع الحساب')
