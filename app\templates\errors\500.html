{% extends "base.html" %}

{% block title %}خطأ في الخادم - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="error-page">
                <h1 class="display-1 text-danger">500</h1>
                <h2 class="mb-4">خطأ في الخادم</h2>
                <p class="lead mb-4">عذراً، حدث خطأ غير متوقع في الخادم. يرجى المحاولة مرة أخرى لاحقاً.</p>
                
                <div class="mb-4">
                    <i class="fas fa-exclamation-triangle fa-5x text-danger"></i>
                </div>
                
                <div class="d-flex justify-content-center gap-3">
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary">
                        <i class="fas fa-home me-2"></i>
                        العودة للرئيسية
                    </a>
                    <button onclick="location.reload()" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-2"></i>
                        إعادة المحاولة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .error-page {
        padding: 4rem 0;
    }
    
    .display-1 {
        font-size: 8rem;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }
</style>
{% endblock %}
