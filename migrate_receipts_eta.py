#!/usr/bin/env python3
"""
Migration script to add ETA eReceipt fields to existing receipts table
سكريبت ترحيل لإضافة حقول الإيصال الإلكتروني لجدول الإيصالات الموجود
"""

import sqlite3
import os
from datetime import datetime

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    db_path = 'instance/systemtax.db'
    if os.path.exists(db_path):
        backup_path = f'instance/systemtax_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        return backup_path
    return None

def migrate_receipts_table():
    """ترحيل جدول الإيصالات لإضافة حقول ETA"""
    db_path = 'instance/systemtax.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # التحقق من وجود الحقول الجديدة
        cursor.execute("PRAGMA table_info(receipts)")
        columns = [column[1] for column in cursor.fetchall()]
        
        # قائمة الحقول الجديدة المطلوبة لـ ETA
        new_columns = [
            ('previous_uuid', 'VARCHAR(100)'),
            ('reference_old_uuid', 'VARCHAR(100)'),
            ('document_type_name', 'VARCHAR(50) DEFAULT "s"'),
            ('document_type_version', 'VARCHAR(10) DEFAULT "1.2"'),
            ('datetime_issued', 'DATETIME'),
            ('exchange_rate', 'DECIMAL(10,5) DEFAULT 1.0'),
            ('branch_code', 'VARCHAR(50)'),
            ('device_serial_number', 'VARCHAR(100)'),
            ('activity_code', 'VARCHAR(10)'),
            ('buyer_type', 'VARCHAR(1)'),
            ('buyer_id', 'VARCHAR(30)'),
            ('buyer_name', 'VARCHAR(100)'),
            ('buyer_mobile', 'VARCHAR(30)'),
            ('payment_number', 'VARCHAR(30)'),
            ('total_sales', 'DECIMAL(14,5) DEFAULT 0'),
            ('total_commercial_discount', 'DECIMAL(14,5) DEFAULT 0'),
            ('total_items_discount', 'DECIMAL(14,5) DEFAULT 0'),
            ('net_amount', 'DECIMAL(14,5) DEFAULT 0'),
            ('fees_amount', 'DECIMAL(14,5) DEFAULT 0'),
            ('total_amount', 'DECIMAL(14,5) DEFAULT 0'),
            ('adjustment', 'DECIMAL(14,5) DEFAULT 0'),
            ('sales_order_name_code', 'VARCHAR(200)'),
            ('order_delivery_mode', 'VARCHAR(30)'),
            ('gross_weight', 'DECIMAL(10,5)'),
            ('net_weight', 'DECIMAL(10,5)'),
            ('eta_qr_code', 'TEXT'),
            ('eta_qr_image_path', 'VARCHAR(500)')
        ]
        
        # إضافة الحقول المفقودة
        added_columns = []
        for column_name, column_type in new_columns:
            if column_name not in columns:
                try:
                    sql = f"ALTER TABLE receipts ADD COLUMN {column_name} {column_type}"
                    cursor.execute(sql)
                    added_columns.append(column_name)
                    print(f"✅ تم إضافة العمود: {column_name}")
                except sqlite3.Error as e:
                    print(f"❌ خطأ في إضافة العمود {column_name}: {e}")
        
        # تحديث الإيصالات الموجودة بقيم افتراضية
        if added_columns:
            try:
                # تحديث datetime_issued للإيصالات الموجودة
                cursor.execute("""
                    UPDATE receipts 
                    SET datetime_issued = datetime(receipt_date || ' 12:00:00')
                    WHERE datetime_issued IS NULL
                """)
                
                # تحديث total_amount للإيصالات الموجودة
                cursor.execute("""
                    UPDATE receipts 
                    SET total_amount = amount
                    WHERE total_amount = 0 OR total_amount IS NULL
                """)
                
                # تحديث net_amount للإيصالات الموجودة
                cursor.execute("""
                    UPDATE receipts 
                    SET net_amount = amount
                    WHERE net_amount = 0 OR net_amount IS NULL
                """)
                
                # تحديث total_sales للإيصالات الموجودة
                cursor.execute("""
                    UPDATE receipts 
                    SET total_sales = amount
                    WHERE total_sales = 0 OR total_sales IS NULL
                """)
                
                print("✅ تم تحديث البيانات الموجودة")
                
            except sqlite3.Error as e:
                print(f"❌ خطأ في تحديث البيانات: {e}")
        
        conn.commit()

        if added_columns:
            print(f"✅ تم إضافة {len(added_columns)} عمود جديد بنجاح")
        else:
            print("ℹ️ جميع الأعمدة موجودة مسبقاً")

        conn.close()
        return True
            
    except Exception as e:
        print(f"❌ خطأ في ترحيل قاعدة البيانات: {e}")
        return False

def create_new_tables():
    """إنشاء الجداول الجديدة للإيصال الإلكتروني"""
    db_path = 'instance/systemtax.db'

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # إنشاء جدول بنود الإيصال
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS receipt_items (
                id VARCHAR(36) PRIMARY KEY,
                receipt_id VARCHAR(36) NOT NULL,
                internal_code VARCHAR(50) NOT NULL,
                description VARCHAR(500) NOT NULL,
                item_type VARCHAR(30) NOT NULL,
                item_code VARCHAR(100) NOT NULL,
                unit_type VARCHAR(30) NOT NULL,
                quantity DECIMAL(10,5) NOT NULL,
                unit_price DECIMAL(14,5) NOT NULL,
                net_sale DECIMAL(14,5) NOT NULL,
                total_sale DECIMAL(14,5) NOT NULL,
                total DECIMAL(14,5) NOT NULL,
                commercial_discount_amount DECIMAL(14,5) DEFAULT 0,
                commercial_discount_rate DECIMAL(5,2) DEFAULT 0,
                item_discount_amount DECIMAL(14,5) DEFAULT 0,
                item_discount_rate DECIMAL(5,2) DEFAULT 0,
                additional_commercial_discount DECIMAL(14,5) DEFAULT 0,
                additional_item_discount DECIMAL(14,5) DEFAULT 0,
                value_difference DECIMAL(14,5) DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (receipt_id) REFERENCES receipts (id)
            )
        """)
        
        # إنشاء جدول إجمالي الضرائب
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS receipt_tax_totals (
                id VARCHAR(36) PRIMARY KEY,
                receipt_id VARCHAR(36) NOT NULL,
                tax_type VARCHAR(30) NOT NULL,
                amount DECIMAL(14,5) NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (receipt_id) REFERENCES receipts (id)
            )
        """)
        
        # إنشاء جدول ضرائب البنود
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS receipt_item_taxes (
                id VARCHAR(36) PRIMARY KEY,
                receipt_item_id VARCHAR(36) NOT NULL,
                tax_type VARCHAR(30) NOT NULL,
                amount DECIMAL(14,5) NOT NULL,
                sub_type VARCHAR(50) NOT NULL,
                rate DECIMAL(5,2),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (receipt_item_id) REFERENCES receipt_items (id)
            )
        """)
        
        # إنشاء الفهارس
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_receipt_items_receipt_id ON receipt_items(receipt_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_receipt_tax_totals_receipt_id ON receipt_tax_totals(receipt_id)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_receipt_item_taxes_item_id ON receipt_item_taxes(receipt_item_id)")
        
        conn.commit()
        conn.close()
        
        # إنشاء جدول إعدادات النظام
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key VARCHAR(100) UNIQUE NOT NULL,
                value TEXT,
                data_type VARCHAR(20) DEFAULT 'string',
                category VARCHAR(50) DEFAULT 'general',
                description TEXT,
                is_encrypted BOOLEAN DEFAULT 0,
                is_system BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_by VARCHAR(100)
            )
        """)

        # إنشاء فهرس للبحث السريع
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_system_settings_key ON system_settings(key)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_system_settings_category ON system_settings(category)")

        conn.commit()
        conn.close()

        print("✅ تم إنشاء الجداول الجديدة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول الجديدة: {e}")
        return False

def main():
    """الدالة الرئيسية للترحيل"""
    print("🚀 بدء ترحيل قاعدة البيانات لدعم الإيصال الإلكتروني...")
    print("=" * 60)
    
    # إنشاء نسخة احتياطية
    backup_path = backup_database()
    
    # ترحيل جدول الإيصالات
    print("\n📋 ترحيل جدول الإيصالات...")
    if not migrate_receipts_table():
        print("❌ فشل في ترحيل جدول الإيصالات")
        return False

    # إنشاء الجداول الجديدة
    print("\n🗄️ إنشاء الجداول الجديدة...")
    if not create_new_tables():
        print("❌ فشل في إنشاء الجداول الجديدة")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 تم ترحيل قاعدة البيانات بنجاح!")
    print("✅ النظام الآن يدعم الإيصال الإلكتروني بالكامل")
    
    if backup_path:
        print(f"💾 النسخة الاحتياطية محفوظة في: {backup_path}")
    
    return True

if __name__ == "__main__":
    main()
