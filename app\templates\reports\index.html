{% extends "base.html" %}

{% block title %}التقارير المالية - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-chart-bar me-3"></i>
                التقارير المالية والضريبية
            </h1>
            <p class="mb-0 mt-2">تقارير شاملة للبيانات المالية والضريبية</p>
        </div>
        <div>
            <div class="btn-group">
                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="fas fa-plus me-2"></i>
                    تقرير جديد
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="{{ url_for('reports.trial_balance') }}">ميزان المراجعة</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.income_statement') }}">قائمة الدخل</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.balance_sheet') }}">الميزانية العمومية</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.tax_report') }}">التقرير الضريبي</a></li>
                    <li><a class="dropdown-item" href="{{ url_for('reports.vat_report') }}">تقرير ضريبة القيمة المضافة</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Report Categories -->
<div class="row mb-4">
    <!-- Financial Reports -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    التقارير المالية
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('reports.trial_balance') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">ميزان المراجعة</h6>
                            <i class="fas fa-balance-scale"></i>
                        </div>
                        <p class="mb-1">عرض أرصدة جميع الحسابات</p>
                    </a>
                    
                    <a href="{{ url_for('reports.income_statement') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">قائمة الدخل</h6>
                            <i class="fas fa-chart-area"></i>
                        </div>
                        <p class="mb-1">الإيرادات والمصروفات والأرباح</p>
                    </a>
                    
                    <a href="{{ url_for('reports.balance_sheet') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">الميزانية العمومية</h6>
                            <i class="fas fa-building"></i>
                        </div>
                        <p class="mb-1">الأصول والخصوم وحقوق الملكية</p>
                    </a>
                    
                    <a href="{{ url_for('reports.cash_flow') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">قائمة التدفقات النقدية</h6>
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <p class="mb-1">التدفقات النقدية الداخلة والخارجة</p>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Tax Reports -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-invoice-dollar me-2"></i>
                    التقارير الضريبية
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('reports.tax_report') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">التقرير الضريبي الشهري</h6>
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <p class="mb-1">تقرير الضرائب الشهرية</p>
                    </a>
                    
                    <a href="{{ url_for('reports.vat_report') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">تقرير ضريبة القيمة المضافة</h6>
                            <i class="fas fa-percentage"></i>
                        </div>
                        <p class="mb-1">ضريبة القيمة المضافة على المبيعات</p>
                    </a>
                    
                    <a href="{{ url_for('reports.withholding_tax') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">تقرير الضريبة المقتطعة</h6>
                            <i class="fas fa-cut"></i>
                        </div>
                        <p class="mb-1">الضرائب المقتطعة من المنبع</p>
                    </a>
                    
                    <a href="{{ url_for('reports.annual_tax') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">التقرير الضريبي السنوي</h6>
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <p class="mb-1">الإقرار الضريبي السنوي</p>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Operational Reports -->
    <div class="col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    التقارير التشغيلية
                </h5>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ url_for('reports.sales_report') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">تقرير المبيعات</h6>
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <p class="mb-1">تحليل المبيعات والعملاء</p>
                    </a>
                    
                    <a href="{{ url_for('reports.purchases_report') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">تقرير المشتريات</h6>
                            <i class="fas fa-truck"></i>
                        </div>
                        <p class="mb-1">تحليل المشتريات والموردين</p>
                    </a>
                    
                    <a href="{{ url_for('reports.aging_report') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">تقرير أعمار الديون</h6>
                            <i class="fas fa-clock"></i>
                        </div>
                        <p class="mb-1">تحليل أعمار ديون العملاء</p>
                    </a>
                    
                    <a href="{{ url_for('reports.journal_report') }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">تقرير القيود اليومية</h6>
                            <i class="fas fa-book"></i>
                        </div>
                        <p class="mb-1">سجل القيود المحاسبية</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">إجمالي الإيرادات</h5>
                <h3 class="text-primary">{{ stats.total_revenue|currency if stats else '0.00 ج.م' }}</h3>
                <small class="text-muted">هذا الشهر</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">إجمالي المصروفات</h5>
                <h3 class="text-danger">{{ stats.total_expenses|currency if stats else '0.00 ج.م' }}</h3>
                <small class="text-muted">هذا الشهر</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">صافي الربح</h5>
                <h3 class="text-success">{{ stats.net_profit|currency if stats else '0.00 ج.م' }}</h3>
                <small class="text-muted">هذا الشهر</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">الضرائب المستحقة</h5>
                <h3 class="text-info">{{ stats.taxes_due|currency if stats else '0.00 ج.م' }}</h3>
                <small class="text-muted">هذا الشهر</small>
            </div>
        </div>
    </div>
</div>

<!-- Recent Reports -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-history me-2"></i>
            التقارير الأخيرة
        </h5>
    </div>
    <div class="card-body">
        {% if recent_reports %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>نوع التقرير</th>
                        <th>الفترة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>أنشأ بواسطة</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for report in recent_reports %}
                    <tr>
                        <td>
                            <i class="fas fa-{{ report.icon }} me-2"></i>
                            {{ report.name }}
                        </td>
                        <td>{{ report.period }}</td>
                        <td>{{ report.created_at|datetime }}</td>
                        <td>{{ report.created_by }}</td>
                        <td>
                            <span class="badge bg-{{ 'success' if report.status == 'completed' else 'warning' }}">
                                {{ report.status_display }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ report.view_url }}" class="btn btn-outline-primary" title="عرض">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ report.pdf_url }}" class="btn btn-outline-info" title="PDF" target="_blank">
                                    <i class="fas fa-file-pdf"></i>
                                </a>
                                <a href="{{ report.excel_url }}" class="btn btn-outline-success" title="Excel">
                                    <i class="fas fa-file-excel"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
            <h5>لا توجد تقارير</h5>
            <p class="text-muted">لم يتم إنشاء أي تقارير بعد.</p>
            <a href="{{ url_for('reports.trial_balance') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إنشاء تقرير جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Report Templates -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-templates me-2"></i>
                    قوالب التقارير المحفوظة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-week fa-2x text-primary mb-2"></i>
                                <h6>التقرير الأسبوعي</h6>
                                <p class="text-muted small">ميزان المراجعة + قائمة الدخل</p>
                                <button class="btn btn-sm btn-outline-primary">تشغيل</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-alt fa-2x text-success mb-2"></i>
                                <h6>التقرير الشهري</h6>
                                <p class="text-muted small">جميع التقارير المالية والضريبية</p>
                                <button class="btn btn-sm btn-outline-success">تشغيل</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card border">
                            <div class="card-body text-center">
                                <i class="fas fa-calendar-check fa-2x text-info mb-2"></i>
                                <h6>التقرير السنوي</h6>
                                <p class="text-muted small">الإقرار الضريبي والقوائم المالية</p>
                                <button class="btn btn-sm btn-outline-info">تشغيل</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-refresh stats every 5 minutes
    setInterval(function() {
        fetch('/reports/stats')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update stats display
                    updateStats(data.stats);
                }
            })
            .catch(error => {
                console.error('Error fetching stats:', error);
            });
    }, 300000); // 5 minutes
});

function updateStats(stats) {
    // Update the stats cards with new data
    // Implementation would update the displayed values
}

function runTemplate(templateName) {
    if (confirm(`هل تريد تشغيل ${templateName}؟`)) {
        // Redirect to template execution
        window.location.href = `/reports/template/${templateName}`;
    }
}
</script>
{% endblock %}
