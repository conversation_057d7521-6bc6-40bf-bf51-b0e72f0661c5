-- تحديث قاعدة البيانات لتتوافق مع المتطلبات الجديدة
-- Egyptian Tax Authority Integration Schema

-- جدول معاملات الضرائب مع مصلحة الضرائب المصرية
CREATE TABLE IF NOT EXISTS tax_transactions (
    id VARCHAR(36) PRIMARY KEY,
    invoice_id VARCHAR(36) REFERENCES invoices(id) ON DELETE CASCADE,
    receipt_id VARCHAR(36) REFERENCES receipts(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('invoice', 'receipt')),
    eta_uuid VARCHAR(100), -- UUID من مصلحة الضرائب
    eta_internal_id VARCHAR(100), -- Internal ID من مصلحة الضرائب
    submission_uuid VARCHAR(100), -- UUID الإرسال
    long_id VARCHAR(200), -- Long ID من مصلحة الضرائب
    hash_key VARCHAR(500), -- Hash Key للتوقيع الرقمي
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    response_status VARCHAR(50) DEFAULT 'pending',
    response_message TEXT,
    response_data JSON, -- البيانات الكاملة للاستجابة
    retry_count INTEGER DEFAULT 0,
    last_retry_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول إعدادات التكامل مع مصلحة الضرائب
CREATE TABLE IF NOT EXISTS eta_settings (
    id VARCHAR(36) PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول أكواد الضرائب المصرية (EGS Codes)
CREATE TABLE IF NOT EXISTS eta_tax_codes (
    id VARCHAR(36) PRIMARY KEY,
    code_type VARCHAR(50) NOT NULL, -- 'EGS', 'GS1', etc.
    item_code VARCHAR(100) NOT NULL,
    code_name_ar VARCHAR(200),
    code_name_en VARCHAR(200),
    description_ar TEXT,
    description_en TEXT,
    parent_code VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    eta_response_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(code_type, item_code)
);

-- جدول سجل الأخطاء والمحاولات
CREATE TABLE IF NOT EXISTS eta_error_log (
    id VARCHAR(36) PRIMARY KEY,
    transaction_id VARCHAR(36) REFERENCES tax_transactions(id),
    error_type VARCHAR(50) NOT NULL,
    error_code VARCHAR(20),
    error_message TEXT,
    error_details JSON,
    occurred_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- تحديث جدول الفواتير لدعم التكامل مع مصلحة الضرائب
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS eta_uuid VARCHAR(100);
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS eta_internal_id VARCHAR(100);
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS eta_submission_uuid VARCHAR(100);
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS eta_long_id VARCHAR(200);
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS eta_hash_key VARCHAR(500);
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS eta_status VARCHAR(50) DEFAULT 'draft';
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS eta_submitted_at TIMESTAMP;
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS document_type_name VARCHAR(50) DEFAULT 'I';
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS document_type_version VARCHAR(10) DEFAULT '1.0';

-- تحديث جدول الإيصالات لدعم التكامل مع مصلحة الضرائب
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS eta_uuid VARCHAR(100);
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS eta_internal_id VARCHAR(100);
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS eta_submission_uuid VARCHAR(100);
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS eta_long_id VARCHAR(200);
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS eta_hash_key VARCHAR(500);
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS eta_status VARCHAR(50) DEFAULT 'draft';
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS eta_submitted_at TIMESTAMP;
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS document_type_name VARCHAR(50) DEFAULT 'R';
ALTER TABLE receipts ADD COLUMN IF NOT EXISTS document_type_version VARCHAR(10) DEFAULT '1.0';

-- إضافة حقول الضرائب للفواتير
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS tax_totals JSON; -- إجمالي الضرائب
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(14,2) DEFAULT 0;
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS net_amount DECIMAL(14,2);
ALTER TABLE invoices ADD COLUMN IF NOT EXISTS tax_amount DECIMAL(14,2) DEFAULT 0;

-- تحديث جدول بنود الفاتورة لدعم الضرائب
ALTER TABLE invoice_lines ADD COLUMN IF NOT EXISTS item_code VARCHAR(100);
ALTER TABLE invoice_lines ADD COLUMN IF NOT EXISTS unit_type VARCHAR(50) DEFAULT 'EA'; -- وحدة القياس
ALTER TABLE invoice_lines ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(14,2) DEFAULT 0;
ALTER TABLE invoice_lines ADD COLUMN IF NOT EXISTS tax_rate DECIMAL(5,2) DEFAULT 14.00; -- معدل الضريبة
ALTER TABLE invoice_lines ADD COLUMN IF NOT EXISTS tax_amount DECIMAL(14,2) DEFAULT 0;
ALTER TABLE invoice_lines ADD COLUMN IF NOT EXISTS net_amount DECIMAL(14,2);
ALTER TABLE invoice_lines ADD COLUMN IF NOT EXISTS sales_total DECIMAL(14,2);

-- إنشاء الفهارس للأداء
CREATE INDEX IF NOT EXISTS idx_tax_transactions_invoice ON tax_transactions(invoice_id);
CREATE INDEX IF NOT EXISTS idx_tax_transactions_receipt ON tax_transactions(receipt_id);
CREATE INDEX IF NOT EXISTS idx_tax_transactions_status ON tax_transactions(response_status);
CREATE INDEX IF NOT EXISTS idx_tax_transactions_sent_at ON tax_transactions(sent_at);
CREATE INDEX IF NOT EXISTS idx_eta_settings_key ON eta_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_eta_tax_codes_type_code ON eta_tax_codes(code_type, item_code);
CREATE INDEX IF NOT EXISTS idx_invoices_eta_status ON invoices(eta_status);
CREATE INDEX IF NOT EXISTS idx_receipts_eta_status ON receipts(eta_status);

-- إدراج الإعدادات الافتراضية لمصلحة الضرائب
INSERT INTO eta_settings (id, setting_key, setting_value, description) VALUES
('eta-001', 'ETA_BASE_URL', 'https://api.invoicing.eta.gov.eg/api/v1', 'Base URL for ETA API'),
('eta-002', 'ETA_CLIENT_ID', '', 'Client ID for ETA authentication'),
('eta-003', 'ETA_CLIENT_SECRET', '', 'Client Secret for ETA authentication (encrypted)'),
('eta-004', 'ETA_ENVIRONMENT', 'sandbox', 'Environment: sandbox or production'),
('eta-005', 'ETA_TIMEOUT', '30', 'API timeout in seconds'),
('eta-006', 'ETA_RETRY_ATTEMPTS', '3', 'Number of retry attempts for failed requests'),
('eta-007', 'ETA_AUTO_SUBMIT', 'false', 'Auto submit invoices to ETA'),
('eta-008', 'COMPANY_TAX_ID', '', 'Company Tax Registration Number'),
('eta-009', 'COMPANY_ACTIVITY_CODE', '', 'Company Activity Code'),
('eta-010', 'COMPANY_BRANCH_ID', '', 'Company Branch ID')
ON CONFLICT (setting_key) DO NOTHING;
