{% extends "base.html" %}

{% block title %}{{ title }} - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-{{ 'plus' if not receipt else 'edit' }} me-3"></i>
                {{ title }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('receipts.index') }}">الإيصالات</a></li>
                    {% if receipt %}
                    <li class="breadcrumb-item"><a href="{{ url_for('receipts.detail', receipt_id=receipt.id) }}">{{ receipt.receipt_number }}</a></li>
                    {% endif %}
                    <li class="breadcrumb-item active">{{ title }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ url_for('receipts.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<form method="POST" id="receiptForm">
    {{ form.hidden_tag() }}
    
    <div class="row">
        <div class="col-lg-8">
            <!-- Receipt Header -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات الإيصال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.receipt_number.label(class="form-label required") }}
                            {{ form.receipt_number(class="form-control" + (" is-invalid" if form.receipt_number.errors else "")) }}
                            {% if form.receipt_number.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.receipt_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.reference_number.label(class="form-label") }}
                            {{ form.reference_number(class="form-control" + (" is-invalid" if form.reference_number.errors else "")) }}
                            {% if form.reference_number.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.reference_number.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.receipt_type.label(class="form-label required") }}
                            {{ form.receipt_type(class="form-select" + (" is-invalid" if form.receipt_type.errors else ""), id="receipt-type-select") }}
                            {% if form.receipt_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.receipt_type.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.receipt_date.label(class="form-label required") }}
                            {{ form.receipt_date(class="form-control" + (" is-invalid" if form.receipt_date.errors else "")) }}
                            {% if form.receipt_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.receipt_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3" id="customer-field">
                            {{ form.customer_id.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.customer_id(class="form-select" + (" is-invalid" if form.customer_id.errors else ""), id="customer-select") }}
                                <button type="button" class="btn btn-outline-secondary" onclick="openCustomerModal()">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            {% if form.customer_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.customer_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3" id="vendor-field" style="display: none;">
                            {{ form.vendor_id.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.vendor_id(class="form-select" + (" is-invalid" if form.vendor_id.errors else ""), id="vendor-select") }}
                                <button type="button" class="btn btn-outline-secondary" onclick="openVendorModal()">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                            {% if form.vendor_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.vendor_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3" id="invoice-field" style="display: none;">
                            {{ form.invoice_id.label(class="form-label") }}
                            {{ form.invoice_id(class="form-select" + (" is-invalid" if form.invoice_id.errors else ""), id="invoice-select") }}
                            {% if form.invoice_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.invoice_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-money-bill me-2"></i>
                        معلومات الدفع
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.amount.label(class="form-label required") }}
                            {{ form.amount(class="form-control" + (" is-invalid" if form.amount.errors else ""), id="amount-input") }}
                            {% if form.amount.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.amount.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            {{ form.currency.label(class="form-label") }}
                            {{ form.currency(class="form-select" + (" is-invalid" if form.currency.errors else "")) }}
                            {% if form.currency.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.currency.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.payment_method.label(class="form-label required") }}
                            {{ form.payment_method(class="form-select" + (" is-invalid" if form.payment_method.errors else ""), id="payment-method-select") }}
                            {% if form.payment_method.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.payment_method.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="col-md-6 mb-3" id="account-field">
                            {{ form.account_id.label(class="form-label") }}
                            {{ form.account_id(class="form-select" + (" is-invalid" if form.account_id.errors else "")) }}
                            {% if form.account_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.account_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Bank/Check Details -->
                    <div id="bank-details" style="display: none;">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.bank_name.label(class="form-label") }}
                                {{ form.bank_name(class="form-control" + (" is-invalid" if form.bank_name.errors else "")) }}
                                {% if form.bank_name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.bank_name.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.bank_reference.label(class="form-label") }}
                                {{ form.bank_reference(class="form-control" + (" is-invalid" if form.bank_reference.errors else "")) }}
                                {% if form.bank_reference.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.bank_reference.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div id="check-details" style="display: none;">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                {{ form.check_number.label(class="form-label") }}
                                {{ form.check_number(class="form-control" + (" is-invalid" if form.check_number.errors else "")) }}
                                {% if form.check_number.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.check_number.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                {{ form.check_date.label(class="form-label") }}
                                {{ form.check_date(class="form-control" + (" is-invalid" if form.check_date.errors else "")) }}
                                {% if form.check_date.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.check_date.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sticky-note me-2"></i>
                        معلومات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else "")) }}
                        {% if form.description.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.description.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else "")) }}
                        {% if form.notes.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.notes.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="form-check">
                        {{ form.is_confirmed(class="form-check-input") }}
                        {{ form.is_confirmed.label(class="form-check-label") }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Actions -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-cogs me-2"></i>
                        الإجراءات
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" name="action" value="save" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>
                            حفظ
                        </button>
                        <button type="submit" name="action" value="save_and_confirm" class="btn btn-success">
                            <i class="fas fa-check me-2"></i>
                            حفظ وتأكيد
                        </button>
                        <a href="{{ url_for('receipts.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>
                            إلغاء
                        </a>
                        {% if receipt %}
                        <hr>
                        <a href="{{ url_for('receipts.detail', receipt_id=receipt.id) }}" class="btn btn-outline-info">
                            <i class="fas fa-eye me-2"></i>
                            عرض التفاصيل
                        </a>
                        <a href="{{ url_for('receipts.pdf', receipt_id=receipt.id) }}" class="btn btn-outline-warning" target="_blank">
                            <i class="fas fa-file-pdf me-2"></i>
                            معاينة PDF
                        </a>
                        {% if receipt.can_be_deleted() %}
                        <form method="POST" action="{{ url_for('receipts.delete', receipt_id=receipt.id) }}" 
                              onsubmit="return confirm('هل أنت متأكد من حذف هذا الإيصال؟')">
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-trash me-2"></i>
                                حذف الإيصال
                            </button>
                        </form>
                        {% endif %}
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Amount in Words -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-spell-check me-2"></i>
                        المبلغ بالحروف
                    </h6>
                </div>
                <div class="card-body">
                    <p id="amount-in-words" class="text-muted">أدخل المبلغ لعرضه بالحروف</p>
                </div>
            </div>
            
            <!-- Customer/Vendor Info -->
            <div class="card mb-3" id="entity-info-card" style="display: none;">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        <span id="entity-title">معلومات العميل</span>
                    </h6>
                </div>
                <div class="card-body" id="entity-info">
                    <!-- Entity info will be loaded here -->
                </div>
            </div>
            
            <!-- Help -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        مساعدة
                    </h6>
                </div>
                <div class="card-body">
                    <h6>أنواع الإيصالات:</h6>
                    <ul class="list-unstyled small">
                        <li><span class="badge bg-success me-2">قبض</span> استلام أموال من العملاء</li>
                        <li><span class="badge bg-warning me-2">دفع</span> دفع أموال للموردين</li>
                    </ul>
                    
                    <h6 class="mt-3">طرق الدفع:</h6>
                    <ul class="small">
                        <li><strong>نقدي:</strong> دفع نقدي مباشر</li>
                        <li><strong>شيك:</strong> دفع بشيك (أدخل رقم الشيك)</li>
                        <li><strong>تحويل:</strong> تحويل بنكي (أدخل المرجع)</li>
                        <li><strong>بطاقة:</strong> دفع بالبطاقة الائتمانية</li>
                    </ul>
                    
                    <h6 class="mt-3">نصائح:</h6>
                    <ul class="small">
                        <li>تأكد من صحة المبلغ قبل الحفظ</li>
                        <li>اربط الإيصال بالفاتورة المناسبة</li>
                        <li>أكد الإيصال بعد التحقق</li>
                        <li>احتفظ بنسخة PDF للسجلات</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Receipt type change handler
    $('#receipt-type-select').change(function() {
        const receiptType = $(this).val();
        toggleEntityFields(receiptType);
        updateAccountChoices(receiptType);
    });
    
    // Payment method change handler
    $('#payment-method-select').change(function() {
        const paymentMethod = $(this).val();
        togglePaymentDetails(paymentMethod);
    });
    
    // Amount change handler
    $('#amount-input').on('input', function() {
        const amount = parseFloat($(this).val()) || 0;
        updateAmountInWords(amount);
    });
    
    // Customer/Vendor selection change
    $('#customer-select, #vendor-select').change(function() {
        const entityType = $(this).attr('id').includes('customer') ? 'customer' : 'vendor';
        const entityId = $(this).val();
        
        if (entityId) {
            loadEntityInfo(entityType, entityId);
            loadInvoices(entityType, entityId);
        } else {
            $('#entity-info-card').hide();
            $('#invoice-field').hide();
        }
    });
    
    // Initialize form based on current values
    const currentReceiptType = $('#receipt-type-select').val();
    if (currentReceiptType) {
        toggleEntityFields(currentReceiptType);
        updateAccountChoices(currentReceiptType);
    }
    
    const currentPaymentMethod = $('#payment-method-select').val();
    if (currentPaymentMethod) {
        togglePaymentDetails(currentPaymentMethod);
    }
    
    // Initialize amount in words
    const currentAmount = parseFloat($('#amount-input').val()) || 0;
    if (currentAmount > 0) {
        updateAmountInWords(currentAmount);
    }
    
    // Load entity info if editing existing receipt
    {% if receipt %}
        {% if receipt.customer_id %}
            loadEntityInfo('customer', '{{ receipt.customer_id }}');
        {% elif receipt.vendor_id %}
            loadEntityInfo('vendor', '{{ receipt.vendor_id }}');
        {% endif %}
    {% endif %}
});

function toggleEntityFields(receiptType) {
    if (receiptType === 'receipt') {
        // Receipt from customer
        $('#customer-field').show();
        $('#vendor-field').hide();
        $('#customer-select').prop('required', true);
        $('#vendor-select').prop('required', false);
    } else if (receiptType === 'payment') {
        // Payment to vendor
        $('#customer-field').hide();
        $('#vendor-field').show();
        $('#customer-select').prop('required', false);
        $('#vendor-select').prop('required', true);
    } else {
        // Both hidden for other types
        $('#customer-field').hide();
        $('#vendor-field').hide();
        $('#customer-select').prop('required', false);
        $('#vendor-select').prop('required', false);
    }
}

function togglePaymentDetails(paymentMethod) {
    $('#bank-details').hide();
    $('#check-details').hide();
    
    if (paymentMethod === 'bank_transfer') {
        $('#bank-details').show();
    } else if (paymentMethod === 'check') {
        $('#check-details').show();
    }
}

function updateAccountChoices(receiptType) {
    // This would update account choices based on receipt type
    // Implementation depends on account structure
}

function updateAmountInWords(amount) {
    // Convert number to Arabic words
    // This is a simplified version - you might want to use a proper library
    if (amount === 0) {
        $('#amount-in-words').text('صفر جنيه مصري');
        return;
    }
    
    // For now, just show the number
    $('#amount-in-words').text(`${amount.toLocaleString('ar-EG')} جنيه مصري`);
}

function loadEntityInfo(entityType, entityId) {
    const url = entityType === 'customer' ? `/customers/${entityId}/info` : `/vendors/${entityId}/info`;
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const entity = data[entityType];
                const title = entityType === 'customer' ? 'معلومات العميل' : 'معلومات المورد';
                
                $('#entity-title').text(title);
                
                const html = `
                    <p><strong>الاسم:</strong> ${entity.name}</p>
                    ${entity.tax_id ? `<p><strong>الرقم الضريبي:</strong> ${entity.tax_id}</p>` : ''}
                    ${entity.email ? `<p><strong>البريد:</strong> ${entity.email}</p>` : ''}
                    ${entity.phone ? `<p><strong>الهاتف:</strong> ${entity.phone}</p>` : ''}
                    <p><strong>الرصيد:</strong> ${entity.balance} ج.م</p>
                `;
                
                $('#entity-info').html(html);
                $('#entity-info-card').show();
            }
        })
        .catch(error => {
            console.error('Error loading entity info:', error);
        });
}

function loadInvoices(entityType, entityId) {
    if (entityType === 'customer') {
        // Load unpaid invoices for customer
        fetch(`/customers/${entityId}/unpaid-invoices`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.invoices.length > 0) {
                    const invoiceSelect = $('#invoice-select');
                    invoiceSelect.empty();
                    invoiceSelect.append('<option value="">اختر الفاتورة (اختياري)</option>');
                    
                    data.invoices.forEach(invoice => {
                        invoiceSelect.append(`<option value="${invoice.id}">${invoice.invoice_number} - ${invoice.total_amount} ج.م</option>`);
                    });
                    
                    $('#invoice-field').show();
                } else {
                    $('#invoice-field').hide();
                }
            })
            .catch(error => {
                console.error('Error loading invoices:', error);
            });
    }
}

function openCustomerModal() {
    window.open('{{ url_for("customers.new") }}', '_blank');
}

function openVendorModal() {
    window.open('{{ url_for("vendors.new") }}', '_blank');
}
</script>
{% endblock %}
