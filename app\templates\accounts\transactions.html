{% extends "base.html" %}

{% block title %}حركات الحساب: {{ account.name }} - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-list-alt me-3"></i>
                حركات الحساب
            </h1>
            <p class="mb-0 mt-2">{{ account.code }} - {{ account.name }}</p>
        </div>
        <div>
            <a href="{{ url_for('accounts.detail', account_id=account.id) }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للحساب
            </a>
            <button type="button" class="btn btn-outline-light" onclick="window.print()">
                <i class="fas fa-print me-2"></i>
                طباعة
            </button>
        </div>
    </div>
</div>

<!-- Account Summary -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center border-primary">
            <div class="card-body">
                <h5 class="card-title text-primary">الرصيد الحالي</h5>
                <h3 class="text-primary">{{ account.get_balance()|currency }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-success">
            <div class="card-body">
                <h5 class="card-title text-success">إجمالي المدين</h5>
                <h3 class="text-success">{{ account.get_total_debits()|currency }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-danger">
            <div class="card-body">
                <h5 class="card-title text-danger">إجمالي الدائن</h5>
                <h3 class="text-danger">{{ account.get_total_credits()|currency }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center border-info">
            <div class="card-body">
                <h5 class="card-title text-info">عدد الحركات</h5>
                <h3 class="text-info">{{ transactions.total if transactions else 0 }}</h3>
            </div>
        </div>
    </div>
</div>

<!-- Transactions Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            كشف حساب تفصيلي
        </h5>
    </div>
    <div class="card-body">
        {% if transactions and transactions.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>التاريخ</th>
                        <th>البيان</th>
                        <th>رقم القيد</th>
                        <th class="text-center">مدين</th>
                        <th class="text-center">دائن</th>
                        <th class="text-center">الرصيد الجاري</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions.items %}
                    <tr>
                        <td>
                            <strong>{{ transaction.journal_entry.entry_date|date }}</strong>
                            <br><small class="text-muted">{{ transaction.journal_entry.created_at|datetime }}</small>
                        </td>
                        <td>
                            <div>
                                {% if transaction.description %}
                                    {{ transaction.description }}
                                {% else %}
                                    {{ transaction.journal_entry.description }}
                                {% endif %}
                            </div>
                            {% if transaction.journal_entry.description_en %}
                                <small class="text-muted">{{ transaction.journal_entry.description_en }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{{ url_for('journal.detail', entry_id=transaction.journal_entry.id) }}" 
                               class="text-decoration-none">
                                {{ transaction.journal_entry.reference_number }}
                            </a>
                        </td>
                        <td class="text-center">
                            {% if transaction.dc == 'D' %}
                                <strong class="text-success">{{ transaction.amount|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if transaction.dc == 'C' %}
                                <strong class="text-danger">{{ transaction.amount|currency }}</strong>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <strong class="text-{{ 'success' if transaction.running_balance >= 0 else 'danger' }}">
                                {{ transaction.running_balance|currency }}
                            </strong>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('journal.detail', entry_id=transaction.journal_entry.id) }}" 
                                   class="btn btn-outline-primary" title="عرض القيد">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if current_user.can_access('journal') %}
                                <a href="{{ url_for('journal.edit', entry_id=transaction.journal_entry.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل القيد">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-dark">
                    <tr>
                        <th colspan="3">الإجمالي</th>
                        <th class="text-center">
                            <strong class="text-success">
                                {{ transactions.items|selectattr('dc', 'equalto', 'D')|map(attribute='amount')|sum|currency }}
                            </strong>
                        </th>
                        <th class="text-center">
                            <strong class="text-danger">
                                {{ transactions.items|selectattr('dc', 'equalto', 'C')|map(attribute='amount')|sum|currency }}
                            </strong>
                        </th>
                        <th class="text-center">
                            <strong class="text-primary">{{ account.get_balance()|currency }}</strong>
                        </th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        </div>

        <!-- Pagination -->
        {% if transactions.pages > 1 %}
        <nav aria-label="تصفح الصفحات" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if transactions.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('accounts.transactions', account_id=account.id, page=transactions.prev_num) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in transactions.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != transactions.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('accounts.transactions', account_id=account.id, page=page_num) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if transactions.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('accounts.transactions', account_id=account.id, page=transactions.next_num) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
            <h5>لا توجد حركات</h5>
            <p class="text-muted">لا توجد حركات محاسبية لهذا الحساب حتى الآن.</p>
            {% if current_user.can_access('journal') %}
            <a href="{{ url_for('journal.new') }}?account_id={{ account.id }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة قيد محاسبي
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Transaction Analysis -->
{% if transactions and transactions.items %}
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    تحليل الحركات
                </h6>
            </div>
            <div class="card-body">
                <canvas id="transactionChart" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-calendar me-2"></i>
                    الحركات الشهرية
                </h6>
            </div>
            <div class="card-body">
                <canvas id="monthlyChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .page-header .btn,
    .row:first-child,
    .row:last-child,
    .pagination {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 11px;
    }
    
    .btn-group {
        display: none !important;
    }
}

.table th {
    background-color: #f8f9fa !important;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.running-balance {
    font-weight: bold;
    font-family: 'Courier New', monospace;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
{% if transactions and transactions.items %}
// Prepare chart data
const transactionData = {
    debits: {{ transactions.items|selectattr('dc', 'equalto', 'D')|map(attribute='amount')|sum }},
    credits: {{ transactions.items|selectattr('dc', 'equalto', 'C')|map(attribute='amount')|sum }}
};

// Transaction Analysis Chart
const transactionCtx = document.getElementById('transactionChart').getContext('2d');
new Chart(transactionCtx, {
    type: 'doughnut',
    data: {
        labels: ['المدين', 'الدائن'],
        datasets: [{
            data: [transactionData.debits, transactionData.credits],
            backgroundColor: ['#28a745', '#dc3545']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Monthly transactions data (simplified)
const monthlyData = {};
{% for transaction in transactions.items %}
const month = '{{ transaction.journal_entry.entry_date.strftime('%Y-%m') }}';
if (!monthlyData[month]) {
    monthlyData[month] = { debits: 0, credits: 0 };
}
{% if transaction.dc == 'D' %}
monthlyData[month].debits += {{ transaction.amount }};
{% else %}
monthlyData[month].credits += {{ transaction.amount }};
{% endif %}
{% endfor %}

// Monthly Chart
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
new Chart(monthlyCtx, {
    type: 'bar',
    data: {
        labels: Object.keys(monthlyData),
        datasets: [
            {
                label: 'المدين',
                data: Object.values(monthlyData).map(d => d.debits),
                backgroundColor: '#28a745'
            },
            {
                label: 'الدائن', 
                data: Object.values(monthlyData).map(d => d.credits),
                backgroundColor: '#dc3545'
            }
        ]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return value.toLocaleString() + ' ج.م';
                    }
                }
            }
        },
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});
{% endif %}

// Auto-refresh every 60 seconds
setInterval(function() {
    // Check for new transactions
    fetch('{{ url_for("accounts.balance", account_id=account.id) }}')
        .then(response => response.json())
        .then(data => {
            // Update balance if changed
            console.log('Balance check:', data.balance);
        })
        .catch(error => console.error('Error checking balance:', error));
}, 60000);
</script>
{% endblock %}
