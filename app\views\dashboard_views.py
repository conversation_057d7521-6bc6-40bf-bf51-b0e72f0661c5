"""
Dashboard and analytics views
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, make_response
from flask_login import login_required, current_user
from sqlalchemy import func, and_, or_, desc, asc
from sqlalchemy.orm import joinedload
from app import db
from app.models.account import Account
from app.models.journal import JournalEntry, JournalLine
from app.models.invoice import Invoice
from app.models.receipt import Receipt
from app.models.customer import Customer
from app.models.vendor import Vendor
from app.forms.dashboard_forms import DashboardFilterForm, KPIConfigForm, ExportDashboardForm
from app.utils.decorators import permission_required
from app.utils.helpers import get_pagination_params
from decimal import Decimal
from datetime import datetime, date, timedelta
from dateutil.relativedelta import relativedelta
import json

# Create blueprint
dashboard_bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')


@dashboard_bp.route('/')
@login_required
def index():
    """Main dashboard page"""
    return redirect(url_for('dashboard.analytics'))


@dashboard_bp.route('/analytics')
@login_required
@permission_required('dashboard')
def analytics():
    """Analytics dashboard with KPIs and charts"""
    form = DashboardFilterForm(request.args)
    
    # Get period dates
    if form.validate():
        date_from = form.date_from.data
        date_to = form.date_to.data
        period_display = get_period_display(form.period.data, date_from, date_to)
    else:
        # Default to current month
        today = date.today()
        date_from = today.replace(day=1)
        date_to = today
        period_display = 'هذا الشهر'
    
    # Get comparison dates
    comp_date_from, comp_date_to = get_comparison_dates(date_from, date_to)
    
    # Calculate KPIs
    kpis = calculate_kpis(date_from, date_to, comp_date_from, comp_date_to)
    
    # Calculate financial metrics
    metrics = calculate_financial_metrics(date_from, date_to)
    
    # Get chart data
    chart_data = get_chart_data(date_from, date_to)
    
    # Get recent activities
    recent_activities = get_recent_activities(limit=10)
    
    # Get alerts
    alerts = get_dashboard_alerts()
    
    return render_template(
        'dashboard/analytics.html',
        form=form,
        kpis=kpis,
        metrics=metrics,
        chart_data=chart_data,
        recent_activities=recent_activities,
        alerts=alerts,
        period_display=period_display,
        date_from=date_from,
        date_to=date_to,
        title='لوحة التحليلات المالية'
    )


@dashboard_bp.route('/refresh', methods=['POST'])
@login_required
@permission_required('dashboard')
def refresh():
    """Refresh dashboard data via AJAX"""
    try:
        # Get current period from request
        period = request.json.get('period', 'month')
        date_from = request.json.get('date_from')
        date_to = request.json.get('date_to')
        
        if date_from:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
        if date_to:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
        
        # Set default dates if not provided
        if not date_from or not date_to:
            today = date.today()
            date_from = today.replace(day=1)
            date_to = today
        
        # Get comparison dates
        comp_date_from, comp_date_to = get_comparison_dates(date_from, date_to)
        
        # Calculate updated data
        kpis = calculate_kpis(date_from, date_to, comp_date_from, comp_date_to)
        chart_data = get_chart_data(date_from, date_to)
        recent_activities = get_recent_activities(limit=10)
        
        return jsonify({
            'success': True,
            'kpis': kpis,
            'chart_data': chart_data,
            'recent_activities': [
                {
                    'title': activity['title'],
                    'description': activity['description'],
                    'type_color': activity['type_color'],
                    'icon': activity['icon'],
                    'created_at': activity['created_at'].isoformat() if activity['created_at'] else None
                }
                for activity in recent_activities
            ]
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'حدث خطأ أثناء التحديث: {str(e)}'
        }), 500


@dashboard_bp.route('/export')
@login_required
@permission_required('dashboard')
def export():
    """Export dashboard data"""
    form = ExportDashboardForm(request.args)
    
    if form.validate():
        # Get data based on form parameters
        export_format = form.export_format.data
        
        # Determine date range
        if form.date_range.data == 'custom':
            date_from = form.custom_date_from.data
            date_to = form.custom_date_to.data
        else:
            date_from, date_to = get_predefined_date_range(form.date_range.data)
        
        # Generate export data
        export_data = generate_export_data(
            date_from, date_to,
            form.include_kpis.data,
            form.include_charts.data,
            form.include_tables.data
        )
        
        # Create export file based on format
        if export_format == 'pdf':
            return export_pdf(export_data, form.include_notes.data)
        elif export_format == 'excel':
            return export_excel(export_data)
        elif export_format == 'csv':
            return export_csv(export_data)
        elif export_format == 'json':
            return export_json(export_data)
        elif export_format == 'image':
            return export_image(export_data)
    
    return render_template(
        'dashboard/export.html',
        form=form,
        title='تصدير بيانات لوحة التحكم'
    )


@dashboard_bp.route('/activity-log')
@login_required
@permission_required('dashboard')
def activity_log():
    """Display activity log"""
    page, per_page = get_pagination_params()
    
    # Get all activities with pagination
    activities = get_recent_activities(page=page, per_page=per_page)
    
    return render_template(
        'dashboard/activity_log.html',
        activities=activities,
        title='سجل الأنشطة'
    )


# Helper functions

def calculate_kpis(date_from, date_to, comp_date_from=None, comp_date_to=None):
    """Calculate Key Performance Indicators"""
    
    # Current period revenue
    revenue_accounts = Account.query.filter(
        Account.type == 'Income',
        Account.is_active == True
    ).all()
    
    total_revenue = Decimal('0')
    for account in revenue_accounts:
        balance = account.get_balance_for_period(date_from, date_to)
        total_revenue += abs(balance)  # Revenue accounts have credit balances
    
    # Current period expenses
    expense_accounts = Account.query.filter(
        Account.type == 'Expense',
        Account.is_active == True
    ).all()
    
    total_expenses = Decimal('0')
    for account in expense_accounts:
        balance = account.get_balance_for_period(date_from, date_to)
        total_expenses += balance  # Expense accounts have debit balances
    
    # Net profit
    net_profit = total_revenue - total_expenses
    
    # Cash balance
    cash_accounts = Account.query.filter(
        Account.type == 'Asset',
        Account.name.ilike('%نقد%'),
        Account.is_active == True
    ).all()
    
    cash_balance = Decimal('0')
    for account in cash_accounts:
        balance = account.get_balance_as_of(date_to)
        cash_balance += balance
    
    # Calculate cash flow for the period
    cash_flow = Decimal('0')
    if cash_accounts:
        for account in cash_accounts:
            opening_balance = account.get_balance_as_of(date_from - timedelta(days=1))
            closing_balance = account.get_balance_as_of(date_to)
            cash_flow += (closing_balance - opening_balance)
    
    # Calculate growth rates if comparison period is provided
    revenue_growth = Decimal('0')
    profit_growth = Decimal('0')
    expense_growth = Decimal('0')
    
    if comp_date_from and comp_date_to:
        # Previous period revenue
        prev_revenue = Decimal('0')
        for account in revenue_accounts:
            balance = account.get_balance_for_period(comp_date_from, comp_date_to)
            prev_revenue += abs(balance)
        
        # Previous period expenses
        prev_expenses = Decimal('0')
        for account in expense_accounts:
            balance = account.get_balance_for_period(comp_date_from, comp_date_to)
            prev_expenses += balance
        
        prev_profit = prev_revenue - prev_expenses
        
        # Calculate growth rates
        if prev_revenue > 0:
            revenue_growth = ((total_revenue - prev_revenue) / prev_revenue) * 100
        
        if prev_profit != 0:
            profit_growth = ((net_profit - prev_profit) / abs(prev_profit)) * 100
        
        if prev_expenses > 0:
            expense_growth = ((total_expenses - prev_expenses) / prev_expenses) * 100
    
    return {
        'total_revenue': total_revenue,
        'total_expenses': total_expenses,
        'net_profit': net_profit,
        'cash_balance': cash_balance,
        'cash_flow': cash_flow,
        'revenue_growth': revenue_growth,
        'profit_growth': profit_growth,
        'expense_growth': expense_growth
    }


def calculate_financial_metrics(date_from, date_to):
    """Calculate financial metrics and ratios"""
    
    # Get basic financial data
    kpis = calculate_kpis(date_from, date_to)
    
    # Calculate profit margin
    profit_margin = Decimal('0')
    if kpis['total_revenue'] > 0:
        profit_margin = (kpis['net_profit'] / kpis['total_revenue']) * 100
    
    # Get total assets
    asset_accounts = Account.query.filter(
        Account.type == 'Asset',
        Account.is_active == True
    ).all()
    
    total_assets = Decimal('0')
    for account in asset_accounts:
        balance = account.get_balance_as_of(date_to)
        total_assets += balance
    
    # Calculate asset turnover
    asset_turnover = Decimal('0')
    if total_assets > 0:
        asset_turnover = kpis['total_revenue'] / total_assets
    
    # Get current liabilities for liquidity ratio
    liability_accounts = Account.query.filter(
        Account.type == 'Liability',
        Account.is_active == True
    ).all()
    
    current_liabilities = Decimal('0')
    for account in liability_accounts:
        balance = account.get_balance_as_of(date_to)
        current_liabilities += abs(balance)  # Liability accounts have credit balances
    
    # Calculate liquidity ratio (simplified as current assets / current liabilities)
    current_assets = kpis['cash_balance']  # Simplified - would include other current assets
    liquidity_ratio = Decimal('0')
    if current_liabilities > 0:
        liquidity_ratio = current_assets / current_liabilities
    
    # Calculate debt ratio
    debt_ratio = Decimal('0')
    if total_assets > 0:
        debt_ratio = (current_liabilities / total_assets) * 100
    
    return {
        'profit_margin': profit_margin,
        'asset_turnover': asset_turnover,
        'liquidity_ratio': liquidity_ratio,
        'debt_ratio': debt_ratio
    }


def get_chart_data(date_from, date_to):
    """Get data for dashboard charts"""
    
    # Generate monthly data for the chart
    chart_labels = []
    revenue_data = []
    expense_data = []
    
    current_date = date_from
    while current_date <= date_to:
        month_start = current_date.replace(day=1)
        if current_date.month == 12:
            month_end = current_date.replace(year=current_date.year + 1, month=1, day=1) - timedelta(days=1)
        else:
            month_end = current_date.replace(month=current_date.month + 1, day=1) - timedelta(days=1)
        
        if month_end > date_to:
            month_end = date_to
        
        # Get revenue for this month
        revenue_accounts = Account.query.filter(
            Account.type == 'Income',
            Account.is_active == True
        ).all()
        
        month_revenue = Decimal('0')
        for account in revenue_accounts:
            balance = account.get_balance_for_period(month_start, month_end)
            month_revenue += abs(balance)
        
        # Get expenses for this month
        expense_accounts = Account.query.filter(
            Account.type == 'Expense',
            Account.is_active == True
        ).all()
        
        month_expenses = Decimal('0')
        for account in expense_accounts:
            balance = account.get_balance_for_period(month_start, month_end)
            month_expenses += balance
        
        chart_labels.append(current_date.strftime('%B %Y'))
        revenue_data.append(float(month_revenue))
        expense_data.append(float(month_expenses))
        
        # Move to next month
        if current_date.month == 12:
            current_date = current_date.replace(year=current_date.year + 1, month=1)
        else:
            current_date = current_date.replace(month=current_date.month + 1)
    
    # Calculate profit distribution
    kpis = calculate_kpis(date_from, date_to)
    profit_distribution = [
        float(kpis['net_profit']),
        float(kpis['total_expenses'] * Decimal('0.1')),  # Estimated taxes
        float(kpis['total_expenses'])
    ]
    
    return {
        'revenue_expense': {
            'labels': chart_labels,
            'revenue': revenue_data,
            'expenses': expense_data
        },
        'profit_distribution': profit_distribution
    }


def get_recent_activities(limit=10, page=None, per_page=None):
    """Get recent system activities"""
    
    activities = []
    
    # Get recent invoices
    recent_invoices = Invoice.query.order_by(desc(Invoice.created_at)).limit(3).all()
    for invoice in recent_invoices:
        activities.append({
            'title': f'فاتورة جديدة: {invoice.invoice_number}',
            'description': f'تم إنشاء فاتورة بقيمة {invoice.total_amount} ج.م للعميل {invoice.customer.name if invoice.customer else "غير محدد"}',
            'type_color': 'primary',
            'icon': 'file-invoice',
            'created_at': invoice.created_at
        })
    
    # Get recent receipts
    recent_receipts = Receipt.query.order_by(desc(Receipt.created_at)).limit(3).all()
    for receipt in recent_receipts:
        activities.append({
            'title': f'إيصال جديد: {receipt.receipt_number}',
            'description': f'تم إنشاء إيصال {receipt.get_type_display()} بقيمة {receipt.amount} ج.م',
            'type_color': 'success',
            'icon': 'receipt',
            'created_at': receipt.created_at
        })
    
    # Get recent journal entries
    recent_entries = JournalEntry.query.filter_by(is_posted=True).order_by(desc(JournalEntry.posted_at)).limit(3).all()
    for entry in recent_entries:
        activities.append({
            'title': f'قيد يومي: {entry.entry_number}',
            'description': f'تم ترحيل قيد بقيمة {entry.total_amount} ج.م',
            'type_color': 'info',
            'icon': 'book',
            'created_at': entry.posted_at
        })
    
    # Sort by creation date
    activities.sort(key=lambda x: x['created_at'] or datetime.min, reverse=True)
    
    # Apply pagination if requested
    if page and per_page:
        start = (page - 1) * per_page
        end = start + per_page
        return activities[start:end]
    
    return activities[:limit]


def get_dashboard_alerts():
    """Get dashboard alerts and notifications"""
    
    alerts = []
    
    # Check for overdue invoices
    overdue_invoices = Invoice.query.filter(
        Invoice.due_date < date.today(),
        Invoice.status.in_(['pending', 'sent'])
    ).count()
    
    if overdue_invoices > 0:
        alerts.append({
            'type': 'warning',
            'icon': 'exclamation-triangle',
            'title': 'فواتير متأخرة',
            'message': f'يوجد {overdue_invoices} فاتورة متأخرة السداد'
        })
    
    # Check for low cash balance
    cash_accounts = Account.query.filter(
        Account.type == 'Asset',
        Account.name.ilike('%نقد%'),
        Account.is_active == True
    ).all()
    
    total_cash = Decimal('0')
    for account in cash_accounts:
        total_cash += account.get_balance_as_of(date.today())
    
    if total_cash < Decimal('10000'):  # Threshold for low cash
        alerts.append({
            'type': 'danger',
            'icon': 'money-bill-wave',
            'title': 'رصيد نقدي منخفض',
            'message': f'الرصيد النقدي الحالي: {total_cash} ج.م'
        })
    
    # Check for unposted journal entries
    unposted_entries = JournalEntry.query.filter_by(is_posted=False).count()
    
    if unposted_entries > 0:
        alerts.append({
            'type': 'info',
            'icon': 'book',
            'title': 'قيود غير مرحلة',
            'message': f'يوجد {unposted_entries} قيد يومي غير مرحل'
        })
    
    return alerts


def get_comparison_dates(date_from, date_to):
    """Get comparison period dates"""
    period_length = (date_to - date_from).days
    comp_date_to = date_from - timedelta(days=1)
    comp_date_from = comp_date_to - timedelta(days=period_length)
    
    return comp_date_from, comp_date_to


def get_period_display(period, date_from, date_to):
    """Get display text for period"""
    if period == 'today':
        return 'اليوم'
    elif period == 'week':
        return 'هذا الأسبوع'
    elif period == 'month':
        return 'هذا الشهر'
    elif period == 'quarter':
        return 'هذا الربع'
    elif period == 'year':
        return 'هذا العام'
    elif period == 'custom':
        return f'من {date_from.strftime("%d/%m/%Y")} إلى {date_to.strftime("%d/%m/%Y")}'
    else:
        return 'هذا الشهر'


# Export functions (placeholder implementations)

def generate_export_data(date_from, date_to, include_kpis, include_charts, include_tables):
    """Generate data for export"""
    data = {}
    
    if include_kpis:
        data['kpis'] = calculate_kpis(date_from, date_to)
    
    if include_charts:
        data['charts'] = get_chart_data(date_from, date_to)
    
    if include_tables:
        data['activities'] = get_recent_activities(limit=50)
    
    return data


def export_pdf(data, notes):
    """Export dashboard as PDF"""
    # Implementation would use a PDF library like ReportLab
    response = make_response("PDF export not implemented yet")
    response.headers['Content-Type'] = 'application/pdf'
    response.headers['Content-Disposition'] = 'attachment; filename=dashboard.pdf'
    return response


def export_excel(data):
    """Export dashboard as Excel"""
    # Implementation would use openpyxl or xlsxwriter
    response = make_response("Excel export not implemented yet")
    response.headers['Content-Type'] = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    response.headers['Content-Disposition'] = 'attachment; filename=dashboard.xlsx'
    return response


def export_csv(data):
    """Export dashboard as CSV"""
    # Implementation would generate CSV data
    response = make_response("CSV export not implemented yet")
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = 'attachment; filename=dashboard.csv'
    return response


def export_json(data):
    """Export dashboard as JSON"""
    response = make_response(json.dumps(data, default=str, ensure_ascii=False, indent=2))
    response.headers['Content-Type'] = 'application/json'
    response.headers['Content-Disposition'] = 'attachment; filename=dashboard.json'
    return response


def export_image(data):
    """Export dashboard as image"""
    # Implementation would generate image from charts
    response = make_response("Image export not implemented yet")
    response.headers['Content-Type'] = 'image/png'
    response.headers['Content-Disposition'] = 'attachment; filename=dashboard.png'
    return response


def get_predefined_date_range(range_type):
    """Get predefined date ranges"""
    today = date.today()
    
    if range_type == 'last_month':
        first_day_last_month = (today.replace(day=1) - timedelta(days=1)).replace(day=1)
        last_day_last_month = today.replace(day=1) - timedelta(days=1)
        return first_day_last_month, last_day_last_month
    
    elif range_type == 'last_quarter':
        # Implementation for last quarter
        return today - timedelta(days=90), today - timedelta(days=1)
    
    elif range_type == 'last_year':
        return today.replace(year=today.year - 1, month=1, day=1), today.replace(year=today.year - 1, month=12, day=31)
    
    else:  # current
        return today.replace(day=1), today
