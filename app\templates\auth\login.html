{% extends "base.html" %}

{% block title %}تسجيل الدخول - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0 mt-5">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="fas fa-calculator fa-3x text-primary mb-3"></i>
                        <h2 class="card-title">{{ APP_NAME }}</h2>
                        <p class="text-muted">نظام محاسبي ويب متكامل</p>
                    </div>
                    
                    <form method="POST">
                        {{ form.hidden_tag() }}
                        
                        <div class="mb-3">
                            {{ form.username.label(class="form-label") }}
                            {{ form.username(class="form-control form-control-lg") }}
                            {% if form.username.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.username.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.password.label(class="form-label") }}
                            {{ form.password(class="form-control form-control-lg") }}
                            {% if form.password.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.password.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3 form-check">
                            {{ form.remember_me(class="form-check-input") }}
                            {{ form.remember_me.label(class="form-check-label") }}
                        </div>
                        
                        <div class="d-grid">
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                        </div>
                    </form>
                    
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            © {{ moment().year }} {{ COMPANY_NAME }}
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    
    .card {
        border-radius: 1rem;
        backdrop-filter: blur(10px);
        background-color: rgba(255, 255, 255, 0.95);
    }
    
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: transform 0.2s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Focus on username field when page loads
    $(document).ready(function() {
        $('#username').focus();
    });
</script>
{% endblock %}
