"""
Invoice forms
"""

from flask_wtf import FlaskForm
from wtforms import StringField, TextAreaField, DateField, SelectField, DecimalField, SubmitField, FieldList, FormField
from wtforms.validators import DataRequired, Length, NumberRange, Optional
from datetime import date, timedelta
from app.models.customer import Customer

class InvoiceLineForm(FlaskForm):
    description = TextAreaField('الوصف', validators=[DataRequired(), Length(max=500)])
    description_en = TextAreaField('الوصف بالإنجليزية', validators=[Length(max=500)])
    unit_price = DecimalField('سعر الوحدة', validators=[
        DataRequired(), 
        NumberRange(min=0, message='السعر يجب أن يكون أكبر من أو يساوي صفر')
    ], places=2)
    quantity = DecimalField('الكمية', validators=[
        DataRequired(), 
        NumberRange(min=0.001, message='الكمية يجب أن تكون أكبر من صفر')
    ], places=3, default=1)
    discount_percent = DecimalField('نسبة الخصم %', validators=[
        NumberRange(min=0, max=100, message='نسبة الخصم يجب أن تكون بين 0 و 100')
    ], places=2, default=0)
    tax_percent = DecimalField('نسبة الضريبة %', validators=[
        NumberRange(min=0, max=100, message='نسبة الضريبة يجب أن تكون بين 0 و 100')
    ], places=2, default=14)

class InvoiceForm(FlaskForm):
    customer_id = SelectField('العميل', coerce=str, validators=[DataRequired()])
    issue_date = DateField('تاريخ الإصدار', validators=[DataRequired()], default=date.today)
    due_date = DateField('تاريخ الاستحقاق', default=lambda: date.today() + timedelta(days=30))
    notes = TextAreaField('ملاحظات', validators=[Length(max=1000)])
    
    # Lines will be handled dynamically via JavaScript
    submit = SubmitField('حفظ الفاتورة')
    
    def __init__(self, *args, **kwargs):
        super(InvoiceForm, self).__init__(*args, **kwargs)
        
        # Populate customer choices
        self.customer_id.choices = [('', 'اختر العميل')]
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        
        for customer in customers:
            self.customer_id.choices.append((
                customer.id, 
                customer.name
            ))

class InvoiceSearchForm(FlaskForm):
    search = StringField('البحث', validators=[Length(max=100)])
    customer_id = SelectField('العميل', coerce=str)
    status = SelectField('الحالة', choices=[
        ('', 'جميع الحالات'),
        ('draft', 'مسودة'),
        ('sent', 'مرسلة'),
        ('paid', 'مدفوعة'),
        ('cancelled', 'ملغية')
    ])
    tax_status = SelectField('حالة الضريبة', choices=[
        ('', 'جميع الحالات'),
        ('pending', 'في الانتظار'),
        ('sent', 'مرسلة'),
        ('accepted', 'مقبولة'),
        ('rejected', 'مرفوضة')
    ])
    date_from = DateField('من تاريخ')
    date_to = DateField('إلى تاريخ')
    submit = SubmitField('بحث')
    
    def __init__(self, *args, **kwargs):
        super(InvoiceSearchForm, self).__init__(*args, **kwargs)
        
        # Populate customer choices
        self.customer_id.choices = [('', 'جميع العملاء')]
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        
        for customer in customers:
            self.customer_id.choices.append((
                customer.id, 
                customer.name
            ))

class QuickInvoiceForm(FlaskForm):
    """Quick invoice form for simple single-line invoices"""
    customer_id = SelectField('العميل', coerce=str, validators=[DataRequired()])
    description = TextAreaField('الوصف', validators=[DataRequired(), Length(max=500)])
    amount = DecimalField('المبلغ', validators=[
        DataRequired(), 
        NumberRange(min=0.01, message='المبلغ يجب أن يكون أكبر من صفر')
    ], places=2)
    tax_percent = DecimalField('نسبة الضريبة %', validators=[
        NumberRange(min=0, max=100, message='نسبة الضريبة يجب أن تكون بين 0 و 100')
    ], places=2, default=14)
    issue_date = DateField('تاريخ الإصدار', validators=[DataRequired()], default=date.today)
    due_date = DateField('تاريخ الاستحقاق', default=lambda: date.today() + timedelta(days=30))
    notes = TextAreaField('ملاحظات', validators=[Length(max=1000)])
    submit = SubmitField('إنشاء فاتورة')
    
    def __init__(self, *args, **kwargs):
        super(QuickInvoiceForm, self).__init__(*args, **kwargs)
        
        # Populate customer choices
        self.customer_id.choices = [('', 'اختر العميل')]
        customers = Customer.query.filter_by(is_active=True).order_by(Customer.name).all()
        
        for customer in customers:
            self.customer_id.choices.append((
                customer.id, 
                customer.name
            ))
