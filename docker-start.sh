#!/bin/bash

# SystemTax Docker Startup Script
# سكريبت تشغيل SystemTax باستخدام Docker

set -e

echo "=========================================="
echo "    SystemTax - نظام الضرائب المتكامل"
echo "=========================================="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Function to check if containers are running
check_containers() {
    print_status "Checking container status..."
    docker-compose ps
}

# Function to view logs
view_logs() {
    print_status "Showing application logs..."
    docker-compose logs -f web
}

# Function to stop all services
stop_services() {
    print_status "Stopping all services..."
    docker-compose down
    print_success "All services stopped."
}

# Function to restart services
restart_services() {
    print_status "Restarting services..."
    docker-compose restart
    print_success "Services restarted."
}

# Function to clean up (remove containers and volumes)
cleanup() {
    print_warning "This will remove all containers and data. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Cleaning up containers and volumes..."
        docker-compose down -v --remove-orphans
        docker system prune -f
        print_success "Cleanup completed."
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to backup database
backup_database() {
    print_status "Creating database backup..."
    timestamp=$(date +"%Y%m%d_%H%M%S")
    backup_file="backup_systemtax_${timestamp}.sql"
    
    docker-compose exec db pg_dump -U systemtax_user systemtax > "$backup_file"
    
    if [ $? -eq 0 ]; then
        print_success "Database backup created: $backup_file"
    else
        print_error "Database backup failed."
    fi
}

# Function to restore database
restore_database() {
    if [ -z "$1" ]; then
        print_error "Please provide backup file path."
        print_status "Usage: $0 restore <backup_file.sql>"
        exit 1
    fi
    
    if [ ! -f "$1" ]; then
        print_error "Backup file not found: $1"
        exit 1
    fi
    
    print_warning "This will replace all existing data. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Restoring database from: $1"
        docker-compose exec -T db psql -U systemtax_user systemtax < "$1"
        
        if [ $? -eq 0 ]; then
            print_success "Database restored successfully."
        else
            print_error "Database restore failed."
        fi
    else
        print_status "Restore cancelled."
    fi
}

# Main execution
case "$1" in
    "start")
        print_status "Starting SystemTax services..."
        
        # Create necessary directories
        mkdir -p uploads instance nginx/ssl
        
        # Start services
        docker-compose up -d
        
        # Wait for services to be ready
        print_status "Waiting for services to start..."
        sleep 10
        
        # Check if services are running
        if docker-compose ps | grep -q "Up"; then
            print_success "SystemTax is now running!"
            echo ""
            echo "🌐 Application URL: http://localhost:8000"
            echo "👤 Default Login: admin / admin123"
            echo "🗄️  Database: PostgreSQL on localhost:5432"
            echo "🔄 Redis Cache: localhost:6379"
            echo ""
            print_status "Use '$0 logs' to view application logs"
            print_status "Use '$0 stop' to stop all services"
        else
            print_error "Some services failed to start. Check logs with '$0 logs'"
        fi
        ;;
    
    "stop")
        stop_services
        ;;
    
    "restart")
        restart_services
        ;;
    
    "status")
        check_containers
        ;;
    
    "logs")
        view_logs
        ;;
    
    "backup")
        backup_database
        ;;
    
    "restore")
        restore_database "$2"
        ;;
    
    "cleanup")
        cleanup
        ;;
    
    "build")
        print_status "Building SystemTax Docker image..."
        docker-compose build --no-cache
        print_success "Build completed."
        ;;
    
    "update")
        print_status "Updating SystemTax..."
        docker-compose pull
        docker-compose build --no-cache
        docker-compose up -d
        print_success "Update completed."
        ;;
    
    *)
        echo "SystemTax Docker Management Script"
        echo ""
        echo "Usage: $0 {start|stop|restart|status|logs|backup|restore|cleanup|build|update}"
        echo ""
        echo "Commands:"
        echo "  start    - Start all services"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        echo "  status   - Show container status"
        echo "  logs     - View application logs"
        echo "  backup   - Create database backup"
        echo "  restore  - Restore database from backup"
        echo "  cleanup  - Remove all containers and data"
        echo "  build    - Build Docker image"
        echo "  update   - Update and restart services"
        echo ""
        exit 1
        ;;
esac
