#!/usr/bin/env python3
"""
إصلاح سريع لصفحة Analytics
Quick fix for Analytics page
"""

import requests
import time

def test_analytics():
    """اختبار صفحة Analytics"""
    
    print('🧪 اختبار صفحة Analytics...')
    
    try:
        # انتظار قليل للتأكد من تشغيل الخادم
        time.sleep(2)
        
        # اختبار صفحة تسجيل الدخول أولاً
        login_response = requests.get('http://localhost:8000/auth/login', timeout=5)
        print(f'✅ صفحة تسجيل الدخول: {login_response.status_code}')
        
        if login_response.status_code != 200:
            print('❌ مشكلة في صفحة تسجيل الدخول')
            return False
        
        # محاولة الوصول لصفحة Analytics (ستعيد توجيه لتسجيل الدخول)
        analytics_response = requests.get('http://localhost:8000/analytics', timeout=5)
        print(f'📊 صفحة Analytics: {analytics_response.status_code}')
        
        if analytics_response.status_code == 302:
            print('⚠️ صفحة Analytics تحتاج تسجيل دخول (طبيعي)')
            return True
        elif analytics_response.status_code == 200:
            print('🎉 صفحة Analytics تعمل بنجاح!')
            return True
        else:
            print(f'❌ مشكلة في صفحة Analytics: {analytics_response.status_code}')
            return False
            
    except Exception as e:
        print(f'❌ خطأ في الاتصال: {e}')
        return False

def test_all_pages():
    """اختبار جميع الصفحات"""
    
    pages = [
        ('الصفحة الرئيسية', 'http://localhost:8000/'),
        ('تسجيل الدخول', 'http://localhost:8000/auth/login'),
        ('Analytics', 'http://localhost:8000/analytics'),
        ('الفواتير', 'http://localhost:8000/invoices/'),
        ('العملاء', 'http://localhost:8000/customers/'),
        ('الإيصالات', 'http://localhost:8000/receipts/'),
        ('الحسابات', 'http://localhost:8000/accounts/'),
        ('الإعدادات', 'http://localhost:8000/settings/')
    ]
    
    print('🧪 اختبار جميع صفحات النظام...')
    print('=' * 45)
    
    success_count = 0
    total_count = len(pages)
    
    for page_name, url in pages:
        try:
            response = requests.get(url, timeout=3)
            if response.status_code == 200:
                status = '✅'
                success_count += 1
            elif response.status_code == 302:
                status = '⚠️'  # إعادة توجيه (تسجيل دخول)
                success_count += 1  # نعتبرها نجاح لأنها تعيد توجيه طبيعي
            else:
                status = '❌'
            
            print(f'{status} {page_name}: {response.status_code}')
            
        except Exception as e:
            print(f'❌ {page_name}: خطأ في الاتصال')
    
    print()
    print('=' * 45)
    print(f'📊 النتائج: {success_count}/{total_count} صفحة تعمل')
    
    if success_count >= 7:
        print('🎉 SystemTax يعمل بنجاح!')
        print('✅ جميع الصفحات متاحة')
        print('✅ النظام جاهز للاستخدام')
        return True
    else:
        print('⚠️ بعض الصفحات تحتاج مراجعة')
        return False

def main():
    """الدالة الرئيسية"""
    
    print('🔧 إصلاح سريع لـ SystemTax')
    print('=' * 30)
    
    # اختبار صفحة Analytics
    analytics_ok = test_analytics()
    
    print()
    
    # اختبار جميع الصفحات
    all_pages_ok = test_all_pages()
    
    print()
    print('=' * 30)
    
    if analytics_ok and all_pages_ok:
        print('🎉 جميع الاختبارات نجحت!')
        print('🚀 SystemTax يعمل بنجاح كامل!')
        print()
        print('🔑 بيانات تسجيل الدخول:')
        print('   👤 المستخدم: admin')
        print('   🔒 كلمة المرور: admin123')
        print()
        print('🌐 رابط النظام: http://localhost:8000')
        print('📊 رابط Analytics: http://localhost:8000/analytics')
        
        return True
    else:
        print('⚠️ بعض الاختبارات فشلت')
        print('🔧 قد تحتاج مراجعة إضافية')
        
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
