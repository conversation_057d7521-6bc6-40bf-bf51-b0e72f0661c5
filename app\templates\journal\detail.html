{% extends "base.html" %}

{% block title %}{{ entry.reference_number }} - تفاصيل القيد - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-file-alt me-3"></i>
                قيد رقم {{ entry.reference_number }}
            </h1>
            <nav aria-label="breadcrumb" class="mt-2">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('journal.index') }}">دفتر اليومية</a></li>
                    <li class="breadcrumb-item active">{{ entry.reference_number }}</li>
                </ol>
            </nav>
        </div>
        <div>
            {% if not entry.is_posted %}
            <a href="{{ url_for('journal.edit', entry_id=entry.id) }}" class="btn btn-warning">
                <i class="fas fa-edit me-2"></i>
                تعديل
            </a>
            {% endif %}
            <a href="{{ url_for('journal.index') }}" class="btn btn-outline-light">
                <i class="fas fa-arrow-left me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Entry Details -->
    <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات القيد
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رقم المرجع:</strong></td>
                                <td><span class="badge bg-primary fs-6">{{ entry.reference_number }}</span></td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ القيد:</strong></td>
                                <td>{{ entry.entry_date|date }}</td>
                            </tr>
                            <tr>
                                <td><strong>البيان:</strong></td>
                                <td>{{ entry.description }}</td>
                            </tr>
                            {% if entry.description_en %}
                            <tr>
                                <td><strong>البيان بالإنجليزية:</strong></td>
                                <td>{{ entry.description_en }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    {% if entry.is_posted %}
                                        <span class="badge bg-success">مرحل</span>
                                    {% else %}
                                        <span class="badge bg-warning">مسودة</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإنشاء:</strong></td>
                                <td>{{ entry.created_at|datetime }}</td>
                            </tr>
                            <tr>
                                <td><strong>المستخدم:</strong></td>
                                <td>{{ entry.created_by.username if entry.created_by else 'غير محدد' }}</td>
                            </tr>
                            {% if entry.posted_at %}
                            <tr>
                                <td><strong>تاريخ الترحيل:</strong></td>
                                <td>{{ entry.posted_at|datetime }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Journal Lines -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    بنود القيد
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th>#</th>
                                <th>الحساب</th>
                                <th>البيان</th>
                                <th class="text-center">مدين</th>
                                <th class="text-center">دائن</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for line in entry.lines %}
                            <tr>
                                <td class="text-center">{{ loop.index }}</td>
                                <td>
                                    <a href="{{ url_for('accounts.detail', account_id=line.account.id) }}" 
                                       class="text-decoration-none">
                                        <strong>{{ line.account.code }}</strong> - {{ line.account.name }}
                                    </a>
                                    {% if line.account.name_en %}
                                        <br><small class="text-muted">{{ line.account.name_en }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if line.description %}
                                        {{ line.description }}
                                    {% else %}
                                        <span class="text-muted">{{ entry.description }}</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if line.dc == 'D' %}
                                        <strong class="text-success">{{ line.amount|currency }}</strong>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if line.dc == 'C' %}
                                        <strong class="text-danger">{{ line.amount|currency }}</strong>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="3" class="text-center">الإجمالي</th>
                                <th class="text-center">
                                    <strong class="text-success">{{ entry.get_total_debits()|currency }}</strong>
                                </th>
                                <th class="text-center">
                                    <strong class="text-danger">{{ entry.get_total_credits()|currency }}</strong>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                
                <!-- Balance Check -->
                {% set difference = entry.get_total_debits() - entry.get_total_credits() %}
                <div class="mt-3">
                    {% if difference|abs < 0.01 %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            القيد متوازن ✓
                        </div>
                    {% else %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            القيد غير متوازن! الفرق: {{ difference|currency }}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Related Transactions -->
        {% if related_entries %}
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>
                    قيود مرتبطة
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم المرجع</th>
                                <th>التاريخ</th>
                                <th>البيان</th>
                                <th>المبلغ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for related in related_entries %}
                            <tr>
                                <td>
                                    <a href="{{ url_for('journal.detail', entry_id=related.id) }}">
                                        {{ related.reference_number }}
                                    </a>
                                </td>
                                <td>{{ related.entry_date|date }}</td>
                                <td>{{ related.description }}</td>
                                <td>{{ related.get_total_amount()|currency }}</td>
                                <td>
                                    <a href="{{ url_for('journal.detail', entry_id=related.id) }}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Sidebar -->
    <div class="col-lg-4">
        <!-- Quick Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if not entry.is_posted %}
                    <a href="{{ url_for('journal.edit', entry_id=entry.id) }}" class="btn btn-outline-warning">
                        <i class="fas fa-edit me-2"></i>
                        تعديل القيد
                    </a>
                    
                    <form method="POST" action="{{ url_for('journal.post', entry_id=entry.id) }}" 
                          onsubmit="return confirm('هل تريد ترحيل هذا القيد؟')">
                        <button type="submit" class="btn btn-outline-success w-100">
                            <i class="fas fa-check me-2"></i>
                            ترحيل القيد
                        </button>
                    </form>
                    
                    <form method="POST" action="{{ url_for('journal.delete', entry_id=entry.id) }}" 
                          onsubmit="return confirm('هل أنت متأكد من حذف هذا القيد؟')">
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash me-2"></i>
                            حذف القيد
                        </button>
                    </form>
                    {% else %}
                    {% if current_user.is_admin() %}
                    <form method="POST" action="{{ url_for('journal.unpost', entry_id=entry.id) }}" 
                          onsubmit="return confirm('هل تريد إلغاء ترحيل هذا القيد؟')">
                        <button type="submit" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-undo me-2"></i>
                            إلغاء الترحيل
                        </button>
                    </form>
                    {% endif %}
                    {% endif %}
                    
                    <a href="{{ url_for('journal.new') }}" class="btn btn-outline-primary">
                        <i class="fas fa-plus me-2"></i>
                        قيد جديد
                    </a>
                    
                    <button type="button" class="btn btn-outline-info" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>
                        طباعة
                    </button>
                </div>
            </div>
        </div>

        <!-- Entry Summary -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    ملخص القيد
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-success">{{ entry.get_total_debits()|currency }}</h4>
                        <small>إجمالي المدين</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-danger">{{ entry.get_total_credits()|currency }}</h4>
                        <small>إجمالي الدائن</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="text-center">
                    <h5 class="text-primary">{{ entry.lines.count() }}</h5>
                    <small>عدد البنود</small>
                </div>
            </div>
        </div>

        <!-- Affected Accounts -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    الحسابات المتأثرة
                </h6>
            </div>
            <div class="card-body">
                {% for line in entry.lines %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <a href="{{ url_for('accounts.detail', account_id=line.account.id) }}" 
                           class="text-decoration-none">
                            <strong>{{ line.account.code }}</strong>
                        </a>
                        <br><small>{{ line.account.name }}</small>
                    </div>
                    <div class="text-end">
                        {% if line.dc == 'D' %}
                            <span class="badge bg-success">مدين</span>
                        {% else %}
                            <span class="badge bg-danger">دائن</span>
                        {% endif %}
                        <br><small>{{ line.amount|currency }}</small>
                    </div>
                </div>
                {% if not loop.last %}<hr>{% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
@media print {
    .page-header .btn,
    .col-lg-4,
    .card-header {
        display: none !important;
    }
    
    .col-lg-8 {
        width: 100% !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .table {
        font-size: 12px;
    }
}

.table th {
    background-color: #f8f9fa !important;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6 !important;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Auto-refresh entry status every 30 seconds
setInterval(function() {
    fetch('{{ url_for("journal.status", entry_id=entry.id) }}')
        .then(response => response.json())
        .then(data => {
            // Update status if changed
            console.log('Entry status:', data.is_posted);
        })
        .catch(error => console.error('Error checking status:', error));
}, 30000);
</script>
{% endblock %}
