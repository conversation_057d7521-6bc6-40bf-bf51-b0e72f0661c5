"""
Journal Entry and Journal Line models for double-entry bookkeeping
"""

import uuid
from datetime import datetime, date
from decimal import Decimal
from app import db

class JournalEntry(db.Model):
    __tablename__ = 'journal_entries'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    entry_date = db.Column(db.Date, nullable=False, index=True, default=date.today)
    reference_number = db.Column(db.String(50), index=True)
    description = db.Column(db.Text)
    description_en = db.Column(db.Text)
    total_amount = db.Column(db.Numeric(14, 2), nullable=False, default=0)
    is_posted = db.Column(db.<PERSON>, default=False, nullable=False)
    posted_at = db.Column(db.DateTime)
    posted_by = db.Column(db.String(36), db.<PERSON><PERSON><PERSON>('users.id'))
    created_by = db.Column(db.String(36), db.<PERSON><PERSON><PERSON>('users.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    lines = db.relationship('JournalLine', backref='journal_entry', lazy='dynamic',
                           cascade='all, delete-orphan')
    
    def __init__(self, entry_date=None, description=None, description_en=None, 
                 reference_number=None, created_by=None):
        self.entry_date = entry_date or date.today()
        self.description = description
        self.description_en = description_en
        self.reference_number = reference_number
        self.created_by = created_by
    
    def add_line(self, account_id, amount, dc, description=None):
        """Add a journal line to this entry"""
        line = JournalLine(
            journal_id=self.id,
            account_id=account_id,
            amount=amount,
            dc=dc,
            description=description
        )
        db.session.add(line)
        return line
    
    def get_total_debits(self):
        """Get total debit amount"""
        total = self.lines.filter_by(dc='D').with_entities(
            db.func.sum(JournalLine.amount)
        ).scalar()
        return float(total or 0)
    
    def get_total_credits(self):
        """Get total credit amount"""
        total = self.lines.filter_by(dc='C').with_entities(
            db.func.sum(JournalLine.amount)
        ).scalar()
        return float(total or 0)
    
    def is_balanced(self):
        """Check if journal entry is balanced (debits = credits)"""
        return abs(self.get_total_debits() - self.get_total_credits()) < 0.01
    
    def calculate_total_amount(self):
        """Calculate and update total amount"""
        self.total_amount = self.get_total_debits()
        return self.total_amount
    
    def get_next_reference_number(self):
        """Generate next reference number"""
        last_entry = JournalEntry.query.filter(
            JournalEntry.entry_date >= date(self.entry_date.year, 1, 1),
            JournalEntry.entry_date <= date(self.entry_date.year, 12, 31)
        ).order_by(JournalEntry.reference_number.desc()).first()
        
        if last_entry and last_entry.reference_number:
            try:
                last_num = int(last_entry.reference_number.split('-')[-1])
                return f"JE-{self.entry_date.year}-{last_num + 1:04d}"
            except:
                pass
        
        return f"JE-{self.entry_date.year}-0001"
    
    @classmethod
    def create_simple_entry(cls, entry_date, description, debit_account_id, 
                           credit_account_id, amount, created_by=None):
        """Create a simple two-line journal entry"""
        entry = cls(
            entry_date=entry_date,
            description=description,
            created_by=created_by
        )
        
        db.session.add(entry)
        db.session.flush()  # Get the ID
        
        # Generate reference number
        entry.reference_number = entry.get_next_reference_number()
        
        # Add debit line
        entry.add_line(debit_account_id, amount, 'D', description)
        
        # Add credit line
        entry.add_line(credit_account_id, amount, 'C', description)
        
        entry.calculate_total_amount()
        
        return entry

    def post(self, posted_by=None):
        """Post the journal entry"""
        if self.is_posted:
            raise ValueError("Journal entry is already posted")

        if not self.is_balanced():
            raise ValueError("Journal entry is not balanced")

        self.is_posted = True
        self.posted_at = datetime.utcnow()
        self.posted_by = posted_by

        return True

    def unpost(self):
        """Unpost the journal entry"""
        if not self.is_posted:
            raise ValueError("Journal entry is not posted")

        self.is_posted = False
        self.posted_at = None
        self.posted_by = None

        return True

    def to_dict(self):
        """Convert journal entry to dictionary"""
        return {
            'id': self.id,
            'entry_date': self.entry_date.isoformat() if self.entry_date else None,
            'reference_number': self.reference_number,
            'description': self.description,
            'description_en': self.description_en,
            'total_amount': float(self.total_amount),
            'total_debits': self.get_total_debits(),
            'total_credits': self.get_total_credits(),
            'is_balanced': self.is_balanced(),
            'lines_count': self.lines.count(),
            'created_by': self.created_by,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<JournalEntry {self.reference_number}: {self.description}>'


class JournalLine(db.Model):
    __tablename__ = 'journal_entry_lines'

    id = db.Column(db.Integer, primary_key=True)
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entries.id'),
                                nullable=False, index=True)
    account_id = db.Column(db.Integer, db.ForeignKey('accounts.id'),
                          nullable=False, index=True)
    description = db.Column(db.Text)
    debit = db.Column(db.Numeric(15, 2), default=0)
    credit = db.Column(db.Numeric(15, 2), default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def __init__(self, journal_entry_id, account_id, debit=0, credit=0, description=None):
        self.journal_entry_id = journal_entry_id
        self.account_id = account_id
        self.debit = debit
        self.credit = credit
        self.description = description

    def get_amount(self):
        """Get the amount (debit or credit)"""
        return self.debit if self.debit > 0 else self.credit

    def get_dc(self):
        """Get debit/credit indicator"""
        return 'D' if self.debit > 0 else 'C'

    def get_dc_display(self):
        """Get Arabic display for debit/credit"""
        return 'مدين' if self.debit > 0 else 'دائن'

    def to_dict(self):
        """Convert journal line to dictionary"""
        return {
            'id': self.id,
            'journal_entry_id': self.journal_entry_id,
            'account_id': self.account_id,
            'account_code': self.account.code if self.account else None,
            'account_name': self.account.name if self.account else None,
            'description': self.description,
            'debit': float(self.debit),
            'credit': float(self.credit),
            'amount': float(self.get_amount()),
            'dc': self.get_dc(),
            'dc_display': self.get_dc_display(),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<JournalLine {self.get_dc()}: {self.get_amount()}>'
