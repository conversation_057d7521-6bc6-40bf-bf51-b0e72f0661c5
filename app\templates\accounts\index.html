{% extends "base.html" %}

{% block title %}دليل الحسابات - {{ APP_NAME }}{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="mb-0">
                <i class="fas fa-list me-3"></i>
                دليل الحسابات
            </h1>
            <p class="mb-0 mt-2">إدارة الحسابات المحاسبية والهيكل الهرمي</p>
        </div>
        <div>
            <a href="{{ url_for('accounts.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                حساب جديد
            </a>
            <a href="{{ url_for('accounts.tree') }}" class="btn btn-outline-light">
                <i class="fas fa-sitemap me-2"></i>
                العرض الهرمي
            </a>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                {{ form.search.label(class="form-label") }}
                {{ form.search(class="form-control", placeholder="البحث في الحسابات...") }}
            </div>
            <div class="col-md-3">
                {{ form.type.label(class="form-label") }}
                {{ form.type(class="form-select") }}
            </div>
            <div class="col-md-3">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search me-2"></i>
                        بحث
                    </button>
                </div>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <a href="{{ url_for('accounts.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-2"></i>
                        مسح
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Accounts Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            <i class="fas fa-table me-2"></i>
            قائمة الحسابات
        </h5>
        <span class="badge bg-primary">{{ accounts.total }} حساب</span>
    </div>
    <div class="card-body">
        {% if accounts.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>رمز الحساب</th>
                        <th>اسم الحساب</th>
                        <th>النوع</th>
                        <th>الحساب الأب</th>
                        <th>الرصيد</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for account in accounts.items %}
                    <tr>
                        <td>
                            <strong class="text-primary">{{ account.code }}</strong>
                        </td>
                        <td>
                            <a href="{{ url_for('accounts.detail', account_id=account.id) }}" 
                               class="text-decoration-none">
                                {{ account.name }}
                            </a>
                            {% if account.name_en %}
                                <br><small class="text-muted">{{ account.name_en }}</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if account.type == 'Asset' else 'danger' if account.type == 'Liability' else 'primary' if account.type == 'Equity' else 'info' if account.type == 'Income' else 'warning' }}">
                                {{ account.get_type_display() }}
                            </span>
                        </td>
                        <td>
                            {% if account.parent %}
                                <small>{{ account.parent.code }} - {{ account.parent.name }}</small>
                            {% else %}
                                <span class="text-muted">حساب رئيسي</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if account.is_leaf_account() %}
                                <span class="fw-bold">{{ account.get_balance()|currency }}</span>
                            {% else %}
                                <span class="text-muted">حساب تجميعي</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if account.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('accounts.detail', account_id=account.id) }}" 
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('accounts.edit', account_id=account.id) }}" 
                                   class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if account.can_be_deleted() %}
                                <form method="POST" action="{{ url_for('accounts.delete', account_id=account.id) }}" 
                                      class="d-inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الحساب؟')">
                                    <button type="submit" class="btn btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if accounts.pages > 1 %}
        <nav aria-label="تصفح الصفحات">
            <ul class="pagination justify-content-center">
                {% if accounts.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('accounts.index', page=accounts.prev_num, **request.args) }}">
                            السابق
                        </a>
                    </li>
                {% endif %}
                
                {% for page_num in accounts.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != accounts.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('accounts.index', page=page_num, **request.args) }}">
                                    {{ page_num }}
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if accounts.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('accounts.index', page=accounts.next_num, **request.args) }}">
                            التالي
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5>لا توجد حسابات</h5>
            <p class="text-muted">لم يتم العثور على أي حسابات تطابق معايير البحث.</p>
            <a href="{{ url_for('accounts.new') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة حساب جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">الأصول</h5>
                <h3 class="text-success">{{ stats.assets_count if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">الخصوم</h5>
                <h3 class="text-danger">{{ stats.liabilities_count if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">رأس المال</h5>
                <h3 class="text-primary">{{ stats.equity_count if stats else 0 }}</h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">الإيرادات</h5>
                <h3 class="text-info">{{ stats.income_count if stats else 0 }}</h3>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Auto-submit search form on type change
    $('#type').change(function() {
        $(this).closest('form').submit();
    });
    
    // Highlight search terms
    const searchTerm = $('#search').val();
    if (searchTerm) {
        $('table tbody td').each(function() {
            const text = $(this).text();
            if (text.toLowerCase().includes(searchTerm.toLowerCase())) {
                $(this).html(text.replace(new RegExp(searchTerm, 'gi'), '<mark>$&</mark>'));
            }
        });
    }
});
</script>
{% endblock %}
