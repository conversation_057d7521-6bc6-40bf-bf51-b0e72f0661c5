# SystemTax - نظام محاسبي ويب متكامل

<div align="center">

![SystemTax Logo](https://via.placeholder.com/200x100/2563eb/ffffff?text=SystemTax)

**نظام محاسبي ويب شامل مع دعم الفاتورة الإلكترونية والإيصال الإلكتروني**

[![Python](https://img.shields.io/badge/Python-3.9+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)](https://flask.palletsprojects.com)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15+-blue.svg)](https://postgresql.org)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

[العربية](#العربية) | [English](#english)

</div>

---

## العربية

### نظرة عامة

SystemTax هو نظام محاسبي ويب متكامل مصمم خصيصاً للشركات المصرية مع دعم كامل للفاتورة الإلكترونية والتكامل مع مصلحة الضرائب المصرية.

## المميزات الرئيسية

### 🔐 المصادقة والتفويض
- نظام تسجيل دخول آمن
- إدارة الصلاحيات (مدير، محاسب، موظف)
- حماية الجلسات والبيانات

### 📊 النظام المحاسبي
- دليل الحسابات الشامل
- القيود اليومية المزدوجة
- دفتر الأستاذ العام
- الميزان العمومي

### 👥 إدارة العملاء والموردين
- قاعدة بيانات شاملة للعملاء
- إدارة بيانات الموردين
- تتبع المعاملات والأرصدة

### 🧾 الفواتير والإيصالات الإلكترونية
- إصدار فواتير إلكترونية بصيغة PDF
- إيصالات إلكترونية
- التكامل مع منظومة الضرائب المصرية
- ترقيم تلقائي للمستندات

### 📈 التقارير والتحليلات
- لوحة تحكم تفاعلية
- تقارير ضريبية دورية
- تصدير التقارير (PDF/CSV/Excel)
- مؤشرات الأداء المالي

## متطلبات النظام

- Python 3.10+
- PostgreSQL 13+
- Redis 6+
- Docker & Docker Compose

## التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd SystemTax
```

### 2. إعداد متغيرات البيئة
```bash
cp .env.example .env
# قم بتحرير ملف .env وإدخال البيانات المطلوبة
```

### 3. تشغيل النظام باستخدام Docker
```bash
docker-compose up -d
```

### 4. إعداد قاعدة البيانات
```bash
docker-compose exec web flask db upgrade
```

### 5. إنشاء مستخدم مدير
```bash
docker-compose exec web python -c "from app.utils.admin import create_admin_user; create_admin_user()"
```

## التشغيل السريع

### للمبتدئين (Windows)
1. تحميل المشروع وفك الضغط
2. تشغيل ملف `start.bat`
3. انتظار اكتمال التثبيت
4. فتح http://localhost:8000

### للمبتدئين (Linux/macOS)
```bash
git clone https://github.com/your-username/systemtax.git
cd systemtax
./start.sh
```

### باستخدام Makefile
```bash
make quickstart
```

## الوصول للنظام

بعد التشغيل، يمكن الوصول للنظام عبر:
- **الواجهة الرئيسية**: http://localhost:8000
- **المستخدم الافتراضي**: admin / admin123
- **قاعدة البيانات**: localhost:5432
- **Redis**: localhost:6379

## هيكل المشروع

```
SystemTax/
├── app/
│   ├── models/          # نماذج قاعدة البيانات
│   ├── views/           # واجهات التطبيق
│   ├── templates/       # قوالب HTML
│   ├── static/          # الملفات الثابتة (CSS, JS, Images)
│   ├── forms/           # نماذج الإدخال
│   ├── utils/           # الأدوات المساعدة
│   └── api/             # واجهات برمجة التطبيقات
├── database/            # سكريبتات قاعدة البيانات
├── tests/               # الاختبارات
├── uploads/             # ملفات التحميل
├── docker-compose.yml   # تكوين Docker
├── Dockerfile           # صورة Docker
└── requirements.txt     # متطلبات Python
```

## التطوير

### تثبيت البيئة المحلية
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows

pip install -r requirements.txt
```

### تشغيل الاختبارات
```bash
pytest tests/
```

### تشغيل النظام في وضع التطوير
```bash
flask run --debug
```

## الأمان

- تشفير كلمات المرور باستخدام bcrypt
- حماية CSRF
- جلسات آمنة
- التحقق من الصلاحيات على جميع المسارات

## الدعم والمساهمة

للإبلاغ عن مشاكل أو طلب مميزات جديدة، يرجى إنشاء issue في المستودع.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الإصدار

الإصدار الحالي: 1.0.0
